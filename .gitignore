# 忽略所有 .log 文件
*.log
*.md
*.yaml
*.yml
.DS_Store
*.sh

.idea

**/pb/

data/
test/
# 忽略所有临时文件
*.tmp

# 忽略所有编译生成的文件
*.o
*.out
*.class

**/bak/
**/logicBak/

# 忽略所有 Node.js 生成的文件
node_modules/
npm-debug.log

# 忽略所有 Python 生成的文件
__pycache__/
*.pyc
*.pyo
*.pyd

# 忽略所有 Go 生成的文件
*.a
*.so

# 忽略所有 Java 生成的文件
target/
*.jar
*.class

# 忽略所有 Visual Studio 生成的文件
*.suo
*.user
*.userosscache
*.sln.docstates

# 忽略所有 IntelliJ IDEA 生成的文件
.idea/
*.iml

# 忽略所有 Eclipse 生成的文件
.metadata/
bin/
*.launch
.settings/
*.project
*.classpath

# 忽略所有 Docker 生成的文件
Dockerfile
.dockerignore
.dockerenv

# 忽略所有环境配置文件
.env
.env.local
.env.development
.env.production

# 忽略所有临时文件
*~

# 忽略所有自动生成的文档
docs/


.cursor/
#go build 执行文件
application/wapp/api/wapp
application/wapp/rpc/pfs/pfs
application/wapp/rpc/xjs/xjs
application/wapp/rpc/ec/ec
application/wapp/rpc/ai/ai
**/routes.go
**/types.go
application/**/rpc/**/client/
!application/**/rpc/**/wechat/client/
application/**/rpc/**/internal/server/

