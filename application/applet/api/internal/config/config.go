package config

import (
	"xj-serv/pkg/middleware"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	rest.RestConf
	JwtAuth struct {
		AccessSecret string
		AccessExpire int
	}
	BizRedis redis.RedisConf
	Page     struct {
		PageNo   int64
		PageSize int64
	}
	PfsRPC       zrpc.RpcClientConf
	AppletRPC    zrpc.RpcClientConf
	AdminUserRpc zrpc.RpcClientConf // 添加AdminUserRpc配置
	CasbinConf   middleware.CasbinConf
	DB           struct {
		DataSource   string
		MaxOpenConns int `json:",default=10"`
		MaxIdleConns int `json:",default=100"`
		MaxLifetime  int `json:",default=3600"`
	}
	Oss struct {
		Endpoint         string
		AccessKeyId      string
		AccessKeySecret  string
		BucketName       string
		ConnectTimeout   int64 `json:",optional"`
		ReadWriteTimeout int64 `json:",optional"`
	}
	Isdev bool
}
