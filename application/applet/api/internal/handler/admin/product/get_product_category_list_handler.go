package product

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/applet/api/internal/logic/admin/product"
	"xj-serv/application/applet/api/internal/svc"
)

// 获取商品列表
func GetProductCategoryListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := product.NewGetProductCategoryListLogic(r.Context(), svcCtx)
		resp, err := l.GetProductCategoryList()
		result.HttpResult(r, w, resp, err)
	}
}
