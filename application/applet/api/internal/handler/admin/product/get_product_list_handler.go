package product

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/product"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 获取商品列表
func GetProductListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetProductListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := product.NewGetProductListLogic(r.Context(), svcCtx)
		resp, err := l.GetProductList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
