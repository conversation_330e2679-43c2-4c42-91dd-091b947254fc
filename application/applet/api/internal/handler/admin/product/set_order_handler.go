package product

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/product"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 设置商品排序权重
func SetOrderHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetProductOrderReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := product.NewSetOrderLogic(r.Context(), svcCtx)
		resp, err := l.SetOrder(&req)
		result.HttpResult(r, w, resp, err)
	}
}
