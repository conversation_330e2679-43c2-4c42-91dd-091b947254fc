package finance

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/applet/api/internal/logic/admin/finance"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 获取代理商列表
func GetAgentListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AgentListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := finance.NewGetAgentListLogic(r.Context(), svcCtx)
		resp, err := l.GetAgentList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
