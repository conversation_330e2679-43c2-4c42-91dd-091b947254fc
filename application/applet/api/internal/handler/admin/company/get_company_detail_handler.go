package company

import (
	"net/http"
	"xj-serv/application/applet/api/internal/logic/admin/company"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetCompanyDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CompanyDetailReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}
		l := company.NewGetCompanyDetailLogic(r.Context(), svcCtx)
		resp, err := l.GetCompanyDetail(&req)
		result.HttpResult(r, w, resp, err)
	}
}
