package company

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/company"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 修改公司信息
func UpdateCompanyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateCompanyReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := company.NewUpdateCompanyLogic(r.Context(), svcCtx)
		resp, err := l.UpdateCompany(&req)
		result.HttpResult(r, w, resp, err)
	}
}
