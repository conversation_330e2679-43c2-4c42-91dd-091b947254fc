package company

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/applet/api/internal/logic/admin/company"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 审核公司
func AuditCompanyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AuditCompanyReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := company.NewAuditCompanyLogic(r.Context(), svcCtx)
		resp, err := l.AuditCompany(&req)
		result.HttpResult(r, w, resp, err)
	}
}
