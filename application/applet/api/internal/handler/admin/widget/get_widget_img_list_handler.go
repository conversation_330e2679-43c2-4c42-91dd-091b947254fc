package widget

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/widget"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 获取图片组件列表
func GetWidgetImgListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetWidgetImgListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := widget.NewGetWidgetImgListLogic(r.Context(), svcCtx)
		resp, err := l.GetWidgetImgList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
