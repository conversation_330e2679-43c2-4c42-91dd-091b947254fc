package user

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/user"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 调整代理商团长套餐数量
func SetAgentTeamPackageHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetAgentTeamPackageReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewSetAgentTeamPackageLogic(r.Context(), svcCtx)
		resp, err := l.SetAgentTeamPackage(&req)
		result.HttpResult(r, w, resp, err)
	}
}
