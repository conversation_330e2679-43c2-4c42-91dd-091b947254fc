package user

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/applet/api/internal/logic/admin/user"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
)

// 导出用户列表
func ExportUserListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewExportUserListLogic(r.Context(), svcCtx)
		resp, err := l.ExportUserList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
