package user

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/applet/api/internal/logic/admin/user"
	"xj-serv/application/applet/api/internal/svc"
)

// 用户等级列表
func GetUserLevelListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := user.NewGetUserLevelListLogic(r.Context(), svcCtx)
		resp, err := l.GetUserLevelList()
		result.HttpResult(r, w, resp, err)
	}
}
