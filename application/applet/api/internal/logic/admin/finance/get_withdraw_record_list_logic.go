package finance

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWithdrawRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取提现记录列表
func NewGetWithdrawRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWithdrawRecordListLogic {
	return &GetWithdrawRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWithdrawRecordListLogic) GetWithdrawRecordList(req *types.WithdrawRecordListReq) (resp *types.WithdrawRecordListResp, err error) {
	// 参数验证和默认值设置
	if req.PageNo <= 0 {
		req.PageNo = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.PageSize > 50 {
		req.PageSize = 50
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetWithdrawRecordList(l.ctx, &pb.WithdrawRecordListReq{
		PageNo:       int32(req.PageNo),
		PageSize:     int32(req.PageSize),
		UserName:     req.UserName,
		UserMobile:   req.UserMobile,
		UserId:       req.UserID,
		ReviewStatus: int32(req.ReviewStatus),
		StartDate:    req.StartDate,
		EndDate:      req.EndDate,
	})
	if err != nil {
		logx.WithContext(l.ctx).Errorf("获取提现记录列表失败: %v", err)
		return nil, err
	}

	// 转换响应数据
	list := make([]types.WithdrawRecordItem, 0, len(rpcResp.List))
	for _, item := range rpcResp.List {
		list = append(list, types.WithdrawRecordItem{
			ID:               item.Id,
			UserID:           item.UserId,
			UserName:         item.UserName,
			UserMobile:       item.UserMobile,
			UserRoleId:       int(item.UserRoleId),
			Amount:           item.Amount,
			ActualAmount:     item.ActualAmount,
			Fee:              item.Fee,
			FeeRatio:         item.FeeRatio,
			OutBillNo:        item.OutBillNo,
			TransferBillNo:   item.TransferBillNo,
			Status:           int(item.Status),
			StatusText:       item.StatusText,
			ReviewStatus:     int(item.ReviewStatus),
			ReviewStatusText: item.ReviewStatusText,
			FailReason:       item.FailReason,
			ApplyTime:        item.ApplyTime,
			ReviewTime:       item.ReviewTime,
			ProcessTime:      item.ProcessTime,
			SuccessTime:      item.SuccessTime,
			InvoiceImg:       item.InvoiceImg,
			Remark:           item.Remark,
		})
	}

	return &types.WithdrawRecordListResp{
		List:     list,
		Total:    rpcResp.Total,
		PageNo:   int(rpcResp.PageNo),
		PageSize: int(rpcResp.PageSize),
	}, nil
}
