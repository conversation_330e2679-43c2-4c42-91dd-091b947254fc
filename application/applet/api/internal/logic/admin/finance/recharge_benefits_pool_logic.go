package finance

import (
	"context"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RechargeBenefitsPoolLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 充值任务池
func NewRechargeBenefitsPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RechargeBenefitsPoolLogic {
	return &RechargeBenefitsPoolLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RechargeBenefitsPoolLogic) RechargeBenefitsPool(req *types.RechargeBenefitsPoolReq) (resp bool, err error) {
	// 参数校验
	if req.UserId <= 0 {
		return false, xerr.NewErrCode(xerr.RequestParamError)
	}
	// 调用RPC服务
	rpcResp, err := l.svcCtx.AppletAdminRPC.RechargeBenefitsPool(l.ctx, &pb.RechargeBenefitsPoolReq{
		UserId:   req.UserId,
		Quantity: req.Quantity,
	})

	if err != nil {
		l.Errorf("调用RPC充值任务池失败: %v", err)
		return false, err
	}

	if !rpcResp.Success {
		return false, xerr.NewErrMsg(l.ctx, "充值任务池失败")
	}

	return true, nil
}
