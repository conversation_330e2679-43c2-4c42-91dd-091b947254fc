package finance

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAgentListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取代理商列表
func NewGetAgentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAgentListLogic {
	return &GetAgentListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAgentListLogic) GetAgentList(req *types.AgentListReq) (resp *types.AgentListResp, err error) {

	// 构建RPC请求
	rpcReq := &pb.GetAgentListRequest{
		Page: &pb.PageRequest{
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
		Name:           req.Name,
		Mobile:         req.Mobile,
		StartTime:      req.StartTime,
		EndTime:        req.EndTime,
		CompanyName:    req.CompanyName,
		RelatedCompany: req.RelatedCompany,
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetAgentList(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("获取代理商列表失败: %v", err)
		return nil, err
	}

	// 构建API响应
	resp = &types.AgentListResp{
		List: make([]types.AgentItem, 0, len(rpcResp.Agents)),
		PageResponse: types.PageResponse{
			Total:    rpcResp.Total,
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
	}

	for _, agent := range rpcResp.Agents {
		resp.List = append(resp.List, types.AgentItem{
			Id:              agent.Id,
			Nickname:        agent.Nickname,
			Mobile:          agent.Mobile,
			PackageCount:    int(agent.PackageCount),
			CompanyName:     agent.CompanyName,
			RelatedCompany:  agent.RelatedCompany,
			TaskRewardCount: int(agent.TaskRewardCount),
			CreatedAt:       agent.CreatedAt,
		})
	}

	return resp, nil
}
