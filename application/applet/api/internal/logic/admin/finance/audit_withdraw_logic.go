package finance

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditWithdrawLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 审核提现申请
func NewAuditWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditWithdrawLogic {
	return &AuditWithdrawLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuditWithdrawLogic) AuditWithdraw(req *types.AuditWithdrawReq) (resp bool, err error) {
	_, err = l.svcCtx.AppletAdminRPC.AuditWithdraw(l.ctx, &pb.AuditWithdrawReq{
		Id:           req.ID,
		ReviewStatus: int32(req.ReviewStatus),
		FailReason:   req.FailReason,
	})
	if err != nil {
		return false, err
	}
	return true, nil
}
