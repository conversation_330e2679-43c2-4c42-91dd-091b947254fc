package company

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateCompanyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 新建公司
func NewCreateCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateCompanyLogic {
	return &CreateCompanyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateCompanyLogic) CreateCompany(req *types.CreateCompanyReq) (resp bool, err error) {
	//// 验证营业执照图片 先注释掉
	//if len(req.LicenseImg) > 0 {
	//	// 检查是否为base64格式
	//	if !strings.HasPrefix(req.LicenseImg, "data:image/") {
	//		return false, xerr.NewErrMsg(l.ctx, "营业执照图片必须是base64格式的图片")
	//	}
	//
	//	// 提取base64数据部分
	//	base64Data := ""
	//	parts := strings.Split(req.LicenseImg, ",")
	//	if len(parts) > 1 {
	//		base64Data = parts[1]
	//	} else {
	//		base64Data = parts[0]
	//	}
	//
	//	// 解码base64以检查实际大小
	//	decoded, err := base64.StdEncoding.DecodeString(base64Data)
	//	if err != nil {
	//		l.Errorf("解码base64图片失败: %v", err)
	//		return false, xerr.NewErrMsg(l.ctx, "无效的base64图片格式")
	//	}
	//
	//	// 检查文件大小限制 (2MB)
	//	if len(decoded) > 2*1024*1024 {
	//		return nil, xerr.NewErrMsg(l.ctx, "营业执照图片不能超过5MB")
	//	}
	//} else {
	//	return false, xerr.NewErrMsg(l.ctx, "营业执照图片不能为空")
	//}
	// 构建RPC请求
	rpcReq := &pb.CreateCompanyRequest{}
	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	if err != nil {
		l.Errorf("复制请求参数失败: %v", err)
		return false, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	_, err = l.svcCtx.AppletAdminRPC.CreateCompany(l.ctx, rpcReq)
	if err != nil {
		return false, err
	}
	return true, nil
}
