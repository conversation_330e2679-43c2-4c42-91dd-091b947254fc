package company

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"github.com/jinzhu/copier"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取公司列表
func NewGetCompanyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyListLogic {
	return &GetCompanyListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCompanyListLogic) GetCompanyList(req *types.CompanyListReq) (resp *types.CompanyListResp, err error) {
	var page pb.PageRequest
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}
	page.PageNo = req.PageNo
	page.PageSize = req.PageSize

	rpcReq := &pb.GetCompanyListRequest{
		PageRequest: &page,
		Name:        req.Name,
		Phone:       req.Phone,
		Status:      req.Status,
		RoleId:      req.RoleId,
		StartTime:   req.StartTime,
		EndTime:     req.EndTime,
	}

	companyList, err := l.svcCtx.AppletAdminRPC.GetCompanyList(l.ctx, rpcReq)
	if err != nil {
		logx.Errorf("l.svcCtx.AppletUserRPC.GetUserList err: %v", err)
		return nil, err
	}
	var typesCompanyInfoList []types.CompanyInfo
	_ = copier.Copy(&typesCompanyInfoList, companyList.List)

	return &types.CompanyListResp{
		List: typesCompanyInfoList,
		PageResponse: types.PageResponse{
			Total:    companyList.Total,
			PageNo:   page.PageNo,
			PageSize: page.PageSize,
		},
	}, nil
}
