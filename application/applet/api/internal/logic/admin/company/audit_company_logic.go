package company

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCompanyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 审核公司
func NewAuditCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCompanyLogic {
	return &AuditCompanyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuditCompanyLogic) AuditCompany(req *types.AuditCompanyReq) (resp bool, err error) {
	_, err = l.svcCtx.AppletAdminRPC.AuditCompany(l.ctx, &pb.AuditCompanyRequest{
		Id:     req.Id,
		Status: req.Status,
		Remark: req.Remark,
	})
	if err != nil {
		return false, err
	}
	return true, nil

}
