package company

import (
	"context"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/application/applet/rpc/pb"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCompanyDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyDetailLogic {
	return &GetCompanyDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCompanyDetailLogic) GetCompanyDetail(req *types.CompanyDetailReq) (resp *types.CompanyDetailResp, err error) {
	rpcReq := &pb.GetCompanyDetailRequest{
		Id:      req.Id,
		AdminId: 1,
	}
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetCompanyDetail(l.ctx, rpcReq)
	if err != nil {
		return nil, err
	}
	resp = &types.CompanyDetailResp{}
	_ = copier.Copy(resp, rpcResp)
	return resp, nil
}
