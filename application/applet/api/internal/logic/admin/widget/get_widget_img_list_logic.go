package widget

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWidgetImgListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取图片组件列表
func NewGetWidgetImgListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWidgetImgListLogic {
	return &GetWidgetImgListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWidgetImgListLogic) GetWidgetImgList(req *types.GetWidgetImgListReq) (resp *types.GetWidgetImgListResp, err error) {
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}

	rpcReq := &pb.GetWidgetImgListReq{
		Type:     int32(req.Type),
		PageNo:   int32(req.PageNo),
		PageSize: int32(req.PageSize),
	}

	widgetImgList, err := l.svcCtx.AppletAdminRPC.GetWidgetImgList(l.ctx, rpcReq)
	if err != nil {
		logx.Errorf("l.svcCtx.AppletUserRPC.GetUserList err: %v", err)
		return nil, err
	}
	var typesWidgetImgInfoList []types.WidgetImgInfo
	_ = copier.Copy(&typesWidgetImgInfoList, widgetImgList.List)

	return &types.GetWidgetImgListResp{
		List: typesWidgetImgInfoList,
		PageResponse: types.PageResponse{
			Total:    widgetImgList.Total,
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
	}, nil
}
