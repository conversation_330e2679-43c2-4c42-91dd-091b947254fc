package widget

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateWidgetImgLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 添加图片组件
func NewCreateWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateWidgetImgLogic {
	return &CreateWidgetImgLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateWidgetImgLogic) CreateWidgetImg(req *types.CreateWidgetImgReq) (resp bool, err error) {
	// 构建RPC请求
	rpcReq := &pb.CreateWidgetImgReq{}
	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	if err != nil {
		l.Errorf("复制请求参数失败: %v", err)
		return false, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	_, err = l.svcCtx.AppletAdminRPC.CreateWidgetImg(l.ctx, rpcReq)
	if err != nil {
		return false, err
	}
	return true, nil
}
