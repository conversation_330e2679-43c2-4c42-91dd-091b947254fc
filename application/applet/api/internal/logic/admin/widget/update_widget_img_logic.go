package widget

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateWidgetImgLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 修改图片组件
func NewUpdateWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateWidgetImgLogic {
	return &UpdateWidgetImgLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateWidgetImgLogic) UpdateWidgetImg(req *types.UpdateWidgetImgReq) (resp bool, err error) {
	// 构建RPC请求
	rpcReq := &pb.UpdateWidgetImgReq{}
	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	if err != nil {
		l.Errorf("复制请求参数失败: %v", err)
		return false, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	_, err = l.svcCtx.AppletAdminRPC.UpdateWidgetImg(l.ctx, rpcReq)
	if err != nil {
		return false, err
	}
	return true, nil
}
