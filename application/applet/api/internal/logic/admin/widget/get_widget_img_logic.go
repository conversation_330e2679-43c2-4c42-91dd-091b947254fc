package widget

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWidgetImgLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查看图片组件
func NewGetWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWidgetImgLogic {
	return &GetWidgetImgLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWidgetImgLogic) GetWidgetImg(req *types.GetWidgetImgReq) (resp *types.WidgetImgInfo, err error) {
	// 构建RPC请求
	rpcReq := &pb.GetWidgetImgReq{}
	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetWidgetImg(l.ctx, rpcReq)
	if err != nil {
		return nil, err
	}
	resp = &types.WidgetImgInfo{}
	_ = copier.Copy(resp, rpcResp)
	return resp, nil
}
