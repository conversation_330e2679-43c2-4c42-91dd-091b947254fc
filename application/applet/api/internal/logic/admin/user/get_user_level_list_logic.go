package user

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserLevelListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户等级列表
func NewGetUserLevelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLevelListLogic {
	return &GetUserLevelListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserLevelListLogic) GetUserLevelList() (resp *types.UserLevelListResp, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetUserLevelList(l.ctx, &pb.EmptyRequest{})
	if err != nil {
		return nil, err
	}

	var list []types.UserLevelItem
	for _, u := range rpcResp.List {
		var item types.UserLevelItem
		_ = copier.Copy(&item, u)
		list = append(list, item)
	}

	return &types.UserLevelListResp{
		List: list,
	}, nil
}
