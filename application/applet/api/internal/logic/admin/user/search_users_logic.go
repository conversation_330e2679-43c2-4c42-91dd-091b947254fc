package user

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 搜索用户
func NewSearchUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUsersLogic {
	return &SearchUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SearchUsersLogic) SearchUsers(req *types.SearchUsersReq) (resp *types.UserListResp, err error) {
	var page pb.PageRequest
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}
	page.PageNo = req.PageNo
	page.PageSize = req.PageSize

	rpcReq := &pb.SearchUserReq{
		PageRequest: &page,
		Keyword:     req.Keyword,
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.AppletAdminRPC.SearchUser(l.ctx, rpcReq)
	if err != nil {
		return nil, err
	}

	// 转换响应数据
	userList := make([]types.XjUser, 0, len(rpcResp.List))
	for _, u := range rpcResp.List {
		var userDetail types.XjUser
		_ = copier.Copy(&userDetail, u)
		userList = append(userList, userDetail)
	}

	return &types.UserListResp{
		List: userList,
		PageResponse: types.PageResponse{
			Total:    rpcResp.Total,
			PageNo:   page.PageNo,
			PageSize: page.PageSize,
		},
	}, nil
}
