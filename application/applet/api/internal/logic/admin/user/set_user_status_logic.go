package user

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 冻结/解冻账户
func NewSetUserStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserStatusLogic {
	return &SetUserStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetUserStatusLogic) SetUserStatus(req *types.SetUserStatusReq) (resp bool, err error) {
	_, err = l.svcCtx.AppletAdminRPC.SetUserStatus(l.ctx, &pb.SetUserStatusReq{
		UserId: req.UserId,
		Status: int32(req.Status),
	})
	if err != nil {
		return false, err
	}
	return true, nil
}
