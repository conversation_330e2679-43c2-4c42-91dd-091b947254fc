package user

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetSalesmanPermissionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置业务员权限
func NewSetSalesmanPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSalesmanPermissionLogic {
	return &SetSalesmanPermissionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetSalesmanPermissionLogic) SetSalesmanPermission(req *types.SetSalesmanPermissionReq) (resp bool, err error) {
	// 调用RPC服务设置业务员权限
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetSalesmanPermission(l.ctx, &pb.SetSalesmanPermissionReq{
		SalesmanUserId: req.SalesmanUserId,
		SalesmanRate:   req.SalesmanRate,
		SalesmanRemark: req.SalesmanRemark,
		IsFullTime:     req.IsFullTime,
	})

	if err != nil {
		l.Errorf("设置业务员权限失败: %v", err)
		return false, err
	}
	if rpcResp.Success {
		return true, nil
	}

	return false, nil
}
