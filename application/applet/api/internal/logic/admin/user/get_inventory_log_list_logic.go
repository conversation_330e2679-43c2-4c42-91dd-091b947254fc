package user

import (
	"context"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInventoryLogListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取团长套餐数日志列表
func NewGetInventoryLogListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInventoryLogListLogic {
	return &GetInventoryLogListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetInventoryLogListLogic) GetInventoryLogList(req *types.InventoryLogListReq) (resp *types.InventoryLogListResp, err error) {
	var page pb.PageRequest
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}
	page.PageNo = req.PageNo
	page.PageSize = req.PageSize

	// 调用RPC服务获取团长套餐数日志列表
	rpcReq := &pb.GetInventoryLogListReq{
		PageRequest:     &page,
		Mobile:          req.Mobile,
		StartTime:       req.StartTime,
		EndTime:         req.EndTime,
		TransactionType: req.TransactionType,
	}

	rpcResp, err := l.svcCtx.AppletAdminRPC.GetInventoryLogList(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("获取团长套餐数日志列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp = &types.InventoryLogListResp{
		List: make([]types.InventoryLogInfo, 0, len(rpcResp.List)),
		PageResponse: types.PageResponse{
			Total:    rpcResp.Total,
			PageNo:   page.PageNo,
			PageSize: page.PageSize,
		},
	}

	// 转换数据
	for _, item := range rpcResp.List {
		resp.List = append(resp.List, types.InventoryLogInfo{
			Id:              item.Id,
			UserId:          item.UserId,
			Nickname:        item.Nickname,
			Mobile:          item.Mobile,
			RoleId:          item.RoleId,
			Level:           int(item.Level),
			ProductId:       item.ProductId,
			ProductType:     item.ProductType,
			OrderId:         item.OrderId,
			TransactionType: item.TransactionType,
			Quantity:        int(item.Quantity),
			BeforeQuantity:  int(item.BeforeQuantity),
			AfterQuantity:   int(item.AfterQuantity),
			Remark:          item.Remark,
			CreatedAt:       item.CreatedAt,
		})
	}

	return resp, nil
}
