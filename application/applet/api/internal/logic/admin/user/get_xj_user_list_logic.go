package user

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetXjUserListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户列表
func NewGetXjUserListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetXjUserListLogic {
	return &GetXjUserListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetXjUserListLogic) GetXjUserList(req *types.XjUserListReq) (resp *types.XjUserListResp, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetXjUserList(l.ctx, &pb.XjUserListReq{
		PageNo:       req.PageNo,
		PageSize:     req.PageSize,
		NickName:     req.NickName,
		Mobile:       req.Mobile,
		Level:        int32(req.Level),
		RoleId:       int32(req.RoleId),
		InviteName:   req.InviteName,
		InviteMobile: req.InviteMobile,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
	})
	if err != nil {
		return nil, err
	}

	var list []types.XjUser
	for _, u := range rpcResp.List {
		var item types.XjUser
		_ = copier.Copy(&item, u)
		list = append(list, item)
	}

	return &types.XjUserListResp{
		List:     list,
		Total:    rpcResp.Total,
		PageNo:   rpcResp.PageNo,
		PageSize: rpcResp.PageSize,
	}, nil
}
