package user

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserRoleListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户等级列表
func NewGetUserRoleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserRoleListLogic {
	return &GetUserRoleListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserRoleListLogic) GetUserRoleList() (resp *types.UserRoleListResp, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetUserRoleList(l.ctx, &pb.EmptyRequest{})
	if err != nil {
		return nil, err
	}

	var list []types.UserRoleItem
	for _, u := range rpcResp.List {
		var item types.UserRoleItem
		_ = copier.Copy(&item, u)
		list = append(list, item)
	}

	return &types.UserRoleListResp{
		List: list,
	}, nil
}
