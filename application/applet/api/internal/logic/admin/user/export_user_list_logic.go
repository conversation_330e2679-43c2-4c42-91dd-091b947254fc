package user

import (
	"context"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ExportUserListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 导出用户列表
func NewExportUserListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ExportUserListLogic {
	return &ExportUserListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ExportUserListLogic) ExportUserList(req *types.UserListReq) (resp string, err error) {
	// todo: add your logic here and delete this line

	return
}
