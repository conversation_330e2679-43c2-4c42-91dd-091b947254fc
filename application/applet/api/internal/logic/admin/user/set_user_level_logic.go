package user

import (
	"context"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserLevelLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置用户等级
func NewSetUserLevelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserLevelLogic {
	return &SetUserLevelLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetUserLevelLogic) SetUserLevel(req *types.SetUserLevelReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetUserLevel(l.ctx, &pb.SetUserLevelReq{
		UserId:  req.UserId,
		LevelId: req.LevelId,
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil

}
