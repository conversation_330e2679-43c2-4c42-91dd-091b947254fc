package user

import (
	"context"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetAgentTeamPackageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 调整代理商团长套餐数量
func NewSetAgentTeamPackageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetAgentTeamPackageLogic {
	return &SetAgentTeamPackageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetAgentTeamPackageLogic) SetAgentTeamPackage(req *types.SetAgentTeamPackageReq) (resp bool, err error) {
	// 参数验证
	if req.AgentUserId <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "代理商ID不能为空")
	}
	if req.Number == 0 {
		return false, xerr.NewErrMsg(l.ctx, "套餐数量不能为0")
	}

	// 调用RPC服务设置业务经理团长套餐数量
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetAgentTeamPackage(l.ctx, &pb.SetAgentTeamPackageReq{
		AgentUserId: req.AgentUserId,
		Number:      req.Number,
	})

	if err != nil {
		l.Errorf("调整代理商团长套餐数量失败: %v", err)
		return false, err
	}

	return rpcResp.Success, nil
}
