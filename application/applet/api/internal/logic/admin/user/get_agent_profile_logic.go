package user

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAgentProfileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取代理商/业务员统计数据
func NewGetAgentProfileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAgentProfileLogic {
	return &GetAgentProfileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAgentProfileLogic) GetAgentProfile(req *types.AgentProfileReq) (resp *types.AgentProfileResp, err error) {

	userId := req.UserId
	if userId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户错误")
	}
	// 调用RPC服务获取个人主页数据
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetAgentProfile(l.ctx, &pb.GetAgentProfileReq{
		UserId: userId,
	})
	if err != nil {
		l.Errorf("获取代理商/业务员个人主页数据失败: %v", err)
		return nil, err
	}

	// 创建响应对象
	resp = &types.AgentProfileResp{}

	// 使用copier复制RPC响应到API响应
	err = copier.Copy(resp, rpcResp)
	if err != nil {
		l.Errorf("复制个人主页数据失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	return resp, nil
}
