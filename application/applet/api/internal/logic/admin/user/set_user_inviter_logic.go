package user

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserInviterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置用户上级
func NewSetUserInviterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserInviterLogic {
	return &SetUserInviterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetUserInviterLogic) SetUserInviter(req *types.SetUserInviterReq) (resp bool, err error) {
	_, err = l.svcCtx.AppletAdminRPC.SetUserInviter(l.ctx, &pb.SetUserInviterReq{
		UserId:    int64(req.UserId),
		InviterId: int64(req.InviterId),
	})
	if err != nil {
		return false, err
	}
	return true, nil

}
