package user

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserRoleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置用户角色
func NewSetUserRoleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserRoleLogic {
	return &SetUserRoleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetUserRoleLogic) SetUserRole(req *types.SetUserRoleReq) (resp bool, err error) {
	// 调用RPC服务
	_, err = l.svcCtx.AppletAdminRPC.SetUserRole(l.ctx, &pb.SetUserRoleReq{
		UserId: int64(req.UserId),
		RoleId: int64(req.RoleId),
	})
	if err != nil {
		return false, err
	}
	return true, nil
}
