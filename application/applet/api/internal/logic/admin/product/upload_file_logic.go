package product

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"time"
	"xj-serv/application/wapp/rpc/pfs/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request // 添加 http.Request 字段
}

// 上传文件
func NewUploadFileLogic(ctx context.Context, svcCtx *svc.ServiceContext, r *http.Request) *UploadFileLogic {
	return &UploadFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *UploadFileLogic) UploadFile(req *types.UploadReq) (resp string, err error) {
	// 使用 l.r 访问 http.Request
	if err = l.r.ParseMultipartForm(10 << 20); err != nil {
		return "", err
	}

	// 获取文件
	file, fileHandler, err := l.r.FormFile("file") // "file" 是表单中的字段名
	if err != nil {
		return "", err
	}
	defer func(file multipart.File) {
		_ = file.Close()
	}(file)

	// 读取文件的前 512 字节以检测 MIME 类型
	buffer := make([]byte, 512)
	_, err = file.Read(buffer)
	if err != nil {
		return "", err
	}

	// 检测文件的 MIME 类型
	fileType := http.DetectContentType(buffer)
	allowedTypes := map[string]bool{
		"image/jpg":  true,
		"image/jpeg": true,
		"image/png":  true,
	}

	if !allowedTypes[fileType] {
		return "", xerr.NewErrCodeMsg(xerr.RequestParamError, fmt.Sprintf("file type not allowed: %s", fileType))
	}

	// 重置文件读取位置
	if _, err = file.Seek(0, io.SeekStart); err != nil {
		return "", err
	}

	// 读取文件内容
	fileContent, err := io.ReadAll(file)
	if err != nil {
		return "", err
	}

	key := fmt.Sprintf("admin/%s%s", time.Now().Format("20060102150405"), filepath.Ext(filepath.Base(fileHandler.Filename)))
	path, err := l.svcCtx.WxAppRPC.UserUpload(l.ctx, &pb.UserUploadFileReq{
		File: fileContent,
		Key:  key,
	})
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s/%s", path.Message, key), nil
}
