package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetIsPreferredLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置是否优选
func NewSetIsPreferredLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetIsPreferredLogic {
	return &SetIsPreferredLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetIsPreferredLogic) SetIsPreferred(req *types.SetIsPreferredReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetProductPreferred(l.ctx, &pb.SetProductPreferredReq{
		ProductId:   uint64(req.ProductId),
		IsPreferred: int32(req.IsPreferred),
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil
}
