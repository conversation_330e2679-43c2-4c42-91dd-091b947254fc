package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateProductCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 添加商品分类
func NewCreateProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateProductCategoryLogic {
	return &CreateProductCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateProductCategoryLogic) CreateProductCategory(req *types.CreateCategoryRequest) (resp bool, err error) {
	_, err = l.svcCtx.AppletAdminRPC.CreateProductCategory(l.ctx, &pb.CreateCategoryRequest{
		FId:   req.FID,
		Name:  req.Name,
		Img:   req.Img,
		Order: req.Order,
	})
	if err != nil {
		return false, err
	}

	return true, nil
}
