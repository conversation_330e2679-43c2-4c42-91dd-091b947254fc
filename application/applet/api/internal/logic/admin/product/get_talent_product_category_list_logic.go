package product

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductCategoryListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 商品分类列表
func NewGetTalentProductCategoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductCategoryListLogic {
	return &GetTalentProductCategoryListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTalentProductCategoryListLogic) GetTalentProductCategoryList(req *types.GetTalentCategoryListRequest) (resp *types.GetTalentCategoryListResponse, err error) {
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}

	rpcReq := &pb.GetTalentCategoryListRequest{
		FId:      req.FID,
		PageNo:   uint32(req.PageNo),
		PageSize: uint32(req.PageSize),
	}

	talentProductCategoryList, err := l.svcCtx.AppletAdminRPC.GetTalentProductCategoryList(l.ctx, rpcReq)
	if err != nil {
		logx.Errorf("l.svcCtx.AppletUserRPC.GetProducts err: %v", err)
		return nil, err
	}
	var categoryInfo []types.CategoryInfo
	_ = copier.Copy(&categoryInfo, talentProductCategoryList.Categories)

	return &types.GetTalentCategoryListResponse{
		Categories: categoryInfo,
		PageResponse: types.PageResponse{
			Total:    int64(talentProductCategoryList.Total),
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
	}, nil
}
