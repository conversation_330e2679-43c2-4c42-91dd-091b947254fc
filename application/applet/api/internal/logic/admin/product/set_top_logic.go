package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetTopLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置商品置顶
func NewSetTopLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetTopLogic {
	return &SetTopLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetTopLogic) SetTop(req *types.SetProductTopReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetProductTop(l.ctx, &pb.SetProductTopReq{
		ProductId: uint64(req.ProductId),
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil
}
