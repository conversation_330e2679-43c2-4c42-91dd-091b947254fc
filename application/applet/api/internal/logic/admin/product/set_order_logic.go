package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetOrderLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置商品排序权重
func NewSetOrderLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetOrderLogic {
	return &SetOrderLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetOrderLogic) SetOrder(req *types.SetProductOrderReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetProductOrder(l.ctx, &pb.SetProductOrderReq{
		ProductId: uint64(req.ProductId),
		Order:     int32(req.Order),
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil
}
