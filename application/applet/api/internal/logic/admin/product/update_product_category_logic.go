package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateProductCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 修改商品分类
func NewUpdateProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateProductCategoryLogic {
	return &UpdateProductCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateProductCategoryLogic) UpdateProductCategory(req *types.UpdateCategoryRequest) (resp bool, err error) {
	if req.ID <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "商品分类ID错误")
	}

	_, err = l.svcCtx.AppletAdminRPC.UpdateProductCategory(l.ctx, &pb.UpdateCategoryRequest{
		Id:    req.ID,
		FId:   req.FID,
		Name:  req.Name,
		Img:   req.Img,
		Order: req.Order,
	})
	if err != nil {
		return false, err
	}

	return true, nil
}
