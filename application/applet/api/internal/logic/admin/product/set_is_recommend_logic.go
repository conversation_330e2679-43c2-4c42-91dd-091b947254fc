package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetIsRecommendLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置是否推荐
func NewSetIsRecommendLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetIsRecommendLogic {
	return &SetIsRecommendLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetIsRecommendLogic) SetIsRecommend(req *types.SetIsRecommendReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetProductRecommend(l.ctx, &pb.SetProductRecommendReq{
		ProductId:   uint64(req.ProductId),
		IsRecommend: int32(req.IsRecommend),
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil
}
