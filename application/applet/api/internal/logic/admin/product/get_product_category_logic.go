package product

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 查看商品分类
func NewGetProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductCategoryLogic {
	return &GetProductCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetProductCategoryLogic) GetProductCategory(req *types.GetCategoryRequest) (resp *types.CategoryInfo, err error) {
	// 构建RPC请求
	rpcReq := &pb.GetTalentCategoryRequest{}
	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	rpcResp, err := l.svcCtx.AppletAdminRPC.GetTalentProductCategory(l.ctx, rpcReq)
	if err != nil {
		return nil, err
	}
	resp = &types.CategoryInfo{}
	_ = copier.Copy(resp, rpcResp)
	return resp, nil
}
