package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteProductCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除商品分类
func NewDeleteProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteProductCategoryLogic {
	return &DeleteProductCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteProductCategoryLogic) DeleteProductCategory(req *types.DeleteCategoryRequest) (resp bool, err error) {
	if req.ID <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "商品分类ID错误")
	}

	_, err = l.svcCtx.AppletAdminRPC.DeleteProductCategory(l.ctx, &pb.DeleteCategoryRequest{
		Id: req.ID,
	})
	if err != nil {
		return false, err
	}

	return true, nil
}
