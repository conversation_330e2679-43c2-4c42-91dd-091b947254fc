package product

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/pb"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取商品列表
func NewGetProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductListLogic {
	return &GetProductListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetProductListLogic) GetProductList(req *types.GetProductListReq) (resp *types.GetProductListResp, err error) {
	var page pb.PageRequest
	if req.PageNo == 0 {
		req.PageNo = l.svcCtx.Config.Page.PageNo
	}
	if req.PageSize == 0 {
		req.PageSize = l.svcCtx.Config.Page.PageSize
	}
	page.PageNo = req.PageNo
	page.PageSize = req.PageSize

	rpcReq := &pb.GetProductsReq{
		PageRequest:         &page,
		CategoryId:          req.CategoryId,
		CCat1:               req.CCat1,
		CCat2:               req.CCat2,
		Title:               req.Title,
		MinPrice:            req.MinPrice,
		MaxPrice:            req.MaxPrice,
		MinCommissionAmount: req.MinCommissionAmount,
		MaxCommissionAmount: req.MaxCommissionAmount,
		IsPreferred:         req.IsPreferred,
		IsRecommend:         req.IsRecommend,
		IsWindow:            req.IsWindow,
		SortBy:              req.SortBy,
		Order:               req.Order,
	}

	productList, err := l.svcCtx.AppletAdminRPC.GetProducts(l.ctx, rpcReq)
	if err != nil {
		logx.Errorf("l.svcCtx.AppletUserRPC.GetProducts err: %v", err)
		return nil, err
	}
	var typesProductInfoList []types.ProductInfo
	_ = copier.Copy(&typesProductInfoList, productList.Products)

	return &types.GetProductListResp{
		List: typesProductInfoList,
		PageResponse: types.PageResponse{
			Total:    productList.Total,
			PageNo:   page.PageNo,
			PageSize: page.PageSize,
		},
	}, nil
}
