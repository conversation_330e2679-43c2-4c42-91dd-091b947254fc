package product

import (
	"context"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchUpdateProductsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 批量商品操作-设置到橱窗、优选、每日推荐
func NewBatchUpdateProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateProductsLogic {
	return &BatchUpdateProductsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BatchUpdateProductsLogic) BatchUpdateProducts(req *types.BatchUpdateProductsReq) (resp bool, err error) {
	// 1. 参数校验
	if len(req.ProductIds) == 0 {
		return false, xerr.NewErrMsg(l.ctx, "商品ID列表不能为空")
	}

	// 2. 检查是否至少有一个要修改的属性
	if req.IsRecommend == nil && req.IsPreferred == nil && req.IsWindow == nil {
		return false, xerr.NewErrMsg(l.ctx, "至少需要修改一个属性")
	}

	// 3. 调用RPC服务
	_, err = l.svcCtx.AppletAdminRPC.BatchUpdateProducts(l.ctx, &pb.BatchUpdateProductsReq{
		ProductIds:  req.ProductIds,
		IsRecommend: req.IsRecommend,
		IsPreferred: req.IsPreferred,
		IsWindow:    req.IsWindow,
		CCat1:       req.CCat1,
		CCat2:       req.CCat2,
	})
	if err != nil {
		return false, err
	}

	return true, nil
}
