package product

import (
	"context"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"

	"github.com/jinzhu/copier"

	"github.com/zeromicro/go-zero/core/logx"

	"xj-serv/application/applet/rpc/pb"
)

type GetProductCategoryListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取商品列表
func NewGetProductCategoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductCategoryListLogic {
	return &GetProductCategoryListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetProductCategoryListLogic) GetProductCategoryList() (resp *types.GetProductCategoryListResp, err error) {
	productCategoryList, err := l.svcCtx.AppletAdminRPC.GetProductCategories(l.ctx, &pb.EmptyRequest{})
	if err != nil {
		logx.Errorf("l.svcCtx.AppletUserRPC.GetProducts err: %v", err)
		return nil, err
	}
	var typesCategoryInfoList []types.ProductCategoryInfo
	_ = copier.Copy(&typesCategoryInfoList, productCategoryList.Categories)

	return &types.GetProductCategoryListResp{
		List: typesCategoryInfoList,
	}, nil
}
