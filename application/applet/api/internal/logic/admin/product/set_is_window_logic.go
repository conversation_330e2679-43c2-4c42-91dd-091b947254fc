package product

import (
	"context"
	"xj-serv/application/applet/api/internal/svc"
	"xj-serv/application/applet/api/internal/types"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetIsWindowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 设置是否橱窗
func NewSetIsWindowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetIsWindowLogic {
	return &SetIsWindowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetIsWindowLogic) SetIsWindow(req *types.SetIsWindowReq) (resp bool, err error) {
	rpcResp, err := l.svcCtx.AppletAdminRPC.SetProductWindow(l.ctx, &pb.SetProductWindowReq{
		ProductId: uint64(req.ProductId),
		IsWindow:  int32(req.IsWindow),
	})
	if err != nil {
		return false, err
	}
	return rpcResp.Success, nil
}
