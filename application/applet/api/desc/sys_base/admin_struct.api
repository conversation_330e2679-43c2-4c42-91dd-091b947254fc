syntax = "v1"


// 用户列表请求
type UserListReq {
    PageRequest
    Nickname string `form:"nickname,optional"`           // 用户昵称
    Mobile string `form:"mobile,optional"`               // 用户手机号
    Level string `form:"level,optional"`                 // 用户等级
    InviterName string `form:"inviterName,optional"`     // 推荐人昵称
    InviterMobile string `form:"inviterMobile,optional"` // 推荐人手机号
    StartTime string `form:"startTime,optional"`         // 注册开始时间
    EndTime string `form:"endTime,optional"`             // 注册结束时间
}

// 用户列表响应
type UserListResp {
    List []XjUser `json:"list"`
    PageResponse
}

// 用户信息
type UserItem {
    Id uint64 `json:"id"`                            // 用户ID
    Nickname string `json:"nickname"`                // 用户昵称
    Mobile string `json:"mobile"`                    // 用户手机号
    Level string `json:"level"`                      // 用户等级
    RegisterTime string `json:"registerTime"`        // 注册时间
    InviterName string `json:"inviterName"`          // 推荐人昵称
    InviterMobile string `json:"inviterMobile"`      // 推荐人手机号
    Avatar string `json:"avatar"`                    // 用户头像
}

// 用户详情请求
type UserDetailReq {
    Id uint64 `path:"id"` // 用户ID
}


type XjUser {
    Id              uint64    `json:"id"`               // 用户ID
    CreatedAt       string    `json:"created_at"`       // 创建时间
    UpdatedAt       string    `json:"updated_at"`       // 更新时间
    Uuid            string    `json:"uuid"`             // UUID
    Mobile          string    `json:"mobile"`           // 手机号
    NickName        string    `json:"nick_name"`        // 昵称
    Avatar          string    `json:"avatar"`           // 头像
    Gender          uint32    `json:"gender"`           // 性别
    Country         string    `json:"country"`          // 国家
    Province        string    `json:"province"`         // 省份
    City            string    `json:"city"`             // 城市
    AddressId       uint32    `json:"address_id"`       // 默认地址ID
    Balance         float64   `json:"balance"`          // 可用余额
    Withdraw        float64   `json:"withdraw"`          // 可提现余额
    Points          uint32    `json:"points"`           // 可用积分
    PayMoney        float64   `json:"pay_money"`        // 总支付金额
    ExpendMoney     float64   `json:"expend_money"`     // 实际消费金额
    GradeId         uint32    `json:"grade_id"`         // 会员等级ID
    Platform        string    `json:"platform"`         // 注册平台
    LastLoginTime   uint32    `json:"last_login_time"`  // 最后登录时间
    Openid          string    `json:"openid"`           // 微信openid
    Unionid         string    `json:"unionid"`          // 微信unionid
    SharerAppid     string    `json:"sharer_appid"`     // 推客appid
    ContactName     string    `json:"contact_name"`     // 联系人姓名
    ContactNumber   string    `json:"contact_number"`   // 联系电话
    ShareCode       string    `json:"share_code"`       // 分享码
    ShareCodeImage  string    `json:"share_code_image"` // 分享码图片
    RegIpv4         string    `json:"reg_ipv4"`         // 注册IP
    InviteFrom      uint64    `json:"invite_from"`      // 邀请人
    InviteName      string    `json:"inviteName"`       // 推荐人昵称
    InviteMobile    string `json:"inviteMobile"`        // 推荐人手机号
    RegSource       string    `json:"reg_source"`       // 注册来源
    Level           int32     `json:"level"`            // 用户等级
    LevelName       string    `json:"level_name"`       // 用户等级名称
    IsEnterprise    int32     `json:"is_enterprise"`    // 是否企业用户
    Birthday        string    `json:"birthday"`         // 生日
    RoleId          int32     `json:"role_id"`          // 角色ID
    RoleName        string    `json:"role_name"`        // 角色名称
    FinderId        string    `json:"finder_id"`        // 视频号ID
    Status          int32     `json:"status"`          // 角色ID
}


// 设置用户等级请求
type SetUserLevelReq {
    UserId  int64 `json:"userId"`  // 用户ID
    LevelId int64 `json:"levelId"` // 等级ID
}

// 设置用户上级请求
type SetUserInviterReq {
    UserId uint64 `json:"userId"`       // 用户ID
    InviterId uint64 `json:"inviterId"` // 推荐人ID
}

// 设置用户角色请求
type SetUserRoleReq {
    UserId uint64 `json:"userId"`  // 用户ID
    RoleId uint64 `json:"roleId"`  // 角色ID
}

// 搜索用户请求
type SearchUsersReq {
    Keyword string `json:"keyword"` // 搜索关键词(昵称或手机号)
    PageNo int64 `json:"pageNo" form:"pageNo,default=1"`
    PageSize int64 `json:"pageSize" form:"pageSize,default=10"`
}

// 搜索用户返回
type SearchUserResp {
    List []XjUser `json:"list"`
    PageResponse
}


// 公司列表请求
type CompanyListReq {
    PageNo int64 `json:"pageNo" form:"pageNo,default=1"`
    PageSize int64 `json:"pageSize" form:"pageSize,default=10"`
    Name string `json:"name" form:"name,optional"`
    Phone string `json:"phone" form:"phone,optional"`
    Status int32 `json:"status" form:"status,optional"`
    RoleId int32 `json:"roleId" form:"roleId,optional"`
    StartTime string `json:"startTime" form:"startTime,optional"`
    EndTime string `json:"endTime" form:"endTime,optional"`
}

// 公司信息
type CompanyInfo {
    Id uint64 `json:"id"`
    UserId uint64 `json:"userId"`
    Name string `json:"name"`
    Address string `json:"address"`
    BankAccount string `json:"bankAccount"`
    BankName string `json:"bankName"`
    TaxNumber string `json:"taxNumber"`
    Phone string `json:"phone"`
    Status int `json:"status"`
    Remark string `json:"remark"`
    LicenseImg string `json:"licenseImg"`
    CreatedAt string `json:"createdAt"`
    UpdatedAt string `json:"updatedAt"`
    Leader string `json:"leader"`
    User XjUser `json:"user"`

}

// 公司列表响应
type CompanyListResp {
    List []CompanyInfo `json:"list"`
    PageResponse
}
type CompanyDetailReq {
    Id uint64 `path:"id"`
}
type CompanyDetailResp {
    CompanyInfo
}
type AuditCompanyReq {
    Id uint64 `json:"id"`          // 公司ID
    Status int32 `json:"status"`   // 审核状态：0-未审核;1-审核通过;2-审核失败;3-审核中
    Remark string `json:"remark"`  // 审核结果
}

// UpdateCompanyReq 公司信息保存请求
type UpdateCompanyReq {
    Id          uint64 `json:"id"`                                 // 公司ID
    Name        string `json:"name" validate:"required"`           // 公司名称
    Address     string `json:"address" validate:"required"`        // 公司地址
    BankAccount string `json:"bank_account" validate:"required"`   // 银行账号
    BankName    string `json:"bank_name" validate:"required"`      // 开户银行
    TaxNumber   string `json:"tax_number" validate:"required"`     // 税号
    Leader      string `json:"leader" validate:"required"`         // 负责人姓名
    Phone       string `json:"phone" validate:"required"`          // 联系电话
    LicenseImg  string `json:"license_img" validate:"required"`    // 营业执照图片

}
type CreateCompanyReq {
    UserId      uint64 `json:"user_id" validate:"required"`       // 用户ID
    Name        string `json:"name" validate:"required"`           // 公司名称
    Address     string `json:"address" validate:"required"`        // 公司地址
    BankAccount string `json:"bank_account" validate:"required"`   // 银行账号
    BankName    string `json:"bank_name" validate:"required"`      // 开户银行
    TaxNumber   string `json:"tax_number" validate:"required"`     // 税号
    Leader      string `json:"leader" validate:"required"`         // 负责人姓名
    Phone       string `json:"phone" validate:"required"`          // 联系电话
    LicenseImg  string `json:"license_img" validate:"required"`    // 营业执照图片
}




// 代理商列表请求
type AgentListReq {
    PageNo int64 `json:"pageNo" form:"pageNo,default=1"`
    PageSize int64 `json:"pageSize" form:"pageSize,default=10"`
    Name      string `form:"name,optional"`       // 代理商名称
    Mobile     string `form:"mobile,optional"`      // 手机号
    StartTime string `form:"startTime,optional"`  // 开始时间
    EndTime   string `form:"endTime,optional"`    // 结束时间
    CompanyName string `form:"companyName,optional"` // 公司名称
    RelatedCompany string `form:"relatedCompany,optional"` // 关联运营商公司名称
}

// 代理商列表请求
type AgentListResp {
    List []AgentItem `json:"list"`
    PageResponse
}
type AgentItem {
    Id uint64 `json:"id"`                           // 用户ID
    Nickname string `json:"nickname"`               // 用户昵称
    Mobile       string `json:"mobile"`       // 手机号
    PackageCount int    `json:"packageCount"` // 套餐数量
    TaskRewardCount int `json:"taskRewardCount"` // 任务奖励次数
    CompanyName  string `json:"companyName"`  // 公司名称
    RelatedCompany string `json:"relatedCompany"` // 关联运营商公司名称
    CreatedAt string `json:"createdAt"`
}

type XjUserListReq  {
    PageNo     int64  `json:"pageNo,default=1"`
    PageSize   int64  `json:"pageSize,default=10"`
    NickName   string `json:"nickName,optional"`    // 用户昵称
    Mobile     string `json:"mobile,optional"`      // 用户手机号
    Level      int8   `json:"level,optional"`       // 用户等级
    RoleId     int8   `json:"roleId,optional"`      // 用户角色
    InviteName string `json:"inviteName,optional"`  // 推荐人昵称
    InviteMobile string `json:"inviteMobile,optional"` // 推荐人手机号
    StartTime  string `json:"startTime,optional"`   // 注册开始时间
    EndTime    string `json:"endTime,optional"`     // 注册结束时间
}

// 响应结构体
type XjUserListResp {
    List         []XjUser `json:"list"`
    Total        int64            `json:"total"`
    PageNo       int64            `json:"pageNo"`
    PageSize     int64            `json:"pageSize"`
}
type UserLevelListResp {
    List []UserLevelItem `json:"list"`
}

type UserLevelItem {
    Id        int64  `json:"id"`
    Name      string `json:"name"`
    Level     int8   `json:"level"`
    CreatedAt string `json:"createdAt"`
    UpdatedAt string `json:"updatedAt"`
}
type UserRoleListResp {
    List []UserRoleItem `json:"list"`
}

type UserRoleItem {
    Id          int64  `json:"id"`          // 主键ID
    RoleId      int64  `json:"roleId"`      // 角色ID
    RoleName    string `json:"roleName"`    // 角色名
    Description string `json:"description"` // 描述
    CreatedAt   string `json:"createdAt"`   // 创建时间
    UpdatedAt   string `json:"updatedAt"`   // 更新时间
}

type SetUserStatusReq {
    UserId int64 `json:"userId"`   // 用户ID
    Status int8  `json:"status"`   // 状态：0=冻结，1=正常
}

type AgentProfileReq {
    UserId uint64 `path:"userId"`
}
// 代理商/业务员个人主页数据响应
type AgentProfileResp {
    // 用户基本信息
    NickName        string  `json:"nick_name"`        // 昵称
    Avatar          string  `json:"avatar"`           // 头像
    Mobile          string  `json:"mobile"`           // 手机号
    IsAgent         bool    `json:"is_agent"`         // 是否是代理商
    SalesmanRate    float64 `json:"salesman_rate"`    // 业务员佣金比例（仅业务员有）
    SalesmanRemark  string  `json:"salesman_remark"`  // 业务员备注（仅业务员有）
    IsFullTime      int64   `json:"is_full_time"`     // 全职兼职（仅业务员有）
    CompanyName     string  `json:"company_name"`     // 公司名称
    HasCompanyInfo bool   `json:"has_company_info"` // 是否填写过公司资料
    // 统计信息（原有的字段）
    TotalCommission    float64 `json:"total_commission"`     // 佣金总额
    SalesmanCommission float64 `json:"salesman_commission"` // 业务经理佣金汇总
    TodayOrderCount    int64 `json:"today_order_count"`   // 今日订单数量
    TodayOrderAmount   float64 `json:"today_order_amount"`  // 今日订单总额
    AiPointTotal       int64 `json:"ai_point_total"`      // AI点总数
    TeamPackageCount   int64 `json:"team_package_count"`  // 团长套餐数量
    TaskRewardCount   int64 `json:"task_reward_count"`  // 任务奖励次数
}

// 调整代理商团长套餐数量
type SetAgentTeamPackageReq {
    AgentUserId uint64 `json:"agent_user_id"` // 代理商用户ID
    Number         int32  `json:"number"`           // 设置的套餐数量
}

// 团长套餐数日志列表请求
type InventoryLogListReq {
    PageRequest
    Mobile           string `json:"mobile,optional"`
    StartTime       string `json:"startTime,optional"`
    EndTime         string `json:"endTime,optional"`
    TransactionType string `json:"transactionType,optional"` // 交易类型(purchase/consumption/adjustment)
}

// 团长套餐数日志信息
type InventoryLogInfo {
    Id              uint64 `json:"id"`
    UserId          uint64 `json:"userId"`
    Nickname        string `json:"nickname"`
    Mobile           string `json:"mobile"`
    RoleId            string `json:"roleId"`
    Level           int    `json:"level"`
    ProductId       uint64 `json:"productId"`



    ProductType     string `json:"productType"`
    OrderId         uint64 `json:"orderId"`
    TransactionType string `json:"transactionType"` // 交易类型(purchase/consumption/adjustment)
    Quantity        int    `json:"quantity"`        // 变更数量(正数为增加，负数为减少)
    BeforeQuantity  int    `json:"beforeQuantity"`  // 变更前数量
    AfterQuantity   int    `json:"afterQuantity"`   // 变更后数量
    Remark          string `json:"remark"`
    CreatedAt       string `json:"createdAt"`
}


// 团长套餐数日志列表响应
type InventoryLogListResp {
    List []InventoryLogInfo `json:"list"`
    PageResponse
}

// 图片组件信息
type WidgetImgInfo {
    Id        int    `json:"id"`
    Title     string `json:"title"`     // 标题
    Status    int    `json:"status"`    // 状态
    Img       string `json:"img"`       // 图片地址
    Link      string `json:"link"`      // 链接地址
    Sort      int    `json:"sort"`      // 排序
    Type      int    `json:"type"`      // 1 banner 2 群二维码 3客服二维码 4分享海报 5我的页面广告
    CreatedAt string `json:"createdAt"` // 创建时间
    UpdatedAt string `json:"updatedAt"` // 更新时间
    AdminId   int    `json:"adminId"`   // 管理员ID
 }
// 添加图片组件请求
type CreateWidgetImgReq {
    Title   string `json:"title"`            // 标题
    Status  int    `json:"status,optional"`  // 状态
    Img     string `json:"img"`              // 图片地址
    Link    string `json:"link"`             // 链接地址
    Sort    int    `json:"sort,optional"`    // 排序
    Type    int    `json:"type"`             // 1 banner 2 群二维码 3客服二维码 4分享海报 5我的页面广告
    AdminId int    `json:"adminId,optional"` // 管理员ID
}

// 修改图片组件请求
type UpdateWidgetImgReq {
    Id int `json:"id"` // 图片组件ID
    Title   string `json:"title"`            // 标题
    Status  int    `json:"status,optional"`  // 状态
    Img     string `json:"img"`              // 图片地址
    Link    string `json:"link"`             // 链接地址
    Sort    int    `json:"sort,optional"`    // 排序
    Type    int    `json:"type"`             // 1 banner 2 群二维码 3客服二维码 4分享海报 5我的页面广告
    AdminId int    `json:"adminId,optional"` // 管理员ID
}

// 查看图片组件请求
type GetWidgetImgReq {
    Id int `json:"id"` // 图片组件ID
}

// 删除图片组件请求
type DeleteWidgetImgReq {
    Id int `json:"id"` // 图片组件ID
}

// 获取图片组件列表请求
type GetWidgetImgListReq {
    Type     int `json:"type"`                // 组件类型
    PageRequest
}

// 获取图片组件列表响应
type GetWidgetImgListResp {
    List     []WidgetImgInfo `json:"list"`     // 图片组件列表
    PageResponse
}

// 设置是否优选
type SetIsPreferredReq {
    ProductId int `json:"product_id"` // 商品ID
    IsPreferred int `json:"is_preferred"` 
}

// 设置是否橱窗
type SetIsWindowReq {
    ProductId int `json:"product_id"` // 商品ID
    IsWindow int `json:"is_window"` 
}

// 设置是否推荐
type SetIsRecommendReq {
    ProductId int `json:"product_id"` // 商品ID
    IsRecommend int `json:"is_recommend"` 
}

// 设置商品排序权重
type SetProductOrderReq {
    ProductId int `json:"product_id"` // 商品ID
    Order int `json:"order"`
}

// 设置商品置顶
type SetProductTopReq {
    ProductId int `json:"product_id"` // 商品ID
}

//商品列表
type GetProductListReq {
	PageNo       int64  `json:"pageNo,default=1"`
	PageSize     int64  `json:"pageSize,default=10"`
    Title string `json:"title" form:"title,optional"`
    MinPrice int32 `json:"minPrice" form:"minPrice,optional"`
    MaxPrice int32 `json:"maxPrice" form:"maxPrice,optional"`
    MinCommissionAmount int32 `json:"minCommissionAmount" form:"minCommissionAmount,optional"`
    MaxCommissionAmount int32 `json:"maxCommissionAmount" form:"maxCommissionAmount,optional"`
    CategoryId uint64 `json:"categoryId" form:"categoryId,optional"`
    CCat1 uint64 `json:"cCat1" form:"cCat1,optional"`
    CCat2 uint64 `json:"cCat2" form:"cCat2,optional"`
    IsPreferred int32 `json:"isPreferred" form:"isPreferred,optional"`
    IsRecommend int32 `json:"isRecommend" form:"isRecommend,optional"`
    IsWindow int32 `json:"isWindow" form:"isWindow,optional"`
    SortBy string `json:"sortBy" form:"sortBy,optional"`
    Order string `json:"order" form:"order,optional"`
}



// 商品列表响应
type GetProductListResp {
    List     []ProductInfo `json:"list"`
    PageResponse
}

// 商品信息
type ProductInfo {
    Id        int    `json:"id"`
    ProductId        int    `json:"product_id"`
    ShopAppid        string    `json:"shop_appid"`
    Title     string `json:"title"`     
    SubTitle     string `json:"sub_title"`
    HeadImg         string    `json:"head_img"`
    MinPrice        float64    `json:"min_price"`
    CommissionAmount float64     `json:"commission_amount"`
    ServiceRatio int     `json:"service_atio"`  
    IsPreferred    int    `json:"is_preferred"`
    IsWindow    int    `json:"is_window"`  
    IsRecommend    int    `json:"is_recommend"`  
    Order    int    `json:"order"`
    Cat1             int       `json:"cat1"`
	Cat2             int       `json:"cat2"`
	Cat3             int       `json:"cat3"`
	CCat1             int       `json:"c_cat1"`
    CCat2             int       `json:"c_cat2"`
    CategoryName         string    `json:"category_name"`
 }


// 商品分类信息
type ProductCategoryInfo {
    Id        int    `json:"id"`
    CatId        int    `json:"cat_id"`
    FCatId        int    `json:"f_cat_id"`
    Name     string `json:"name"`
    Level   int    `json:"level"`
 }
 type GetProductCategoryListResp{
     List     []ProductCategoryInfo `json:"list"`
 }

 // 批量修改商品请求
 type BatchUpdateProductsReq {
     ProductIds    []uint64 `json:"product_ids"`     // 商品ID列表
     IsRecommend   *int32   `json:"is_recommend,optional"`   // 是否推荐(0/1)
     IsPreferred   *int32   `json:"is_preferred,optional"`    // 是否优选(0/1)
     IsWindow      *int32   `json:"is_window,optional"`       // 是否橱窗(0/1)
     CCat1      *uint64   `json:"c_cat1,optional"`       // 自定义一级分类
     CCat2      *uint64   `json:"c_cat2,optional"`       // 自定义二级分类

 }


 type CreateCategoryRequest {
 	FID   uint64 `json:"f_id"`
 	Name  string `json:"name"`
 	Img   string `json:"img"`
 	Order uint64   `json:"order"`
 }

type DeleteCategoryRequest {
 	ID uint64 `json:"id"`
}
type GetCategoryRequest {
 	ID uint64 `json:"id"`
}

type UpdateCategoryRequest {
    ID    uint64 `json:"id"`
  	FID   uint64  `json:"f_id"`
  	Name  string `json:"name"`
  	Img   string `json:"img"`
  	Order uint64   `json:"order"`
}

type CategoryInfo {
  	ID        uint64    `json:"id"`
  	FID       uint64   `json:"f_id"`
  	Name      string  `json:"name"`
  	Img       string  `json:"img"`
  	Order     uint64    `json:"order"`
  	CreatedAt string  `json:"created_at"`
  	UpdatedAt string  `json:"updated_at"`
  	Children  []CategoryInfo `json:"children,omitempty"`
}

type GetTalentCategoryListRequest {
   	FID          uint64 `json:"f_id"`
	PageNo       int64  `json:"pageNo,default=1"`
	PageSize     int64  `json:"pageSize,default=10"`

}

type GetTalentCategoryListResponse {
  	Categories []CategoryInfo `json:"categories"`
  	PageResponse
}

// 审核提现请求
type AuditWithdrawReq {
    ID           uint64 `json:"id"`           // 记录ID
    ReviewStatus int    `json:"reviewStatus"` // 审核状态：1-通过，2-拒绝
    FailReason       string `json:"failReason"`       // 失败原因
}

// 提现记录列表请求
type WithdrawRecordListReq {
    PageNo      int    `json:"pageNo,optional"`      // 页码
    PageSize    int    `json:"pageSize,optional"`    // 每页数量
    UserName    string `json:"userName,optional"`    // 用户姓名
    UserMobile   string `json:"userMobile,optional"`   // 用户手机号
    UserID      string `json:"userId,optional"`      // 用户ID
    //Status      int    `json:"status,optional"`      // 提现状态
    ReviewStatus int   `json:"reviewStatus,optional"` // 审核状态
    StartDate   string `json:"startDate,optional"`   // 开始日期
    EndDate     string `json:"endDate,optional"`     // 结束日期
}

// 提现记录列表项
type WithdrawRecordItem {
    ID               uint64  `json:"id"`                // 记录ID
    UserID           uint64  `json:"userId"`            // 用户ID
    UserName         string  `json:"userName"`          // 用户姓名
    UserMobile        string  `json:"userMobile"`         // 用户手机号
    UserRoleId        int  `json:"userRoleId"`         // 用户角色ID
    Amount           uint64  `json:"amount"`            // 申请提现金额
    ActualAmount     uint64  `json:"actualAmount"`      // 实际到账金额
    Fee              uint64  `json:"fee"`               // 手续费
    FeeRatio         float64 `json:"feeRatio"`          // 手续费率
    OutBillNo        string  `json:"outBillNo"`         // 商户单号
    TransferBillNo   string  `json:"transferBillNo"`    // 微信单号
    Status           int     `json:"status"`            // 提现状态
    StatusText       string  `json:"statusText"`        // 提现状态文本
    ReviewStatus     int     `json:"reviewStatus"`      // 审核状态
    ReviewStatusText string  `json:"reviewStatusText"`  // 审核状态文本
    FailReason       string  `json:"failReason"`        // 失败原因
    ApplyTime        string  `json:"applyTime"`         // 申请时间
    ReviewTime       string  `json:"reviewTime"`        // 审核时间
    ProcessTime      string  `json:"processTime"`       // 处理时间
    SuccessTime      string  `json:"successTime"`       // 成功时间
    InvoiceImg       string  `json:"invoiceImg"`        // 发票图片
    Remark           string  `json:"remark"`            // 备注
}

// 提现记录列表响应
type WithdrawRecordListResp {
    List      []WithdrawRecordItem `json:"list"`      // 记录列表
    Total     int64                `json:"total"`     // 总数
    PageNo    int                  `json:"pageNo"`    // 当前页码
    PageSize  int                  `json:"pageSize"`  // 每页数量
}

type UploadReq {

}
// 充值任务池请求
type RechargeBenefitsPoolReq {
    UserId   uint64 `json:"user_id"`   // 用户ID
    Quantity int32  `json:"quantity"` // 充值数量
}

// 设置业务员权限请求
type SetSalesmanPermissionReq {
    SalesmanUserId uint64  `json:"salesman_user_id"` // 业务员userid
    SalesmanRate   float64 `json:"salesman_rate"`    // 代理给业务员佣金比例
    SalesmanRemark string  `json:"salesman_remark"`  // 业务员备注
    IsFullTime     int64    `json:"is_full_time"`     // 全职兼职
}


