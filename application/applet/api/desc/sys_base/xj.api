syntax = "v1"

import (
	"sys_struct.api"
	"admin_struct.api"
)

@server(
	prefix: /v1/sys
	group: usernocasbin
)
service applet-api {
	@doc "用户登录"
	@handler Login
	post /login (LoginRequest) returns (LoginResponse)

	@doc "生成验证码"
	@handler RandomImage
	get /randomImage (RandomImageRequest)   returns (RandomImageResponse)

}

@server(
	prefix: /v1/sys
	group: user
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "分页获取用户列表"
	@handler GetUserList
	get /getUserList (GetUserListRequest)  returns (GetUserListResponse)

	@doc "新增（注册）用户 - 管理员"
	@handler Register
	post /register (RegisterRequest)  returns (RegisterResponse)

	@doc "修改用户信息"
	@handler UpdateUserInfo
	put /updateUserInfo (UpdateUserInfoRequest)  returns (MessageResponse)

	@doc "重置用户密码 默认密码：goZero"
	@handler ResetUserPassword
	put /resetUserPassword (ResetUserPasswordRequest)  returns (MessageResponse)

	@doc "删除用户"
	@handler DeleteUser
	delete /deleteUser (DeleteUserRequest)  returns (MessageResponse)

}

@server(
	prefix: /v1/sys/menu
	group: menu
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "获取菜单"
	@handler GetMenu
	get /getMenu (GetMenuRequest) returns (GetMenuResponse)

	@doc "分页获取base_menu列表"
	@handler GetMenuList
	get /getMenuList (GetMenuListRequest) returns (GetMenuListResponse)

	@doc "新增 base_menu"
	@handler AddBaseMenu
	post /addBaseMenu (AddBaseMenuRequest) returns (AddBaseMenuResponse)

	@doc "获取用户动态路由树  -- 用于角色管理的设置权限"
	@handler GetBaseMenuTree
	get /getBaseMenuTree (GetBaseMenuTreeRequest) returns (GetBaseMenuTreeResponse)

	@doc "获取指定角色menu  -- 用于角色管理的设置权限"
	@handler GetMenuAuthority
	get /getMenuAuthority (GetMenuAuthorityRequest) returns (GetMenuAuthorityResponse)

	@doc "根据id获取菜单"
	@handler GetBaseMenuById
	get /getBaseMenuById (GetBaseMenuByIdRequest) returns (GetBaseMenuByIdResponse)

	@doc "更新系统菜单"
	@handler UpdateBaseMenu
	put /updateBaseMenu (UpdateBaseMenuRequest) returns (MessageResponse)

	@doc "删除系统菜单"
	@handler DeleteBaseMenu
	delete /deleteBaseMenu (DeleteBaseMenuRequest) returns (MessageResponse)

}

@server(
	prefix: /v1/sys/authority
	group: authority
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "获取角色列表"
	@handler GetAuthorityList
	get /getAuthorityList (GetAuthorityListRequest) returns (GetAuthorityListResponse)

	@doc "增加角色和base_menu关联关系"
	@handler AddAuthorityMenu
	post /addAuthorityMenu (AddAuthorityMenuRequest) returns (MessageResponse)

	@doc "更新角色 -- 设为首页"
	@handler UpdateAuthority
	put /updateAuthority (UpdateAuthorityRequest) returns (UpdateAuthorityResponse)

	@doc "创建角色"
	@handler CreateAuthority
	post /createAuthority (CreateAuthorityRequest) returns (CreateAuthorityResponse)

	@doc "删除角色"
	@handler DeleteAuthority
	delete /deleteAuthority (DeleteAuthorityRequest) returns (MessageResponse)
}

@server(
	prefix: /v1/sys/api
	group: api
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "获取api列表"
	@handler GetApiList
	get /getApiList (GetApiListRequest) returns (GetApiListResponse)

	@doc "创建/增加 api列表"
	@handler CreateApi
	post /createApi (CreateApiRequest) returns (MessageResponse)

	@doc "删除 api列表"
	@handler DeleteApi
	delete /deleteApi (DeleteApiRequest) returns (MessageResponse)

	@doc "获取 所有api"
	@handler GetAllApiList
	get /getAllApiList (GetAllApiListRequest) returns (GetAllApiListResponse)

	@doc "删除多条api"
	@handler DeleteApisByIds
	delete /deleteApisByIds (DeleteApisByIdsRequest) returns (MessageResponse)

	@doc "更新api"
	@handler UpdateApi
	put /updateApi (UpdateApiRequest) returns (MessageResponse)
}

@server(
	prefix: /v1/sys/casbin
	group: casbin
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "根据角色id获取对应的casbin数据"
	@handler GetPathByAuthorityId
	get /getPathByAuthorityId (GetPathByAuthorityIdRequest) returns (GetPathByAuthorityIdResponse)

	@doc "更新一个角色的对应的casbin数据"
	@handler UpdateCasbinData
	put /updateCasbinData (UpdateCasbinDataRequest) returns (MessageResponse)

	@doc "更新一个角色的对应的casbin数据 用api的ids 查数据"
	@handler UpdateCasbinDataByApiIds
	put /updateCasbinDataByApiIds (UpdateCasbinDataByApiIdsRequest) returns (MessageResponse)

}

@server(
	prefix: /v1/sys/base
	group: base
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	@doc "上传图片"
	@handler UploadFileImg
	post /uploadFileImg (UploadFileImgRequest) returns (UploadFileImgResponse)

	@doc "邮箱发送验证码 - 暂时用于注册账号"
	@handler SendEmailCode
	post /sendEmailCode (SendEmailCodeRequest) returns (MessageResponse)
}

@server(
	prefix: /v1/sys/dictionary
	group: dictionary
	jwt: JwtAuth
	middleware: Authority
)
service applet-api {
	// =================  SysDictionary ==============

	@doc "获取SysDictionary列表"
	@handler GetSysDictionaryList
	get /getSysDictionaryList (GetSysDictionaryListRequest) returns (GetSysDictionaryListResponse)

	@doc "新建SysDictionary"
	@handler CreateSysDictionary
	post /createSysDictionary (CreateSysDictionaryRequest) returns (MessageResponse)

	@doc "根据ID或者type获取SysDictionary"
	@handler GetSysDictionaryDetails
	get /getSysDictionaryDetails (GetSysDictionaryDetailsRequest) returns (GetSysDictionaryDetailsResponse)

	@doc "更新SysDictionary"
	@handler UpdateSysDictionary
	put /updateSysDictionary (UpdateSysDictionaryRequest) returns (MessageResponse)

	@doc "删除SysDictionary"
	@handler DeleteSysDictionary
	delete /deleteSysDictionary (DeleteSysDictionaryRequest) returns (MessageResponse)

	// =================  SysDictionaryInfo ==============

	@doc "获取SysDictionaryInfo列表"
	@handler GetSysDictionaryInfoList
	get /getSysDictionaryInfoList (GetSysDictionaryInfoListRequest) returns (GetSysDictionaryInfoListResponse)

	@doc "根据id获取SysDictionaryInfo详情"
	@handler GetSysDictionaryInfoListDetailsById
	get /getSysDictionaryInfoListDetailsById (GetSysDictionaryInfoListDetailsByIdRequest) returns (GetSysDictionaryInfoListDetailsByIdResponse)

	@doc "更新SysDictionaryInfo"
	@handler UpdateSysDictionaryInfo
	put /updateSysDictionaryInfo (UpdateSysDictionaryInfoRequest) returns (MessageResponse)

	@doc "创建SysDictionaryInfo"
	@handler CreateSysDictionaryInfo
	post /createSysDictionaryInfo (CreateSysDictionaryInfoRequest) returns (MessageResponse)

	@doc "删除SysDictionaryInfo"
	@handler DeleteSysDictionaryInfo
	delete /deleteSysDictionaryInfo (DeleteSysDictionaryInfoRequest) returns (MessageResponse)
}

@server(
	prefix: /v1/admin/user
	group: admin/user
	jwt: JwtAuth
)
service applet-api {
    @doc "获取用户列表"
    @handler GetXjUserList
    post /list (XjUserListReq) returns (XjUserListResp)

    @doc "获取用户详情"
    @handler GetUserDetail
    get /detail/:id (UserDetailReq) returns (XjUser)

    @doc "设置用户等级"
    @handler SetUserLevel
    post /set-level (SetUserLevelReq) returns (bool)

    @doc "设置用户上级"
    @handler SetUserInviter
    post /set-inviter (SetUserInviterReq) returns (bool)

    @doc "设置用户角色"
    @handler SetUserRole
    post /set-role (SetUserRoleReq) returns (bool)

    @doc "搜索用户"
    @handler SearchUsers
    post /search (SearchUsersReq) returns (SearchUserResp)

    @doc "导出用户列表"
    @handler ExportUserList
    get /export (UserListReq) returns (string)

    @doc "用户等级列表"
    @handler GetUserLevelList
    get /level/list () returns (UserLevelListResp)

    @doc "用户等级列表"
    @handler GetUserRoleList
    get /role/list () returns (UserRoleListResp)

    @doc "冻结/解冻账户"
    @handler SetUserStatus
    post /set-status (SetUserStatusReq) returns (bool)

    @doc  "获取代理商/业务员统计数据"
    @handler GetAgentProfile
    get /profile/:userId (AgentProfileReq) returns (AgentProfileResp)

    @doc "调整代理商团长套餐数量"
    @handler SetAgentTeamPackage
    post /set_agent_team_package (SetAgentTeamPackageReq) returns (bool)

    @doc "获取团长套餐数日志列表"
    @handler GetInventoryLogList
    post /inventory_log_list (InventoryLogListReq) returns (InventoryLogListResp)

    @doc "设置业务员权限"
    @handler SetSalesmanPermission
    post /set_salesman_permission (SetSalesmanPermissionReq) returns (bool)
}

@server(
	prefix: /v1/admin/company
	group: admin/company
	jwt: JwtAuth
)
service applet-api {
    @doc "获取公司列表"
    @handler GetCompanyList
    post /list (CompanyListReq) returns (CompanyListResp)

    @doc "获取公司详情"
    @handler GetCompanyDetail
    get /detail/:id (CompanyDetailReq) returns (CompanyDetailResp)

	@doc "审核公司"
	@handler AuditCompany
	post /audit (AuditCompanyReq) returns (bool)

	@doc "修改公司信息"
	@handler UpdateCompany
	post /update (UpdateCompanyReq) returns (bool)

	@doc "新建公司"
    @handler CreateCompany
    post /create (CreateCompanyReq) returns (bool)


}


@server(
	prefix: /v1/admin/finance
	group: admin/finance
    jwt: JwtAuth
)
service applet-api {
	@doc "获取代理商列表"
	@handler GetAgentList
	post /agent_list (AgentListReq) returns (AgentListResp)

	@doc "获取提现记录列表"
    @handler GetWithdrawRecordList
    post /withdraw_record/list (WithdrawRecordListReq) returns (WithdrawRecordListResp)

    @doc "审核提现申请"
    @handler AuditWithdraw
    post /withdraw_record/audit (AuditWithdrawReq) returns (bool)

    @doc "充值任务池"
    @handler RechargeBenefitsPool
    post /benefits/pool/recharge (RechargeBenefitsPoolReq) returns (bool)
}

@server(
	prefix: /v1/admin/widget
	group: admin/widget
	jwt: JwtAuth
)
service applet-api {
    @doc "添加图片组件"
    @handler CreateWidgetImg
    post /img/create (CreateWidgetImgReq) returns (bool)

    @doc "修改图片组件"
    @handler UpdateWidgetImg
    post /img/update (UpdateWidgetImgReq) returns (bool)

    @doc "查看图片组件"
    @handler GetWidgetImg
    post /img/get (GetWidgetImgReq) returns (WidgetImgInfo)

    @doc "删除图片组件"
    @handler DeleteWidgetImg
    post /img/delete (DeleteWidgetImgReq) returns (bool)

    @doc "获取图片组件列表"
    @handler GetWidgetImgList
    post /img/list (GetWidgetImgListReq) returns (GetWidgetImgListResp)
}
@server(
	prefix: /v1/admin/product
	group: admin/product
	jwt: JwtAuth
)
service applet-api {
    @doc "设置是否优选"
    @handler SetIsPreferred
    post /set/preferred (SetIsPreferredReq) returns (bool)

	@doc "设置是否橱窗"
    @handler SetIsWindow
    post /set/window (SetIsWindowReq) returns (bool)

	@doc "设置是否推荐"
    @handler SetIsRecommend
    post /set/recommend (SetIsRecommendReq) returns (bool)

    @doc "设置商品排序权重"
    @handler SetOrder
    post /set/order (SetProductOrderReq) returns (bool)

    @doc "设置商品置顶"
    @handler SetTop
    post /set/top (SetProductTopReq) returns (bool)

    @doc "获取商品列表"
    @handler GetProductList
    post /list (GetProductListReq) returns (GetProductListResp)

    @doc "获取商品列表"
    @handler GetProductCategoryList
    get /category/list () returns (GetProductCategoryListResp)

    @doc "批量商品操作-设置到橱窗、优选、每日推荐"
    @handler BatchUpdateProducts
    post /batchUpdate (BatchUpdateProductsReq) returns (bool)

    @doc "添加商品分类"
    @handler createProductCategory
    post /category/create (CreateCategoryRequest) returns (bool)

    @doc "修改商品分类"
    @handler updateProductCategory
    post /category/update (UpdateCategoryRequest) returns (bool)

    @doc "删除商品分类"
    @handler deleteProductCategory
    post /category/delete (DeleteCategoryRequest) returns (bool)

    @doc "查看商品分类"
    @handler getProductCategory
    post /category/get (GetCategoryRequest) returns (CategoryInfo)

    @doc "商品分类列表"
    @handler getTalentProductCategoryList
    post /category/talent/list (GetTalentCategoryListRequest) returns (GetTalentCategoryListResponse)

    @doc "上传文件"
    @handler UploadFile
    post /upload (UploadReq) returns (string)
}