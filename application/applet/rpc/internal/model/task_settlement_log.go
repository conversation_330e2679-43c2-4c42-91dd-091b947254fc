package model

import "time"

// TaskSettlementLog 任务结算日志
type TaskSettlementLog struct {
	ID          uint64    `gorm:"primaryKey"`
	Date        string    `gorm:"column:date;index"`   // 日期
	Type        string    `gorm:"column:type"`         // 类型
	Completed   int8      `gorm:"column:completed"`    // 完成状态：0未开始，1处理中，2已完成
	ErrMsg      string    `gorm:"column:err_msg"`      // 错误信息
	LastUpdated time.Time `gorm:"column:last_updated"` // 上次更新时间

}

// TableName 表名
func (TaskSettlementLog) TableName() string {
	return "task_settlement_log"
}
