package model

import (
	"time"
	"xj-serv/pkg/model"
)

type Orders struct {
	model.MODEL_BASE
	OrderNo        string    `json:"order_no" gorm:"column:order_no"`               // 订单号
	OrderType      string    `json:"order_type" gorm:"column:order_type"`           // 订单类型(实物订单 虚拟订单)
	TotalPrice     uint      `json:"total_price" gorm:"column:total_price"`         // 商品总金额(不含优惠折扣)
	OrderPrice     uint      `json:"order_price" gorm:"column:order_price"`         // 订单金额(含优惠折扣)
	PayPrice       uint      `json:"pay_price" gorm:"column:pay_price"`             // 实际付款金额(包含运费)
	UpdatePrice    uint      `json:"update_price" gorm:"column:update_price"`       // 后台修改的订单金额(差价)
	BuyerRemark    string    `json:"buyer_remark" gorm:"column:buyer_remark"`       // 买家留言
	PayMethod      string    `json:"pay_method" gorm:"column:pay_method"`           // 支付方式(微信/支付宝/余额)
	PayStatus      string    `json:"pay_status" gorm:"column:pay_status"`           // 付款状态(未付款 已付款)
	PayTime        time.Time `json:"pay_time" gorm:"column:pay_time"`               // 付款时间
	TradeID        string    `json:"trade_id" gorm:"column:trade_id"`               // 第三方交易记录ID
	DeliveryType   string    `json:"delivery_type" gorm:"column:delivery_type"`     // 配送方式(快递配送 门店自提 无需配送)
	ExpressPrice   uint      `json:"express_price" gorm:"column:express_price"`     // 运费金额
	DeliveryStatus string    `json:"delivery_status" gorm:"column:delivery_status"` // 发货状态(未发货 已发货 部分发货)
	DeliveryTime   time.Time `json:"delivery_time" gorm:"column:delivery_time"`     // 发货时间
	ReceiptStatus  string    `json:"receipt_status" gorm:"column:receipt_status"`   // 收货状态(未收货 已收货)
	ReceiptTime    time.Time `json:"receipt_time" gorm:"column:receipt_time"`       // 收货时间
	OrderStatus    string    `json:"order_status" gorm:"column:order_status"`       // 订单状态(进行中 已取消 已关闭 已完成)
	PointsBonus    uint      `json:"points_bonus" gorm:"column:points_bonus"`       // 赠送的爆点数量
	MerchantRemark string    `json:"merchant_remark" gorm:"column:merchant_remark"` // 商家备注
	IsSettled      uint8     `json:"is_settled" gorm:"column:is_settled"`           // 订单是否已结算(0未结算 1已结算)
	SettledTime    time.Time `json:"settled_time" gorm:"column:settled_time"`       // 订单结算时间
	Platform       string    `json:"platform" gorm:"column:platform"`               // 来源客户端 (小程序,后台等)
	UserID         uint64    `json:"user_id" gorm:"column:user_id"`                 // 用户ID
	ProductID      uint      `json:"product_id" gorm:"column:product_id"`           // 产品ID
	UpdateStatus   string    `gorm:"column:update_status;type:varchar(32);default:normal;comment:更新状态(normal/updating)"`
}

func (m *Orders) TableName() string {
	return "orders"
}
