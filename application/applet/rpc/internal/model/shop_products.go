package model

import (
	"gorm.io/gorm"
	"time"
	"xj-serv/pkg/model"
)

type ShopProducts struct {
	model.MODEL_BASE
	ShopID         uint64               `json:"shop_id" gorm:"column:shop_id"`
	ProductId      uint64               `json:"product_id" gorm:"column:product_id"`
	Title          string               `json:"title" gorm:"column:title"`
	SubTitle       string               `json:"sub_title" gorm:"column:sub_title"`
	HeadImgs       string               `json:"head_imgs" gorm:"column:head_imgs"`
	DescInfo       string               `json:"desc_info" gorm:"column:desc_info"`
	Skus           string               `json:"skus" gorm:"column:skus"`
	Cats           string               `json:"cats" gorm:"-"`
	Cat1           uint                 `json:"cat1" gorm:"column:cat1"`
	Cat2           uint                 `json:"cat2" gorm:"column:cat2"`
	Cat3           uint                 `json:"cat3" gorm:"column:cat3"`
	MinPrice       uint                 `json:"min_price" gorm:"column:min_price"`
	Show           int8                 `json:"show" gorm:"column:show"` // 首页是否展示 1展示
	OnSale         int8                 `json:"on_sale" gorm:"column:on_sale"`
	Sales          uint64               `json:"sales" gorm:"column:sales"`
	SalesUpdatedAt time.Time            `json:"sales_updated_at" gorm:"column:sales_updated_at"`
	ShopInfo       ShopInfo             `json:"shop_info" gorm:"foreignKey:ID;references:ShopID;"`
	ShopName       string               `json:"shop_name" gorm:"-"`
	ShopQrCode     string               `json:"shop_qr_code" gorm:"-"`
	CatInfo1       ShopProductsCategory `json:"cat_info1" gorm:"foreignKey:CatID;references:Cat1;"`
	CatInfo2       ShopProductsCategory `json:"cat_info2" gorm:"foreignKey:CatID;references:Cat2;"`
	CatInfo3       ShopProductsCategory `json:"cat_info3" gorm:"foreignKey:CatID;references:Cat3;"`
}

func (m *ShopProducts) TableName() string {
	return "shop_products"
}

func (m *ShopProducts) AfterFind(tx *gorm.DB) (err error) {
	m.ShopName = m.ShopInfo.ShopName
	m.ShopQrCode = m.ShopInfo.QrCode
	return
}
