package model

import (
	"xj-serv/pkg/model"
)

type UserInventory struct {
	model.MODEL_BASE
	UserID         uint64 `json:"user_id" gorm:"column:user_id"`                 // 用户ID
	ProductID      uint64 `json:"product_id" gorm:"column:product_id"`           // 产品ID
	ProductType    string `json:"product_type" gorm:"column:product_type"`       // 产品类型(如套餐A、套餐B等)
	Quantity       uint   `json:"quantity" gorm:"column:quantity"`               // 当前库存数量
	TotalPurchased uint   `json:"total_purchased" gorm:"column:total_purchased"` // 累计购买数量
	TotalConsumed  uint   `json:"total_consumed" gorm:"column:total_consumed"`   // 累计消耗数量
}

func (m *UserInventory) TableName() string {
	return "user_inventory"
}
