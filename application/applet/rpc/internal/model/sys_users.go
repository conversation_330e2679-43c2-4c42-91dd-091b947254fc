package model

import (
	"xj-serv/pkg/model"

	"github.com/gofrs/uuid/v5"
)

type SysUser struct {
	model.MODEL_BASE
	UUID          uuid.UUID      `json:"uuid" gorm:"index;comment:用户UUID"`                                                                                                                    // 用户UUID
	Username      string         `json:"userName" gorm:"index;comment:用户登录名"`                                                                                                                 // 用户登录名
	Password      string         `json:"-"  gorm:"comment:用户登录密码"`                                                                                                                            // 用户登录密码
	NickName      string         `json:"nickName" gorm:"default:系统用户;comment:用户昵称"`                                                                                                           // 用户昵称
	SideMode      string         `json:"sideMode" gorm:"default:dark;comment:用户侧边主题"`                                                                                                         // 用户侧边主题
	HeaderImg     string         `json:"headerImg" gorm:"default:'https://xj-serv.oss-cn-beijing.aliyuncs.common/xj-serv/1705302257128_4d5477fec18994d0adb839d47fa30937_1.jpg';comment:用户头像"` // 用户头像
	BaseColor     string         `json:"baseColor" gorm:"default:#fff;comment:基础颜色"`                                                                                                          // 基础颜色
	ActiveColor   string         `json:"activeColor" gorm:"default:#1890ff;comment:活跃颜色"`                                                                                                     // 活跃颜色
	AuthorityId   int64          `json:"authorityId" gorm:"default:101;comment:用户角色ID"`                                                                                                       // 用户角色ID
	Authority     SysAuthority   `json:"authority" gorm:"foreignKey:AuthorityId;references:AuthorityId;comment:用户角色"`
	Authorities   []SysAuthority `json:"authorities" gorm:"many2many:sys_user_authority;"`
	Phone         string         `json:"phone"  gorm:"comment:用户手机号"`                     // 用户手机号
	Email         string         `json:"email"  gorm:"comment:用户邮箱"`                      // 用户邮箱
	Enable        int64          `json:"enable" gorm:"default:1;comment:用户是否被冻结 1正常 2冻结"` //用户是否被冻结 1正常 2冻结
	RelatedUserId int64          `json:"relatedUserId" gorm:"default:0;comment:关联xj_app的用户id"`
}

func (SysUser) TableName() string {
	return "sys_users"
}
