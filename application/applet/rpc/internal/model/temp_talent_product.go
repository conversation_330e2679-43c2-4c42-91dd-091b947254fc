package model

import (
	"time"
	"xj-serv/pkg/model"
)

// TempTalentProduct 临时商品表，用于缓存商品信息
type TempTalentProduct struct {
	model.MODEL_BASE
	ProductId        int64     `gorm:"column:product_id;not null"`
	ShopAppid        string    `gorm:"column:shop_appid;not null"`
	Title            string    `gorm:"column:title"`
	SubTitle         string    `gorm:"column:sub_title"`
	HeadImgs         string    `gorm:"column:head_imgs;type:json"`
	DescInfo         string    `gorm:"column:desc_info;type:json"`
	Skus             string    `gorm:"column:skus;type:json"`
	CommissionInfo   string    `gorm:"column:commission_info;type:json"`
	MinPrice         int64     `gorm:"column:min_price"`
	CommissionAmount int64     `gorm:"column:commission_amount"`      // 佣金（单位：分）
	IsRecommend      int32     `gorm:"column:is_recommend;default:0"` // 是否推荐
	IsPreferred      int32     `gorm:"column:is_preferred;default:0"` // 是否优选
	IsWindow         int32     `gorm:"column:is_window;default:0"`    // 是否优选
	PlanType         int8      `gorm:"column:plan_type;default:0"`
	CommissionType   int8      `gorm:"column:commission_type;default:0"`
	Show             int8      `gorm:"column:show;default:0"`
	OnSale           int8      `gorm:"column:on_sale;default:0"`
	Order            int64     `gorm:"column:order;default:0"`
	Cat1             int       `gorm:"column:cat1"`
	Cat2             int       `gorm:"column:cat2"`
	Cat3             int       `gorm:"column:cat3"`
	CCat1            int       `gorm:"column:c_cat1"`
	CCat2            int       `gorm:"column:c_cat2"`
	Sales            int       `gorm:"column:sales"`
	ExpiresAt        time.Time `gorm:"column:expires_at;not null"`
}

// TableName 指定表名
func (TempTalentProduct) TableName() string {
	return "temp_talent_products"
}
