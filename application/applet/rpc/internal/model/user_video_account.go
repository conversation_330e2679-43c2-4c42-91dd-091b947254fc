package model

import (
	"xj-serv/pkg/model"
)

type UserVideoAccount struct {
	model.MODEL_BASE
	VideoAccount             string                   `json:"video_account" gorm:"column:video_account"`
	UserID                   uint64                   `json:"user_id" gorm:"column:user_id"`
	IsOfficial               int8                     `json:"is_official" gorm:"column:is_official"`
	Name                     string                   `json:"name" gorm:"column:name"`
	LogoUrl                  string                   `json:"logo_url" gorm:"column:logo_url"`
	QrCode                   string                   `json:"qr_code" gorm:"column:qr_code"`
	CoverUrl                 string                   `json:"cover_url" gorm:"column:cover_url"`
	CategoryId               int8                     `json:"category_id" gorm:"column:category_id"`
	Description              string                   `json:"description" gorm:"column:is_official"`
	Show                     int8                     `json:"show" gorm:"column:show"`
	Order                    int8                     `json:"order" gorm:"column:order"`
	City                     string                   `json:"city" gorm:"column:city"`
	UserVideoAccountCategory UserVideoAccountCategory `json:"user_video_account_category" gorm:"foreignKey:ID;references:CategoryId;"`
}

func (m *UserVideoAccount) TableName() string {
	return "user_video_account"
}
