package model

import (
	"xj-serv/pkg/model"
)

type Products struct {
	model.MODEL_BASE
	Name         string `json:"name" gorm:"column:name"`
	Description  string `json:"description" gorm:"column:description"`
	Price        uint   `json:"price" gorm:"column:price"`
	Stocks       uint   `json:"stocks" gorm:"column:stocks"`
	OnSale       int8   `json:"on_sale" gorm:"column:on_sale"`
	Cover        string `json:"cover" gorm:"column:cover"`
	Content      string `json:"content" gorm:"column:content"`
	ProductType  string `json:"product_type" gorm:"column:product_type"`
	LockedStocks uint32 `gorm:"column:locked_stocks;type:int unsigned;default:0;comment:锁定库存"`
}

func (m *Products) TableName() string {
	return "products"
}
