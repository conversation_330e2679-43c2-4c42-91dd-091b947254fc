package model

import (
	"xj-serv/pkg/model"
)

type UserRole struct {
	model.MODEL_BASE
	RoleId      int    `json:"role_id" gorm:"column:role_id;not null"`                           // 角色ID
	RoleName    string `json:"role_name" gorm:"column:role_name;type:varchar(50);not null"`      // 角色名
	Description string `json:"description" gorm:"column:description;type:varchar(200);not null"` // 描述
}

func (m *UserRole) TableName() string {
	return "user_role"
}
