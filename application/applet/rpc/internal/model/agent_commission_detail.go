package model

import (
	"xj-serv/pkg/model"
)

// AgentCommissionDetail 代理商-业务员分佣明细
type AgentCommissionDetail struct {
	model.MODEL_BASE
	OrderID        string `json:"order_id" gorm:"column:order_id;type:varchar(64);uniqueIndex:uk_order_id"` // 订单ID
	OrderAmount    int64  `json:"order_amount" gorm:"column:order_amount;default:0;not null"`               // 订单佣金（机构所得）
	AgentAmount    int64  `json:"agent_amount" gorm:"column:agent_amount;default:0;not null"`               // 代理商佣金（机构分给代理商）
	SalesmanAmount int64  `json:"salesman_amount" gorm:"column:salesman_amount;default:0;not null"`         // 代理商佣金（代理商分给业务员）
	AgentUserID    uint64 `json:"agent_user_id" gorm:"column:agent_user_id"`                                // 代理商用户ID
	SalesmanUserID uint64 `json:"salesman_user_id" gorm:"column:salesman_user_id"`                          // 业务员用户ID
	Remark         string `json:"remark" gorm:"column:remark;type:varchar(40)"`                             // 备注
}

// TableName 返回表名
func (m *AgentCommissionDetail) TableName() string {
	return "agent_commission_detail"
}
