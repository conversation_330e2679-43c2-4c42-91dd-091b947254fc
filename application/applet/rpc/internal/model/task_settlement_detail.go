package model

import (
	"time"
)

type TaskSettlementDetail struct {
	ID           uint64 `gorm:"primaryKey"`
	CreatedAt    time.Time
	UpdatedAt    time.Time
	DeletedAt    *time.Time `gorm:"index"`
	Amount       int64
	AmountChange int64
	Type         int32
	UserID       uint64
	FromUser     uint64
	CreateTime   time.Time
	AccountType  string
	AssociatedID string
	Remark       string
}

func (TaskSettlementDetail) TableName() string {
	return "task_settlement_detail"
}
