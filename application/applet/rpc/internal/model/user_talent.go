package model

import (
	"time"
)

// UserTalent corresponds to the user_talent table
type UserTalent struct {
	ID          int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID      int64      `gorm:"column:user_id;type:bigint unsigned;not null;index:idx_user_id" json:"user_id"` // 新增: 关联的用户 ID
	TalentAppid string     `gorm:"column:talent_appid;type:varchar(64);not null;uniqueIndex:uk_talent_appid" json:"talent_appid"`
	Nickname    string     `gorm:"column:nickname;type:varchar(128);not null;default:''" json:"nickname"`                                           // 注意：SQL中此字段名为 nickname
	HeadImgURL  string     `gorm:"column:head_img_url;type:varchar(512);not null;default:''" json:"head_img_url"`                                   // 注意：SQL中此字段名为 head_img_url
	BindTime    *time.Time `gorm:"column:bind_time;type:datetime;default:null" json:"bind_time"`                                                    // 使用指针表示 nullable, 类型改为 datetime
	CreatedAt   time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`                            // 类型改为 datetime
	UpdatedAt   time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP" json:"updated_at"` // 类型改为 datetime
}

// TableName specifies the table name for the UserTalent model
func (UserTalent) TableName() string {
	return "user_talent"
}
