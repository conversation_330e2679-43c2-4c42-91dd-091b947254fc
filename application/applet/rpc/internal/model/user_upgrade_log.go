package model

import (
	"xj-serv/pkg/model"
)

// UserUpgradeLog 用户升级日志
type UserUpgradeLog struct {
	model.MODEL_BASE
	UserID      int64 `gorm:"column:user_id;index"` // 用户ID
	OldLevel    int8  `gorm:"column:old_level"`     // 旧等级
	NewLevel    int8  `gorm:"column:new_level"`     // 新等级
	InviteCount int   `gorm:"column:invite_count"`  // 邀请用户数
	OrderCount  int   `gorm:"column:order_count"`   // 订单数量
}

// TableName 表名
func (UserUpgradeLog) TableName() string {
	return "user_upgrade_log"
}
