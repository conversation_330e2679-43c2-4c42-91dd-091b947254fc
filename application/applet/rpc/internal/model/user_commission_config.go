package model

import (
	"time"
	"xj-serv/pkg/model"
)

// UserCommissionConfig 推客佣金配置表
type UserCommissionConfig struct {
	model.MODEL_BASE
	Level          int        `json:"level" gorm:"column:level;uniqueIndex"`              // 推客级别
	CommissionRate float64    `json:"commission_rate" gorm:"column:commission_rate"`      // 佣金比例(百分比)
	Description    string     `json:"description" gorm:"column:description"`              // 描述
	Status         uint8      `json:"status" gorm:"column:status;default:1"`              // 状态：0-禁用 1-启用
	CreatedAt      time.Time  `json:"created_at" gorm:"column:created_at;autoCreateTime"` // 创建时间
	UpdatedAt      time.Time  `json:"updated_at" gorm:"column:updated_at;autoUpdateTime"` // 更新时间
	DeletedAt      *time.Time `gorm:"column:deleted_at" json:"deleted_at"`                // 删除时间
}

// TableName 设置表名
func (m *UserCommissionConfig) TableName() string {
	return "user_commission_config"
}
