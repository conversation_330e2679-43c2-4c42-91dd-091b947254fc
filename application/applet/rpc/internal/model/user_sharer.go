package model

import (
	"time"
)

// UserSharer corresponds to the user_sharer table
type UserSharer struct {
	ID              int64      `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	UserID          int64      `gorm:"column:user_id;type:bigint unsigned;not null;index:idx_user_id" json:"user_id"` // 新增: 关联的用户 ID
	SharerAppid     string     `gorm:"column:sharer_appid;type:varchar(64);not null;uniqueIndex:uk_sharer_appid" json:"sharer_appid"`
	Nickname        string     `gorm:"column:nickname;type:varchar(128);not null;default:''" json:"nickname"`         // 新增: 推客昵称
	HeadImgURL      string     `gorm:"column:head_img_url;type:varchar(512);not null;default:''" json:"head_img_url"` // 新增: 推客头像 URL
	BindTime        *time.Time `gorm:"column:bind_time;type:datetime;default:null" json:"bind_time"`                  // 使用指针表示 nullable, 类型改为 datetime
	CommissionRatio int        `gorm:"column:commission_ratio;not null;default:0" json:"commission_ratio"`
	CommissionType  uint8      `gorm:"column:commission_type;type:tinyint unsigned;not null;default:0" json:"commission_type"`
	CreatedAt       time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP" json:"created_at"`                            // 类型改为 datetime
	UpdatedAt       time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP" json:"updated_at"` // 类型改为 datetime
}

// TableName specifies the table name for the UserSharer model
func (UserSharer) TableName() string {
	return "user_sharer"
}
