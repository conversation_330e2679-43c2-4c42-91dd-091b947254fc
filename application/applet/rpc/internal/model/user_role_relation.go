package model

import (
	"xj-serv/pkg/model"
)

// UserRoleRelation 代理商用户关联表
type UserRoleRelation struct {
	model.MODEL_BASE
	AgentUserID    uint64 `json:"agent_user_id" gorm:"column:agent_user_id;not null;uniqueIndex:uk_agent_id_promoter_user_id"`       // 代理商userid
	PromoterUserID uint64 `json:"promoter_user_id" gorm:"column:promoter_user_id;not null;uniqueIndex:uk_agent_id_promoter_user_id"` // 业务员userid
}

func (m *UserRoleRelation) TableName() string {
	return "user_role_relation"
}
