package model

import (
	"xj-serv/pkg/model"
)

//type  struct {
//	model.MODEL_BASE
//	Name  string `json:"name" gorm:"column:name"`
//	Level int64  `json:"level" gorm:"column:level"`
//}

// WidgetImg 图片组件表
type WidgetImg struct {
	model.MODEL_BASE
	Title   string `gorm:"column:title" json:"title"`                // 标题
	Status  int8   `gorm:"column:status;default:0" json:"status"`    // 状态
	Img     string `gorm:"column:img" json:"img"`                    // 图片地址
	Link    string `gorm:"column:link" json:"link"`                  // 链接地址
	Sort    int    `gorm:"column:sort;default:0" json:"sort"`        // 排序
	Type    int8   `gorm:"column:type;default:0" json:"type"`        // 1 banner 2 群二维码 3客服二维码 4分享海报 5我的页面广告
	AdminID int    `gorm:"column:admin_id;default:0" json:"adminId"` // 管理员ID
}

// TableName 表名
func (w *WidgetImg) TableName() string {
	return "widget_img"
}
