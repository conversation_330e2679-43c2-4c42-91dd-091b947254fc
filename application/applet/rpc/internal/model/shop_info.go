package model

import "xj-serv/pkg/model"

type ShopInfo struct {
	model.MODEL_BASE
	ShopName      string `json:"shop_name" gorm:"column:shop_name"`
	ShopAppID     string `json:"shop_app_id" gorm:"column:shop_app_id"`
	ShopAppSecret string `json:"shop_app_secret" gorm:"column:shop_app_secret"`
	MsgToken      string `json:"msg_token" gorm:"column:msg_token"`
	MsgSecret     string `json:"msg_secret" gorm:"column:msg_secret"`
	ShopCode      string `json:"shop_code" gorm:"column:shop_code"`
	QrCode        string `json:"qr_code" gorm:"column:qr_code"`
}

func (m *ShopInfo) TableName() string {
	return "shop_info"
}
