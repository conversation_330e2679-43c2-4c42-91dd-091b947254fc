package model

import (
	"xj-serv/pkg/model"
)

type TempLiveGoods struct {
	model.MODEL_BASE
	ProductID               uint64 `json:"product_id" gorm:"column:product_id"`
	ProductName             string `json:"product_name" gorm:"column:product_name"`
	ProductImgUrl           string `json:"product_img_url" gorm:"column:product_img_url"`
	ProductPrice            int    `json:"product_price" gorm:"column:product_price"`
	PredictCommissionAmount int    `json:"predict_commission_amount" gorm:"column:predict_commission_amount"`
	TalentAppid             string `json:"talent_appid" gorm:"column:talent_appid"`
}

func (m *TempLiveGoods) TableName() string {
	return "temp_live_goods"
}
