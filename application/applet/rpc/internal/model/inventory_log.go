package model

import (
	"xj-serv/pkg/model"
)

type InventoryLog struct {
	model.MODEL_BASE
	UserID          uint64 `json:"user_id" gorm:"column:user_id"`                   // 用户ID
	ProductID       uint64 `json:"product_id" gorm:"column:product_id"`             // 产品ID
	ProductType     string `json:"product_type" gorm:"column:product_type"`         // 产品类型
	OrderID         uint64 `json:"order_id" gorm:"column:order_id"`                 // 关联订单ID(购买时)
	TransactionType string `json:"transaction_type" gorm:"column:transaction_type"` // 交易类型(购买/消耗/调整)
	Quantity        int    `json:"quantity" gorm:"column:quantity"`                 // 变更数量(正数为增加，负数为减少)
	BeforeQuantity  int    `json:"before_quantity" gorm:"column:before_quantity"`   // 变更前数量
	AfterQuantity   int    `json:"after_quantity" gorm:"column:after_quantity"`     // 变更后数量
	Remark          string `json:"remark" gorm:"column:remark"`                     // 备注
}

func (m *InventoryLog) TableName() string {
	return "inventory_log"
}
