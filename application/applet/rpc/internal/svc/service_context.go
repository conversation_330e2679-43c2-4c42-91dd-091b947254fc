package svc

import (
	"github.com/zeromicro/go-zero/core/logx"
	"xj-serv/application/applet/rpc/internal/config"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/rabbitmq"
	tcc_redis "xj-serv/pkg/redis"

	"github.com/zeromicro/go-zero/core/stores/redis"
)

type ServiceContext struct {
	Config       config.Config
	DB           *orm.DB
	AppDB        *orm.DB
	TccRedis     *tcc_redis.TccRedisHelper
	BizRedis     *redis.Redis
	QueueManager *rabbitmq.QueueManager
}

func NewServiceContext(c config.Config) *ServiceContext {
	db := orm.MustNewMysql(&orm.Config{
		DSN:          c.DB.DataSource,
		MaxOpenConns: c.DB.MaxOpenConns,
		MaxIdleCnns:  c.DB.MaxIdleConns,
		MaxLifetime:  c.DB.MaxLifetime,
	})

	appdb := orm.MustNewMysql(&orm.Config{
		DSN:          c.AppDB.DataSource,
		MaxOpenConns: c.AppDB.MaxOpenConns,
		MaxIdleCnns:  c.AppDB.MaxIdleConns,
		MaxLifetime:  c.AppDB.MaxLifetime,
	})

	rds := redis.MustNewRedis(redis.RedisConf{
		Host: c.BizRedis.Host,
		Type: c.BizRedis.Type,
		Pass: c.BizRedis.Pass,
	})

	// 初始化RabbitMQ队列管理器
	queueManager, err := rabbitmq.InitQueueManagerFromServiceConfig(c)
	if err != nil {
		logx.Errorf("初始化RabbitMQ队列管理器失败: %v", err)
	} else {
		logx.Info("RabbitMQ队列管理器初始化成功")
	}

	return &ServiceContext{
		Config:       c,
		DB:           db,
		AppDB:        appdb,
		BizRedis:     rds,
		QueueManager: queueManager,
	}
}
