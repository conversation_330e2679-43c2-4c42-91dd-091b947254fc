package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/config"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type RechargeBenefitsPoolLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRechargeBenefitsPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RechargeBenefitsPoolLogic {
	return &RechargeBenefitsPoolLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 充值任务池
func (l *RechargeBenefitsPoolLogic) RechargeBenefitsPool(in *pb.RechargeBenefitsPoolReq) (*pb.BoolResp, error) {
	// 参数校验
	if in.UserId <= 0 || in.Quantity == 0 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证代理商身份
	var agent model.User
	if err := l.svcCtx.AppDB.Where("id = ? AND role_id = ?", in.UserId, config.RoleAgent).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("用户ID %d 不是代理商角色", in.UserId)
			return &pb.BoolResp{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "不是代理商，无法设置任务奖励次数")
		}
		l.Errorf("查询代理商信息失败: %v", err)
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询代理商信息失败")
	}

	// 使用事务处理
	err := l.svcCtx.AppDB.Transaction(func(tx *gorm.DB) error {
		// 查询是否存在记录
		var benefitsPool model.UserBenefitsPool
		result := tx.Where("user_id = ? AND type = ?", in.UserId, 4).First(&benefitsPool)

		if result.Error != nil {
			if result.Error == gorm.ErrRecordNotFound {
				// 不存在记录，创建新记录
				// 判断充值数量是否大于0
				if in.Quantity <= 0 {
					l.Errorf("充值数量必须大于0: %d", in.Quantity)
					return xerr.NewErrMsg(l.ctx, "充值数量必须大于0")
				}
				benefitsPool = model.UserBenefitsPool{
					Type:     4,
					Quantity: int(in.Quantity),
					UserID:   in.UserId,
				}
				if err := tx.Create(&benefitsPool).Error; err != nil {
					l.Errorf("创建任务池记录失败: %v", err)
					return err
				}
				l.Infof("成功为用户 %d 创建任务池4并充值 %d 次", in.UserId, in.Quantity)
			} else {
				// 查询出错
				l.Errorf("查询任务池记录失败: %v", result.Error)
				return result.Error
			}
		} else {
			// 存在记录，更新数量

			// 判断充值数量是否大于0
			if in.Quantity < 0 && benefitsPool.Quantity+int(in.Quantity) < 0 {
				l.Errorf("减少的数量必须小于已有数量: %d", in.Quantity)
				return xerr.NewErrMsg(l.ctx, "减少的数量必须小于已有数量")
			}
			if err := tx.Model(&benefitsPool).Update("quantity", gorm.Expr("quantity + ?", in.Quantity)).Error; err != nil {
				l.Errorf("更新任务池数量失败: %v", err)
				return err
			}
			l.Infof("成功为用户 %d 的任务池4充值 %d 次", in.UserId, in.Quantity)
		}

		// // 记录充值日志
		// poolLog := model.UserBenefitsPoolLog{
		//     TaskType:       4, // 任务类型为4
		//     BenefitsUserId: in.UserId,
		//     UserID:         in.UserId, // 自己给自己充值
		//     Quantity:       int(in.Quantity),
		//     Remark:         "管理员充值",
		// }
		// if err := tx.Create(&poolLog).Error; err != nil {
		//     l.Errorf("创建充值日志失败: %v", err)
		//     return err
		// }

		return nil
	})

	if err != nil {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "充值任务池失败: "+err.Error())
	}

	return &pb.BoolResp{Success: true}, nil
}
