package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductCategoryListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTalentProductCategoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductCategoryListLogic {
	return &GetTalentProductCategoryListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 商品分类列表接口
func (l *GetTalentProductCategoryListLogic) GetTalentProductCategoryList(in *pb.GetTalentCategoryListRequest) (*pb.GetTalentCategoryListResponse, error) {
	var categories []*pb.CategoryInfo
	var totalCount int64 // 用于记录总数

	baseQuery := l.svcCtx.AppDB.Model(&model.TalentProductsCategory{})

	if in.FId == 0 {
		countQuery := baseQuery.Where("f_id = 0 OR f_id IS NULL")
		if err := countQuery.Count(&totalCount).Error; err != nil {
			l.Errorf("Failed to count top-level categories: %v", err)
			return nil, err
		}

		var topLevelModels []model.TalentProductsCategory
		pageSize := in.PageSize
		page := in.PageNo
		if pageSize == 0 {
			pageSize = 10 // 默认每页数量
		}
		if page == 0 {
			page = 1 // 默认页码
		}
		offset := (page - 1) * pageSize

		pagedQuery := baseQuery.Where("f_id = 0 OR f_id IS NULL").
			Order("`order` ASC"). // 按照 order 字段排序
			Limit(int(pageSize)).
			Offset(int(offset))

		if err := pagedQuery.Find(&topLevelModels).Error; err != nil {
			l.Errorf("Failed to retrieve paginated top-level categories: %v", err)
			return nil, err
		}

		//  为每个顶层分类递归构建子分类树
		for _, topCatModel := range topLevelModels {
			pbCat := convertModelToPbCategoryInfo(&topCatModel)
			children, err := l.buildCategoryTree(pbCat.Id) // 递归查找子分类
			if err != nil {
				l.Errorf("Failed to build category tree for top-level ID %d: %v", pbCat.Id, err)
				return nil, err
			}
			pbCat.Children = children
			categories = append(categories, pbCat)
		}

	} else { // 如果 f_id 大于 0，查询指定 f_id 的所有分类，不分页
		var directChildrenModels []model.TalentProductsCategory
		query := baseQuery.Where("f_id = ?", in.FId).Order("`order` ASC") // 按照 order 字段排序

		if err := query.Find(&directChildrenModels).Error; err != nil {
			l.Errorf("Failed to retrieve categories for parent ID %d: %v", in.FId, err)
			return nil, err
		}

		totalCount = int64(len(directChildrenModels)) // 直接子分类的总数

		// 2. 为每个直接子分类递归构建子分类树
		for _, directChildModel := range directChildrenModels {
			pbCat := convertModelToPbCategoryInfo(&directChildModel)
			children, err := l.buildCategoryTree(pbCat.Id) // 递归查找子分类
			if err != nil {
				l.Errorf("Failed to build category tree for ID %d: %v", pbCat.Id, err)
				return nil, err
			}
			pbCat.Children = children
			categories = append(categories, pbCat)
		}
	}

	// 最终对返回的顶层或直接子分类进行排序（即使已经在查询中排序，这里确保最终顺序）
	// sort.Slice(categories, func(i, j int) bool {
	// 	return categories[i].Order < categories[j].Order
	// })
	// 由于已经在 GORM 查询中使用了 Order("`order` ASC")，这里可能不需要额外的排序
	// 但如果需要对子分类也确保排序，需要在 buildCategoryTree 中处理

	return &pb.GetTalentCategoryListResponse{
		Categories: categories,
		Total:      uint64(totalCount),
	}, nil
}

// buildCategoryTree 辅助函数：递归构建指定父分类下的所有子分类树。
func (l *GetTalentProductCategoryListLogic) buildCategoryTree(parentID uint64) ([]*pb.CategoryInfo, error) {
	var childrenModels []model.TalentProductsCategory
	if err := l.svcCtx.AppDB.Model(&model.TalentProductsCategory{}).
		Where("f_id = ?", parentID).
		Order("`order` ASC"). // 子分类也按照 order 排序
		Find(&childrenModels).Error; err != nil {
		l.Errorf("Failed to retrieve children for parent ID %d: %v", parentID, err)
		return nil, err
	}

	var pbChildren []*pb.CategoryInfo
	for _, childModel := range childrenModels {
		pbChild := convertModelToPbCategoryInfo(&childModel)
		// 递归调用，构建子分类的子分类
		grandChildren, err := l.buildCategoryTree(pbChild.Id)
		if err != nil {
			l.Errorf("Failed to build category tree for child ID %d: %v", pbChild.Id, err)
			return nil, err
		}
		pbChild.Children = grandChildren
		pbChildren = append(pbChildren, pbChild)
	}

	return pbChildren, nil
}

// convertModelToPbCategoryInfo 辅助函数：将 GORM 模型转换为 Protobuf CategoryInfo
func convertModelToPbCategoryInfo(m *model.TalentProductsCategory) *pb.CategoryInfo {
	pbCat := &pb.CategoryInfo{
		Id:        uint64(m.ID),
		FId:       uint64(m.FID),
		Name:      m.Name,
		Img:       m.Img,
		Order:     uint64(m.Order),
		CreatedAt: m.CreatedAt.Format("2006-01-02 15:04:05"),
		UpdatedAt: m.UpdatedAt.Format("2006-01-02 15:04:05"),
	}
	return pbCat
}
