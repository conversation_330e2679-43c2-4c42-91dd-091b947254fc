package adminlogic

import (
	"context"
	"time"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetInventoryLogListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetInventoryLogListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetInventoryLogListLogic {
	return &GetInventoryLogListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取团长套餐数日志列表
func (l *GetInventoryLogListLogic) GetInventoryLogList(in *pb.GetInventoryLogListReq) (*pb.GetInventoryLogListResp, error) {
	// 构建查询
	db := l.svcCtx.AppDB.Table("inventory_log il").
		Select("il.*, u.mobile, u.nick_name, u.role_id, u.level").
		Joins("LEFT JOIN user_user u ON il.user_id = u.id").
		Where("il.deleted_at IS NULL")

	// 添加查询条件
	if in.Mobile != "" {
		db = db.Where("u.mobile LIKE ?", "%"+in.Mobile+"%")
	}

	if in.TransactionType != "" {
		db = db.Where("il.transaction_type = ?", in.TransactionType)
	}

	if in.StartTime != "" && in.EndTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("il.created_at BETWEEN ? AND ?", startTime, endTime)
	} else if in.StartTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		db = db.Where("il.created_at >= ?", startTime)
	} else if in.EndTime != "" {
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("il.created_at <= ?", endTime)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算团长套餐数日志总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	type Result struct {
		model.InventoryLog
		Mobile   string `gorm:"column:mobile"`
		Nickname string `gorm:"column:nick_name"`
		RoleId   string `gorm:"column:role_id"`
		Level    int    `gorm:"column:level"`
	}

	var results []Result
	offset := int((in.PageRequest.PageNo - 1) * in.PageRequest.PageSize)
	limit := int(in.PageRequest.PageSize)

	if err := db.Order("il.id DESC").Offset(offset).Limit(limit).Find(&results).Error; err != nil {
		l.Errorf("查询团长套餐数日志列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp := &pb.GetInventoryLogListResp{
		List:     make([]*pb.InventoryLogInfo, 0, len(results)),
		Total:    total,
		PageNo:   in.PageRequest.PageNo,
		PageSize: in.PageRequest.PageSize,
	}

	// 转换数据
	for _, result := range results {
		resp.List = append(resp.List, &pb.InventoryLogInfo{
			Id:              uint64(result.ID),
			UserId:          result.UserID,
			Nickname:        result.Nickname,
			Mobile:          result.Mobile,
			RoleId:          result.RoleId,
			Level:           int32(result.Level),
			ProductId:       result.ProductID,
			ProductType:     result.ProductType,
			OrderId:         result.OrderID,
			TransactionType: result.TransactionType,
			Quantity:        int32(result.Quantity),
			BeforeQuantity:  int32(result.BeforeQuantity),
			AfterQuantity:   int32(result.AfterQuantity),
			Remark:          result.Remark,
			CreatedAt:       result.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return resp, nil
}
