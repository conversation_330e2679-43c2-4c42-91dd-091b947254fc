package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserLevelLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetUserLevelLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserLevelLogic {
	return &SetUserLevelLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置用户等级
func (l *SetUserLevelLogic) SetUserLevel(in *pb.SetUserLevelReq) (*pb.BoolResp, error) {
	// 校验用户是否存在
	var user model.User
	if err := l.svcCtx.AppDB.First(&user, in.UserId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 校验等级是否存在
	var level model.UserLevel
	if err := l.svcCtx.AppDB.First(&level, in.LevelId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 更新用户等级
	if err := l.svcCtx.AppDB.Model(&user).Update("level", level.Level).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	return &pb.BoolResp{Success: true}, nil
}
