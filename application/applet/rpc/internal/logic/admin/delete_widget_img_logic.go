package adminlogic

import (
	"context"
	"time"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteWidgetImgLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteWidgetImgLogic {
	return &DeleteWidgetImgLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除图片组件
func (l *DeleteWidgetImgLogic) DeleteWidgetImg(in *pb.DeleteWidgetImgReq) (*pb.BoolResp, error) {
	// 软删除图片组件
	now := time.Now()
	result := l.svcCtx.AppDB.Model(&model.WidgetImg{}).
		Where("id = ?", in.Id).
		Update("deleted_at", now)

	if result.Error != nil {
		l.Errorf("删除图片组件失败: %v", result.Error)
		return nil, result.Error
	}

	// 检查是否找到并更新了记录
	if result.RowsAffected == 0 {
		l.Errorf("未找到ID为%d的图片组件", in.Id)
		return &pb.BoolResp{Success: false}, nil
	}

	return &pb.BoolResp{Success: true}, nil
}
