package adminlogic

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserLevelListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserLevelListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserLevelListLogic {
	return &GetUserLevelListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户等级列表
func (l *GetUserLevelListLogic) GetUserLevelList(in *pb.EmptyRequest) (*pb.UserLevelListResp, error) {
	var levels []model.UserLevel
	if err := l.svcCtx.AppDB.Model(&model.UserLevel{}).Find(&levels).Error; err != nil {
		return nil, err
	}

	var list []*pb.UserLevelItem
	for _, lv := range levels {
		item := &pb.UserLevelItem{}
		_ = copier.Copy(item, &lv)
		// 时间格式化
		item.CreatedAt = lv.CreatedAt.Format("2006-01-02 15:04:05")
		item.UpdatedAt = lv.UpdatedAt.Format("2006-01-02 15:04:05")
		list = append(list, item)
	}

	return &pb.UserLevelListResp{
		List: list,
	}, nil
}
