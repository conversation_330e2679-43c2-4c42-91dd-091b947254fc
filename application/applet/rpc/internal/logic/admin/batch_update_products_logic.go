package adminlogic

import (
	"context"
	"encoding/json"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type BatchUpdateProductsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewBatchUpdateProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BatchUpdateProductsLogic {
	return &BatchUpdateProductsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 批量设置商品
func (l *BatchUpdateProductsLogic) BatchUpdateProducts(in *pb.BatchUpdateProductsReq) (*pb.BoolResp, error) {
	// 1. 参数校验
	if len(in.ProductIds) == 0 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "商品ID列表不能为空")
	}

	// 2. 构建更新字段
	updates := make(map[string]interface{})
	if in.IsRecommend != nil {
		updates["is_recommend"] = *in.IsRecommend
	}
	if in.IsPreferred != nil {
		updates["is_preferred"] = *in.IsPreferred
	}

	if in.IsPreferred != nil && *in.IsPreferred == 1 && in.CCat1 != nil && in.CCat2 != nil {
		updates["is_preferred"] = *in.IsPreferred
		updates["c_cat1"] = *in.CCat1
		updates["c_cat2"] = *in.CCat2
	}
	if in.IsWindow != nil {
		updates["is_window"] = *in.IsWindow
	}

	if len(updates) == 0 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "至少需要修改一个属性")
	}

	// 3. 执行批量更新
	result := l.svcCtx.AppDB.Model(&model.TempTalentProduct{}).
		Where("product_id IN ?", in.ProductIds).
		Updates(updates)

	if result.Error != nil {
		l.Logger.Errorf("批量更新商品失败: %v", result.Error)
		return &pb.BoolResp{
			Success: false,
		}, result.Error
	}

	//发送消息
	// 查询所有商品的 shop_appid
	type ProductInfo struct {
		ProductId uint64
		ShopAppid string
	}

	var products []ProductInfo
	if err := l.svcCtx.AppDB.Model(&model.TempTalentProduct{}).
		Select("product_id, shop_appid").
		Where("product_id IN ?", in.ProductIds).
		Find(&products).Error; err != nil {
		return nil, xerr.NewErrCode(xerr.DbError)
	}

	// 构建商品ProductId到shop_appid的映射
	productMap := make(map[uint64]string)
	for _, p := range products {
		productMap[p.ProductId] = p.ShopAppid
	}

	// 发送消息到队列
	for _, productID := range in.ProductIds {
		shopAppid, exists := productMap[productID]
		if !exists {
			l.Logger.Errorf("商品[%d]未找到对应的shop_appid", productID)
			continue
		}

		if err := l.sendShareMsgMessage(productID, shopAppid); err != nil {
			l.Logger.Errorf("发送商品[%d]分享消息失败: %v", productID, err)
			// 不影响主流程，继续处理其他商品
			continue
		}
	}

	return &pb.BoolResp{
		Success: true,
	}, nil
}

// sendShareMsgMessage 发送分享消息到队列
func (l *BatchUpdateProductsLogic) sendShareMsgMessage(productID uint64, shopAppid string) error {
	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		return xerr.NewErrMsg(l.ctx, "队列管理器未初始化")
	}

	// 构建消息内容
	message := map[string]interface{}{
		"type":       "gen_share_msg",
		"product_id": productID,
		"shop_appid": shopAppid,
		"scene":      "所有",
	}

	// 序列化消息
	messageBytes, err := json.Marshal(message)
	if err != nil {
		l.Logger.Errorf("序列化消息失败: %v", err)
		return xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化消息失败")
	}

	// 发送消息到队列
	err = l.svcCtx.QueueManager.PublishToQueue(
		"ShareMsgGeneration", // 队列名称
		messageBytes,         // 消息内容
	)
	if err != nil {
		l.Logger.Errorf("发送消息到队列失败: %v", err)
		return xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送消息到队列失败")
	}

	l.Logger.Infof("已发送商品[%s]的分享消息到队列", productID)
	return nil
}
