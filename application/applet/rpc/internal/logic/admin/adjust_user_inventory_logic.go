package adminlogic

import (
	"context"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/emptypb"
	"gorm.io/gorm"
	"time"
	"xj-serv/application/applet/rpc/internal/model"
	tcc_redis "xj-serv/pkg/redis"
	"xj-serv/pkg/result/xerr"
	ec_order "xj-serv/types/order"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type AdjustUserInventoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAdjustUserInventoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AdjustUserInventoryLogic {
	return &AdjustUserInventoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 调整用户库存
func (l *AdjustUserInventoryLogic) AdjustUserInventory(in *pb.AdjustUserInventoryReq) (*pb.AdjustUserInventoryResp, error) {
	l.Infof("调整用户库存请求: %+v", in)

	// 获取数据库连接
	db := l.svcCtx.AppDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 根据TCC操作类型执行不同的逻辑
	switch in.TccAction {
	case "try":
		return l.adjustUserInventoryTry(in)
	case "confirm":
		return l.adjustUserInventoryConfirm(in)
	case "cancel":
		_, err := l.adjustUserInventoryCancel(in)
		if err != nil {
			return nil, err
		}
		return &pb.AdjustUserInventoryResp{
			Success:     true,
			OperationID: in.OperationId,
			Message:     "库存调整已取消",
		}, nil
	case "": // 空字符串表示普通调整（非TCC模式）
		var resp *pb.AdjustUserInventoryResp
		err := db.Transaction(func(tx *gorm.DB) error {
			var err error
			resp, err = l.processInventoryAdjustment(tx, in)
			return err
		})
		if err != nil {
			return nil, err
		}
		l.Infof("库存调整成功: %s", resp.OperationID)
		return resp, nil
	default:
		return nil, fmt.Errorf("不支持的TCC操作类型: %s", in.TccAction)
	}
}

// adjustUserInventoryTry TCC模式的Try阶段 - 检查并锁定资源
func (l *AdjustUserInventoryLogic) adjustUserInventoryTry(in *pb.AdjustUserInventoryReq) (*pb.AdjustUserInventoryResp, error) {
	l.Infof("TCC-Try: 调整用户库存请求: %+v", in)

	// 获取数据库连接
	db := l.svcCtx.AppDB

	var resp *pb.AdjustUserInventoryResp

	err := db.Transaction(func(tx *gorm.DB) error {
		// 参数校验和用户角色检查
		_, product, err := l.validateAndCheckRole(tx, in.UserID, in.ProductID, in.Quantity, in.TransactionType)
		if err != nil {
			return err
		}

		// 查询用户库存
		_, beforeQuantity, exists, err := l.getUserInventory(tx, in.UserID, in.ProductID, true)
		if err != nil {
			return err
		}

		// 根据交易类型处理库存
		if in.TransactionType == ec_order.TransactionTypeConsumption {
			// 消费类型必须存在记录且库存足够
			if !exists {
				l.Errorf("用户库存不存在，无法消费, 用户ID: %d, 产品ID: %d", in.UserID, in.ProductID)
				return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法消费")
			}

			// 消费类型的数量应该是负数
			if in.Quantity >= 0 {
				l.Errorf("消费类型的数量应为负数, 用户ID: %d, 产品ID: %d, 数量: %d", in.UserID, in.ProductID, in.Quantity)
				return xerr.NewErrCodeMsg(xerr.ParamsValidateError, "消费类型的数量应为负数")
			}

			// 检查库存是否足够
			if beforeQuantity < uint(-in.Quantity) {
				l.Errorf("用户库存不足, 用户ID: %d, 产品ID: %d, 当前库存: %d, 需要消费: %d",
					in.UserID, in.ProductID, beforeQuantity, -in.Quantity)
				return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不足")
			}
		} else if in.TransactionType == ec_order.TransactionTypePurchase {
			// 购买类型的数量应该是正数
			if in.Quantity <= 0 {
				l.Errorf("购买类型的数量应为正数, 用户ID: %d, 产品ID: %d, 数量: %d", in.UserID, in.ProductID, in.Quantity)
				return xerr.NewErrCodeMsg(xerr.ParamsValidateError, "购买类型的数量应为正数")
			}
		} else if in.TransactionType == ec_order.TransactionTypeAdjustment {
			// 调整类型可以是正数或负数
			// 如果是减少库存且记录不存在，则报错
			if in.Quantity < 0 && !exists {
				l.Errorf("用户库存不存在，无法减少库存, 用户ID: %d, 产品ID: %d", in.UserID, in.ProductID)
				return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法减少库存")
			}

			// 如果是减少库存，检查库存是否足够
			if in.Quantity < 0 && beforeQuantity < uint(-in.Quantity) {
				l.Errorf("用户库存不足, 用户ID: %d, 产品ID: %d, 当前库存: %d, 需要减少: %d",
					in.UserID, in.ProductID, beforeQuantity, -in.Quantity)
				return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不足")
			}
		}

		// 计算调整后的库存
		var afterQuantity uint
		if in.Quantity > 0 {
			afterQuantity = beforeQuantity + uint(in.Quantity)
		} else {
			afterQuantity = beforeQuantity - uint(-in.Quantity)
		}

		// 生成操作ID
		operationID := uuid.New().String()

		// 创建库存调整预留记录（使用Redis）
		reservationData := &tcc_redis.InventoryAdjustmentData{
			OperationID:     operationID,
			UserID:          in.UserID,
			ProductID:       in.ProductID,
			ProductType:     product.ProductType,
			Quantity:        in.Quantity,
			BeforeQuantity:  int(beforeQuantity),
			AfterQuantity:   int(afterQuantity),
			Remark:          in.Remark,
			TransactionType: in.TransactionType, // 添加交易类型
			Status:          "pending",
			CreatedAt:       time.Now().Unix(),
		}

		if err := l.svcCtx.TccRedis.SaveInventoryAdjustmentReservation(l.ctx, reservationData); err != nil {
			l.Errorf("保存库存调整预留记录到Redis失败: %v", err)
			return err
		}

		// 将操作ID添加到 gRPC 元数据中
		md := metadata.Pairs(
			"operation_id", operationID,
			"user_id", fmt.Sprintf("%d", in.UserID),
			"product_id", fmt.Sprintf("%d", in.ProductID),
			"quantity", fmt.Sprintf("%d", in.Quantity),
			"transaction_type", in.TransactionType,
		)

		// 将元数据添加到上下文中
		if err := grpc.SendHeader(l.ctx, md); err != nil {
			l.Errorf("发送元数据头失败: %v", err)
		}

		resp = &pb.AdjustUserInventoryResp{
			Success:        true,
			OperationID:    operationID,
			BeforeQuantity: uint32(beforeQuantity),
			AfterQuantity:  uint32(afterQuantity),
			Message:        "库存调整资源锁定成功，等待确认",
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	l.Infof("TCC-Try: 库存调整资源锁定成功: %s", resp.OperationID)
	return resp, nil
}

// adjustUserInventoryConfirm TCC模式的Confirm阶段 - 确认调整
func (l *AdjustUserInventoryLogic) adjustUserInventoryConfirm(in *pb.AdjustUserInventoryReq) (*pb.AdjustUserInventoryResp, error) {
	l.Infof("TCC-Confirm: 确认调整用户库存: %+v", in)

	// 获取数据库连接
	db := l.svcCtx.AppDB

	var resp *pb.AdjustUserInventoryResp

	err := db.Transaction(func(tx *gorm.DB) error {
		// 从Redis获取库存调整预留记录
		reservationData, err := l.svcCtx.TccRedis.GetInventoryAdjustmentReservation(l.ctx, in.OperationId)
		if err != nil {
			l.Errorf("获取库存调整预留记录失败: %v", err)
			return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "库存调整预留记录不存在或已处理")
		}

		// 检查状态
		if reservationData.Status != "pending" {
			l.Errorf("库存调整预留记录状态异常, 操作ID: %s, 当前状态: %s", in.OperationId, reservationData.Status)
			return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "库存调整预留记录状态异常")
		}

		// 查询产品信息
		var product model.Products
		if err := tx.First(&product, reservationData.ProductID).Error; err != nil {
			l.Errorf("查询产品信息失败: %v", err)
			return err
		}

		// 查询用户库存
		inventory, _, exists, err := l.getUserInventory(tx, reservationData.UserID, reservationData.ProductID, false)
		if err != nil {
			return err
		}

		// 根据交易类型处理库存
		transactionType := reservationData.TransactionType
		if transactionType == ec_order.TransactionTypeConsumption {
			// 消费类型必须存在记录
			if !exists {
				l.Errorf("用户库存不存在，无法消费, 用户ID: %d, 产品ID: %d", reservationData.UserID, reservationData.ProductID)
				return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法消费")
			}

			// 更新库存和消耗总量
			if err := tx.Model(&inventory).Updates(map[string]interface{}{
				"quantity":       gorm.Expr("quantity + ?", reservationData.Quantity),        // 减少库存
				"total_consumed": gorm.Expr("total_consumed + ?", -reservationData.Quantity), // 增加消耗总量
			}).Error; err != nil {
				l.Errorf("更新用户库存失败: %v", err)
				return err
			}
		} else if transactionType == ec_order.TransactionTypePurchase {
			if !exists {
				// 如果记录不存在，则创建新记录
				inventory = model.UserInventory{
					UserID:         reservationData.UserID,
					ProductID:      reservationData.ProductID,
					ProductType:    reservationData.ProductType,
					Quantity:       uint(reservationData.Quantity),
					TotalPurchased: uint(reservationData.Quantity),
					TotalConsumed:  0,
				}
				if err := tx.Create(&inventory).Error; err != nil {
					l.Errorf("创建用户库存记录失败: %v", err)
					return err
				}
			} else {
				// 更新库存和购买总量
				if err := tx.Model(&inventory).Updates(map[string]interface{}{
					"quantity":        gorm.Expr("quantity + ?", reservationData.Quantity),        // 增加库存
					"total_purchased": gorm.Expr("total_purchased + ?", reservationData.Quantity), // 增加购买总量
				}).Error; err != nil {
					l.Errorf("更新用户库存失败: %v", err)
					return err
				}
			}
		} else if transactionType == ec_order.TransactionTypeAdjustment {
			if !exists {
				// 如果记录不存在且是增加库存，则创建新记录
				if reservationData.Quantity > 0 {
					inventory = model.UserInventory{
						UserID:         reservationData.UserID,
						ProductID:      reservationData.ProductID,
						ProductType:    reservationData.ProductType,
						Quantity:       uint(reservationData.Quantity),
						TotalPurchased: 0,
						TotalConsumed:  0,
					}
					if err := tx.Create(&inventory).Error; err != nil {
						l.Errorf("创建用户库存记录失败: %v", err)
						return err
					}
				} else {
					l.Errorf("用户库存不存在，无法减少库存, 用户ID: %d, 产品ID: %d", reservationData.UserID, reservationData.ProductID)
					return xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法减少库存")
				}
			} else {
				// 更新库存
				if err := tx.Model(&inventory).Update("quantity", gorm.Expr("quantity + ?", reservationData.Quantity)).Error; err != nil {
					l.Errorf("更新用户库存失败: %v", err)
					return err
				}
			}
		}

		// 记录库存变更日志
		inventoryLog := &model.InventoryLog{
			UserID:          reservationData.UserID,
			ProductID:       reservationData.ProductID,
			ProductType:     reservationData.ProductType,
			OrderID:         0,                               // 非订单操作，设为0
			TransactionType: reservationData.TransactionType, // 使用预留记录中的交易类型
			Quantity:        int(reservationData.Quantity),
			BeforeQuantity:  reservationData.BeforeQuantity,
			AfterQuantity:   reservationData.AfterQuantity,
			Remark:          reservationData.Remark,
		}

		if err := tx.Create(inventoryLog).Error; err != nil {
			l.Errorf("创建库存日志失败: %v", err)
			return err
		}

		// 更新Redis中的库存调整预留记录状态
		if err := l.svcCtx.TccRedis.UpdateInventoryAdjustmentStatus(l.ctx, in.OperationId, "confirmed"); err != nil {
			l.Errorf("更新库存调整预留记录状态失败: %v", err)
			// 不返回错误，因为库存已经调整成功
		}

		resp = &pb.AdjustUserInventoryResp{
			Success:        true,
			OperationID:    in.OperationId,
			BeforeQuantity: uint32(reservationData.BeforeQuantity),
			AfterQuantity:  uint32(reservationData.AfterQuantity),
			Message:        "库存调整已确认",
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	l.Infof("TCC-Confirm: 库存调整成功: %s", resp.OperationID)
	return resp, nil
}

// adjustUserInventoryCancel TCC模式的Cancel阶段 - 取消调整
func (l *AdjustUserInventoryLogic) adjustUserInventoryCancel(in *pb.AdjustUserInventoryReq) (*emptypb.Empty, error) {
	l.Infof("TCC-Cancel: 取消调整用户库存: %+v", in)

	// 从Redis获取库存调整预留记录
	reservationData, err := l.svcCtx.TccRedis.GetInventoryAdjustmentReservation(l.ctx, in.OperationId)
	if err != nil {
		l.Infof("获取库存调整预留记录失败，无需取消: %v", err)
		return &emptypb.Empty{}, nil
	}

	// 检查状态
	if reservationData.Status != "pending" {
		l.Infof("库存调整预留记录状态不是pending，无需取消: %s, 当前状态: %s", in.OperationId, reservationData.Status)
		return &emptypb.Empty{}, nil
	}

	// 更新Redis中的库存调整预留记录状态
	if err := l.svcCtx.TccRedis.UpdateInventoryAdjustmentStatus(l.ctx, in.OperationId, "cancelled"); err != nil {
		l.Errorf("更新库存调整预留记录状态失败: %v", err)
		return &emptypb.Empty{}, err
	}

	l.Infof("TCC-Cancel: 库存调整取消成功: %s", in.OperationId)
	return &emptypb.Empty{}, nil
}

// processInventoryAdjustment 处理库存调整的核心逻辑（供普通调整使用）
func (l *AdjustUserInventoryLogic) processInventoryAdjustment(tx *gorm.DB, in *pb.AdjustUserInventoryReq) (*pb.AdjustUserInventoryResp, error) {
	// 参数校验和用户角色检查
	_, product, err := l.validateAndCheckRole(tx, in.UserID, in.ProductID, in.Quantity, in.TransactionType)
	if err != nil {
		return nil, err
	}

	// 查询用户库存
	inventory, beforeQuantity, exists, err := l.getUserInventory(tx, in.UserID, in.ProductID, false)
	if err != nil {
		return nil, err
	}

	var afterQuantity uint

	// 根据交易类型处理库存
	if in.TransactionType == ec_order.TransactionTypeConsumption {
		// 消费类型必须存在记录且库存足够
		if !exists {
			l.Errorf("用户库存不存在，无法消费, 用户ID: %d, 产品ID: %d 传入类型 %s 判定类型 %s", in.UserID, in.ProductID, in.TransactionType, ec_order.TransactionTypeConsumption)
			return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法消费")
		}

		// 消费类型的数量应该是负数
		if in.Quantity >= 0 {
			l.Errorf("消费类型的数量应为负数, 用户ID: %d, 产品ID: %d, 数量: %d", in.UserID, in.ProductID, in.Quantity)
			return nil, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "消费类型的数量应为负数")
		}

		// 检查库存是否足够
		if beforeQuantity < uint(-in.Quantity) {
			l.Errorf("用户库存不足, 用户ID: %d, 产品ID: %d, 当前库存: %d, 需要消费: %d",
				in.UserID, in.ProductID, beforeQuantity, -in.Quantity)
			return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不足")
		}

		// 计算调整后的库存
		afterQuantity = beforeQuantity - uint(-in.Quantity)

		// 更新库存和消耗总量
		if err := tx.Model(&inventory).Updates(map[string]interface{}{
			"quantity":       afterQuantity,
			"total_consumed": gorm.Expr("total_consumed + ?", -in.Quantity),
		}).Error; err != nil {
			l.Errorf("更新用户库存失败: %v", err)
			return nil, err
		}
	} else if in.TransactionType == ec_order.TransactionTypePurchase {
		// 购买类型的数量应该是正数
		if in.Quantity <= 0 {
			l.Errorf("购买类型的数量应为正数, 用户ID: %d, 产品ID: %d, 数量: %d", in.UserID, in.ProductID, in.Quantity)
			return nil, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "购买类型的数量应为正数")
		}

		if !exists {
			// 如果记录不存在，则创建新记录
			beforeQuantity = 0
			afterQuantity = uint(in.Quantity)

			inventory = model.UserInventory{
				UserID:         in.UserID,
				ProductID:      in.ProductID,
				ProductType:    product.ProductType,
				Quantity:       afterQuantity,
				TotalPurchased: uint(in.Quantity),
				TotalConsumed:  0,
			}

			if err := tx.Create(&inventory).Error; err != nil {
				l.Errorf("创建用户库存记录失败: %v", err)
				return nil, err
			}
		} else {
			// 计算调整后的库存
			afterQuantity = beforeQuantity + uint(in.Quantity)

			// 更新库存和购买总量
			if err := tx.Model(&inventory).Updates(map[string]interface{}{
				"quantity":        afterQuantity,
				"total_purchased": gorm.Expr("total_purchased + ?", in.Quantity),
			}).Error; err != nil {
				l.Errorf("更新用户库存失败: %v", err)
				return nil, err
			}
		}
	} else if in.TransactionType == ec_order.TransactionTypeAdjustment {
		// 调整类型可以是正数或负数
		if !exists {
			// 如果是减少库存且记录不存在，则报错
			if in.Quantity < 0 {
				l.Errorf("用户库存不存在，无法减少库存, 用户ID: %d, 产品ID: %d", in.UserID, in.ProductID)
				return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不存在，无法减少库存")
			}

			// 如果是增加库存且记录不存在，则创建新记录
			beforeQuantity = 0
			afterQuantity = uint(in.Quantity)

			inventory = model.UserInventory{
				UserID:         in.UserID,
				ProductID:      in.ProductID,
				ProductType:    product.ProductType,
				Quantity:       afterQuantity,
				TotalPurchased: 0,
				TotalConsumed:  0,
			}

			if err := tx.Create(&inventory).Error; err != nil {
				l.Errorf("创建用户库存记录失败: %v", err)
				return nil, err
			}
		} else {
			// 检查库存是否足够（如果是减少库存）
			if in.Quantity < 0 && beforeQuantity < uint(-in.Quantity) {
				l.Errorf("用户库存不足, 用户ID: %d, 产品ID: %d, 当前库存: %d, 需要减少: %d",
					in.UserID, in.ProductID, beforeQuantity, -in.Quantity)
				return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户库存不足")
			}

			// 计算调整后的库存
			if in.Quantity > 0 {
				afterQuantity = beforeQuantity + uint(in.Quantity)
			} else {
				afterQuantity = beforeQuantity - uint(-in.Quantity)
			}

			// 更新库存
			if err := tx.Model(&inventory).Update("quantity", afterQuantity).Error; err != nil {
				l.Errorf("更新用户库存失败: %v", err)
				return nil, err
			}
		}
	}

	// 生成操作ID
	operationID := uuid.New().String()

	// 记录库存变更日志
	inventoryLog := &model.InventoryLog{
		UserID:          in.UserID,
		ProductID:       in.ProductID,
		ProductType:     product.ProductType,
		OrderID:         0,                  // 非订单操作，设为0
		TransactionType: in.TransactionType, // 使用请求中的交易类型
		Quantity:        int(in.Quantity),
		BeforeQuantity:  int(beforeQuantity),
		AfterQuantity:   int(afterQuantity),
		Remark:          in.Remark,
	}

	if err := tx.Create(inventoryLog).Error; err != nil {
		l.Errorf("创建库存日志失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp := &pb.AdjustUserInventoryResp{
		Success:        true,
		OperationID:    operationID,
		BeforeQuantity: uint32(beforeQuantity),
		AfterQuantity:  uint32(afterQuantity),
		Message:        "库存调整成功",
	}

	return resp, nil
}

// validateAndCheckRole 验证参数并检查用户角色
func (l *AdjustUserInventoryLogic) validateAndCheckRole(tx *gorm.DB, userID, productID uint64, quantity int32, transactionType string) (model.User, model.Products, error) {
	// 参数校验
	if userID == 0 {
		return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "用户ID不能为空")
	}
	if productID == 0 {
		return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "产品ID不能为空")
	}
	if quantity == 0 {
		return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "调整数量不能为0")
	}

	// 检查交易类型是否有效
	validTypes := []string{ec_order.TransactionTypePurchase, ec_order.TransactionTypeConsumption, ec_order.TransactionTypeAdjustment}
	isValidType := false
	for _, t := range validTypes {
		if transactionType == t {
			isValidType = true
			break
		}
	}
	if !isValidType {
		return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "无效的交易类型")
	}

	// 只有 purchase 和 adjustment 类型需要检查用户角色
	if transactionType == ec_order.TransactionTypePurchase || transactionType == ec_order.TransactionTypeAdjustment {
		// 检查用户角色是否允许调整库存
		var user model.User
		if err := tx.Select("role_id").First(&user, userID).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				l.Errorf("用户不存在, 用户ID: %d", userID)
				return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户不存在")
			}
			l.Errorf("查询用户信息失败: %v", err)
			return model.User{}, model.Products{}, err
		}

		// 检查用户角色是否允许调整库存（例如role_id为3或4的用户）
		allowedRoles := []uint{2, 3, 4} // 允许调整库存的角色ID
		isAllowed := false
		for _, roleID := range allowedRoles {
			if uint(user.RoleId) == roleID {
				isAllowed = true
				break
			}
		}

		if !isAllowed {
			l.Errorf("用户角色不允许调整库存, 用户ID: %d, 角色ID: %d", userID, user.RoleId)
			return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "用户角色不允许调整库存")
		}
	}

	// 查询产品信息
	var product model.Products
	if err := tx.First(&product, productID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("产品不存在, 产品ID: %d", productID)
			return model.User{}, model.Products{}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "产品不存在")
		}
		l.Errorf("查询产品信息失败: %v", err)
		return model.User{}, model.Products{}, err
	}

	return model.User{}, product, nil
}

// getUserInventory 查询用户库存
func (l *AdjustUserInventoryLogic) getUserInventory(tx *gorm.DB, userID, productID uint64, forUpdate bool) (model.UserInventory, uint, bool, error) {
	var inventory model.UserInventory
	var query *gorm.DB

	if forUpdate {
		query = tx.Set("gorm:query_option", "FOR UPDATE")
	} else {
		query = tx
	}

	result := query.Where("user_id = ? AND product_id = ?", userID, productID).First(&inventory)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return model.UserInventory{}, 0, false, nil
		}
		l.Errorf("查询用户库存失败: %v", result.Error)
		return model.UserInventory{}, 0, false, result.Error
	}

	return inventory, inventory.Quantity, true, nil
}
