package adminlogic

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"xj-serv/application/applet/rpc/internal/config"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"
	ec_order "xj-serv/types/order"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetAgentTeamPackageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetAgentTeamPackageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetAgentTeamPackageLogic {
	return &SetAgentTeamPackageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 给代理商设置团长套餐数量
func (l *SetAgentTeamPackageLogic) SetAgentTeamPackage(in *pb.SetAgentTeamPackageReq) (*pb.BoolResp, error) {
	// 参数校验
	if in.AgentUserId <= 0 || in.Number <= 0 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证代理商身份
	var agent model.User
	if err := l.svcCtx.AppDB.Where("id = ? AND role_id = ?", in.AgentUserId, config.RoleAgent).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("用户ID %d 不是代理商角色", in.AgentUserId)
			return &pb.BoolResp{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "不是代理商，无法设置团长套餐")
		}
		l.Errorf("查询代理商信息失败: %v", err)
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询代理商信息失败")
	}

	////  查询代理商的团长套餐库存
	//var agentInventory model.UserInventory
	//if err := l.svcCtx.AppDB.Where("user_id = ? AND product_id = ?", in.AgentUserId, 1).First(&agentInventory).Error; err != nil {
	//	return &pb.BoolResp{
	//		Success: false,
	//	}, xerr.NewErrMsg(l.ctx, "查询团长套餐库存失败")
	//}
	//
	////  检查代理商的团长套餐库存是否足够
	//if in.Number < 0 && agentInventory.Quantity < uint(-in.Number) {
	//	l.Errorf("代理商ID %d 的团长套餐库存不足，当前库存: %d, 需要: %d",
	//		in.AgentUserId, agentInventory.Quantity, in.Number)
	//	return &pb.BoolResp{
	//		Success: false,
	//	}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("团长套餐库存不足，当前库存: %d", agentInventory.Quantity))
	//}

	//  开始事务，调整库存
	err := l.svcCtx.AppDB.Transaction(func(tx *gorm.DB) error {
		// 6.1 调用adjustUserInventory减少代理商的团长套餐库存
		adjustLogic := NewAdjustUserInventoryLogic(l.ctx, l.svcCtx)
		_, err := adjustLogic.AdjustUserInventory(&pb.AdjustUserInventoryReq{
			UserID:          in.AgentUserId,
			ProductID:       1,         // 团长产品ID
			Quantity:        in.Number, // 消耗指定数量
			Remark:          fmt.Sprintf("后台管理员给代理商ID:%d，调整团长套餐数", in.AgentUserId),
			TransactionType: ec_order.TransactionTypeAdjustment,
			TccAction:       "",
		})
		if err != nil {
			l.Errorf("调整代理商团长套餐库存失败: %v", err)
			return xerr.NewErrMsg(l.ctx, "调整代理商团长套餐库存失败")
		}

		return nil
	})

	if err != nil {
		return &pb.BoolResp{
			Success: false,
		}, err
	}

	return &pb.BoolResp{
		Success: true,
	}, nil
}
