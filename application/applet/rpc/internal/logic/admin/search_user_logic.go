package adminlogic

import (
	"context"
	"strings"
	"xj-serv/application/applet/rpc/internal/model"

	"github.com/jinzhu/copier"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchUserLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSearchUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUserLogic {
	return &SearchUserLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 搜索用户
func (l *SearchUserLogic) SearchUser(in *pb.SearchUserReq) (*pb.SearchUserResp, error) {

	db := l.svcCtx.AppDB.Model(&model.User{})

	// 添加搜索条件
	if in.Keyword != "" {
		keyword := strings.TrimSpace(in.Keyword)
		db = db.Where("mobile LIKE ? OR nick_name LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	}
	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算用户总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var users []model.User
	offset := int((in.PageRequest.PageNo - 1) * in.PageRequest.PageSize)
	limit := int(in.PageRequest.PageSize)

	if err := db.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		l.Errorf("查询用户失败: %v", err)
		return nil, err
	}

	// 转换为响应格式
	userList := make([]*pb.XjUser, 0, len(users))
	for _, u := range users {
		pbUser := &pb.XjUser{}
		_ = copier.Copy(pbUser, &u)
		userList = append(userList, pbUser)
	}

	return &pb.SearchUserResp{
		List:     userList,
		Total:    total,
		PageNo:   in.PageRequest.PageNo,
		PageSize: in.PageRequest.PageSize,
	}, nil
}
