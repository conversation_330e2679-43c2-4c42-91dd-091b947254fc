package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"github.com/jinzhu/copier"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetXjUserListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetXjUserListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetXjUserListLogic {
	return &GetXjUserListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户列表  这里加个xj做区分
func (l *GetXjUserListLogic) GetXjUserList(in *pb.XjUserListReq) (*pb.XjUserListResp, error) {
	// 获取当前登录的管理后台用户ID
	adminUserId := in.AdminUserId

	// 查询当前管理后台用户的关联用户ID
	var adminUser model.SysUser
	if err := l.svcCtx.DB.Where("id = ?", adminUserId).First(&adminUser).Error; err != nil {
		l.Logger.Errorf("查询管理后台用户失败: %v", err)
		return nil, err
	}

	var users []model.User
	var total int64
	query := l.svcCtx.AppDB.Model(&model.User{})

	// 如果管理后台用户关联了用户，则只查询该用户邀请链下的用户
	if adminUser.RelatedUserId > 0 {
		// 使用递归查询获取邀请链下的所有用户ID
		inviteUserIds, err := l.getInviteChainUserIds(adminUser.RelatedUserId)
		if err != nil {
			l.Logger.Errorf("获取邀请链用户失败: %v", err)
			return nil, err
		}

		// 如果没有下级用户，只能查看关联用户自己
		if len(inviteUserIds) == 0 {
			inviteUserIds = append(inviteUserIds, adminUser.RelatedUserId)
		}

		query = query.Where("id IN (?)", inviteUserIds)
	}

	if in.NickName != "" {
		query = query.Where("nick_name LIKE ?", "%"+in.NickName+"%")
	}
	if in.Mobile != "" {
		query = query.Where("mobile LIKE ?", "%"+in.Mobile+"%")
	}
	if in.Level > -1 {
		query = query.Where("level = ?", in.Level)
	}
	if in.RoleId > -1 {
		query = query.Where("role_id = ?", in.RoleId)
	}
	if in.StartTime != "" && in.EndTime != "" {
		query = query.Where("created_at BETWEEN ? AND ?", in.StartTime, in.EndTime)
	}
	query = query.Order("id DESC")
	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, err
	}
	offset := (in.PageNo - 1) * in.PageSize
	if err := query.Offset(int(offset)).Limit(int(in.PageSize)).Find(&users).Error; err != nil {
		return nil, err
	}

	// 推荐人信息
	inviteUserIds := make([]int64, 0)
	for _, u := range users {
		if u.InviteFrom > 0 {
			inviteUserIds = append(inviteUserIds, int64(u.InviteFrom))
		}
	}
	inviteUserMap := make(map[int64]model.User)
	if len(inviteUserIds) > 0 {
		var inviteUsers []model.User
		if err := l.svcCtx.AppDB.Model(&model.User{}).Where("id IN (?)", inviteUserIds).Find(&inviteUsers).Error; err == nil {
			for _, iu := range inviteUsers {
				inviteUserMap[iu.ID] = iu
			}
		}
	}
	// 读取所有角色
	var roles []model.UserRole
	roleMap := make(map[int]string)
	if err := l.svcCtx.AppDB.Model(&model.UserRole{}).Find(&roles).Error; err == nil {
		for _, r := range roles {
			roleMap[r.RoleId] = r.RoleName
		}
	}

	// 读取所有等级
	var levels []model.UserLevel
	levelMap := make(map[int]string)
	if err := l.svcCtx.AppDB.Model(&model.UserLevel{}).Find(&levels).Error; err == nil {
		for _, lv := range levels {
			levelMap[int(lv.Level)] = lv.Name
		}
	}

	var list []*pb.XjUser
	for _, u := range users {
		xjUser := &pb.XjUser{}
		_ = copier.Copy(xjUser, &u)
		// 推荐人昵称、手机号补充
		if invite, ok := inviteUserMap[int64(u.InviteFrom)]; ok {
			xjUser.InviteName = invite.NickName
			xjUser.InviteMobile = invite.Mobile
		}
		// 角色名
		if roleName, ok := roleMap[int(u.RoleId)]; ok {
			xjUser.RoleName = roleName
		}
		// 等级名
		if levelName, ok := levelMap[int(u.Level)]; ok {
			xjUser.LevelName = levelName
		}
		// 格式化时间
		xjUser.CreatedAt = u.CreatedAt.Format("2006-01-02 15:04:05")
		xjUser.UpdatedAt = u.UpdatedAt.Format("2006-01-02 15:04:05")
		list = append(list, xjUser)
	}

	return &pb.XjUserListResp{
		List:     list,
		Total:    total,
		PageNo:   in.PageNo,
		PageSize: in.PageSize,
	}, nil
}

// getInviteChainUserIds 递归获取邀请链下的所有用户ID
func (l *GetXjUserListLogic) getInviteChainUserIds(relatedUserId int64) ([]int64, error) {
	// 使用递归SQL查询获取所有下级用户ID
	query := `WITH RECURSIVE user_hierarchy AS (
        -- 初始查询：获取关联用户
        SELECT id, invite_from
        FROM user_user
        WHERE id = ?

        UNION ALL

        -- 递归查询：获取所有下级用户
        SELECT u.id, u.invite_from
        FROM user_user u
        INNER JOIN user_hierarchy h ON u.invite_from = h.id
    )
    -- 获取所有用户ID（包括关联用户自己）
    SELECT id
    FROM user_hierarchy;`

	var userIds []int64
	if err := l.svcCtx.AppDB.Raw(query, relatedUserId).Scan(&userIds).Error; err != nil {
		return nil, err
	}

	return userIds, nil
}
