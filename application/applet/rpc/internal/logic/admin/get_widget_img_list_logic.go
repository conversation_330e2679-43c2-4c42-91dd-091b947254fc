package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWidgetImgListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetWidgetImgListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWidgetImgListLogic {
	return &GetWidgetImgListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取图片组件列表
func (l *GetWidgetImgListLogic) GetWidgetImgList(in *pb.GetWidgetImgListReq) (*pb.GetWidgetImgListResp, error) {
	// 构建查询
	db := l.svcCtx.AppDB.Model(&model.WidgetImg{}).Where("deleted_at IS NULL")

	// 根据类型筛选
	if in.Type > 0 {
		db = db.Where("type = ?", in.Type)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算图片组件总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var widgetImgs []model.WidgetImg
	pageNo := int(in.PageNo)
	if pageNo <= 0 {
		pageNo = 1
	}
	pageSize := int(in.PageSize)
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (pageNo - 1) * pageSize

	if err := db.Order("sort DESC, id DESC").
		Offset(offset).
		Limit(pageSize).
		Find(&widgetImgs).Error; err != nil {
		l.Errorf("查询图片组件列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp := &pb.GetWidgetImgListResp{
		List:     make([]*pb.WidgetImgInfo, 0, len(widgetImgs)),
		Total:    total,
		PageNo:   int32(pageNo),
		PageSize: int32(pageSize),
	}

	// 转换数据
	for _, img := range widgetImgs {
		widgetImgInfo := &pb.WidgetImgInfo{
			Id:        int32(img.ID),
			Title:     img.Title,
			Status:    int32(img.Status),
			Img:       img.Img,
			Link:      img.Link,
			Sort:      int32(img.Sort),
			Type:      int32(img.Type),
			CreatedAt: img.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt: img.UpdatedAt.Format("2006-01-02 15:04:05"),
			AdminId:   int32(img.AdminID),
		}
		resp.List = append(resp.List, widgetImgInfo)
	}

	return resp, nil
}
