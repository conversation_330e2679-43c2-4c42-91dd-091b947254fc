package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateWidgetImgLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateWidgetImgLogic {
	return &UpdateWidgetImgLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修改图片组件
func (l *UpdateWidgetImgLogic) UpdateWidgetImg(in *pb.UpdateWidgetImgReq) (*pb.BoolResp, error) {
	update := map[string]interface{}{
		"title":    in.Title,
		"status":   in.Status,
		"img":      in.Img,
		"link":     in.Link,
		"sort":     in.Sort,
		"type":     in.Type,
		"admin_id": in.AdminId,
	}
	if err := l.svcCtx.AppDB.Model(&model.WidgetImg{}).Where("id = ?", in.Id).Updates(update).Error; err != nil {
		l.Errorf("图片组件修改失败: %v", err)
		return nil, err
	}
	return &pb.BoolResp{Success: true}, nil
}
