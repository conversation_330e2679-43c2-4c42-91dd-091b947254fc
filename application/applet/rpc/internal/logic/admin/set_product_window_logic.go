package adminlogic

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetProductWindowLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetProductWindowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetProductWindowLogic {
	return &SetProductWindowLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置/取消橱窗商品
func (l *SetProductWindowLogic) SetProductWindow(in *pb.SetProductWindowReq) (*pb.BoolResp, error) {
	// 1. 参数校验
	if in.ProductId == 0 {
		return &pb.BoolResp{Success: false}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 2. 查询商品是否存在
	var product model.TempTalentProduct
	err := l.svcCtx.AppDB.Where("product_id = ?", in.ProductId).First(&product).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &pb.BoolResp{Success: false}, xerr.NewErrMsg(l.ctx, "商品不存在")
		}
		return &pb.BoolResp{Success: false}, xerr.NewErrMsg(l.ctx, "数据库查询失败")
	}

	// 3. 更新排序字段
	err = l.svcCtx.AppDB.Model(&product).Update("is_window", in.IsWindow).Error
	if err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 4. 返回成功
	return &pb.BoolResp{Success: true}, nil
}
