package adminlogic

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTalentProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductCategoryLogic {
	return &GetTalentProductCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 商品分类信息
func (l *GetTalentProductCategoryLogic) GetTalentProductCategory(in *pb.GetTalentCategoryRequest) (*pb.CategoryInfo, error) {
	var talentProductsCategory model.TalentProductsCategory
	if err := l.svcCtx.AppDB.First(&talentProductsCategory, in.Id).Error; err != nil {
		return nil, err
	}
	// 组装详情
	resp := &pb.CategoryInfo{}
	_ = copier.Copy(resp, &talentProductsCategory)
	// 格式化时间
	createdAtStr := talentProductsCategory.CreatedAt.Format("2006-01-02 15:04:05")
	updatedAtStr := talentProductsCategory.UpdatedAt.Format("2006-01-02 15:04:05")
	resp.CreatedAt = createdAtStr
	resp.UpdatedAt = updatedAtStr
	return resp, nil
}
