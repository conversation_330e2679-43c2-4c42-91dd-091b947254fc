package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateProductCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateProductCategoryLogic {
	return &UpdateProductCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修改商品分类
func (l *UpdateProductCategoryLogic) UpdateProductCategory(in *pb.UpdateCategoryRequest) (*pb.BoolResp, error) {
	update := map[string]interface{}{
		"f_id":  in.FId,
		"name":  in.Name,
		"img":   in.Img,
		"order": in.Order,
	}
	var category model.TalentProductsCategory
	err := l.svcCtx.AppDB.Where("id = ?", in.Id).First(&category).Error
	if err != nil {
		l.Errorf("Failed to check parent category existence: %v", err)
		return &pb.BoolResp{Success: false}, err
	}

	if in.FId > 0 {
		var parentCategory model.TalentProductsCategory
		err := l.svcCtx.AppDB.Where("id = ?", in.FId).First(&parentCategory).Error
		if err != nil {
			l.Errorf("Failed to check parent category existence: %v", err)
			return &pb.BoolResp{Success: false}, err
		}
	}

	if err := l.svcCtx.AppDB.Model(&model.TalentProductsCategory{}).Where("id = ?", in.Id).Updates(update).Error; err != nil {
		l.Errorf("商品分类修改失败: %v", err)
		return nil, err
	}
	return &pb.BoolResp{Success: true}, nil
}
