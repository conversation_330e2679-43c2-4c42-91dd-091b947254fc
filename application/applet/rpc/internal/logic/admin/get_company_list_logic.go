package adminlogic

import (
	"context"
	"time"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCompanyListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyListLogic {
	return &GetCompanyListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取公司列表
func (l *GetCompanyListLogic) GetCompanyList(in *pb.GetCompanyListRequest) (*pb.GetCompanyListResp, error) {
	// 构建查询条件
	db := l.svcCtx.AppDB.Model(&model.CompanyInfo{})

	// 添加查询条件
	if in.Name != "" {
		db = db.Where("company_info.name LIKE ?", "%"+in.Name+"%")
	}
	if in.Phone != "" {
		db = db.Where("company_info.phone LIKE ?", "%"+in.Phone+"%")
	}
	if in.Status != -1 {
		db = db.Where("company_info.status = ?", in.Status)
	}
	if in.StartTime != "" && in.EndTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("company_info.created_at BETWEEN ? AND ?", startTime, endTime)
	} else if in.StartTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		db = db.Where("company_info.created_at >= ?", startTime)
	} else if in.EndTime != "" {
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("company_info.created_at <= ?", endTime)
	}
	if in.RoleId != -1 {
		db = db.Joins("JOIN user_user ON user_user.id = company_info.user_id").Where("user_user.role_id = ?", in.RoleId)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算公司总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var companies []model.CompanyInfo
	offset := int((in.PageRequest.PageNo - 1) * in.PageRequest.PageSize)
	limit := int(in.PageRequest.PageSize)

	if err := db.Preload("User").Order("company_info.id DESC").Offset(offset).Limit(limit).Find(&companies).Error; err != nil {
		l.Errorf("查询公司列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp := &pb.GetCompanyListResp{
		List:     make([]*pb.CompanyInfo, 0, len(companies)),
		Total:    total,
		PageNo:   in.PageRequest.PageNo,
		PageSize: in.PageRequest.PageSize,
	}

	// 转换数据
	for _, company := range companies {
		var pbUser pb.XjUser
		// 结构体字段名一致时可直接 copy
		_ = copier.Copy(&pbUser, &company.User)
		resp.List = append(resp.List, &pb.CompanyInfo{
			Id:          company.ID,
			UserId:      company.UserID,
			Name:        company.Name,
			Address:     company.Address,
			BankAccount: company.BankAccount,
			BankName:    company.BankName,
			TaxNumber:   company.TaxNumber,
			LicenseImg:  company.LicenseImg,
			Phone:       company.Phone,
			Status:      int32(company.Status),
			Leader:      company.Leader,
			CreatedAt:   company.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:   company.UpdatedAt.Format("2006-01-02 15:04:05"),
			User:        &pbUser,
		})
	}

	return resp, nil
}
