package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductCategoriesLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetProductCategoriesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductCategoriesLogic {
	return &GetProductCategoriesLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取商品分类列表
func (l *GetProductCategoriesLogic) GetProductCategories(in *pb.EmptyRequest) (*pb.GetProductCategoriesResp, error) {
	// 2. 查询所有分类
	var categories []model.ShopProductsCategory
	err := l.svcCtx.AppDB.Where("f_cat_id=0").Order("`order` DESC").Find(&categories).Error
	if err != nil {
		l.Logger.Errorf("查询商品分类失败: %v", err)
		return nil, err
	}

	// 3. 检查是否查询到数据
	if len(categories) == 0 {
		l.Logger.Info("未查询到商品分类数据")
		return &pb.GetProductCategoriesResp{
			Categories: []*pb.ProductCategoryInfo{},
		}, nil
	}

	// 4. 转换为响应格式
	respCategories := make([]*pb.ProductCategoryInfo, 0, len(categories))
	for _, category := range categories {
		respCategories = append(respCategories, &pb.ProductCategoryInfo{
			Id:     uint64(category.ID),
			CatId:  uint64(category.CatID),
			FCatId: uint64(category.FCatID),
			Name:   category.Name,
			Level:  int32(category.Level),
		})
	}

	// 5. 记录日志
	l.Logger.Infof("成功获取商品分类列表，共 %d 条记录", len(respCategories))

	return &pb.GetProductCategoriesResp{
		Categories: respCategories,
	}, nil
}
