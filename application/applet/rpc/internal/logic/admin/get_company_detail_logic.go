package adminlogic

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCompanyDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyDetailLogic {
	return &GetCompanyDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetCompanyDetailLogic) GetCompanyDetail(in *pb.GetCompanyDetailRequest) (*pb.CompanyDetailResponse, error) {
	//var company model.CompanyInfo
	//if err := l.svcCtx.AppDB.Where("id = ?", in.Id).First(&company).Error; err != nil {
	//	return nil, err
	//}
	//return &pb.CompanyDetailResponse{
	//	Id:          company.ID,
	//	UserId:      company.UserID,
	//	Name:        company.Name,
	//	Address:     company.Address,
	//	BankAccount: company.BankAccount,
	//	BankName:    company.BankName,
	//	TaxNumber:   company.TaxNumber,
	//	Phone:       company.Phone,
	//	LicenseImg:  company.LicenseImg,
	//	Status:      int32(company.Status),
	//	CreatedAt:   company.CreatedAt.Format("2006-01-02 15:04:05"),
	//	UpdatedAt:   company.UpdatedAt.Format("2006-01-02 15:04:05"),
	//	Remark:      company.Remark,
	//}, nil

	var company model.CompanyInfo
	// 通过 Preload 自动加载用户信息
	if err := l.svcCtx.AppDB.Preload("User").First(&company, in.Id).Error; err != nil {
		return nil, err
	}

	// 组装用户信息
	userDetail := &pb.XjUser{}
	_ = copier.Copy(userDetail, &company.User)

	// 组装公司详情
	resp := &pb.CompanyDetailResponse{}
	resp.CreatedAt = company.CreatedAt.Format("2006-01-02 15:04:05")
	resp.UpdatedAt = company.UpdatedAt.Format("2006-01-02 15:04:05")

	_ = copier.Copy(resp, &company)
	resp.User = userDetail

	return resp, nil
}
