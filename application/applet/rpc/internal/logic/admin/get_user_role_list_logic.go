package adminlogic

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserRoleListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserRoleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserRoleListLogic {
	return &GetUserRoleListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户角色列表
func (l *GetUserRoleListLogic) GetUserRoleList(in *pb.EmptyRequest) (*pb.UserRoleListResp, error) {
	var roles []model.UserRole
	if err := l.svcCtx.AppDB.Model(&model.UserRole{}).Find(&roles).Error; err != nil {
		return nil, err
	}

	var list []*pb.UserRoleItem
	for _, r := range roles {
		item := &pb.UserRoleItem{}
		_ = copier.Copy(item, &r)
		// 时间格式化
		item.CreatedAt = r.CreatedAt.Format("2006-01-02 15:04:05")
		item.UpdatedAt = r.UpdatedAt.Format("2006-01-02 15:04:05")
		list = append(list, item)
	}

	return &pb.UserRoleListResp{
		List: list,
	}, nil
}
