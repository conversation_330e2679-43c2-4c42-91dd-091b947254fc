package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetProductTopLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetProductTopLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetProductTopLogic {
	return &SetProductTopLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置商品置顶
func (l *SetProductTopLogic) SetProductTop(in *pb.SetProductTopReq) (*pb.BoolResp, error) {
	// 1. 参数验证
	if in.ProductId <= 0 {
		return &pb.BoolResp{Success: false}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 2. 查询当前最大order值
	var maxOrder int64
	err := l.svcCtx.AppDB.Model(&model.TempTalentProduct{}).
		Select("COALESCE(MAX(`order`), 0)").
		Scan(&maxOrder).Error
	if err != nil {
		return nil, xerr.NewErrMsg(l.ctx, "查询最大排序权重失败")
	}

	// 3. 更新产品order值
	result := l.svcCtx.AppDB.Model(&model.TempTalentProduct{}).
		Where("product_id = ?", in.ProductId).
		Update("order", maxOrder+1)

	if result.Error != nil {
		return nil, xerr.NewErrMsg(l.ctx, "更新置顶排序失败")
	}

	if result.RowsAffected == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "商品不存在")
	}

	return &pb.BoolResp{Success: true}, nil
}
