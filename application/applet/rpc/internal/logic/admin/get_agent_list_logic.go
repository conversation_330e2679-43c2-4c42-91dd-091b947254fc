package adminlogic

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"
)

type GetAgentListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAgentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAgentListLogic {
	return &GetAgentListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取代理商列表
func (l *GetAgentListLogic) GetAgentList(in *pb.GetAgentListRequest) (*pb.GetAgentListResponse, error) {
	// 参数处理
	pageNo := in.Page.PageNo
	pageSize := in.Page.PageSize
	if pageNo < 1 {
		pageNo = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}

	// 构建基础查询 - 先只查询用户表获取代理商列表
	baseQuery := l.svcCtx.AppDB.Table("user_user AS u").
		Select("u.id, u.nick_name as nickname, u.mobile, u.created_at").
		Where("u.role_id = ?", 3) // 只查询代理商角色

	// 添加用户表相关筛选条件
	if in.Name != "" {
		baseQuery = baseQuery.Where("u.nick_name LIKE ?", "%"+in.Name+"%")
	}
	if in.Mobile != "" {
		baseQuery = baseQuery.Where("u.mobile LIKE ?", "%"+in.Mobile+"%")
	}

	if in.StartTime != "" {
		baseQuery = baseQuery.Where("u.created_at >= ?", in.StartTime)
	}
	if in.EndTime != "" {
		baseQuery = baseQuery.Where("u.created_at <= ?", in.EndTime)
	}

	// 如果有公司名称筛选，添加JOIN和条件
	if in.CompanyName != "" {
		baseQuery = baseQuery.Joins("JOIN company_info AS ci ON u.id = ci.user_id").
			Where("ci.name LIKE ?", "%"+in.CompanyName+"%")
	}

	// 如果有关联运营商公司名称筛选，添加JOIN和条件
	if in.RelatedCompany != "" {
		baseQuery = baseQuery.Joins("JOIN user_user AS invite_user ON u.invite_from = invite_user.id").
			Joins("JOIN company_info AS rc ON invite_user.id = rc.user_id").
			Where("rc.name LIKE ?", "%"+in.RelatedCompany+"%")
	}

	// 计算总数
	var total int64
	if err := baseQuery.Count(&total).Error; err != nil {
		l.Logger.Errorf("Count agents error: %v", err)
		return nil, err
	}

	// 没有数据直接返回
	if total == 0 {
		return &pb.GetAgentListResponse{
			Agents:   make([]*pb.AgentInfo, 0),
			Total:    0,
			PageNo:   pageNo,
			PageSize: pageSize,
		}, nil
	}

	// 查询代理商基本信息
	type AgentBasic struct {
		Id        uint64    `json:"id"`
		Nickname  string    `json:"nickname"`
		Mobile    string    `json:"mobile"`
		Status    int32     `json:"status"`
		CreatedAt time.Time `json:"create_at"`
	}

	var agentsBasic []AgentBasic
	err := baseQuery.Limit(int(pageSize)).
		Offset(int((pageNo - 1) * pageSize)).
		Find(&agentsBasic).Error

	if err != nil {
		l.Logger.Errorf("Find agents error: %v", err)
		return nil, err
	}

	// 构造响应数据
	result := &pb.GetAgentListResponse{
		Agents:   make([]*pb.AgentInfo, 0, len(agentsBasic)),
		Total:    total,
		PageNo:   pageNo,
		PageSize: pageSize,
	}

	// 如果没有代理商，直接返回
	if len(agentsBasic) == 0 {
		return result, nil
	}

	// 提取所有代理商ID
	var agentIds []uint64
	for _, agent := range agentsBasic {
		agentIds = append(agentIds, agent.Id)
	}

	// 查询套餐数量
	type InventoryData struct {
		UserId   uint64 `json:"user_id"`
		Quantity uint   `json:"quantity"`
	}
	var inventories []InventoryData
	if err := l.svcCtx.AppDB.Table("user_inventory").
		Select("user_id, quantity").
		Where("user_id IN ? AND product_id = ?", agentIds, 1).
		Find(&inventories).Error; err != nil {
		l.Logger.Errorf("Find inventories error: %v", err)
		// 继续执行，不返回错误
	}

	// 创建套餐数量映射
	inventoryMap := make(map[uint64]uint)
	for _, inv := range inventories {
		inventoryMap[inv.UserId] = inv.Quantity
	}

	// 查询公司信息
	type CompanyData struct {
		UserId uint64 `json:"user_id"`
		Name   string `json:"name"`
	}
	var companies []CompanyData
	if err := l.svcCtx.AppDB.Table("company_info").
		Select("user_id, name").
		Where("user_id IN ?", agentIds).
		Find(&companies).Error; err != nil {
		l.Logger.Errorf("Find companies error: %v", err)
		// 继续执行，不返回错误
	}

	// 创建公司名称映射
	companyMap := make(map[uint64]string)
	for _, company := range companies {
		companyMap[company.UserId] = company.Name
	}

	// 查询邀请人ID
	type InviteData struct {
		Id         uint64 `json:"id"`
		InviteFrom uint64 `json:"invite_from"`
	}
	var inviteData []InviteData
	if err := l.svcCtx.AppDB.Table("user_user").
		Select("id, invite_from").
		Where("id IN ?", agentIds).
		Find(&inviteData).Error; err != nil {
		l.Logger.Errorf("Find invite data error: %v", err)
		// 继续执行，不返回错误
	}

	// 创建邀请人ID映射
	inviteMap := make(map[uint64]uint64)
	var inviterIds []uint64
	for _, invite := range inviteData {
		if invite.InviteFrom > 0 {
			inviteMap[invite.Id] = invite.InviteFrom
			inviterIds = append(inviterIds, invite.InviteFrom)
		}
	}

	// 查询邀请人公司信息
	var inviterCompanies []CompanyData
	if len(inviterIds) > 0 {
		if err := l.svcCtx.AppDB.Table("company_info").
			Select("user_id, name").
			Where("user_id IN ?", inviterIds).
			Find(&inviterCompanies).Error; err != nil {
			l.Logger.Errorf("Find inviter companies error: %v", err)
			// 继续执行，不返回错误
		}
	}

	// 创建邀请人公司名称映射
	inviterCompanyMap := make(map[uint64]string)
	for _, company := range inviterCompanies {
		inviterCompanyMap[company.UserId] = company.Name
	}

	// 查询任务奖励池
	type BenefitsPoolData struct {
		UserId   uint64 `json:"user_id"`
		Quantity int32  `json:"quantity"`
	}
	var benefitsPools []BenefitsPoolData
	if err := l.svcCtx.AppDB.Table("user_benefits_pool").
		Select("user_id, quantity").
		Where("user_id IN ? AND type = ?", agentIds, 4).
		Find(&benefitsPools).Error; err != nil {
		l.Logger.Errorf("Find benefits pools error: %v", err)
		// 继续执行，不返回错误
	}

	// 创建任务奖励池映射
	benefitsPoolMap := make(map[uint64]int32)
	for _, pool := range benefitsPools {
		benefitsPoolMap[pool.UserId] = pool.Quantity
	}

	// 构建最终响应
	for _, agent := range agentsBasic {
		// 获取套餐数量
		packageCount := int32(inventoryMap[agent.Id])

		// 获取公司名称
		companyName := companyMap[agent.Id]

		// 获取关联运营商公司名称
		var relatedCompanyName string
		if inviterId, exists := inviteMap[agent.Id]; exists {
			relatedCompanyName = inviterCompanyMap[inviterId]
		}

		// 获取任务奖励次数
		taskRewardCount := benefitsPoolMap[agent.Id]

		// 添加到结果列表
		result.Agents = append(result.Agents, &pb.AgentInfo{
			Id:              agent.Id,
			Nickname:        agent.Nickname,
			Mobile:          agent.Mobile,
			PackageCount:    packageCount,
			CompanyName:     companyName,
			RelatedCompany:  relatedCompanyName,
			TaskRewardCount: taskRewardCount,
			CreatedAt:       agent.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return result, nil
}
