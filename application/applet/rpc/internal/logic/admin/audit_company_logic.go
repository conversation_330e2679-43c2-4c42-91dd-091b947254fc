package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditCompanyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAuditCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditCompanyLogic {
	return &AuditCompanyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 审核公司
func (l *AuditCompanyLogic) AuditCompany(in *pb.AuditCompanyRequest) (*pb.NoDataResponse, error) {
	update := map[string]interface{}{
		"status": in.Status,
		"remark": in.Remark,
	}
	if err := l.svcCtx.AppDB.Model(&model.CompanyInfo{}).Where("id = ?", in.Id).Updates(update).Error; err != nil {
		l.Errorf("公司审核失败: %v", err)
		return nil, err
	}
	return &pb.NoDataResponse{}, nil
}
