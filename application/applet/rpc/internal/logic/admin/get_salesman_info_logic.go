package adminlogic

import (
	"context"
	"gorm.io/gorm"
	"time"
	"xj-serv/application/applet/rpc/internal/config"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSalesmanInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSalesmanInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSalesmanInfoLogic {
	return &GetSalesmanInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取员工信息
func (l *GetSalesmanInfoLogic) GetSalesmanInfo(in *pb.GetSalesmanInfoReq) (*pb.GetSalesmanInfoResp, error) {
	l.Infof("获取代理商ID为%d的员工ID为%d的信息", in.AgentUserId, in.SalesmanUserId)

	// 参数校验
	if in.SalesmanUserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证代理商身份
	var agentUser model.User
	if err := l.svcCtx.AppDB.Where("id = ?", in.AgentUserId).First(&agentUser).Error; err != nil {
		l.Errorf("查询代理商信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询代理商信息失败")
	}
	if agentUser.RoleId != config.RoleAgent {
		l.Errorf("用户ID %d 不是代理商角色，无法获取员工信息", in.AgentUserId)
		return nil, xerr.NewErrMsg(l.ctx, "只有代理商可以获取员工信息")
	}

	// 验证业务员身份
	var salesmanUser model.User
	if err := l.svcCtx.AppDB.Where("id = ?", in.SalesmanUserId).First(&salesmanUser).Error; err != nil {
		l.Errorf("查询业务员信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询业务员信息失败")
	}
	if salesmanUser.RoleId != config.RoleSalesman {
		l.Errorf("用户ID %d 不是业务员角色", in.SalesmanUserId)
		return nil, xerr.NewErrMsg(l.ctx, "该用户不是业务员")
	}

	// 查询代理商与业务员的关系
	var agentRelation model.AgentRelation
	if err := l.svcCtx.AppDB.Where("agent_user_id = ? AND salesman_user_id = ?", in.AgentUserId, in.SalesmanUserId).First(&agentRelation).Error; err != nil {
		l.Errorf("查询代理商与业务员关系失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "该业务员不属于您的团队")
	}

	// 初始化响应
	resp := &pb.GetSalesmanInfoResp{
		SalesmanUserId:     in.SalesmanUserId,
		NickName:           salesmanUser.NickName,
		Avatar:             salesmanUser.Avatar,
		Mobile:             salesmanUser.Mobile,
		SalesmanRate:       float64(agentRelation.SalesmanRate),
		SalesmanRemark:     agentRelation.SalesmanRemark,
		IsFullTime:         agentRelation.IsFullTime,
		CreatedAt:          agentRelation.CreatedAt.Format(time.DateTime),
		TotalCommission:    0,
		SalesmanCommission: 0,
		TodayOrderCount:    0,
		TodayOrderAmount:   0,
		AiPointTotal:       0,
		TeamPackageCount:   0,
	}

	// 查询团长套餐数量（从user_inventory表中获取）
	var teamPackage model.UserInventory
	if err := l.svcCtx.AppDB.Where("user_id = ? AND product_id = ?", in.SalesmanUserId, 1).First(&teamPackage).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			l.Errorf("查询团长套餐数量失败: %v", err)
			// 不返回错误，继续执行
		}
		// 如果记录不存在，TeamPackageCount保持为0
	} else {
		// 记录存在，设置团长套餐数量
		resp.TeamPackageCount = int64(teamPackage.Quantity)
	}

	// 获取今天的开始时间
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)

	// 获取业务员佣金汇总
	var salesmanCommission float64
	if err := l.svcCtx.AppDB.Model(&model.AgentCommissionDetail{}).
		Where("salesman_user_id = ?", in.SalesmanUserId).
		Select("COALESCE(SUM(salesman_amount), 0)").
		Scan(&salesmanCommission).Error; err != nil {
		l.Errorf("计算业务员佣金汇总失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "计算业务员佣金汇总失败")
	}
	resp.SalesmanCommission = float64(salesmanCommission) / 100

	// 查找所有下级用户的 sharer_appid
	sharerAppids, err := l.findAllDownlineSharerAppids(l.svcCtx.AppDB, in.SalesmanUserId)
	if err != nil {
		l.Errorf("查找下级用户失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查找下级用户失败")
	}

	// 如果有下级用户，计算今日订单数量和总额
	if len(sharerAppids) > 0 {
		var todayOrderCount int64
		var todayOrderAmount float64
		if err := l.svcCtx.AppDB.Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Count(&todayOrderCount).Error; err != nil {
			l.Errorf("计算今日订单数量失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单数量失败")
		}
		resp.TodayOrderCount = todayOrderCount

		if err := l.svcCtx.AppDB.Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Select("COALESCE(SUM(actual_payment), 0)").
			Scan(&todayOrderAmount).Error; err != nil {
			l.Errorf("计算今日订单总额失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单总额失败")
		}
		resp.TodayOrderAmount = float64(todayOrderAmount) / 100
	}

	return resp, nil
}

// 递归查找所有用户的 sharer_appid
func (l *GetSalesmanInfoLogic) findAllDownlineSharerAppids(db *orm.DB, userId uint64) ([]string, error) {
	// 使用 WITH RECURSIVE 递归查询所有用户
	query := `WITH RECURSIVE user_hierarchy AS (
        -- 初始查询：获取当前用户
        SELECT id, invite_from, sharer_appid
        FROM user_user
        WHERE id = ?
        
        UNION ALL
        
        -- 递归查询：获取所有下级用户
        SELECT u.id, u.invite_from, u.sharer_appid
        FROM user_user u
        INNER JOIN user_hierarchy h ON u.invite_from = h.id
    )
    -- 获取所有有效的 sharer_appid
    SELECT sharer_appid
    FROM user_hierarchy
    WHERE sharer_appid IS NOT NULL AND sharer_appid != '';`

	var sharerAppids []string
	if err := db.Raw(query, userId).Scan(&sharerAppids).Error; err != nil {
		return nil, err
	}

	return sharerAppids, nil
}
