package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateCompanyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateCompanyLogic {
	return &UpdateCompanyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修改公司
func (l *UpdateCompanyLogic) UpdateCompany(in *pb.UpdateCompanyRequest) (*pb.NoDataResponse, error) {
	update := map[string]interface{}{
		"name":         in.Name,
		"address":      in.Address,
		"bank_account": in.BankAccount,
		"bank_name":    in.BankName,
		"tax_number":   in.<PERSON>N<PERSON>ber,
		"leader":       in.Leader,
		"phone":        in.Phone,
		"license_img":  in.LicenseImg,
	}
	if err := l.svcCtx.AppDB.Model(&model.CompanyInfo{}).Where("id = ?", in.Id).Updates(update).Error; err != nil {
		l.Errorf("公司信息修改失败: %v", err)
		return nil, err
	}
	return &pb.NoDataResponse{}, nil
}
