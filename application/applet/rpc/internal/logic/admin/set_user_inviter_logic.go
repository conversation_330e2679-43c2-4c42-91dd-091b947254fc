package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserInviterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetUserInviterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserInviterLogic {
	return &SetUserInviterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置用户上级
func (l *SetUserInviterLogic) SetUserInviter(in *pb.SetUserInviterReq) (*pb.BoolResp, error) {
	// 校验用户是否存在
	var user model.User
	if err := l.svcCtx.AppDB.First(&user, in.UserId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 校验邀请人是否存在
	var inviter model.User
	if err := l.svcCtx.AppDB.First(&inviter, in.InviterId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 更新用户的invite_from字段
	if err := l.svcCtx.AppDB.Model(&user).Update("invite_from", in.InviterId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	return &pb.BoolResp{Success: true}, nil
}
