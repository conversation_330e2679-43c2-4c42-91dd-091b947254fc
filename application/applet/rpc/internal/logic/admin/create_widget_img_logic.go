package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateWidgetImgLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateWidgetImgLogic {
	return &CreateWidgetImgLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 添加图片组件
func (l *CreateWidgetImgLogic) CreateWidgetImg(in *pb.CreateWidgetImgReq) (*pb.BoolResp, error) {
	// 创建图片组件记录
	widgetImg := &model.WidgetImg{
		Title:   in.Title,
		Status:  int8(in.Status),
		Img:     in.Img,
		Link:    in.Link,
		Sort:    int(in.Sort),
		Type:    int8(in.Type),
		AdminID: int(in.AdminId),
	}
	// 保存到数据库
	if err := l.svcCtx.AppDB.Create(widgetImg).Error; err != nil {
		l.Errorf("添加图片组件失败: %v", err)
		return nil, err
	}
	return &pb.BoolResp{Success: true}, nil
}
