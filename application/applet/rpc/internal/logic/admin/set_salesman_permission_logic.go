package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetSalesmanPermissionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetSalesmanPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSalesmanPermissionLogic {
	return &SetSalesmanPermissionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置业务员权限
func (l *SetSalesmanPermissionLogic) SetSalesmanPermission(in *pb.SetSalesmanPermissionReq) (*pb.BoolResp, error) {
	// / 参数验证

	if in.SalesmanUserId <= 0 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "业务员ID不能为空")
	}

	if in.SalesmanRate < 0 || in.SalesmanRate > 100 {
		return &pb.BoolResp{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "佣金比例必须在0-100之间")
	}

	// 查询是否已存在关联关系
	var existingRelation model.AgentRelation

	err := l.svcCtx.AppDB.Where("salesman_user_id = ? AND deleted_at IS NULL",
		in.SalesmanUserId).First(&existingRelation).Error

	// 如果已存在关联关系，则更新
	if err == nil {
		// 更新现有记录
		existingRelation.SalesmanRate = in.SalesmanRate
		existingRelation.SalesmanRemark = in.SalesmanRemark
		existingRelation.IsFullTime = in.IsFullTime

		if err := l.svcCtx.AppDB.Save(&existingRelation).Error; err != nil {
			l.Errorf("更新业务员权限失败: %v", err)
			return &pb.BoolResp{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "更新业务员权限失败")
		}

		return &pb.BoolResp{
			Success: true,
		}, nil
	}

	return &pb.BoolResp{
		Success: false,
	}, err
}
