package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserRoleLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetUserRoleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserRoleLogic {
	return &SetUserRoleLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 修改用户角色
func (l *SetUserRoleLogic) SetUserRole(in *pb.SetUserRoleReq) (*pb.NoDataResponse, error) {
	// 更新用户角色
	err := l.svcCtx.AppDB.Model(&model.User{}).
		Where("id = ?", in.UserId).
		Update("role_id", in.RoleId).Error
	if err != nil {
		return nil, err
	}

	return &pb.NoDataResponse{}, nil
}
