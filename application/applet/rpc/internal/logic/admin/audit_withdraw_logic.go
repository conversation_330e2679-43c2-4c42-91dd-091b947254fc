package adminlogic

import (
	"context"
	"time"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuditWithdrawLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAuditWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuditWithdrawLogic {
	return &AuditWithdrawLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 审核提现申请
func (l *AuditWithdrawLogic) AuditWithdraw(in *pb.AuditWithdrawReq) (*pb.BoolResp, error) {
	update := map[string]interface{}{
		"id":            in.Id,
		"review_status": in.ReviewStatus,
		"fail_reason":   in.FailReason,
		"review_time":   time.Now(),
	}
	if err := l.svcCtx.AppDB.Model(&model.WithdrawRecord{}).Where("id = ?", in.Id).Updates(update).Error; err != nil {
		l.Errorf("提现审核失败: %v", err)
		return nil, err
	}
	return &pb.BoolResp{Success: true}, nil
}
