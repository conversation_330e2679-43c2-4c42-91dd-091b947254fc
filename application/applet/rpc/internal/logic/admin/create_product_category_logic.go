package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateProductCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateProductCategoryLogic {
	return &CreateProductCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 添加商品分类
func (l *CreateProductCategoryLogic) CreateProductCategory(in *pb.CreateCategoryRequest) (*pb.BoolResp, error) {
	category := &model.TalentProductsCategory{
		Name:  in.Name,
		Img:   in.Img,
		Order: uint(in.Order),
		FID:   uint(in.FId),
	}
	if in.FId > 0 {
		var parentCategory model.TalentProductsCategory
		err := l.svcCtx.AppDB.Where("id = ?", in.FId).First(&parentCategory).Error
		if err != nil {
			l.Errorf("Failed to check parent category existence: %v", err)
			return &pb.BoolResp{Success: false}, err
		}
	}
	if err := l.svcCtx.AppDB.Create(category).Error; err != nil {
		l.Errorf("Create category failed: %v", err)
		return &pb.BoolResp{Success: false}, err
	}

	return &pb.BoolResp{Success: true}, nil
}
