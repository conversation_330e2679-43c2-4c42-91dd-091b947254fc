package adminlogic

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteProductCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteProductCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteProductCategoryLogic {
	return &DeleteProductCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除商品分类
func (l *DeleteProductCategoryLogic) DeleteProductCategory(in *pb.DeleteCategoryRequest) (*pb.BoolResp, error) {
	// todo: add your logic here and delete this line
	// 1. 检查商品分类是否存在
	var category model.TalentProductsCategory
	result := l.svcCtx.AppDB.Where("id = ?", in.Id).First(&category)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			l.Errorf("Delete category failed: category with ID %d not found.", in.Id)
			// 返回分类不存在的明确错误信息
			return &pb.BoolResp{Success: false}, fmt.Errorf("商品分类 ID %d 不存在", in.Id)
		}
		// 其他数据库错误
		l.Errorf("Failed to retrieve category with ID %d for deletion: %v", in.Id, result.Error)
		return &pb.BoolResp{Success: false}, result.Error
	}

	// 2. 执行软删除
	// GORM 会自动更新 'deleted_at' 字段
	if err := l.svcCtx.AppDB.Delete(&category).Error; err != nil {
		l.Errorf("Failed to soft delete category with ID %d: %v", in.Id, err)
		return &pb.BoolResp{Success: false}, err
	}

	l.Infof("Successfully soft deleted category with ID %d", in.Id)
	return &pb.BoolResp{Success: true}, nil
}
