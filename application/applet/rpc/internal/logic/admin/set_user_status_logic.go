package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetUserStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetUserStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetUserStatusLogic {
	return &SetUserStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 冻结、解冻用户
func (l *SetUserStatusLogic) SetUserStatus(in *pb.SetUserStatusReq) (*pb.BoolResp, error) {
	// 校验用户是否存在
	var user model.User
	if err := l.svcCtx.AppDB.First(&user, in.UserId).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	// 更新用户状态
	if err := l.svcCtx.AppDB.Model(&user).Update("status", in.Status).Error; err != nil {
		return &pb.BoolResp{Success: false}, err
	}

	return &pb.BoolResp{Success: true}, nil
}
