package adminlogic

import (
	"context"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateCompanyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateCompanyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateCompanyLogic {
	return &CreateCompanyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建公司
func (l *CreateCompanyLogic) CreateCompany(in *pb.CreateCompanyRequest) (*pb.NoDataResponse, error) {
	company := model.CompanyInfo{
		UserID:      in.UserId,
		Name:        in.Name,
		Address:     in.Address,
		BankAccount: in.BankAccount,
		BankName:    in.BankName,
		TaxNumber:   in.TaxNumber,
		Leader:      in.Leader,
		Phone:       in.Phone,
		LicenseImg:  in.LicenseImg,
	}
	if err := l.svcCtx.AppDB.Create(&company).Error; err != nil {
		l.Errorf("新建公司失败: %v", err)
		return nil, err
	}
	return &pb.NoDataResponse{}, nil
}
