package adminlogic

import (
	"context"
	"fmt"
	"time"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetBenefitsPoolLogListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetBenefitsPoolLogListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetBenefitsPoolLogListLogic {
	return &GetBenefitsPoolLogListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取任务池充值记录列表
func (l *GetBenefitsPoolLogListLogic) GetBenefitsPoolLogList(in *pb.GetBenefitsPoolLogListReq) (*pb.GetBenefitsPoolLogListResp, error) {
	l.Infof("开始获取任务池充值记录列表, mobile=%s", in.Mobile)

	// 构建查询
	db := l.svcCtx.AppDB.Table("user_benefits_pool_log log").
		Select("log.*, u.phone as user_phone, u.nick_name as user_nickname, bu.phone as benefits_user_phone, bu.nick_name as benefits_user_nickname").
		Joins("LEFT JOIN user u ON log.user_id = u.id").
		Joins("LEFT JOIN user bu ON log.benefits_user_id = bu.id").
		Where("log.deleted_at IS NULL")

	// 添加查询条件
	if in.Mobile != "" {
		db = db.Where("u.phone LIKE ? OR bu.phone LIKE ?", "%"+in.Mobile+"%", "%"+in.Mobile+"%")
	}

	if in.StartTime != "" && in.EndTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("log.created_at BETWEEN ? AND ?", startTime, endTime)
	} else if in.StartTime != "" {
		startTime, _ := time.Parse("2006-01-02", in.StartTime)
		db = db.Where("log.created_at >= ?", startTime)
	} else if in.EndTime != "" {
		endTime, _ := time.Parse("2006-01-02", in.EndTime)
		endTime = endTime.Add(24 * time.Hour) // 包含结束当天
		db = db.Where("log.created_at <= ?", endTime)
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算任务池充值记录总数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("计算任务池充值记录总数失败: %v", err))
	}

	// 分页查询
	type Result struct {
		model.UserBenefitsPoolLog
		UserPhone            string `gorm:"column:user_phone"`
		UserNickname         string `gorm:"column:user_nickname"`
		BenefitsUserPhone    string `gorm:"column:benefits_user_phone"`
		BenefitsUserNickname string `gorm:"column:benefits_user_nickname"`
	}

	var results []Result
	offset := int((in.PageRequest.PageNo - 1) * in.PageRequest.PageSize)
	limit := int(in.PageRequest.PageSize)

	if err := db.Order("log.id DESC").Offset(offset).Limit(limit).Find(&results).Error; err != nil {
		l.Errorf("查询任务池充值记录列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询任务池充值记录列表失败: %v", err))
	}

	// 构建响应
	resp := &pb.GetBenefitsPoolLogListResp{
		List:     make([]*pb.BenefitsPoolLogInfo, 0, len(results)),
		Total:    total,
		PageNo:   uint64(in.PageRequest.PageNo),
		PageSize: uint64(in.PageRequest.PageSize),
	}

	// 转换数据
	for _, result := range results {
		logInfo := &pb.BenefitsPoolLogInfo{
			Id:             uint64(result.ID),
			BenefitsUserId: result.BenefitsUserId,
			UserId:         result.UserID,
			TaskType:       result.TaskType,
			CreatedAt:      result.CreatedAt.Format("2006-01-02 15:04:05"),
			UserInfo: &pb.XjUser{
				Id:       result.UserID,
				NickName: result.UserNickname,
				Mobile:   result.UserPhone,
			},
			BenefitsUserInfo: &pb.XjUser{
				Id:       result.BenefitsUserId,
				NickName: result.BenefitsUserNickname,
				Mobile:   result.BenefitsUserPhone,
			},
		}

		resp.List = append(resp.List, logInfo)
	}

	return resp, nil
}
