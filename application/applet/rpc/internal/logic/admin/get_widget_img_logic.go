package adminlogic

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWidgetImgLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetWidgetImgLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWidgetImgLogic {
	return &GetWidgetImgLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 查看图片组件
func (l *GetWidgetImgLogic) GetWidgetImg(in *pb.GetWidgetImgReq) (*pb.WidgetImgInfo, error) {
	var widgetImg model.WidgetImg
	if err := l.svcCtx.AppDB.First(&widgetImg, in.Id).Error; err != nil {
		return nil, err
	}
	// 组装详情
	resp := &pb.WidgetImgInfo{}
	_ = copier.Copy(resp, &widgetImg)
	// 格式化时间
	createdAtStr := widgetImg.CreatedAt.Format("2006-01-02 15:04:05")
	updatedAtStr := widgetImg.UpdatedAt.Format("2006-01-02 15:04:05")
	resp.CreatedAt = createdAtStr
	resp.UpdatedAt = updatedAtStr
	return resp, nil
}
