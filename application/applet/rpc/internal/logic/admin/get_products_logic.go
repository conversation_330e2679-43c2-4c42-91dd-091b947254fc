package adminlogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"xj-serv/application/applet/rpc/internal/model"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductsLogic {
	return &GetProductsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取商品列表
func (l *GetProductsLogic) GetProducts(in *pb.GetProductsReq) (*pb.GetProductsResp, error) {
	// 构建查询
	db := l.svcCtx.AppDB.Model(&model.TempTalentProduct{})

	// 根据分类ID筛选
	if in.CategoryId > 0 {
		db = db.Where("cat1 = ? OR cat2 = ? OR cat3 = ?", in.CategoryId, in.CategoryId, in.CategoryId)
	}

	// 添加查询条件
	if in.Title != "" {
		db = db.Where("title LIKE ? OR product_id LIKE ?", "%"+in.Title+"%", "%"+in.Title+"%")
	}
	if in.IsPreferred > -1 {
		db = db.Where("is_preferred = ?", in.IsPreferred)
	}

	if in.CCat1 > 0 {
		db = db.Where("c_cat1 = ?", in.CCat1)
	}
	if in.CCat2 > 0 {
		db = db.Where("c_cat2 = ?", in.CCat2)
	}

	if in.IsRecommend > -1 {
		db = db.Where("is_recommend = ?", in.IsRecommend)
	}
	if in.IsWindow > -1 {
		db = db.Where("is_window = ?", in.IsWindow)
	}

	if in.MinPrice > 0 && in.MaxPrice > 0 {
		db = db.Where("min_price >= ? AND min_price <= ?", in.MinPrice*100, in.MaxPrice*100)
	} else if in.MinPrice > 0 {
		db = db.Where("min_price >= ?", in.MinPrice*100)
	} else if in.MaxPrice > 0 {
		db = db.Where("min_price <= ?", in.MaxPrice*100)
	}

	if in.MinCommissionAmount > 0 && in.MaxCommissionAmount > 0 {
		db = db.Where("commission_amount >= ? AND commission_amount <= ?", in.MinCommissionAmount*100, in.MaxCommissionAmount*100)
	} else if in.MinCommissionAmount > 0 {
		db = db.Where("commission_amount >= ?", in.MinCommissionAmount*100)
	} else if in.MaxCommissionAmount > 0 {
		db = db.Where("commission_amount <= ?", in.MaxCommissionAmount*100)
	}

	db = db.Where("on_sale = 1")
	db = db.Where("product_status = 5")
	db = db.Where("`show` = 1")

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("计算商品总数失败: %v", err)
		return nil, err
	}

	// 分页查询
	var products []model.TempTalentProduct
	pageNo := int(in.PageRequest.PageNo)
	if pageNo <= 0 {
		pageNo = 1
	}
	pageSize := int(in.PageRequest.PageSize)
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (pageNo - 1) * pageSize

	if in.SortBy != "" {
		orderStr := buildOrderClause(in.SortBy, in.Order)
		db = db.Order(orderStr)
	} else {
		db = db.Order("`order` DESC, id DESC")
	}

	if err := db.
		Offset(offset).
		Limit(pageSize).
		Find(&products).Error; err != nil {
		l.Errorf("查询商品列表失败: %v", err)
		return nil, err
	}

	// 获取分类信息，用于展示分类名称
	var categories []model.ShopProductsCategory
	if err := l.svcCtx.AppDB.Find(&categories).Error; err != nil {
		l.Errorf("获取分类信息失败: %v", err)
		return nil, err
	}

	// 构建分类ID到名称的映射
	categoryMap := make(map[uint]string)
	for _, category := range categories {
		categoryMap[uint(category.ID)] = category.Name
	}

	// 构建响应
	resp := &pb.GetProductsResp{
		Products: make([]*pb.ProductInfo, 0, len(products)),
		Total:    total,
		PageNo:   int32(pageNo),
		PageSize: int32(pageSize),
	}

	// 转换数据
	for _, product := range products {
		// 获取服务费率
		var serviceRate int32
		if product.CommissionInfo != "" {
			var commissionInfoMap map[string]interface{}
			if err := json.Unmarshal([]byte(product.CommissionInfo), &commissionInfoMap); err == nil {
				if rate, ok := commissionInfoMap["service_ratio"]; ok {
					if rateFloat, ok := rate.(float64); ok {
						serviceRate = int32(rateFloat / 10000)
					}
				}
			}
		}

		// 获取分类名称
		var categoryName string
		if product.Cat1 > 0 && categoryMap[uint(product.Cat1)] != "" {
			categoryName = categoryMap[uint(product.Cat1)]
		} else if product.Cat2 > 0 && categoryMap[uint(product.Cat2)] != "" {
			categoryName = categoryMap[uint(product.Cat2)]
		} else if product.Cat3 > 0 && categoryMap[uint(product.Cat3)] != "" {
			categoryName = categoryMap[uint(product.Cat3)]
		}

		productInfo := &pb.ProductInfo{
			Id:               uint64(product.ID),
			ProductId:        uint64(product.ProductId),
			ShopAppid:        product.ShopAppid,
			Title:            product.Title,
			SubTitle:         product.SubTitle,
			ServiceRatio:     serviceRate,
			MinPrice:         float64(product.MinPrice / 100),
			CommissionAmount: float64(product.CommissionAmount / 100),
			IsPreferred:      product.IsPreferred,
			IsRecommend:      product.IsRecommend,
			IsWindow:         product.IsWindow,
			Order:            uint64(product.Order),
			Cat1:             uint32(product.Cat1),
			Cat2:             uint32(product.Cat2),
			Cat3:             uint32(product.Cat3),
			CCat1:            uint32(product.CCat1),
			CCat2:            uint32(product.CCat2),
			CategoryName:     categoryName,
			HeadImg:          product.HeadImgs,
		}

		resp.Products = append(resp.Products, productInfo)
	}

	return resp, nil
}
func buildOrderClause(sortBy, order string) string {
	// 1. 定义排序字段映射
	sortFieldMap := map[string]string{
		"price":      "min_price",
		"commission": "commission_amount",
		"created_at": "created_at",
		"order":      "order",
	}

	// 2. 获取数据库字段名
	dbField, exists := sortFieldMap[sortBy]
	if !exists {
		// 如果排序字段不存在，返回默认排序
		return "`order` DESC, created_at DESC"
	}

	// 3. 验证排序方向
	orderDirection := "DESC"
	if strings.ToLower(order) == "asc" {
		orderDirection = "ASC"
	}

	// 4. 构建排序子句
	return fmt.Sprintf("%s %s", dbField, orderDirection)
}
