package adminlogic

import (
	"context"
	"time"
	"xj-serv/application/applet/rpc/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWithdrawRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetWithdrawRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWithdrawRecordListLogic {
	return &GetWithdrawRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取提现记录列表
func (l *GetWithdrawRecordListLogic) GetWithdrawRecordList(in *pb.WithdrawRecordListReq) (*pb.WithdrawRecordListResp, error) {
	// 参数验证和默认值设置
	if in.PageNo <= 0 {
		in.PageNo = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}

	// 构建查询条件
	db := l.svcCtx.AppDB.Model(&model.WithdrawRecord{})

	// 关联用户表获取用户信息
	db = db.Joins("User")

	// 添加查询条件
	if in.UserId != "" {
		db = db.Where("user_id = ?", in.UserId)
	}
	if in.UserName != "" {
		db = db.Where("User.nick_name LIKE ?", "%"+in.UserName+"%")
	}
	if in.UserMobile != "" {
		db = db.Where("User.mobile LIKE ?", "%"+in.UserMobile+"%")
	}
	//if in.Status > -1 {
	//	db = db.Where("status = ?", in.Status-1)
	//}
	if in.ReviewStatus > -1 {
		db = db.Where("review_status = ?", in.ReviewStatus)
	}

	// 日期范围查询
	if in.StartDate != "" {
		startTime, err := time.Parse("2006-01-02", in.StartDate)
		if err == nil {
			db = db.Where("apply_time >= ?", startTime)
		}
	}
	if in.EndDate != "" {
		endTime, err := time.Parse("2006-01-02", in.EndDate)
		if err == nil {
			// 添加一天，使查询包含结束当天
			endTime = endTime.Add(24 * time.Hour)
			db = db.Where("apply_time < ?", endTime)
		}
	}

	// 计算总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		l.Logger.Errorf("查询提现记录总数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询提现记录总数失败")
	}

	// 分页查询
	offset := (int(in.PageNo) - 1) * int(in.PageSize)
	var records []model.WithdrawRecord
	if err := db.Preload("User").Order("id DESC").Offset(offset).Limit(int(in.PageSize)).Find(&records).Error; err != nil {
		l.Logger.Errorf("查询提现记录列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询提现记录列表失败")
	}

	// 构建响应数据
	resp := &pb.WithdrawRecordListResp{
		List:     make([]*pb.WithdrawRecordItem, 0, len(records)),
		Total:    total,
		PageNo:   in.PageNo,
		PageSize: in.PageSize,
	}

	// 转换数据
	for _, record := range records {
		item := &pb.WithdrawRecordItem{
			Id:               uint64(record.ID),
			UserId:           record.UserID,
			UserName:         record.User.NickName,
			UserMobile:       record.User.Mobile,
			UserRoleId:       int32(record.User.RoleId),
			Amount:           record.Amount,
			ActualAmount:     record.ActualAmount,
			Fee:              record.Fee,
			FeeRatio:         record.FeeRatio,
			OutBillNo:        record.OutBillNo,
			TransferBillNo:   record.TransferBillNo,
			Status:           int32(record.Status),
			StatusText:       l.getStatusText(record.Status),
			ReviewStatus:     int32(record.ReviewStatus),
			ReviewStatusText: l.getReviewStatusText(record.ReviewStatus),
			FailReason:       record.FailReason,
			Remark:           record.Remark,
			InvoiceImg:       record.InvoiceImg,
		}

		// 格式化时间
		item.ApplyTime = record.ApplyTime.Format("2006-01-02 15:04:05")
		if record.ReviewTime != nil {
			item.ReviewTime = record.ReviewTime.Format("2006-01-02 15:04:05")
		}
		if record.ProcessTime != nil {
			item.ProcessTime = record.ProcessTime.Format("2006-01-02 15:04:05")
		}
		if record.SuccessTime != nil {
			item.SuccessTime = record.SuccessTime.Format("2006-01-02 15:04:05")
		}

		resp.List = append(resp.List, item)
	}

	return resp, nil
}

// 获取提现状态文本
func (l *GetWithdrawRecordListLogic) getStatusText(status model.WithdrawStatus) string {
	switch status {
	case model.WithdrawStatusPending:
		return "待处理"
	case model.WithdrawStatusProcess:
		return "提现中"
	case model.WithdrawStatusSuccess:
		return "提现成功"
	case model.WithdrawStatusFailed:
		return "提现失败"
	case model.WithdrawStatusCancelled:
		return "已取消"
	default:
		return "未知状态"
	}
}

// 获取审核状态文本
func (l *GetWithdrawRecordListLogic) getReviewStatusText(status model.ReviewStatus) string {
	switch status {
	case model.ReviewStatusPending:
		return "待审核"
	case model.ReviewStatusApproved:
		return "审核通过"
	case model.ReviewStatusRejected:
		return "审核拒绝"
	default:
		return "未知状态"
	}
}
