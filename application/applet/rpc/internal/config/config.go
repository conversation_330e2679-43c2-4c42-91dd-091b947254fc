package config

import (
	"xj-serv/pkg/middleware"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	BizRedis redis.RedisConf
	DB       struct {
		DataSource   string
		MaxOpenConns int `json:",default=10"`
		MaxIdleConns int `json:",default=100"`
		MaxLifetime  int `json:",default=3600"`
	}
	AppDB struct {
		DataSource   string
		MaxOpenConns int `json:",default=10"`
		MaxIdleConns int `json:",default=100"`
		MaxLifetime  int `json:",default=3600"`
	}

	JwtAuth struct {
		AccessSecret string
		AccessExpire int64
	}
	CasbinConf middleware.CasbinConf
	Default    struct {
		UserPassword string
	}
	// 添加RabbitMQ配置
	RabbitMQ struct {
		Host        string
		Port        int
		Username    string
		Password    string
		VirtualHost string
		// 默认参数
		DefaultParams struct {
			Durable    bool `json:",default=true"`
			AutoDelete bool `json:",default=false"`
			Exclusive  bool `json:",default=false"`
			NoWait     bool `json:",default=false"`
			Mandatory  bool `json:",default=false"`
			Immediate  bool `json:",default=false"`
		}
		// 队列配置
		Queues map[string]struct {
			Exchange   string
			RoutingKey string
			QueueName  string
		}
	}
}
