syntax = "proto3";

option go_package = "./pb";

package pb;
import "validate/validate.proto";
message EmptyRequest {}
message NoDataResponse {}

message BoolResp{
  bool Success = 1;
}

message GetUserInfoRequest {
  string UserName = 1;
  string Password = 2;
}

message GetUserInfoResponse {
  UserInfo UserInfo = 1;
}

message UserInfo{
  string UUID = 1;
  string Username = 2;
  string Password = 3;
  string NickName = 4;
  string SideMode = 5;
  string HeaderImg = 6;
  string BaseColor = 7;
  string ActiveColor = 8;
  int64 AuthorityId = 9;
  SysAuthority Authority = 10;
  repeated SysAuthority Authorities = 11;
  string Phone = 12;
  string Email = 13;
  int64 Enable = 14;

  int64  ID = 15;
  string CreatedAt = 16;
  string UpdatedAt = 17;
  string DeletedAt = 18;

}

message SysAuthority{
  int64 AuthorityId = 1;
  string AuthorityName = 2;
  int64 ParentId = 3;
  string DefaultRouter = 4;
  repeated SysAuthority DataAuthorityId = 5;
  repeated SysAuthority children = 6;
  repeated SysBaseMenu SysBaseMenus = 7;
  string showMenuIds = 8;

  string CreatedAt = 9;
  string UpdatedAt = 10;
  string DeletedAt = 11;
}


message SysBaseMenuParameter {
  int64 SysBaseMenuID = 1;
  string Type = 2;
  string Key = 3;
  string Value = 4;

  int64  ID = 5;
  string CreatedAt = 6;
  string UpdatedAt = 7;
  string DeletedAt = 8;
}

message SysBaseMenuBtn{
  string Name = 1;
  string Desc = 2;
  int64 SysBaseMenuID = 3;

  int64  ID = 4;
  string CreatedAt = 5;
  string UpdatedAt = 6;
  string DeletedAt = 7;
}

message GetUserTokeRequest {
  int64 ID = 1;
  string UUID = 2;
  int64 AuthorityId = 3;
  string Username = 4;
  string NickName = 5;
}

message GetUserTokeResponse {
  string Token = 1;
  int64  ExpiresAt = 2;
}

message GetMenuTreeRequest{
    int64 AuthorityId = 1;
}


message SysBaseMenu{
  int64 MenuLevel = 1;
  int64 ParentId = 2;
  string Path = 3;
  string Name = 4;
  bool Hidden = 5;
  string Component = 6;
  int64 Sort = 7;
  Meta Meta = 8;
  repeated SysAuthority SysAuthoritys = 9;
  repeated SysBaseMenu Children = 10;
  repeated SysBaseMenuParameter Parameters = 11;
  repeated SysBaseMenuBtn MenuBtn = 12;



  int64  ID = 13;
  string CreatedAt = 14;
  string UpdatedAt = 15;
  string DeletedAt = 16;
}

//  SysMenu struct
message SysMenu {
  //  SysBaseMenu struct
  SysBaseMenu SysBaseMenu = 1;
//  int64 MenuLevel = 1;
//  string ParentId = 2;
//  string Path = 3;
//  string Name = 4;
//  bool Hidden = 5;
//  string Component = 6;
//  int64 Sort = 7;
//  Meta Meta = 8;
//  repeated SysAuthority SysAuthoritys = 9;
//  repeated SysBaseMenu Children = 10;
//  repeated SysBaseMenuParameter Parameters = 11;
//  repeated SysBaseMenuBtn MenuBtn = 12;




  string MenuId = 13;
  int64 AuthorityId = 14;
  repeated SysMenu Children = 15;
  repeated SysBaseMenuParameter Parameters = 16;
  map<string, int64> Btns = 17;

  int64  ID = 18;
  string CreatedAt = 19;
  string UpdatedAt = 20;
  string DeletedAt = 21;
}

// Meta struct
message Meta {
  string ActiveName = 1;
  bool KeepAlive = 2;
  bool DefaultMenu = 3;
  string Title = 4;
  string Icon = 5;
  bool CloseTab = 6;
}

message GetMenuTreeResponse {
  repeated SysMenu SysMenu = 1;
}

message GetMenuBaseInfoListResponse {
  repeated SysBaseMenu SysBaseMenu = 1;
  int64 total = 2;
}

message AddMenuBaseRequest{
  SysBaseMenu SysBaseMenu = 1;
}

message PageRequest {
  int64 pageNo  = 1;
  int64 PageSize  = 2;
  string Keyword  = 3;
}

message GetAuthorityListRequest {
    PageRequest Page = 1;
}

message GetAuthorityListResponse {
  repeated SysAuthority SysAuthority = 1;
  int64 total = 2;
}

message SysApi {
  string Path = 1;
  string Description = 2;
  string ApiGroup = 3;
  string Method = 4;

  int64  ID = 5;
  string CreatedAt = 6;
  string UpdatedAt = 7;
  string DeletedAt = 8;
}

message GetApiListRequest {
    SysApi SysApi = 1;
    PageRequest PageRequest = 2;
    string OrderKey = 3;
    bool Desc = 4;
}

message GetApiListResponse {
  repeated SysApi SysApi = 1;
  int64 total = 2;
}

message CreateApiRequest {
  SysApi SysApi = 1;
}

message DeleteApiRequest {
  SysApi SysApi = 1;
}

message GetAllApiListResponse{
  repeated SysApi ApiList = 1;
}

message CasbinInfo {
    string  Path = 1;
    string  Method = 2;
}

message GetPathByAuthorityIdRequest {
    int64 AuthorityId = 1;
}

message GetPathByAuthorityIdResponse {
  repeated CasbinInfo CasbinInfoList = 1;
}

message GetBaseMenuTreeResponse {
  repeated SysBaseMenu SysBaseMenuList = 1;
}

message GetMenuAuthorityRequest {
  int64 AuthorityId = 1;
}

message GetMenuAuthorityResponse {
  repeated SysMenu SysMenuList = 1;
}

//message AuthorityMenu {
//    int64 MenuId = 1;
//    int64 AuthorityId = 2;
//}

message AddAuthorityMenuRequest {
  int64 AuthorityId = 1;
  string MenuIds = 2;
}

message UpdateCasbinDataRequest {
  int64 AuthorityId = 1;
  repeated CasbinInfo CasbinInfoList = 2;
}

message UpdateAuthorityRequest {
  SysAuthority SysAuthority = 1;
}

message UpdateAuthorityResponse {
  SysAuthority SysAuthority = 1;
}

message GetBaseMenuByIdRequest {
  int64 ID = 1;
}

message GetBaseMenuByIdResponse {
    SysBaseMenu SysBaseMenu = 1;
}

message UpdateBaseMenuRequest {
  SysBaseMenu SysBaseMenu = 1;
}

message CreateAuthorityRequest {
    SysAuthority SysAuthority = 1;
}

message CreateAuthorityResponse {
  SysAuthority SysAuthority = 1;
}

message GetUserListRequest {
  PageRequest PageRequest = 1;
}

message GetUserListResponse {
  repeated UserInfo UserInfoList = 1;
  int64 Total = 2;
}

message RegisterRequest {
  UserInfo UserInfo = 1;
  repeated int64 AuthorityIds = 2;
}

message RegisterResponse {
  UserInfo UserInfo = 1;
}

message UpdateUserInfoRequest {
  UserInfo UserInfo = 1;
}

message UpdateUserAuthoritiesRequest {
  int64 ID = 1;
  repeated int64 AuthorityIds = 2;
}

message ResetUserPasswordRequest {
  int64 UserID = 1;
}

message DeleteUserRequest {
  int64 UserID = 1;
}

message DeleteApisByIdsRequest {
  repeated int64 Ids = 1;
}

message UpdateCasbinDataByApiIdsRequest {
  int64 AuthorityId = 1;
  repeated int64 ApiIds = 2;
}

message UpdateCasbinDataByApiIdsResponse {
  repeated SysApi SysApis = 1;
}

message UpdateApiRequest {
  SysApi SysApi = 1;
}

message DeleteBaseMenuRequest {
  int64 ID = 1;
}

message DeleteAuthorityRequest {
  int64 ID = 1;
}

message SysDictionary {
  string Name = 1;
  string Type = 2;
  int64 Status = 3;
  string Desc = 4;
  repeated SysDictionaryInfo SysDictionaryInfoList = 5;

  int64  ID = 6;
  string CreatedAt = 7;
  string UpdatedAt = 8;
  string DeletedAt = 9;
}

message SysDictionaryInfo {
  string Label = 1;
  int64 Value = 2;
  string Extend = 3;
  int64 Status = 4;
  int64 Sort = 5;
  int64 SysDictionaryID = 6;

  int64  ID = 7;
  string CreatedAt = 8;
  string UpdatedAt = 9;
  string DeletedAt = 10;
}

message DictionaryListResponse {
  repeated SysDictionary SysDictionaryList = 1;
}

message GetSysDictionaryInfoListRequest {
  SysDictionaryInfo SysDictionaryInfo = 1;
  PageRequest PageRequest = 2;
}

message GetSysDictionaryInfoListResponse {
  repeated SysDictionaryInfo SysDictionaryInfoList = 1;
  int64 Total = 2;
}

message CreateSysDictionaryRequest {
    SysDictionary SysDictionary = 1;
}

message UpdateSysDictionaryRequest {
  SysDictionary SysDictionary = 1;
}

message GetSysDictionaryDetailsRequest {
  int64 ID = 1;
  string Type = 2;  // 字典名（英）
  int64 Status = 3; // 状态 1开启 2关闭
}

message GetSysDictionaryDetailsResponse {
    SysDictionary SysDictionary = 1;
}

message DeleteSysDictionaryRequest {
  int64 ID = 1;
}

message GetSysDictionaryInfoListDetailsByIdRequest {
  int64 ID = 1;
}

message GetSysDictionaryInfoListDetailsByIdResponse {
  SysDictionaryInfo SysDictionaryInfo = 1;
}

message UpdateSysDictionaryInfoRequest {
  SysDictionaryInfo SysDictionaryInfo = 1;
}

message CreateSysDictionaryInfoRequest {
  SysDictionaryInfo SysDictionaryInfo = 1;
}

message DeleteSysDictionaryInfoRequest {
  int64 ID = 1;
}

service User {
  //  获取用户信息
  rpc GetUserInfo (GetUserInfoRequest) returns (GetUserInfoResponse);
  // 获取Token
  rpc GetUserToke (GetUserTokeRequest) returns(GetUserTokeResponse);
  // 分页获取用户列表
  rpc GetUserList (GetUserListRequest) returns(GetUserListResponse);
  // 新增（注册）用户 - 管理员
  rpc Register (RegisterRequest) returns(RegisterResponse);
  // 修改用户信息
  rpc UpdateUserInfo (UpdateUserInfoRequest) returns(NoDataResponse);
  // 修改用户和角色的关系信息 -- 和上  在修改用户信息的时候请求
  rpc UpdateUserAuthorities (UpdateUserAuthoritiesRequest) returns(NoDataResponse);
  // 重置用户密码 默认密码：goZero
  rpc ResetUserPassword (ResetUserPasswordRequest) returns(NoDataResponse);
  // 删除用户
  rpc DeleteUser (DeleteUserRequest) returns(NoDataResponse);

}


service Menu {
  // 获取菜单-路由
  rpc GetMenuTree (GetMenuTreeRequest) returns (GetMenuTreeResponse);
  // 获取系统基础菜单列表
  rpc GetMenuBaseInfoList (NoDataResponse) returns (GetMenuBaseInfoListResponse);
  // 添加系统基础菜单列表
  rpc AddMenuBase (AddMenuBaseRequest) returns (NoDataResponse);
  // 获取用户动态路由树  -- 用于角色管理的设置权限
  rpc GetBaseMenuTree (NoDataResponse) returns (GetBaseMenuTreeResponse);
  // 获取指定角色menu  -- 用于角色管理的设置权限
  rpc GetMenuAuthority (GetMenuAuthorityRequest) returns (GetMenuAuthorityResponse);
  // 根据id获取系统菜单
  rpc GetBaseMenuById (GetBaseMenuByIdRequest) returns (GetBaseMenuByIdResponse);
  // 更新系统菜单
  rpc UpdateBaseMenu (UpdateBaseMenuRequest) returns (NoDataResponse);
  // 删除系统菜单
  rpc DeleteBaseMenu (DeleteBaseMenuRequest) returns (NoDataResponse);

}


service Authority {
  // 获取角色列表
  rpc GetAuthorityList (GetAuthorityListRequest) returns (GetAuthorityListResponse);
  // 增加base_menu和角色关联关系 -- 用于角色管理的设置权限
  rpc AddAuthorityMenu (AddAuthorityMenuRequest) returns (NoDataResponse);
  // 更新角色 -- 设为首页
  rpc UpdateAuthority (UpdateAuthorityRequest) returns (UpdateAuthorityResponse);
  // 创建角色
  rpc CreateAuthority (CreateAuthorityRequest) returns (CreateAuthorityResponse);
  // 删除角色
  rpc DeleteAuthority (DeleteAuthorityRequest) returns (NoDataResponse);


}

service Api {
  // 获取API列表
  rpc GetApiList (GetApiListRequest) returns (GetApiListResponse);
  // 创建/添加 API列表
  rpc CreateApi (CreateApiRequest) returns (NoDataResponse);
  // 删除API列表
  rpc DeleteApi (DeleteApiRequest) returns (NoDataResponse);
  // 获取全部API列表
  rpc GetAllApiList (NoDataResponse) returns (GetAllApiListResponse);
  // 删除多条api
  rpc DeleteApisByIds (DeleteApisByIdsRequest) returns (NoDataResponse);
  // 更新api
  rpc UpdateApi (UpdateApiRequest) returns (NoDataResponse);

}

service Casbin {
  // 根据角色id获取对应的casbin数据
  rpc GetPathByAuthorityId (GetPathByAuthorityIdRequest) returns (GetPathByAuthorityIdResponse);
  // 更新一个角色的对应的casbin数据
  rpc UpdateCasbinData (UpdateCasbinDataRequest) returns (NoDataResponse);
  // 更新一个角色的对应的casbin数据 用api的ids 查数据
  //  rpc UpdateCasbinDataByApiIds (UpdateCasbinDataByApiIdsRequest) returns (NoDataResponse);
  rpc UpdateCasbinDataByApiIds (UpdateCasbinDataByApiIdsRequest) returns (UpdateCasbinDataByApiIdsResponse);

}

service Dictionary {
  // =================  SysDictionary ==============

  // 获取SysDictionary列表 -- all
  rpc GetSysDictionaryList (NoDataResponse) returns (DictionaryListResponse);
  // 新建SysDictionary
  rpc CreateSysDictionary (CreateSysDictionaryRequest) returns (NoDataResponse);
  // 根据ID或者type获取SysDictionary
  rpc GetSysDictionaryDetails (GetSysDictionaryDetailsRequest) returns (GetSysDictionaryDetailsResponse);
  // 更新SysDictionary
  rpc UpdateSysDictionary (UpdateSysDictionaryRequest) returns (NoDataResponse);
  // 更新SysDictionary
  rpc DeleteSysDictionary (DeleteSysDictionaryRequest) returns (NoDataResponse);

  // =================  SysDictionaryInfo ==============

  // 获取SysDictionaryInfo列表 -- 分页带搜索
  rpc GetSysDictionaryInfoList (GetSysDictionaryInfoListRequest) returns (GetSysDictionaryInfoListResponse);
  // 根据id获取SysDictionaryInfo详情
  rpc GetSysDictionaryInfoListDetailsById (GetSysDictionaryInfoListDetailsByIdRequest) returns (GetSysDictionaryInfoListDetailsByIdResponse);
  // 更新SysDictionaryInfo
  rpc UpdateSysDictionaryInfo (UpdateSysDictionaryInfoRequest) returns (NoDataResponse);
  // 创建SysDictionaryInfo
  rpc CreateSysDictionaryInfo (CreateSysDictionaryInfoRequest) returns (NoDataResponse);
  // 删除SysDictionaryInfo
  rpc DeleteSysDictionaryInfo (DeleteSysDictionaryInfoRequest) returns (NoDataResponse);
}






//以下是管理后台的业务逻辑
message XjUser {
  uint64 id = 1;              // 用户ID
  string created_at = 2;      // 创建时间
  string updated_at = 3;      // 更新时间
  string uuid = 4;            // UUID
  string mobile = 5;          // 手机号
  string nick_name = 6;       // 昵称
  string avatar = 7;          // 头像
  uint32 gender = 8;          // 性别
  string country = 9;         // 国家
  string province = 10;       // 省份
  string city = 11;           // 城市
  uint32 address_id = 12;     // 默认地址ID
  double balance = 13;        // 可用余额
  uint32 points = 14;         // 可用积分
  double pay_money = 15;      // 总支付金额
  double expend_money = 16;   // 实际消费金额
  uint32 grade_id = 17;       // 会员等级ID
  string platform = 18;       // 注册平台
  uint32 last_login_time = 19;// 最后登录时间
  string openid = 20;         // 微信openid
  string unionid = 21;        // 微信unionid
  string sharer_appid = 22;   // 推客appid
  string contact_name = 23;   // 联系人姓名
  string contact_number = 24; // 联系电话
  string share_code = 25;     // 分享码
  string share_code_image = 26;// 分享码图片
  string reg_ipv4 = 27;       // 注册IP
  uint64 invite_from = 28;    // 邀请人
  string reg_source = 29;     // 注册来源
  int32 level = 30;          // 用户等级
  string level_name = 31;  // 添加等级名称
  int32 is_enterprise = 32;   // 是否企业用户
  string birthday = 33;       // 生日
  double withdraw = 34;        // 可提现余额
  int32 role_id = 35;          // 角色ID
  string finder_id = 36;         // 视频号ID
  string invite_name = 37;         // 邀请人昵称
  string invite_mobile = 38;         // 邀请人手机号
  string role_name = 39;          // 角色名称
  uint32 status = 40;          // 状态
}

// 获取公司列表请求
message GetCompanyListRequest {
  PageRequest PageRequest = 1;
  string name = 2;
  string phone = 3;
  int32 status = 4;
  string startTime = 5;
  string endTime = 6;
  int32 role_id = 7; // -1=全部，3=代理商，4=运营商
}

// 公司信息
message CompanyInfo {
  uint64 id = 1;
  uint64 userId = 2;
  string name = 3;
  string address = 4;
  string bankAccount = 5;
  string bankName = 6;
  string taxNumber = 7;
  string phone = 8;
  int32 status = 9;
  string createdAt = 10;
  string updatedAt = 11;
  string leader = 12;
  string licenseImg = 14;
  XjUser user = 13;
}

// 公司列表响应
message GetCompanyListResp {
  repeated CompanyInfo list = 1;
  int64 total = 2;
  int64 pageNo = 3;
  int64 pageSize = 4;
}

// 获取公司详情请求
message GetCompanyDetailRequest {
  uint64 id = 1;
  uint64 adminId = 2; // 管理员ID，用于权限检查
}

// 公司详情响应
message CompanyDetailResponse {
  uint64 id = 1;
  uint64 userId = 2;
  string name = 3;
  string address = 4;
  string bankAccount = 5;
  string bankName = 6;
  string taxNumber = 7;
  string phone = 8;
  string licenseImg = 9;
  int32 status = 10;
  string createdAt = 11;
  string updatedAt = 12;
  string remark = 13;
  string leader = 14;
  XjUser user = 15;
}

// 审核公司请求
message AuditCompanyRequest {
  uint64 id = 1;
  int32 status = 2;
  string remark = 3;
}

//修改公司请求
message UpdateCompanyRequest {
  uint64 id = 1;
  string name = 2;
  string address = 3;
  string bankAccount = 4;
  string bankName = 5;
  string taxNumber = 6;
  string leader = 7;
  string phone = 8;
  string license_img = 9;
}

message CreateCompanyRequest {
  uint64 user_id = 1;
  string name = 2;
  string address = 3;
  string bank_account = 4;
  string bank_name = 5;
  string tax_number = 6;
  string leader = 7;
  string phone = 8;
  string license_img = 9;
}

// 获取代理商列表请求
message GetAgentListRequest {
    PageRequest Page = 1;  // 分页信息
    string name = 2;        // 代理商名称
    string mobile = 3;       // 手机号
    int32 status = 4;       // 状态
    string startTime = 5;   // 开始时间
    string endTime = 6;     // 结束时间
    string companyName = 7; // 公司名称
    string relatedCompany = 8; // 关联运营商公司名称
}

// 代理商信息
message AgentInfo {
    uint64 id = 1;           // 代理商ID
    string nickname = 2;     // 昵称
    string mobile = 3;       // 手机号
    int32 packageCount = 4;  // 套餐数量
    int32 taskRewardCount = 5; // 任务奖励次数
    string companyName = 6;  // 公司名称
    string relatedCompany = 7; // 关联运营商公司名称
    string createdAt = 8;

}

// 获取代理商列表响应
message GetAgentListResponse {
    repeated AgentInfo Agents = 1;  // 代理商列表
    int64 Total = 2;                // 总数
    int64 PageNo = 3;               // 当前页
    int64 PageSize = 4;             // 每页大小
}


// 设置业务员权限请求
message SetSalesmanPermissionReq {
  uint64 salesman_user_id = 1;  // 业务员userid
  double salesman_rate = 2;     // 代理给业务员佣金比例
  string salesman_remark = 3;   // 业务员备注
  int64 is_full_time = 4;        // 全职兼职
}


// 搜索用户请求
message SearchUserReq {
  string Keyword = 1;      // 搜索关键词(昵称或手机号)
  PageRequest PageRequest = 2;
}

// 搜索用户响应
message SearchUserResp {
  repeated XjUser list = 1; // 用户列表
  int64 total = 2;
  int64 pageNo = 3;
  int64 pageSize = 4;
}
message SetUserRoleReq {
  int64 user_id = 1;  // 用户ID
  int64 role_id = 2;  // 角色ID
}

message SetUserLevelReq {
  int64 user_id = 1;  // 用户ID
  int64 level_id = 2;  // 等级ID
}

// 获取用户列表请求
message XjUserListReq {
  int64 page_no = 1;
  int64 page_size = 2;
  string nick_name = 3;
  string mobile = 4;
  int32 level = 5;
  int32 role_id = 6;
  string invite_name = 7;
  string invite_mobile = 8;
  string start_time = 9;
  string end_time = 10;
}


message XjUserListResp {
  repeated XjUser list = 1;
  int64 total = 2;
  int64 page_no = 3;
  int64 page_size = 4;
}

message UserLevelItem {
  int64 id = 1;
  string name = 2;
  int32 level = 3;
  string created_at = 4;
  string updated_at = 5;
}

message UserLevelListResp {
  repeated UserLevelItem list = 1;
}
message UserRoleItem {
  int64 id = 1;
  int64 role_id = 2;
  string role_name = 3;
  string description = 4;
  string created_at = 5;
  string updated_at = 6;
}

message UserRoleListResp {
  repeated UserRoleItem list = 1;
}

// 设置用户邀请人请求
message SetUserInviterReq {
  int64 user_id = 1;      // 被设置的用户ID
  int64 inviter_id = 2;   // 邀请人用户ID
}

message SetUserStatusReq {
  int64 user_id = 1;   // 用户ID
  int32 status = 2;    // 状态：0=冻结，1=正常
}

// 获取代理商/业务员个人主页数据请求
message GetAgentProfileReq {
  uint64 user_id = 1;  // 用户ID
}


// 获取代理商/业务员个人主页数据响应
message GetAgentProfileResp {
  // 用户基本信息
  string nick_name = 1;        // 昵称
  string avatar = 2;           // 头像
  string mobile = 3;           // 手机号
  bool is_agent = 4;           // 是否是代理商
  double salesman_rate = 5;    // 业务员佣金比例（仅业务员有）
  string salesman_remark = 6;  // 业务员备注（仅业务员有）
  int64 is_full_time = 7;      // 全职兼职（仅业务员有）
  string company_name = 8;     // 公司名称
  bool has_company_info = 20;  // 是否填写过公司资料

  // 统计信息
  double total_commission = 9;     // 佣金总额
  double salesman_commission = 10; // 业务经理佣金汇总
  int64 today_order_count = 11;   // 今日订单数量
  double today_order_amount = 12;  // 今日订单总额
  int64 ai_point_total = 13;      // AI点总数
  int64 team_package_count = 14;  // 团长套餐数量
  int64   task_reward_count = 15; // 任务奖励次数
}

// 获取员工信息请求
message GetSalesmanInfoReq {
  uint64 agent_user_id = 1;     // 代理商用户ID
  uint64 salesman_user_id = 2;  // 业务员用户ID
}

// 获取员工信息响应
message GetSalesmanInfoResp {
  uint64 salesman_user_id = 1;   // 业务员用户ID
  string nick_name = 2;          // 业务员昵称
  string avatar = 3;             // 业务员头像
  string mobile = 4;             // 业务员手机号
  double salesman_rate = 5;      // 代理给业务员佣金比例
  string salesman_remark = 6;    // 业务员备注
  int64 is_full_time = 7;        // 全职兼职
  string created_at = 8;         // 创建时间

  // 收益统计
  double total_commission = 9;     // 佣金总额
  double salesman_commission = 10; // 业务经理佣金汇总
  int64 today_order_count = 11;   // 今日订单数量
  double today_order_amount = 12;  // 今日订单总额
  int64 ai_point_total = 13;      // AI点总数
  int64 team_package_count = 14;  // 团长套餐数量
}

// 代理商给业务经理设置团长套餐数量请求
message SetAgentTeamPackageReq {
  uint64 agent_user_id = 1;    // 代理商用户ID
  int32 number = 2;            // 设置的套餐数量
}
// 调整用户库存请求
message AdjustUserInventoryReq {
  uint64 UserID = 1 [(validate.rules).uint64 = {gt: 0}];     // 用户ID
  uint64 ProductID = 2 [(validate.rules).uint64 = {gt: 0}];  // 产品ID
  int32 Quantity = 3;                                        // 调整数量(正数增加，负数减少)
  string Remark = 4;                                         // 调整原因备注
  string TransactionType = 5;                                // 调整类型
  string TccAction = 6;                                     // TCC操作类型: try/confirm/cancel
  string OperationId = 7;                                   // 操作ID（confirm/cancel时必填）
}

// 调整用户库存响应
message AdjustUserInventoryResp {
  bool Success = 1;          // 是否成功
  string OperationID = 2;    // 操作ID
  uint32 BeforeQuantity = 3; // 调整前数量
  uint32 AfterQuantity = 4;  // 调整后数量
  string Message = 5;        // 消息
}
// 团长套餐数日志列表请求
message GetInventoryLogListReq {
  PageRequest PageRequest = 1;
  string mobile = 2;
  string startTime = 3;
  string endTime = 4;
  string transactionType = 5;
}

// 团长套餐数日志信息
message InventoryLogInfo {
  uint64 id = 1;
  uint64 userId = 2;
  string nickname = 3;
  string mobile = 4;
  string roleId = 5;
  int32 level = 6;
  uint64 productId = 7;
  string productType = 8;
  uint64 orderId = 9;
  string transactionType = 10;
  int32 quantity = 11;
  int32 beforeQuantity = 12;
  int32 afterQuantity = 13;
  string remark = 14;
  string createdAt = 15;
}

// 团长套餐数日志列表响应
message GetInventoryLogListResp {
  repeated InventoryLogInfo list = 1;
  int64 total = 2;
  int64 pageNo = 3;
  int64 pageSize = 4;
}

// 图片组件信息
message WidgetImgInfo {
  int32 id = 1;
  string title = 2;
  int32 status = 3;
  string img = 4;
  string link = 5;
  int32 sort = 6;
  int32 type = 7;
  string createdAt = 8;
  string updatedAt = 9;
  int32 adminId = 10;
}

// 添加图片组件请求
message CreateWidgetImgReq {
  string title = 1;
  int32 status = 2;
  string img = 3;
  string link = 4;
  int32 sort = 5;
  int32 type = 6;
  int32 adminId = 7;
}

// 修改图片组件请求
message UpdateWidgetImgReq {
  int32 id = 1;
  string title = 2;
  int32 status = 3;
  string img = 4;
  string link = 5;
  int32 sort = 6;
  int32 type = 7;
  int32 adminId = 8;
}

// 查看图片组件请求
message GetWidgetImgReq {
  int32 id = 1;
}


// 删除图片组件请求
message DeleteWidgetImgReq {
  int32 id = 1;
}



// 获取图片组件列表请求
message GetWidgetImgListReq {
  int32 type = 1;
  int32 pageNo = 2;
  int32 pageSize = 3;
}

// 获取图片组件列表响应
message GetWidgetImgListResp {
  repeated WidgetImgInfo list = 1;
  int64 total = 2;
  int32 pageNo = 3;
  int32 pageSize = 4;
}

// 任务池充值记录列表请求
message GetBenefitsPoolLogListReq {
  string mobile = 1;        // 手机号
  string startTime = 2;     // 开始时间
  string endTime = 3;       // 结束时间
  PageRequest pageRequest = 4; // 分页请求
}
// 任务池充值记录信息
message BenefitsPoolLogInfo {
  uint64 id = 1;
  uint64 benefitsUserId = 2;
  uint64 userId = 3;
  string taskType = 4;
  string createdAt = 5;
  XjUser userInfo = 6;    // 用户信息
  XjUser benefitsUserInfo = 7; // 福利用户信息
}
// 任务池充值记录列表响应
message GetBenefitsPoolLogListResp {
  repeated BenefitsPoolLogInfo list = 1;
  int64 total = 2;
  uint64 pageNo = 3;
  uint64 pageSize = 4;
}

// 充值任务池请求
message RechargeBenefitsPoolReq {
  uint64 userId = 1;        // 用户ID
  int32 type = 2;           // 任务池类型
  int32 quantity = 3;       // 充值数量
  string remark = 4;        // 备注
  uint64 operatorId = 5;    // 操作人ID
}

// 商品分类信息
message ProductCategoryInfo {
  uint64 id = 1;
  uint64 catId = 2;
  uint64 fCatId = 3;
  string name = 4;
  int32 level = 5;
}


// 获取商品分类列表响应
message GetProductCategoriesResp {
  repeated ProductCategoryInfo categories = 1;
}

// 商品信息
message ProductInfo {
  uint64 id = 1;
  uint64 productId = 2;
  string shopAppid = 3;
  string title = 4;
  string subTitle = 5;
  string headImg = 6;  // 主图第一张
  double minPrice = 7;  // 最低价格（单位：分）
  double commissionAmount = 8;  // 佣金（单位：分）
  int32 serviceRatio = 9;  // 服务费率（百分比）
  int32 isPreferred = 10;  // 是否优选
  int32 isRecommend = 11;  // 是否推荐
  int32 isWindow = 12;  // 是否橱窗
  uint64 order = 13;  // 排序权重
  uint32 cat1 = 14;  // 一级分类ID
  uint32 cat2 = 15;  // 二级分类ID
  uint32 cat3 = 16;  // 三级分类ID
  uint32 cCat1 = 18;  // 自定义一级分类ID
  uint32 cCat2 = 19;  // 自定义二级分类ID
  string categoryName = 17;  // 分类名称
}

// 获取商品列表请求
message GetProductsReq {
  uint64 categoryId = 1;  // 分类ID，0表示全部
  uint64 cCat1 = 13;  // 自定义一级分类
  uint64 cCat2 = 14;  // 自定义二级分类
  string title = 2;
  int32 minPrice = 3;
  int32 maxPrice = 4;
  int32 minCommissionAmount = 5;
  int32 maxCommissionAmount = 6;
  int32 isPreferred = 7;  // 是否优选
  int32 isRecommend = 8;  // 是否优选
  int32 isWindow = 9;  // 是否优选
  string sortBy = 10;     // 排序字段：min_price(价格), commission_amount(佣金), created_at(创建时间), order(排序权重)
  string order = 11;      // 排序方式：asc(升序), desc(降序)
  PageRequest PageRequest = 12;  // 分页参数
}

// 获取商品列表响应
message GetProductsResp {
  repeated ProductInfo products = 1;
  int64 total = 2;
  int32 pageNo = 3;
  int32 pageSize = 4;
}

// 设置/取消优选商品请求
message SetProductPreferredReq {
  uint64 productId = 1;
  int32 isPreferred = 2;  // 1-设为优选，0-取消优选
}

// 设置/取消橱窗商品请求
message SetProductWindowReq {
  uint64 productId = 1;
  int32 isWindow = 2;
}

// 设置/取消推荐商品请求
message SetProductRecommendReq {
  uint64 productId = 1;
  int32 isRecommend = 2;
}



// 设置商品排序请求
message SetProductOrderReq {
  uint64 productId = 1;
  int32 order = 2;  // 排序权重
}

// 设置商品置顶请求
message SetProductTopReq {
  uint64 productId = 1;
}

// 批量修改商品请求
message BatchUpdateProductsReq {
  repeated uint64 product_ids = 1;  // 商品ID列表
  optional int32 is_recommend = 2;  // 是否推荐
  optional int32 is_preferred = 3;  // 是否优选
  optional int32 is_window = 4;     // 是否橱窗
  optional uint64 c_cat1 = 5;       //自定义一级分类
  optional uint64 c_cat2 = 6;       //自定义二级分类

}

message CreateCategoryRequest {
  uint64 f_id = 1;
  string name = 2;
  string img = 3;
  uint64 order = 4;
}

message UpdateCategoryRequest {
  uint64 id = 1;
  uint64 f_id = 2;
  string name = 3;
  string img = 4;
  uint64 order = 5;
}

message DeleteCategoryRequest {
  uint64 id = 1;
}




message CategoryInfo {
  uint64 id = 1;
  uint64 f_id = 2;
  string name = 3;
  string img = 4;
  uint64 order = 5;
  string created_at = 6;
  string updated_at = 7;
  string deleted_at = 8;
  repeated CategoryInfo children = 9;
}

message GetTalentCategoryListResponse {
  repeated CategoryInfo categories = 1;
  uint64 total = 2; // 总分类数量
  int32 pageNo = 3;
  int32 pageSize = 4;
}


message GetTalentCategoryListRequest {
  uint64 f_id = 1; // Optional: filter by parent category ID. 0 for top-level.
  uint32 pageNo = 2; // 当前页码
  uint32 page_size = 3; // 每页数量
}

message GetTalentCategoryRequest {
  uint64 id = 1;
}


// 提现记录项
message WithdrawRecordItem {
  uint64 id = 1;               // 记录ID
  uint64 userId = 2;           // 用户ID
  string userName = 3;         // 用户姓名
  string userMobile = 4;        // 用户手机号
  int32 userRoleId = 22;       //用户角色
  uint64 amount = 5;           // 申请提现金额
  uint64 actualAmount = 6;     // 实际到账金额
  uint64 fee = 7;              // 手续费
  double feeRatio = 8;         // 手续费率
  string outBillNo = 9;        // 商户单号
  string transferBillNo = 10;  // 微信单号
  int32 status = 11;           // 提现状态
  string statusText = 12;      // 提现状态文本
  int32 reviewStatus = 13;     // 审核状态
  string reviewStatusText = 14;// 审核状态文本
  string failReason = 15;      // 失败原因
  string applyTime = 16;       // 申请时间
  string reviewTime = 17;      // 审核时间
  string processTime = 18;     // 处理时间
  string successTime = 19;     // 成功时间
  string invoiceImg = 20;      // 发票图片
  string remark = 21;          // 备注
}
// 提现记录列表请求
message WithdrawRecordListReq {
  int32 pageNo = 1;       // 页码
  int32 pageSize = 2;     // 每页数量
  string userName = 3;    // 用户姓名
  string userMobile = 4;   // 用户手机号
  string userId = 5;      // 用户ID
  int32 userRoleId = 10;       //用户角色
  int32 status = 6;       // 提现状态
  int32 reviewStatus = 7; // 审核状态
  string startDate = 8;   // 开始日期
  string endDate = 9;     // 结束日期
}
// 提现记录列表响应
message WithdrawRecordListResp {
  repeated WithdrawRecordItem list = 1; // 记录列表
  int64 total = 2;                      // 总数
  int32 pageNo = 3;                     // 当前页码
  int32 pageSize = 4;                   // 每页数量
}

// 审核提现请求
message AuditWithdrawReq {
  uint64 id = 1;           // 记录ID
  int32 reviewStatus = 2;  // 审核状态：1-通过，2-拒绝
  string fail_reason = 3;  // 失败原因
//  uint64 reviewerId = 3;   // 审核人ID
//  string remark = 4;       // 备注
}



service Admin {
  // 获取公司列表
  rpc GetCompanyList(GetCompanyListRequest) returns (GetCompanyListResp);
  // 获取公司详情
  rpc GetCompanyDetail(GetCompanyDetailRequest) returns (CompanyDetailResponse);
  // 审核公司
  rpc AuditCompany(AuditCompanyRequest) returns (NoDataResponse);
  // 获取代理商列表
  rpc GetAgentList(GetAgentListRequest) returns (GetAgentListResponse);
  // 修改公司
  rpc UpdateCompany(UpdateCompanyRequest) returns (NoDataResponse);
  // 创建公司
  rpc CreateCompany(CreateCompanyRequest) returns (NoDataResponse);
  // 搜索用户
  rpc SearchUser(SearchUserReq) returns (SearchUserResp);
  // 修改用户角色
  rpc SetUserRole(SetUserRoleReq) returns (NoDataResponse);
  //获取用户列表  这里加个xj做区分
  rpc GetXjUserList(XjUserListReq) returns (XjUserListResp);
  //获取用户等级列表
  rpc GetUserLevelList(EmptyRequest) returns (UserLevelListResp);
  //获取用户角色列表
  rpc GetUserRoleList(EmptyRequest) returns (UserRoleListResp);
  //设置用户上级
  rpc SetUserInviter(SetUserInviterReq) returns (BoolResp);
  //设置用户等级
  rpc SetUserLevel(SetUserLevelReq) returns (BoolResp);
  //冻结、解冻用户
  rpc SetUserStatus(SetUserStatusReq) returns (BoolResp);
  // 获取代理商/业务员个人主页数据
  rpc GetAgentProfile(GetAgentProfileReq) returns (GetAgentProfileResp);
  // 获取员工信息
  rpc GetSalesmanInfo(GetSalesmanInfoReq) returns (GetSalesmanInfoResp);
  // 调整用户库存
  rpc AdjustUserInventory(AdjustUserInventoryReq) returns (AdjustUserInventoryResp);
  // 给代理商设置团长套餐数量
  rpc SetAgentTeamPackage(SetAgentTeamPackageReq) returns (BoolResp);
  // 获取团长套餐数日志列表
  rpc GetInventoryLogList(GetInventoryLogListReq) returns (GetInventoryLogListResp);
  // 添加图片组件
  rpc CreateWidgetImg(CreateWidgetImgReq) returns (BoolResp);
  // 修改图片组件
  rpc UpdateWidgetImg(UpdateWidgetImgReq) returns (BoolResp);
  // 查看图片组件
  rpc GetWidgetImg(GetWidgetImgReq) returns (WidgetImgInfo);
  // 删除图片组件
  rpc DeleteWidgetImg(DeleteWidgetImgReq) returns (BoolResp);
  // 获取图片组件列表
  rpc GetWidgetImgList(GetWidgetImgListReq) returns (GetWidgetImgListResp);
  // 获取任务池充值记录列表
  rpc GetBenefitsPoolLogList (GetBenefitsPoolLogListReq) returns (GetBenefitsPoolLogListResp);
  // 充值任务池
  rpc RechargeBenefitsPool (RechargeBenefitsPoolReq) returns (BoolResp);
  // 获取商品分类列表
  rpc GetProductCategories(EmptyRequest) returns (GetProductCategoriesResp);
  // 获取优选商品列表
  rpc GetProducts(GetProductsReq) returns (GetProductsResp);
  // 设置/取消优选商品
  rpc SetProductPreferred(SetProductPreferredReq) returns (BoolResp);
  // 设置/取消推荐商品
  rpc SetProductRecommend(SetProductRecommendReq) returns (BoolResp);
  // 设置/取消橱窗商品
  rpc SetProductWindow(SetProductWindowReq) returns (BoolResp);
  // 设置商品排序
  rpc SetProductOrder(SetProductOrderReq) returns (BoolResp);
  // 设置商品置顶
  rpc SetProductTop(SetProductTopReq) returns (BoolResp);
  //批量设置商品
  rpc BatchUpdateProducts(BatchUpdateProductsReq) returns (BoolResp);
  //添加商品分类
  rpc CreateProductCategory(CreateCategoryRequest) returns (BoolResp);
  //修改商品分类
  rpc UpdateProductCategory(UpdateCategoryRequest) returns (BoolResp);
  //删除商品分类
  rpc DeleteProductCategory(DeleteCategoryRequest) returns (BoolResp);
  // 商品分类信息
  rpc GetTalentProductCategory(GetTalentCategoryRequest) returns (CategoryInfo);
  // 商品分类列表接口
  rpc GetTalentProductCategoryList(GetTalentCategoryListRequest) returns (GetTalentCategoryListResponse);
  // 获取提现记录列表
  rpc GetWithdrawRecordList(WithdrawRecordListReq) returns (WithdrawRecordListResp);
  // 审核提现申请
  rpc AuditWithdraw(AuditWithdrawReq) returns (BoolResp);

  // 设置业务员权限
  rpc SetSalesmanPermission(SetSalesmanPermissionReq) returns(BoolResp);
}

