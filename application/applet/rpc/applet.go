package main

import (
	"flag"
	"fmt"

	"xj-serv/application/applet/rpc/internal/config"
	adminServer "xj-serv/application/applet/rpc/internal/server/admin"
	apiServer "xj-serv/application/applet/rpc/internal/server/api"
	authorityServer "xj-serv/application/applet/rpc/internal/server/authority"
	casbinServer "xj-serv/application/applet/rpc/internal/server/casbin"
	dictionaryServer "xj-serv/application/applet/rpc/internal/server/dictionary"
	menuServer "xj-serv/application/applet/rpc/internal/server/menu"
	userServer "xj-serv/application/applet/rpc/internal/server/user"
	"xj-serv/application/applet/rpc/internal/svc"
	"xj-serv/application/applet/rpc/pb"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/applet.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		pb.RegisterUserServer(grpcServer, userServer.NewUserServer(ctx))
		pb.RegisterMenuServer(grpcServer, menuServer.NewMenuServer(ctx))
		pb.RegisterAuthorityServer(grpcServer, authorityServer.NewAuthorityServer(ctx))
		pb.RegisterApiServer(grpcServer, apiServer.NewApiServer(ctx))
		pb.RegisterCasbinServer(grpcServer, casbinServer.NewCasbinServer(ctx))
		pb.RegisterDictionaryServer(grpcServer, dictionaryServer.NewDictionaryServer(ctx))
		pb.RegisterAdminServer(grpcServer, adminServer.NewAdminServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
