// user.proto
syntax = "proto3";
package wapp.xjs;
option go_package = "./pb";

import "validate/validate.proto";

message Empty {}

message BoolReq{
  bool Success = 1;
}

message IDReq{
  uint64 id = 1 [(validate.rules).uint64 = {gt: 0}];
}

message PageRequest {
  // @inject_tag: json:"page_no"
  uint64 PageNo = 1 [json_name = "page_no", (validate.rules).uint64 = {gte: 1, lte: 20}];
  // @inject_tag: json:"page_size"
  uint64 PageSize = 2 [json_name = "page_size", (validate.rules).uint64 = {gte: 1, lte: 15}];
  // @inject_tag: json:"keyword,omitempty"
  string Keyword = 3 [json_name = "keyword"];
}
message UserLoginReq {
  string Phone = 1 [(validate.rules).string = {min_len: 1}];
  string Nickname = 2 [(validate.rules).string = {min_len: 1}];
  string Avatar = 3;
  string OpenId = 4 [(validate.rules).string = {min_len: 1}];
  string UnionId = 5;
  string Ip = 6;
  // @inject_tag: json:"share_code"
  string ShareCode = 7 [json_name = "share_code"];
}

message UserTokeReq {
  uint64 ID = 1 [(validate.rules).uint64 = {gt: 0}];
  string UUID = 2 [(validate.rules).string = {min_len: 1}];
  string Mobile = 3 [(validate.rules).string = {min_len: 1}];
  string NickName = 4;
  string Avatar = 5;
  // @inject_tag: json:"isEnterprise"
  bool IsEnterprise = 6;
}

message UserTokeResp {
  AccessToken AccessToken = 1;
  RefreshToken  RefreshToken = 2;
}

message AccessToken {
  string Token = 1;
  int64  ExpiresAt = 2;
}

message RefreshToken {
  string Token = 1;
  int64  ExpiresAt = 2;
}

message ModifyUserInfoReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1  [json_name = "user_id"];
  UserInfo UserInfo = 2;
}

message UserInfo {
  // @inject_tag: json:"nickname"
  string NickName = 1  [json_name = "nickname"];
  // @inject_tag: json:"share_code"
  string ShareCode = 2 [json_name = "share_code"];
  // @inject_tag: json:"avatar"
  string Avatar = 3 [json_name = "avatar"];
  // @inject_tag: json:"city"
  string City = 4 [json_name = "city"];
  // @inject_tag: json:"birthday"
  string Birthday = 5 [json_name = "birthday"];
  // @inject_tag: json:"mobile"
  string Mobile = 6 [json_name = "mobile"];
  // @inject_tag: json:"points"
  uint64 Points = 7 [json_name = "points"];
  // @inject_tag: json:"share_code_image"
  string ShareCodeImage = 8 [json_name = "share_code_image"];
  // @inject_tag: json:"union_id"
  string UnionId = 9 [json_name = "union_id"];
  // @inject_tag: json:"id"
  uint64 Id = 10 [json_name = "id"];
  // @inject_tag: json:"openfinderid"
  string OpenFinderId = 11 [json_name = "openfinderid"];  // 视频号OpenID
  // @inject_tag: json:"opentalentid"
  string OpenTalentId = 12 [json_name = "opentalentid"];  // 达人OpenID
  // @inject_tag: json:"finder_id"
  string FinderId = 13 [json_name = "finder_id"];  // 视频号ID
}

message UserInfoResp {
  // @inject_tag: json:"user_info"
  UserInfo UserInfo = 1 [json_name = "user_info"];
  // @inject_tag: json:"level"
  UserLevel Level = 2 [json_name = "level"];
}

// 用户详情响应
message UserDetailResp {
  uint64 id = 1;              // 用户ID
  string created_at = 2;      // 创建时间
  string updated_at = 3;      // 更新时间
  string uuid = 4;            // UUID
  string mobile = 5;          // 手机号
  string nick_name = 6;       // 昵称
  string avatar = 7;          // 头像
  uint32 gender = 8;          // 性别
  string country = 9;         // 国家
  string province = 10;       // 省份
  string city = 11;           // 城市
  uint32 address_id = 12;     // 默认地址ID
  double balance = 13;        // 可用余额
  uint32 points = 14;         // 可用积分
  double pay_money = 15;      // 总支付金额
  double expend_money = 16;   // 实际消费金额
  uint32 grade_id = 17;       // 会员等级ID
  string platform = 18;       // 注册平台
  uint32 last_login_time = 19;// 最后登录时间
  string openid = 20;         // 微信openid
  string unionid = 21;        // 微信unionid
  string sharer_appid = 22;   // 推客appid
  string contact_name = 23;   // 联系人姓名
  string contact_number = 24; // 联系电话
  string share_code = 25;     // 分享码
  string share_code_image = 26;// 分享码图片
  string reg_ipv4 = 27;       // 注册IP
  uint64 invite_from = 28;    // 邀请人
  string reg_source = 29;     // 注册来源
  int32 level = 30;          // 用户等级
  string level_name = 31;  // 添加等级名称字段
  int32 is_enterprise = 32;   // 是否企业用户
  string birthday = 33;       // 生日
  double withdraw = 34;        // 可提现余额
  int32 role_id = 35;          // 角色ID
  string finder_id = 36;         // 视频号ID
}

message UserLevel {
  // @inject_tag: json:"id"
  uint64 Id = 1 [json_name = "id"];
  // @inject_tag: json:"name"
  string Name = 6 [json_name = "name"];
}

// 获取结算明细请求
message GetUserSettlementDetailReq {
  uint64 user_id = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
  PageRequest PageRequest = 3;
  string StartDate = 4;
  string EndDate = 5;
  int32 type = 6;                     // 明细类型
}

// 结算明细项
message UserSettlementDetailItem {
  uint64 id = 1;                      // ID
  double amount = 2;                   // 金额
  double amount_change = 3;            // 变动金额
  int32 type = 4;                     // 明细类型
  string create_time = 5;             // 创建时间
  string remark = 6;                  // 备注
  string order_id = 7;                // 订单ID
  string product_name = 8;            // 商品名称
}
message SettlementSummary {
  double TotalAmount = 1; // 总收益
  double DirectAmount = 2; // 直接收益
  double IndirectAmount = 3; // 间接收益
  double AgentAmount = 4; // 代理商｜运营商收益
}
// 获取结算明细响应
message GetUserSettlementDetailResp {
  repeated UserSettlementDetailItem list = 1;  // 明细列表
  uint64 Total = 2;
  SettlementSummary Summary = 3;
}

message NewShareTaskReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gt: 0}];
  // @inject_tag: json:"user_id"
  uint64 FatherId = 2 [json_name = "father_id", (validate.rules).uint64 = {gte: 0}];
}



//
//message MasPaidOrderInfo {
//  // @inject_tag: json:"order_id"
//  string OrderId = 1 [json_name = "order_id"];
//  // @inject_tag: json:"pay_time"
//  string PayTime = 2 [json_name = "pay_time"];
//}



message JobsVideoAccount {
  // @inject_tag: json:"is_complete"
  uint64 IsComplete = 1 [json_name = "is_complete", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"is_official"
  uint64 IsOfficial = 2 [json_name = "is_official", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"level"
  uint64 Level = 3 [json_name = "level", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"category"
  string Category = 4 [json_name = "category", (validate.rules).string = {min_len: 1}];

}

message UserJobsResp {
  // @inject_tag: json:"jobs"
  repeated JobsVideoAccount Jobs = 1 [json_name = "jobs"];

}




message GetUserCustomersListReq {
  PageRequest PageRequest = 1;
  uint64 UserId = 2;
}

message GetUserCustomersListResp {
  repeated Customer UserCustomersList = 1;
  uint64 Total = 2;
}

message Customer {
  uint64 Id  = 1;
  string Cover = 2;
  string Nickname  = 3;
  string Mobile = 4;
  // @inject_tag: json:"reg_time"
  string RegTime = 5 [json_name = "reg_time"];
}

message JoinBonusReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"sid"
  uint64 SId = 2 [json_name = "sid", (validate.rules).uint64 = {gte: 1}];
}





message NewProjectReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"p_id"
  uint64 PId = 2 [json_name = "p_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"user_project_name"
  string UserProjectName = 3 [json_name = "user_project_name", (validate.rules).string = {min_len: 2}];
}

message ExecProjectReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"p_id"
  uint64 PId = 2 [json_name = "p_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"pre_id"
  uint64 PreId = 3 [json_name = "pre_id", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"re_execute"
  bool ReExecute = 4 [json_name = "re_execute"];
}

message ReqProjectAuthorityReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"no"
  string No = 2 [json_name = "no", (validate.rules).string = {min_len: 1}];
}

message ConfirmProjectAuthorityReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"no"
  string No = 2 [json_name = "no", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"path"
  string Path = 3 [json_name = "path", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"check_status"
  uint64 CheckStatus = 4 [json_name = "check_status"];
  // @inject_tag: json:"media_metadata"
  MediaMetadata MediaMetadata = 5 [json_name = "media_metadata"];
}

message MediaMetadata{
  uint64 Duration = 1;
  uint64 Width = 2;
  uint64 Height = 3;
}

message ReqProjectAuthorityResp {
  // @inject_tag: json:"token"
  TxCosTmpToken Token = 1 [json_name = "token"];
  // @inject_tag: json:"bucket"
  Bucket Bucket = 2 [json_name = "bucket"];
  // @inject_tag: json:"success"
  bool Success = 3 [json_name = "success"];
  // @inject_tag: json:"remote_path"
  string RemotePath = 4 [json_name = "remote_path"];
  // @inject_tag: json:"err_msg"
  string ErrMsg = 5 [json_name = "err_msg"];
  // @inject_tag: json:"max_size"
  uint64 MaxSize = 6 [json_name = "max_size"];
  // @inject_tag: json:"min_size"
  uint64 MinSize = 7 [json_name = "min_size"];
}

message ReqProjectUploadAuthReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"no"
  string No = 2 [json_name = "no", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"file_ext"
  string FileExt = 3 [json_name = "file_ext", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"content_length"
  int64 ContentLength = 4 [json_name = "content_length"];
}

message ReqProjectUploadAuthResp {
  // @inject_tag: json:"url"
  string Url = 1 [json_name = "url"];
  // @inject_tag: json:"path"
  string Path = 2 [json_name = "path"];
  // @inject_tag: json:"content_type"
  string ContentType = 3 [json_name = "content_type"];
  // @inject_tag: json:"content_disposition"
  string ContentDisposition = 4 [json_name = "content_disposition"];
  // @inject_tag: json:"content_length"
  int64 ContentLength = 5 [json_name = "content_length"];
}

message Bucket {
  // @inject_tag: json:"app_id"
  int64 AppID = 1 [json_name = "app_id"];
  // @inject_tag: json:"region"
  string Region = 2 [json_name = "region"];
  // @inject_tag: json:"name"
  string Name = 3 [json_name = "name"];
  // @inject_tag: json:"storage_type"
  string StorageType = 4 [json_name = "storage_type"];
}

message TxCosTmpToken {
  // @inject_tag: json:"tmp_secret_id"
  string TmpSecretId = 1 [json_name = "tmp_secret_id"];
  // @inject_tag: json:"tmp_secret_key"
  string TmpSecretKey  = 2 [json_name = "tmp_secret_key"];
  // @inject_tag: json:"session_token"
  string SessionToken  = 3 [json_name = "session_token"];
  // @inject_tag: json:"expired_time"
  int64 ExpiredTime = 4 [json_name = "expired_time"];
}

message UserProjectsResp {
  // @inject_tag: json:"list"
  repeated UserProject List = 1 [json_name = "list"];
  uint64 Total = 2;
}

message UpdateUserProjectInfoReq {
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserId = 2 [json_name = "user_id"];
  // @inject_tag: json:"cover"
  string Cover = 6 [json_name = "cover"];
  // @inject_tag: json:"name"
  string Name = 7 [json_name = "name"];
  // @inject_tag: json:"publish"
  uint64 Publish = 8 [json_name = "publish"];
  // @inject_tag: json:"points"
  uint64 Points = 9 [json_name = "points"];
  // @inject_tag: json:"category_id"
  uint64 CategoryId = 10 [json_name = "category_id"];
  // @inject_tag: json:"emotional_id"
  uint64 EmotionalID = 11 [json_name = "emotional_id"];
  // @inject_tag: json:"status"
  uint64 Status = 12 [json_name = "status"];
}

message UserProject {
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserId = 2 [json_name = "user_id"];
  // @inject_tag: json:"is_draft"
  uint64 IsDraft = 3 [json_name = "is_draft"];
  // @inject_tag: json:"project_id"
  uint64 ProjectId = 4 [json_name = "project_id"];
  // @inject_tag: json:"status"
  uint64 Status = 5 [json_name = "status"];
  // @inject_tag: json:"cover"
  string Cover = 6 [json_name = "cover"];
  // @inject_tag: json:"name"
  string Name = 7 [json_name = "name"];
  // @inject_tag: json:"earnings"
  uint64 Earnings = 8 [json_name = "earnings"];
  // @inject_tag: json:"name"
  uint64 Favorites = 9 [json_name = "favorites"];
  // @inject_tag: json:"references"
  uint64 References = 10 [json_name = "references"];
  // @inject_tag: json:"create_time"
  string CreateTime = 11 [json_name = "create_time"];
  // @inject_tag: json:"publish"
  uint64 Publish = 12 [json_name = "publish"];
  // @inject_tag: json:"points"
  uint64 Points = 13 [json_name = "points"];
  // @inject_tag: json:"last_failure"
  string LastFailure = 14 [json_name = "last_failure"];
  // @inject_tag: json:"ai_category"
  AiCategory AiCategory = 15 [json_name = "ai_category"];
  // @inject_tag: json:"ai_user_token"
  repeated AiUserToken AiUserToken = 16 [json_name = "ai_user_token"];
  // @inject_tag: json:"ai_user_materials"
  repeated AiUserMaterials AiUserMaterials = 17 [json_name = "ai_user_materials"];
  // @inject_tag: json:"emotional_template"
  EmotionalTemplate EmotionalTemplate = 18 [json_name = "emotional_template"];
}

message UserProjectReq {
  PageRequest PageRequest = 1;
  // @inject_tag: json:"id"
  uint64 ID = 2 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserId = 3 [json_name = "user_id"];
  // @inject_tag: json:"status"
  int32 Status = 4 [json_name = "status"];
}

message CategoryListResp{
  // @inject_tag: json:"accounts"
  repeated AiCategory List = 1 [json_name = "list"];
}

message AiCategory {
  // @inject_tag: json:"name"
  string Name = 1 [json_name = "name"];
  // @inject_tag: json:"id"
  uint64 ID = 2 [json_name = "id"];
}

message AiUserToken {
  // @inject_tag: json:"no"
  string No = 1 [json_name = "no"];
  // @inject_tag: json:"order"
  uint64 Order = 2 [json_name = "order"];
  // @inject_tag: json:"status"
  uint64 Status = 3 [json_name = "status"];
  // @inject_tag: json:"materials_type"
  uint64 MaterialsType = 4 [json_name = "materials_type"];
}

message AiUserMaterials {
  // @inject_tag: json:"content"
  string Content = 1 [json_name = "content"];
  // @inject_tag: json:"type"
  uint64 Type = 2 [json_name = "type"];
  // @inject_tag: json:"token_no"
  string TokenNo = 3 [json_name = "token_no"];
  // @inject_tag: json:"duration"
  uint64 Duration = 4 [json_name = "duration"];
  // @inject_tag: json:"width"
  uint64 Width = 5 [json_name = "width"];
  // @inject_tag: json:"height"
  uint64 Height = 6 [json_name = "height"];
}

message ExecProjectCallbackReq {
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"status"
  bool Status = 2 [json_name = "status"];
  // @inject_tag: json:"message"
  string Message = 3 [json_name = "message"];
}

message DeleteUserProjectInfoReq{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserID = 2 [json_name = "user_id"];
}

message PublishReq{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserID = 2 [json_name = "user_id"];
  // @inject_tag: json:"point"
  uint64 Point = 3 [json_name = "point"];
  // @inject_tag: json:"category_id"
  uint64 CategoryID = 4 [json_name = "category_id"];
}

message CommunityListReq{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"category_id"
  int32 CategoryId = 2 [json_name = "category_id"];
  PageRequest PageRequest = 3;
}

message EmotionalListResp{
  // @inject_tag: json:"accounts"
  repeated EmotionalTemplate List = 1 [json_name = "list"];
}

message EmotionalTemplate{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"id"
  string Name = 2 [json_name = "name"];
  // @inject_tag: json:"id"
  string Text = 3 [json_name = "text"];
}

message FavoriteProjectReq{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserID = 2 [json_name = "user_id"];
}

message FavoriteListReq{
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id"];
  // @inject_tag: json:"user_id"
  uint64 UserID = 2 [json_name = "user_id"];
  PageRequest PageRequest = 3;
}

message FavoriteStatusByIDReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  // @inject_tag: json:"ids"
  repeated uint64 IDS = 2 [json_name = "ids"];
}

message FavoriteStatusByIDResp{
  // @inject_tag: json:"status"
  repeated bool Status = 2 [json_name = "status"];
}

message BuyProjectReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  // @inject_tag: json:"buy_project_id"
  repeated uint64 BuyProjectID = 2 [json_name = "buy_project_id"];
  // @inject_tag: json:"project_id"
  uint64 ProjectID = 3 [json_name = "project_id"];
}

message IncomeExpenditureReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  PageRequest PageRequest = 2;
}

message IncomeExpenditureResp{
  // @inject_tag: json:"total"
  uint64 Total = 1 [json_name = "total"];
  // @inject_tag: json:"list"
  repeated IncomeExpenditure List = 2 [json_name = "list"];
}

message IncomeExpenditure{
  // @inject_tag: json:"project_name"
  string ProjectName = 1 [json_name = "project_name"];
  // @inject_tag: json:"created_time"
  string CreatedTime = 2 [json_name = "created_time"];
  // @inject_tag: json:"buyer_name"
  string BuyerName = 3 [json_name = "buyer_name"];
  // @inject_tag: json:"points"
  int64 Points = 4 [json_name = "points"];
}

message ConfirmBuyProjectLogsReq{
  // @inject_tag: json:"ids"
  repeated uint64 IDS = 1 [json_name = "ids"];
}

// 用户网络同步请求
message SyncUserNetworkReq {
  uint64 user_id = 1;       // 用户ID
  uint64 father_id = 2;     // 上级用户ID
  string network_type = 3;  // 网络类型
  int64 timestamp = 4;      // 时间戳
}

// 用户网络同步响应
message SyncUserNetworkResp {
  bool success = 1;         // 是否成功
}

service User {
  // 微信注册并登录
  rpc LoginAndRegister (UserLoginReq) returns (UserTokeReq);
  // 生成jwtToken
  rpc GetUserToken (UserTokeReq) returns(UserTokeResp);
  // 获取用户信息
  rpc GetUserInfo (IDReq) returns(UserInfoResp);
  // 获取用户信息详情
  rpc GetUserDetail (IDReq) returns(UserDetailResp);
  // 更新用户信息
  rpc ModifyUserInfo (ModifyUserInfoReq) returns(BoolReq);
  // 获取用户客户列表
  rpc GetUserCustomers (GetUserCustomersListReq) returns(GetUserCustomersListResp);
  // 获取结算明细
  rpc GetUserSettlementDetail(GetUserSettlementDetailReq) returns (GetUserSettlementDetailResp);
  // 获取用户粉丝列表
  rpc GetUserFansList (GetUserFansListReq) returns (GetUserFansListResp);
  // 获取用户收益统计
  rpc GetUserIncomeStat (GetUserIncomeStatReq) returns (GetUserIncomeStatResp);
  // 获取用户订单列表
  rpc GetUserOrders(GetUserOrdersReq) returns(GetUserOrdersResp);
  // 同步用户网络关系
  rpc SyncUserNetwork(SyncUserNetworkReq) returns (SyncUserNetworkResp);
}

service Tasks {
  // 新的分享任务建立用户状态
  rpc NewShareTask (NewShareTaskReq) returns (BoolReq);

  // 检查用户购买任务是否完成
  rpc UserBuyTaskCompletedCheck (IDReq) returns (BoolReq);
  // 用户任务
  rpc UserJobs (IDReq) returns (UserJobsResp);

  // 参与分红活动
  rpc JoinBonus(JoinBonusReq)  returns (BoolReq);
  // 资金结算
  rpc FundsSettlement(Empty) returns (BoolReq);

  // 项目分类列表
  rpc CategoryList(IDReq) returns (CategoryListResp);
  // 用户创建新的项目
  rpc NewProject(NewProjectReq) returns (IDReq);
  // 用户项目列表
  rpc UserProjects(UserProjectReq) returns (UserProjectsResp);
  // 用户项目信息
  rpc UserProjectInfo(UserProjectReq) returns (UserProject);
  // 更行用户项目信息
  rpc UpdateUserProjectInfo(UpdateUserProjectInfoReq) returns (BoolReq);
  // 删除用户项目信息
  rpc DeleteUserProjectInfo(DeleteUserProjectInfoReq) returns (BoolReq);
  // 执行用户项目
  rpc ExecProject(ExecProjectReq) returns (BoolReq);
  // 发布用户项目
  rpc PublishProject(PublishReq) returns (BoolReq);
  // 取消发布用户项目
  rpc UnPublishProject(PublishReq) returns (BoolReq);
  // 收藏用户项目
  rpc FavoriteProject(FavoriteProjectReq) returns (BoolReq);
  // 用户收藏列表
  rpc FavoriteList(FavoriteListReq) returns (UserProjectsResp);
  // 根据项目ID查询用户收藏状态
  rpc FavoriteStatusById(FavoriteStatusByIDReq) returns (FavoriteStatusByIDResp);
  // 回调项目执行结果
  rpc ExecProjectCallback(ExecProjectCallbackReq) returns (BoolReq);
  // 请求项目权限
  rpc ReqProjectAuthority(ReqProjectAuthorityReq) returns (ReqProjectAuthorityResp);
  // 请求项目上传URL
  rpc ReqProjectUploadAuth(ReqProjectUploadAuthReq) returns (ReqProjectUploadAuthResp);
  // 确认项目权限
  rpc ConfirmProjectAuthority(ConfirmProjectAuthorityReq) returns (BoolReq);
  // 购买产品扣除爆点
  rpc BuyProject(BuyProjectReq) returns(BoolReq);
  // 确认购买产品扣除爆点的日志
  rpc ConfirmBuyProjectLogs(ConfirmBuyProjectLogsReq) returns(BoolReq);
  // 收支明细
  rpc IncomeExpenditure(IncomeExpenditureReq) returns (IncomeExpenditureResp);
  // 更新热点项目
  rpc UpdateCommunityHotProject(Empty) returns (BoolReq);
  // 社区共享项目列表
  rpc CommunityList(CommunityListReq) returns (UserProjectsResp);
  // 用户项目信息
  rpc CommunityProjectInfo(IDReq) returns (UserProject);
  // 情绪列表
  rpc EmotionalList(Empty) returns (EmotionalListResp);
}

message LoadHistoryMessageReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  // @inject_tag: json:"conversation_id"
  uint64 ConversationId = 2 [json_name = "conversation_id"];
  PageRequest PageRequest = 3;
}

message LoadHistoryMessageResp{
  // @inject_tag: json:"list"
  repeated ChatHistoryMessage List = 1 [json_name = "list"];
  uint64 Total = 2 [json_name = "total"];
}

message ChatHistoryMessage{
  // @inject_tag: json:"answer"
  string Answer = 1 [json_name = "answer"];
  // @inject_tag: json:"question"
  string Question = 2 [json_name = "question"];
}

message AddHistoryMessageReq{
  // @inject_tag: json:"answer"
  string Answer = 1 [json_name = "answer"];
  // @inject_tag: json:"question"
  string Question = 2 [json_name = "question"];
  // @inject_tag: json:"user_id"
  uint64 UserID = 3 [json_name = "user_id"];
  // @inject_tag: json:"conversation_id"
  string ConversationId = 4 [json_name = "conversation_id"];
}

service RagChat {
  // 从数据库加载历史消息
  rpc LoadHistoryMessage(LoadHistoryMessageReq) returns (LoadHistoryMessageResp);
  // 添加消息到数据库和内存缓存
  rpc AddHistoryMessage(AddHistoryMessageReq) returns (BoolReq);
  // 清空当前会话历史
  rpc ClearHistoryMessage(LoadHistoryMessageReq) returns (BoolReq);
  // 获取用户的所有会话列表
//  rpc UserHistoryMessageList(AddHistoryMessageReq) returns (BoolReq);
}

//***************************************NAS相关接口***************************************
message SetPassReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  // @inject_tag: json:"password"
  string Password = 2 [json_name = "password", (validate.rules).string = {pattern: "^[0-9]{6}$"}];
  // @inject_tag: json:"re_password"
  string RePassword = 3 [json_name = "re_password", (validate.rules).string = {pattern: "^[0-9]{6}$"}];
}
message CheckPassReq{
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
  // @inject_tag: json:"password"
  string Password = 2 [json_name = "password"];
}
message FolderDeleteReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 2 [json_name = "folder_id", (validate.rules).uint64 = {gte: 1}];
}
message FolderRenameReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 2 [json_name = "folder_id", (validate.rules).uint64 = {gte: 1}];
  string NewName = 3 [json_name = "new_name", (validate.rules).string = {min_len: 1}];
}
message FolderCreateReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"folder_name"
  string FolderName = 2 [json_name = "folder_name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"parent_folder_id"
  uint64 ParentFolderId = 3 [json_name = "parent_folder_id", (validate.rules).uint64 = {gte: 1}];
}
message FolderInfoReq{
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 1 [json_name = "folder_id"];
}
message FolderInfoResp{
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 1 [json_name = "folder_id"];
  // @inject_tag: json:"folder_name"
  string FolderName = 2 [json_name = "folder_name"];
  // @inject_tag: json:"folder_path"
  string FolderPath = 3 [json_name = "folder_path"];
  // @inject_tag: json:"folder_id_path"
  string FolderIdPath = 4 [json_name = "folder_id_path"];
}
message DocumentCreateReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"file_name"
  string FileName = 2 [json_name = "file_name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"file_path"
  string FilePath = 3 [json_name = "file_path", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"file_size"
  uint64 FileSize = 4 [json_name = "file_size"];
  // @inject_tag: json:"mime_type"
  string MimeType = 5 [json_name = "mime_type"];
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 6 [json_name = "folder_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"checksum"
  string Checksum = 7 [json_name = "checksum"]; // 文件校验和，用于验证文件完整性
  // @inject_tag: json:"summary"
  string Summary = 8 [json_name = "summary"];
  // @inject_tag: json:"tags"
  repeated string Tags = 9 [json_name = "tags"];
}
message DocumentListReq {
  PageRequest PageRequest = 1;
  uint64 UserId = 2 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
  uint64 FolderId = 3 [json_name = "folder_id", (validate.rules).uint64 = {gte: 1}];
}
message DocumentItem{
  // @inject_tag: json:"file_id"
  uint64 Id = 1 [json_name = "id"];
  // @inject_tag: json:"file_name"
  string Name = 2 [json_name = "name"];
  // @inject_tag: json:"file_size"
  uint64 FileSize = 3 [json_name = "file_size"];
  // @inject_tag: json:"mime_type"
  string MimeType = 4 [json_name = "mime_type"];
  // @inject_tag: json:"update_time"
  string UpdateTime = 5 [json_name = "update_time"];
}
message DocumentListResp{
  // @inject_tag: json:"list"
  repeated DocumentItem List = 1 [json_name = "list"];
  uint64 Total = 2 [json_name = "total"];
  uint64 ParentFolderId = 3 [json_name = "parent_folder_id"]; // 上级文件夹ID
}
message DocumentCreateResp{
  // @inject_tag: json:"file_id"
  uint64 FileId = 1 [json_name = "file_id"];
}
message FolderCreateResp{
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 1 [json_name = "folder_id"];
}
message DocumentDeleteReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"document_id"
  uint64 DocumentId = 2 [json_name = "document_id", (validate.rules).uint64 = {gte: 1}];
}
message DocumentRenameReq{
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"document_id"
  uint64 DocumentId = 2 [json_name = "document_id", (validate.rules).uint64 = {gte: 1}];
  string NewName = 3 [json_name = "new_name", (validate.rules).string = {min_len: 1}];
}
message DocumentInfoReq{
  // @inject_tag: json:"document_id"
  uint64 DocumentId = 1 [json_name = "document_id", (validate.rules).uint64 = {gte: 1}];
  uint64 UserId = 2 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];

}
message DocumentInfoResp{
  // @inject_tag: json:"file_id"
  uint64 DocumentId = 1 [json_name = "file_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"file_name"
  string DocumentName = 2 [json_name = "file_name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"file_size"
  uint64 FileSize = 3 [json_name = "file_size"];
  // @inject_tag: json:"mime_type"
  string MimeType = 4 [json_name = "mime_type"];
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 5 [json_name = "folder_id"];
  // @inject_tag: json:"file_path"
  string FilePath = 6 [json_name = "file_path"];
  // @inject_tag: json:"user_id"
  uint64 UserId = 7 [json_name = "user_id"];
  // @inject_tag: json:"uploader_id"
  uint64 UploaderId = 8 [json_name = "uploader_id"];
  // @inject_tag: json:"update_time"
  string UpdateTime = 9 [json_name = "update_time"];
  // @inject_tag: json:"create_time"
  string CreateTime = 10 [json_name = "create_time"];
  // @inject_tag: json:"tags"
  repeated string Tags = 11 [json_name = "tags"];
  // @inject_tag: json:"file_path"
  string Description = 12 [json_name = "description"];
}

message DocumentCheckExistReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"checksum"
  string Checksum = 2 [json_name = "checksum", (validate.rules).string = {min_len: 1}];
}

message DocumentCheckExistResp {
  // @inject_tag: json:"exists"
  bool Exists = 1 [json_name = "exists"];
  // @inject_tag: json:"file_id"
  uint64 FileId = 2 [json_name = "file_id"];
  // @inject_tag: json:"file_name"
  string FileName = 3 [json_name = "file_name"];
}

message FolderTreeNodeResp {
  // @inject_tag: json:"id"
  uint64 Id = 1 [json_name = "id"];
  // @inject_tag: json:"name"
  string Name = 2 [json_name = "name"];
  // @inject_tag: json:"parent_id"
  uint64 ParentId = 3 [json_name = "parent_id"];
  // @inject_tag: json:"children"
  repeated FolderTreeNodeResp Children = 4 [json_name = "children"];
}

message FolderTreeReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];
  // @inject_tag: json:"folder_id"
  uint64 FolderId = 2 [json_name = "folder_id"];
}

message FolderTreeResp {
  // @inject_tag: json:"list"
  repeated FolderTreeNodeResp List = 1 [json_name = "list"];
}



message CheckPassSetReq {
  // @inject_tag: json:"user_id"
  uint64 UserID = 1 [json_name = "user_id"];
}

message CheckPassSetResp {
  // @inject_tag: json:"is_set"
  bool IsSet = 1 [json_name = "is_set"];
}
message DocumentListAppReq {
  PageRequest PageRequest = 1;
  uint64 UserId = 2 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
}

message DocumentListAppResp{
  // @inject_tag: json:"list"
  repeated DocumentItem List = 1 [json_name = "list"];
  uint64 Total = 2 [json_name = "total"];
}

message DocumentMoveReq {
    repeated uint64 FileIds = 1 [(validate.rules).repeated = {min_items: 1, items: {uint64: {gt: 0}}}];
    uint64 ToFolderId = 2 [(validate.rules).uint64 = {gte: 0}];
    uint64 UserId = 3 [json_name = "user_id", (validate.rules).uint64 = {gte: 1}];
}
message DocumentMoveResp {
  // @inject_tag: json:"list"
  repeated DocumentMoveItem List = 1 [json_name = "list"];
}

message DocumentMoveItem {
  // @inject_tag: json:"file_name"
  string FileName = 1 [json_name = "file_name"];
  // @inject_tag: json:"from_folder"
  string FromFolder = 2 [json_name = "from_folder"];
  // @inject_tag: json:"to_folder" 
  string ToFolder = 3 [json_name = "to_folder"];
}

service Nas {
  // 检查授权密码
  rpc SetPass(SetPassReq) returns (BoolReq);
  rpc CheckPass(CheckPassReq) returns (BoolReq);
  // 检查密码是否设置
  rpc CheckPassSet(CheckPassSetReq) returns (CheckPassSetResp);
  // 文件夹操作
  rpc FolderCreate(FolderCreateReq) returns (FolderCreateResp);
  rpc FolderDelete(FolderDeleteReq) returns (BoolReq);
  rpc FolderRename(FolderRenameReq) returns (BoolReq);
  rpc FolderInfo(FolderInfoReq) returns (FolderInfoResp);
  rpc FolderTree(FolderTreeReq) returns (FolderTreeResp); // 获取目录树
  // 文件操作
  rpc DocumentCreate(DocumentCreateReq) returns (DocumentCreateResp);
  rpc DocumentDelete(DocumentDeleteReq) returns (BoolReq);
  rpc DocumentRename(DocumentRenameReq) returns (BoolReq);
  rpc DocumentInfo(DocumentInfoReq) returns (DocumentInfoResp);
  rpc DocumentList(DocumentListReq) returns (DocumentListResp);
  rpc DocumentCheckExist(DocumentCheckExistReq) returns (DocumentCheckExistResp);
  rpc DocumentListApp(DocumentListAppReq) returns (DocumentListAppResp);
  rpc DocumentMove(DocumentMoveReq) returns (DocumentMoveResp);
}

// 粉丝列表请求
message GetUserFansListReq {
  uint64 user_id = 1;
  PageRequest page_request = 2;
}

// 粉丝列表响应
message GetUserFansListResp {
  repeated UserFansItem list = 1;
  int64 total = 2;
  int64 today_count = 3;
  int64 yest_count = 4;
}

// 粉丝项
message UserFansItem {
  uint64 id = 1;
  string nick_name = 2;
  string avatar = 3;
  string created_at = 4;
  string mobile = 5;          // 手机号
  int64  level = 6;
}

// 用户收益统计请求
message GetUserIncomeStatReq {
  uint64 user_id = 1;
}

// 用户收益统计响应
message GetUserIncomeStatResp {
  double balance = 1;           // 我的余额（分）
  double today_income = 2;      // 今日收益（分）
  double yesterday_income = 3;  // 昨日收益（分）
  double current_month_income = 4; // 本月收益（分）
  double last_month_income = 5; // 上月收益（分）
  int64 order_count = 6;       // 订单总数
  int64 fans_count = 7;        // 粉丝总数
  double withdraw = 8;           // 可提现（分）
  double settlement = 9;           // 已结算金额（分）
}

// 获取用户订单列表请求
message GetUserOrdersReq {
  int64 userId = 1;                // 用户ID
  int64 page = 2;                  // 页码
  int64 pageSize = 3;              // 每页数量
  int32 commissionStatus = 4;      // 佣金单状态：0-全部，20-未结算，100-已结算，200-取消结算
}

// 订单信息
message OrderInfo {
  string orderID = 1;              // 订单号
  string productTitle = 2;         // 商品标题
  string productThumbImg = 3;      // 商品缩略图
  int64 actualPayment = 4;         // 支付金额(分)
  int64 sharerAmount = 5;          // 预估收益(分)
  string orderStatus = 6;           // 订单状态
  string commissionStatus = 7;      // 佣金单状态
  int64 createTime = 8;            // 下单时间戳
}

// 获取用户订单列表响应
message GetUserOrdersResp {
  repeated OrderInfo orders = 1;   // 订单列表
  int64 total = 2;                 // 总数量
}

// 用户提现请求
message UserWithdrawReq {
  double withdraw = 1; // 提现金额
  uint64 user_id = 2;  // 用户ID
}

// 获取用户推荐关系链请求
message GetUserReferralChainReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
}

// 获取用户推荐关系链响应
message GetUserReferralChainResp {
  // @inject_tag: json:"direct_referrer"
  UserInfo DirectReferrer = 1;  // 直接上级
  // @inject_tag: json:"indirect_referrer"
  UserInfo IndirectReferrer = 2;  // 上级的上级
}

// 处理订单福利任务请求
message ProcessOrderBenefitsTaskReq {
  // @inject_tag: json:"order_id"
  string OrderId = 1 [(validate.rules).string = {min_len: 1}];  // 订单ID
}

// 处理订单福利任务响应
message ProcessOrderBenefitsTaskResp {
  // @inject_tag: json:"success"
  bool Success = 1 [json_name = "success"];  // 处理结果
  // @inject_tag: json:"message"
  string Message = 2 [json_name = "message"];  // 处理消息
}

// 处理用户推荐福利任务请求
message ProcessUserReferralBenefitsReq {
  // @inject_tag: json:"batch_size"
  int32 BatchSize = 1 [json_name = "batch_size"];  // 批处理大小，默认100
}

// 处理用户推荐福利任务响应
message ProcessUserReferralBenefitsResp {
  // @inject_tag: json:"success"
  bool Success = 1 [json_name = "success"];  // 处理结果
  // @inject_tag: json:"message"
  string Message = 2 [json_name = "message"];  // 处理消息
  // @inject_tag: json:"processed_count"
  int32 ProcessedCount = 3 [json_name = "processed_count"];  // 处理用户数
  // @inject_tag: json:"updated_count"
  int32 UpdatedCount = 4 [json_name = "updated_count"];  // 更新福利池数
}

// 获取随机福利池请求
message GetRandomBenefitsPoolReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID，可选，用于排除自己
}

// 福利池信息
message BenefitsPoolInfo {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [json_name = "user_id"];  // 用户ID
  // @inject_tag: json:"type"
  int32 Type = 2 [json_name = "type"];  // 福利池类型
  // @inject_tag: json:"quantity"
  int32 Quantity = 3 [json_name = "quantity"];  // 福利池数量
  // @inject_tag: json:"user_info"
  UserInfo UserInfo = 4 [json_name = "user_info"];  // 用户信息
}

// 获取随机福利池响应
message GetRandomBenefitsPoolResp {
  // @inject_tag: json:"type3_pool"
  BenefitsPoolInfo Type3Pool = 1 [json_name = "type3_pool"];  // 类型3福利池
  // @inject_tag: json:"type4_pool"
  BenefitsPoolInfo Type4Pool = 2 [json_name = "type4_pool"];  // 类型4福利池
}

// 处理用户网体变更请求
message ProcessNetworkChangeReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
}

// 处理用户网体变更响应
message ProcessNetworkChangeResp {
  // @inject_tag: json:"success"
  bool Success = 1 [json_name = "success"];  // 处理结果
  // @inject_tag: json:"message"
  string Message = 2 [json_name = "message"];  // 处理消息
  // @inject_tag: json:"changed_users"
  repeated uint64 ChangedUsers = 3 [json_name = "changed_users"];  // 变更的用户ID列表
}

// 获取用户任务完成情况请求
message GetUserTasksStatusReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
}

// 获取用户任务完成情况响应
message GetUserTasksStatusResp {
  // @inject_tag: json:"direct_referrer"
  bool DirectReferrer = 1 [json_name = "direct_referrer"];  // 任务1：直接推荐人
  // @inject_tag: json:"indirect_referrer"
  bool IndirectReferrer = 2 [json_name = "indirect_referrer"];  // 任务2：间接推荐人
  // @inject_tag: json:"type3_pool"
  bool Type3Pool = 3 [json_name = "type3_pool"];  // 任务3：类型3福利池
  // @inject_tag: json:"type4_pool"
  bool Type4Pool = 4 [json_name = "type4_pool"];  // 任务4：类型4福利池
}

// 获取用户任务完成进度请求
message GetUserTasksProgressReq {
  // @inject_tag: json:"user_id"
  uint64 UserId = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
}

// 获取用户任务完成进度响应
message GetUserTasksProgressResp {
  // @inject_tag: json:"direct_referrer"
  int32 DirectReferrer = 1 [json_name = "direct_referrer"];  // 任务1：直接推荐人订单数
  // @inject_tag: json:"indirect_referrer"
  int32 IndirectReferrer = 2 [json_name = "indirect_referrer"];  // 任务2：间接推荐人订单数
  // @inject_tag: json:"type3_pool"
  int32 Type3Pool = 3 [json_name = "type3_pool"];  // 任务3：类型3福利池订单数
  // @inject_tag: json:"type4_pool"
  int32 Type4Pool = 4 [json_name = "type4_pool"];  // 任务4：类型4福利池订单数
}

// 记录用户浏览商品信息请求
message RecordProductViewReq {
  uint64 UserId = 1;       // 用户ID
  uint64 SellerId = 2;      //
  uint64 ProductId = 3;    // 商品ID
  string ShopAppid = 4;    // 店铺AppID
  int32 PoolType = 5;      // 任务池类型
}

// 记录用户浏览商品信息响应
message RecordProductViewResp {
  bool success = 1;
  string message = 2;
}

// 新人福利服务
service Benefits {
  // 记录用户浏览商品信息
  rpc RecordProductView(RecordProductViewReq) returns (RecordProductViewResp);
  // 获取用户任务完成情况
  rpc GetUserTasksStatus (GetUserTasksStatusReq) returns (GetUserTasksStatusResp);
  // 获取用户任务完成进度
  rpc GetUserTasksProgress (GetUserTasksProgressReq) returns (GetUserTasksProgressResp);
  // 获取用户推荐关系链
  rpc GetUserReferralChain (GetUserReferralChainReq) returns (GetUserReferralChainResp);
  // 处理订单福利任务
  rpc ProcessOrderBenefitsTask (ProcessOrderBenefitsTaskReq) returns (ProcessOrderBenefitsTaskResp);
  // 处理用户推荐福利任务
  rpc ProcessUserReferralBenefits (ProcessUserReferralBenefitsReq) returns (ProcessUserReferralBenefitsResp);
  // 处理用户助力福利任务
  rpc ProcessUserHelpBenefits (ProcessUserReferralBenefitsReq) returns (ProcessUserReferralBenefitsResp);
  // 获取随机福利池
  rpc GetRandomBenefitsPool (GetRandomBenefitsPoolReq) returns (GetRandomBenefitsPoolResp);
  // 处理用户网体变更
  rpc ProcessNetworkChange (ProcessNetworkChangeReq) returns (ProcessNetworkChangeResp);
}