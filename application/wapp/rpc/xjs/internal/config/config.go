package config

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	BizRedis redis.RedisConf
	// 添加RabbitMQ配置
	RabbitMQ struct {
		Host        string
		Port        int
		Username    string
		Password    string
		VirtualHost string
		// 默认参数
		DefaultParams struct {
			Durable    bool `json:",default=true"`
			AutoDelete bool `json:",default=false"`
			Exclusive  bool `json:",default=false"`
			NoWait     bool `json:",default=false"`
			Mandatory  bool `json:",default=false"`
			Immediate  bool `json:",default=false"`
		}
		// 队列配置
		Queues map[string]struct {
			Exchange   string
			RoutingKey string
			QueueName  string
		}
	}
	DB struct {
		Master struct {
			DataSource   string
			MaxOpenConns int `json:",default=10"`
			MaxIdleConns int `json:",default=100"`
			MaxLifetime  int `json:",default=3600"`
		}
		Slaves []struct {
			DataSource   string
			MaxOpenConns int `json:",default=10"`
			MaxIdleConns int `json:",default=100"`
			MaxLifetime  int `json:",default=3600"`
		}
	}
	JwtAuth struct {
		AccessSecret  string
		AccessExpire  int64
		RefreshSecret string
		RefreshExpire int64
		Issuer        string
	}
	WxApp struct {
		AccessTokenPrefix string
		AppId             string
		AppSecret         string
		AesSecret         string
	}
	BLParams struct {
		MaxBuyLevel             int8
		PeerLevelGroup          int8
		DirectRegisterUserOwner uint64
	}
	NewUserRewards struct {
		Point struct {
			Register int64
			Share    int64
		}
		Exposure struct {
			NewUser  int64
			Talent1  int64
			Talent12 int64
			Talent48 int64
			Talent99 int64
		}
	}
	WxLeague struct {
		AccessTokenPrefix string
		AppId             string
		AppSecret         string
		AesSecret         string
	}
	XjsRPC zrpc.RpcClientConf
	PfsRPC zrpc.RpcClientConf
	AiRPC  zrpc.RpcClientConf
	EcRPC  zrpc.RpcClientConf
}
