package benefitslogic

import (
	"context"
	"errors"
	"gorm.io/gorm"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserTasksProgressLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserTasksProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserTasksProgressLogic {
	return &GetUserTasksProgressLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户任务完成进度
func (l *GetUserTasksProgressLogic) GetUserTasksProgress(in *pb.GetUserTasksProgressReq) (*pb.GetUserTasksProgressResp, error) {
	l.Infof("获取用户任务完成进度: userId=%d", in.UserId)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户ID不能为空")
	}

	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "获取数据库连接失败")
	}

	// 查询用户信息，获取unionID
	var user model.User
	if err := db.Where("id = ?", in.UserId).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("用户不存在: userId=%d", in.UserId)
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户不存在")
		}
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询用户信息失败")
	}

	if user.Unionid == "" {
		l.Errorf("用户unionID为空: userId=%d", in.UserId)
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户unionID为空")
	}

	// 初始化响应
	resp := &pb.GetUserTasksProgressResp{
		DirectReferrer:   0,
		IndirectReferrer: 0,
		Type3Pool:        0,
		Type4Pool:        0,
	}

	// 构建基础查询条件
	baseCondition := func() *gorm.DB {
		return db.Model(&model.CommissionOrders{}).
			Where("buyer_union_id = ?", user.Unionid).
			Where("commission_status IN (?, ?)", "20", "200").
			Where("order_status IN (?, ?, ?, ?)", "20", "21", "30", "100")
	}

	// 查询任务1：benefits_pool_no = 1的订单数
	var task1Count int64
	if err := baseCondition().Where("benefits_pool_no = ?", 1).Count(&task1Count).Error; err != nil {
		l.Errorf("查询任务1订单数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询任务1订单数失败")
	}
	resp.DirectReferrer = int32(task1Count)

	// 查询任务2：benefits_pool_no = 2的订单数
	var task2Count int64
	if err := baseCondition().Where("benefits_pool_no = ?", 2).Count(&task2Count).Error; err != nil {
		l.Errorf("查询任务2订单数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询任务2订单数失败")
	}
	resp.IndirectReferrer = int32(task2Count)

	// 查询任务3：benefits_pool_no = 3的订单数
	var task3Count int64
	if err := baseCondition().Where("benefits_pool_no = ?", 3).Count(&task3Count).Error; err != nil {
		l.Errorf("查询任务3订单数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询任务3订单数失败")
	}
	resp.Type3Pool = int32(task3Count)

	// 查询任务4：benefits_pool_no = 4的订单数
	var task4Count int64
	if err := baseCondition().Where("benefits_pool_no = ?", 4).Count(&task4Count).Error; err != nil {
		l.Errorf("查询任务4订单数失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询任务4订单数失败")
	}
	resp.Type4Pool = int32(task4Count)

	l.Infof("用户任务完成进度查询成功: userId=%d, task1=%d, task2=%d, task3=%d, task4=%d",
		in.UserId, resp.DirectReferrer, resp.IndirectReferrer, resp.Type3Pool, resp.Type4Pool)

	return resp, nil
}
