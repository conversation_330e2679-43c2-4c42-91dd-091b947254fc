package benefitslogic

import (
	"context"
	"errors"
	"fmt"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type ProcessNetworkChangeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProcessNetworkChangeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessNetworkChangeLogic {
	return &ProcessNetworkChangeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ProcessNetworkChange 处理用户网体变更
func (l *ProcessNetworkChangeLogic) ProcessNetworkChange(in *pb.ProcessNetworkChangeReq) (*pb.ProcessNetworkChangeResp, error) {
	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户ID不能为空")
	}

	l.Infof("开始处理用户网体变更: userId=%d", in.UserId)

	// 查询用户信息
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户不存在")
		}
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询用户信息失败")
	}

	// 查询用户的网络关系
	var userNetwork model.UserDynamicNetworks
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", in.UserId, "benefits").First(&userNetwork).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户网络关系不存在")
		}
		l.Errorf("查询用户网络关系失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询用户网络关系失败")
	}

	// 检查用户的任务完成情况
	taskBitMask := common.NewTaskBitmask(8)
	if userNetwork.Mark != "" {
		if err := taskBitMask.LoadFromBinary(userNetwork.Mark); err != nil {
			l.Errorf("解析任务标记失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("解析任务标记失败: %v", err))
		}
	}

	// 检查是否完成了所有任务（前4位都为1）
	if !taskBitMask.IsAllCompleted(4) {
		l.Infof("用户未完成所有任务，不进行网体变更: userId=%d, mark=%s", in.UserId, userNetwork.Mark)
		return &pb.ProcessNetworkChangeResp{
			Success: false,
			Message: "用户未完成所有任务，不进行网体变更",
		}, nil
	}

	// 查询用户的父级
	var parentNetwork model.UserDynamicNetworks
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ?", userNetwork.FatherID).First(&parentNetwork).Error; err != nil {
		l.Errorf("查询父级网络关系失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询父级网络关系失败")
	}

	// 查找系统根节点(label=system)
	var systemRootNetwork model.UserDynamicNetworks
	if err := l.svcCtx.GetSlaveDB().Where("label = ? AND network_type = ?", "system", "benefits").First(&systemRootNetwork).Error; err != nil {
		l.Errorf("查询系统根节点失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询系统根节点失败")
	}

	// 查找离系统根节点最近的祖宗节点
	var targetParentID uint64

	// 检查父级是否直接在系统根节点下
	if parentNetwork.FatherID == systemRootNetwork.UserID {
		// 如果父级直接在系统根节点下，则将用户移动到系统根节点下
		targetParentID = systemRootNetwork.UserID

	} else {
		// 否则，查找父级的上级链，直到找到系统根节点的直接子节点
		currentID := parentNetwork.FatherID
		var ancestorID uint64 = 0

		for currentID != systemRootNetwork.UserID && currentID != 0 {
			var ancestor model.UserDynamicNetworks
			if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", currentID, "benefits").First(&ancestor).Error; err != nil {
				l.Errorf("查询祖先节点失败: %v, currentID=%d", err, currentID)
				return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询祖先节点失败")
			}

			// 如果当前节点的父级是系统根节点，则找到了目标节点
			if ancestor.FatherID == systemRootNetwork.UserID {
				ancestorID = ancestor.UserID
				break
			}

			// 继续向上查找
			currentID = ancestor.FatherID
		}

		// 如果找到了系统根节点的直接子节点，则使用它作为目标父级
		if ancestorID != 0 {
			targetParentID = ancestorID
		} else {
			// 如果没有找到，则使用系统根节点作为目标父级
			targetParentID = systemRootNetwork.UserID
		}
	}

	// 查询父级直推的用户数量和列表
	var directUsers []model.UserDynamicNetworks
	if err := l.svcCtx.GetSlaveDB().Where("father_id = ? AND network_type = ?", userNetwork.FatherID, "benefits").Find(&directUsers).Error; err != nil {
		l.Errorf("查询父级直推用户失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询父级直推用户失败")
	}

	// 检查父级直推用户数量是否大于等于BenefitsDirectSharers
	if len(directUsers) < BenefitsDirectSharersForNetworkChange {
		l.Infof("父级直推用户数量不足%d个，不进行网体变更: fatherId=%d, directCount=%d", BenefitsDirectSharersForNetworkChange, userNetwork.FatherID, len(directUsers))
		return &pb.ProcessNetworkChangeResp{
			Success: false,
			Message: fmt.Sprintf("父级直推用户数量不足%d个，不进行网体变更", BenefitsDirectSharersForNetworkChange),
		}, nil
	}

	// 查找已完成任务的直推用户（至少需要BenefitsDirectSharers个）
	var completedUsers []model.UserDynamicNetworks
	for _, du := range directUsers {
		duTaskBitMask := common.NewTaskBitmask(8)
		if du.Mark != "" {
			if err := duTaskBitMask.LoadFromBinary(du.Mark); err != nil {
				l.Errorf("解析直推用户任务标记失败: %v", err)
				continue
			}
		}

		if duTaskBitMask.IsAllCompleted(4) {
			completedUsers = append(completedUsers, du)
			if len(completedUsers) >= BenefitsDirectSharersForNetworkChange {
				break
			}
		}
	}

	// 检查已完成任务的直推用户数量是否大于等于BenefitsDirectSharersForNetworkChange
	if len(completedUsers) < BenefitsDirectSharersForNetworkChange {
		l.Infof("父级已完成任务的直推用户数量不足2个，不进行网体变更: fatherId=%d, completedCount=%d", userNetwork.FatherID, len(completedUsers))
		return &pb.ProcessNetworkChangeResp{
			Success: false,
			Message: fmt.Sprintf("父级已完成任务的直推用户数量不足%d个，不进行网体变更", BenefitsDirectSharersForNetworkChange),
		}, nil
	}

	// 开始事务，进行网体变更
	var changedUsers []uint64
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 1. 将已完成任务的直推用户（最多取BenefitsDirectSharers个）的父级改为目标父级（系统根节点的直接子节点或系统根节点）
		for i, cu := range completedUsers {
			if i >= BenefitsDirectSharersForNetworkChange {
				break
			}

			// 更新用户的父级
			if err := tx.Model(&model.UserDynamicNetworks{}).
				Where("id = ?", cu.ID).
				Update("father_id", targetParentID).Error; err != nil {
				return err
			}

			changedUsers = append(changedUsers, cu.UserID)
			l.Infof("已将用户的父级从 %d 更改为 %d: userId=%d", cu.FatherID, targetParentID, cu.UserID)
		}

		// 2. 将父级（d节点）移动到系统根节点下
		if err := tx.Model(&model.UserDynamicNetworks{}).
			Where("id = ?", parentNetwork.ID).
			Update("father_id", systemRootNetwork.UserID).Error; err != nil {
			return err
		}

		changedUsers = append(changedUsers, parentNetwork.UserID)
		l.Infof("已将父级从 %d 移动到系统根节点下: userId=%d", parentNetwork.FatherID, parentNetwork.UserID)

		return nil
	})

	if err != nil {
		l.Errorf("执行网体变更事务失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("执行网体变更事务失败: %v", err))
	}

	l.Infof("成功完成用户网体变更: userId=%d, changedUsers=%v", in.UserId, changedUsers)
	return &pb.ProcessNetworkChangeResp{
		Success:      true,
		Message:      "成功完成用户网体变更",
		ChangedUsers: changedUsers,
	}, nil
}
