package benefitslogic

import (
	"context"
	"errors"
	"fmt"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetUserTasksStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserTasksStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserTasksStatusLogic {
	return &GetUserTasksStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetUserTasksStatus 获取用户任务完成情况
func (l *GetUserTasksStatusLogic) GetUserTasksStatus(in *pb.GetUserTasksStatusReq) (*pb.GetUserTasksStatusResp, error) {
	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户ID不能为空")
	}

	l.Infof("开始查询用户任务完成情况: userId=%d", in.UserId)

	// 查询用户的网络关系
	var userNetwork model.UserDynamicNetworks
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", in.UserId, "benefits").First(&userNetwork).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户网络关系不存在")
		}
		l.Errorf("查询用户网络关系失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询用户网络关系失败")
	}

	// 初始化响应
	resp := &pb.GetUserTasksStatusResp{
		DirectReferrer:   false,
		IndirectReferrer: false,
		Type3Pool:        false,
		Type4Pool:        false,
	}

	// 如果mark为空，直接返回所有任务未完成
	if userNetwork.Mark == "" {
		return resp, nil
	}

	// 使用pkg包中的函数还原mark
	taskBitMask := common.NewTaskBitmask(8)
	if err := taskBitMask.LoadFromBitFormat([]byte(userNetwork.Mark)); err != nil {
		l.Errorf("解析任务标记失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("解析任务标记失败: %v", err))
	}

	// 设置任务完成状态
	resp.DirectReferrer = taskBitMask.IsTaskCompleted(1)   // 任务1
	resp.IndirectReferrer = taskBitMask.IsTaskCompleted(2) // 任务2
	resp.Type3Pool = taskBitMask.IsTaskCompleted(3)        // 任务3
	resp.Type4Pool = taskBitMask.IsTaskCompleted(4)        // 任务4

	l.Infof("用户任务完成情况: userId=%d, task1=%v, task2=%v, task3=%v, task4=%v",
		in.UserId, resp.DirectReferrer, resp.IndirectReferrer, resp.Type3Pool, resp.Type4Pool)

	return resp, nil
}
