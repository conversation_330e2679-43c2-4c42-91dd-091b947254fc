package benefitslogic

const (
	// BenefitsSingleTaskCompletedOrders 完成单项任务所需订单数
	BenefitsSingleTaskCompletedOrders = 2

	// BenefitsDirectSharersForNetworkChange 网体变更时直推用户数判定
	BenefitsDirectSharersForNetworkChange = 2

	// BenefitsFatherWindowGoods 上级的橱窗商品数量判定
	BenefitsFatherWindowGoods = 10

	// BenefitsPoolIncNewSharers 任务池奖励转化新手推客数判定
	BenefitsPoolIncNewSharers = 6

	// BenefitsPoolIncAllTaskCompletedUsers 帮扶完成所有任务池任务用户判定
	BenefitsPoolIncAllTaskCompletedUsers = 12

	// BenefitsReferralRewardsNums 福利推荐奖励总数
	BenefitsReferralRewardsNums = 2

	// BenefitsHelpRewardsNums 福利帮扶奖励总数
	BenefitsHelpRewardsNums = 200
)

const (
	BenefitsPollLogShareType = "share"
	BenefitsPollLogHelpType  = "help"
)
