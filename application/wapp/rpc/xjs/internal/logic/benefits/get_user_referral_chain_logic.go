package benefitslogic

import (
	"context"
	"fmt"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserReferralChainLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserReferralChainLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserReferralChainLogic {
	return &GetUserReferralChainLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetUserReferralChain 获取用户推荐关系链
func (l *GetUserReferralChainLogic) GetUserReferralChain(in *pb.GetUserReferralChainReq) (*pb.GetUserReferralChainResp, error) {

	// 参数验证
	if err := in.Validate(); err != nil {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 初始化响应
	resp := &pb.GetUserReferralChainResp{}

	// 查找用户的直接上级
	directReferrer, err := l.findValidReferrer(in.UserId)
	if err != nil {
		l.Logger.Errorf("查找用户[%d]的直接上级失败: %v", in.UserId, err)
		return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("查找用户[%d]的直接上级失败: %v", in.UserId, err))
	}

	if directReferrer != nil {
		// 将直接上级信息复制到响应中
		resp.DirectReferrer = &pb.UserInfo{}
		err = copier.Copy(resp.DirectReferrer, directReferrer)
		if err != nil {
			l.Logger.Errorf("复制直接上级信息失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("复制直接上级信息失败: %v", err))
		}

		// 确保OpenFinderId和OpenTalentId字段被正确复制
		resp.DirectReferrer.OpenFinderId = directReferrer.Openfinderid
		resp.DirectReferrer.OpenTalentId = directReferrer.Opentalentid
		resp.DirectReferrer.FinderId = directReferrer.FinderId

		// 查找上级的上级
		indirectReferrer, err := l.findValidReferrer(uint64(directReferrer.ID))
		if err != nil {
			l.Logger.Errorf("查找用户[%d]的间接上级失败: %v", directReferrer.ID, err)
			return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("查找用户[%d]的间接上级失败: %v", directReferrer.ID, err))
		}

		if indirectReferrer != nil {
			// 将上级的上级信息复制到响应中
			resp.IndirectReferrer = &pb.UserInfo{}
			err = copier.Copy(resp.IndirectReferrer, indirectReferrer)
			if err != nil {
				l.Logger.Errorf("复制间接上级信息失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("复制间接上级信息失败: %v", err))
			}

			// 确保OpenFinderId和OpenTalentId字段被正确复制
			resp.IndirectReferrer.OpenFinderId = indirectReferrer.Openfinderid
			resp.IndirectReferrer.OpenTalentId = indirectReferrer.Opentalentid
			resp.IndirectReferrer.FinderId = indirectReferrer.FinderId
		}
	}

	return resp, nil
}

// findValidReferrer 递归查找有效的上级（橱窗中商品数量大于等于BenefitsSingleTaskCompletedOrders件）
func (l *GetUserReferralChainLogic) findValidReferrer(userId uint64) (*model.User, error) {
	// 查询用户的直接上级ID
	var network model.UserDynamicNetworks
	err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", userId, "benefits").First(&network).Error
	if err != nil {
		if err.Error() == "record not found" {
			// 用户没有上级，返回nil
			return nil, nil
		}
		return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("查询用户网络关系失败: %v", err))
	}

	if network.FatherID == 0 {
		// 没有上级，返回nil
		return nil, nil
	}

	// 查询上级用户信息
	var referrer model.User
	err = l.svcCtx.GetSlaveDB().Where("id = ?", network.FatherID).First(&referrer).Error
	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, fmt.Sprintf("查询上级用户信息失败: %v", err))
	}

	// 检查上级的FinderId是否有效
	//if len(referrer.Openfinderid) <= 0 || len(referrer.FinderId) <= 0 {
	if len(referrer.Openfinderid) <= 0 {
		// FinderId无效，递归查找上级的上级
		l.Logger.Infof("用户[%d]的上级[%d]的FinderId无效，继续查找上级的上级", userId, referrer.ID)
		return l.findValidReferrer(network.FatherID)
	}

	// 检查上级是否完成所有任务
	isTaskCompleted, err := l.checkUserTaskCompleted(uint64(referrer.ID))
	if err != nil {
		l.Logger.Errorf("检查用户[%d]任务完成情况失败: %v", referrer.ID, err)
		// 错误不中断递归，继续向上查找
	}

	if !isTaskCompleted {
		// 任务未完成，递归查找上级的上级
		l.Logger.Infof("用户[%d]的上级[%d]未完成所有任务，继续查找上级的上级", userId, referrer.ID)
		return l.findValidReferrer(network.FatherID)
	}

	// 检查上级的橱窗商品数量是否大于等于BenefitsFatherWindowGoods件
	isValid, err := l.checkUserWindowProducts(uint64(referrer.ID), referrer.Openfinderid)
	if err != nil {
		l.Logger.Errorf("检查用户[%d]橱窗商品失败: %v", referrer.ID, err)
		// 错误不中断递归，继续向上查找
	}

	if isValid {
		// 橱窗商品数量大于等于BenefitsFatherWindowGoods件，返回该上级
		return &referrer, nil
	}

	// 橱窗商品数量小于BenefitsFatherWindowGoods件，递归查找上级的上级
	return l.findValidReferrer(network.FatherID)
}

// checkUserWindowProducts 检查用户橱窗中的商品数量是否大于等于BenefitsFatherWindowGoods件
func (l *GetUserReferralChainLogic) checkUserWindowProducts(userId uint64, openfinderid string) (bool, error) {
	// 如果openfinderid为空，直接返回false
	if openfinderid == "" {
		return false, nil
	}

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(
		l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId,
		l.svcCtx.Config.WxLeague.AppSecret,
		l.svcCtx.Config.WxLeague.AccessTokenPrefix,
	)
	if err != nil {
		return false, fmt.Errorf("获取微信访问令牌失败: %v", err)
	}

	// 初始化微信客户端
	wxclient := client.NewWechatClient()

	// 构建获取橱窗商品请求
	req := &wxmodels.GetWindowAllRequest{
		Appid:        l.svcCtx.Config.WxLeague.AppId,
		OpenFinderId: openfinderid,
		IsGetAll:     true,
	}

	// 调用微信API获取橱窗商品
	resp, err := wxclient.GetWindowProducts(l.ctx, req, token)
	if err != nil {
		return false, fmt.Errorf("调用微信API获取橱窗商品失败: %v", err)
	}

	// 检查响应状态
	if !resp.IsSuccess() {
		return false, fmt.Errorf("获取橱窗商品失败: %s", resp.ErrMsg)
	}

	// 如果没有商品，直接返回false
	if len(resp.List) == 0 {
		l.Logger.Infof("用户[%d]橱窗中没有商品", userId)
		return false, nil
	}

	// 收集所有商品ID
	var productIds []string
	for _, product := range resp.List {
		productIds = append(productIds, product.ProductId)
	}

	// 查询这些商品是否存在于temp_talent_products表中
	var validProducts []model.TempTalentProduct
	err = l.svcCtx.GetSlaveDB().Where("product_id IN ?", productIds).Find(&validProducts).Error
	if err != nil {
		l.Logger.Errorf("查询商品在temp_talent_products表中是否存在失败: %v", err)
		return false, fmt.Errorf("查询商品在数据库中是否存在失败: %v", err)
	}

	// 计算有效商品数量
	validProductCount := len(validProducts)
	l.Logger.Infof("用户[%d]橱窗商品总数: %d, 有效商品数量: %d", userId, len(resp.List), validProductCount)

	// 检查有效商品数量是否大于等于BenefitsFatherWindowGoods件
	return validProductCount >= BenefitsFatherWindowGoods, nil
}

// checkUserTaskCompleted 检查用户是否完成所有任务
func (l *GetUserReferralChainLogic) checkUserTaskCompleted(userId uint64) (bool, error) {
	// 查询用户的任务完成情况
	var network model.UserDynamicNetworks
	err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", userId, "benefits").First(&network).Error
	if err != nil {
		if err.Error() == "record not found" {
			// 没有找到记录，视为未完成
			return false, nil
		}
		return false, fmt.Errorf("查询用户任务完成情况失败: %v", err)
	}

	// 如果mark为空，直接返回未完成
	if network.Mark == "" {
		return false, nil
	}

	// 使用TaskBitmask解析mark
	taskBitMask := common.NewTaskBitmask(8)
	if err := taskBitMask.LoadFromBinary(network.Mark); err != nil {
		l.Logger.Errorf("解析用户[%d]的任务标记失败: %v", userId, err)
		return false, fmt.Errorf("解析任务标记失败: %v", err)
	}

	// 检查是否完成了所有任务（前4位都为1）
	return taskBitMask.IsAllCompleted(4), nil
}
