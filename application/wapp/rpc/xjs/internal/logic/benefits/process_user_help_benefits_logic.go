package benefitslogic

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProcessUserHelpBenefitsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProcessUserHelpBenefitsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessUserHelpBenefitsLogic {
	return &ProcessUserHelpBenefitsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ProcessUserHelpBenefits 处理用户助力福利任务
// 福利任务池4的维护
func (l *ProcessUserHelpBenefitsLogic) ProcessUserHelpBenefits(in *pb.ProcessUserReferralBenefitsReq) (*pb.ProcessUserReferralBenefitsResp, error) {
	l.Infof("开始处理用户助力福利任务")

	// 设置默认批处理大小
	batchSize := 100
	if in.BatchSize > 0 {
		batchSize = int(in.BatchSize)
	}

	// 获取所有根用户（没有上级的用户）
	var rootUsers []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = 0 OR invite_from IS NULL").Find(&rootUsers).Error; err != nil {
		l.Errorf("获取根用户列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("获取根用户列表失败: %v", err))
	}

	processedCount := 0
	updatedCount := 0

	// 从根用户开始，按照网络结构处理
	for _, rootUser := range rootUsers {
		processed, updated, err := l.processUserTreeHelps(uint64(rootUser.ID), batchSize, &processedCount)
		if err != nil {
			l.Errorf("处理用户 %d 的助力树失败: %v", rootUser.ID, err)
			continue
		}

		processedCount += processed
		updatedCount += updated

		// 每处理一批用户后休息一下，避免数据库压力过大
		if processedCount%batchSize == 0 {
			l.Infof("已处理 %d 个用户，更新了 %d 个福利池", processedCount, updatedCount)
			time.Sleep(100 * time.Millisecond)
		}
	}

	l.Infof("用户助力福利任务处理完成，共处理 %d 个用户，更新了 %d 个福利池", processedCount, updatedCount)
	return &pb.ProcessUserReferralBenefitsResp{
		Success:        true,
		Message:        "处理完成",
		ProcessedCount: int32(processedCount),
		UpdatedCount:   int32(updatedCount),
	}, nil
}

// processUserTreeHelps 递归处理用户及其下级的助力关系
func (l *ProcessUserHelpBenefitsLogic) processUserTreeHelps(userID uint64, batchSize int, totalProcessed *int) (int, int, error) {
	// 处理当前用户的直接下级
	processed, updated, err := l.processUserHelps(userID)
	if err != nil {
		return 0, 0, err
	}

	// 获取当前用户的直接下级
	var directChildren []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = ?", userID).Find(&directChildren).Error; err != nil {
		return processed, updated, err
	}

	// 递归处理每个直接下级
	for _, child := range directChildren {
		childProcessed, childUpdated, err := l.processUserTreeHelps(uint64(child.ID), batchSize, totalProcessed)
		if err != nil {
			l.Errorf("处理用户 %d 的下级 %d 失败: %v", userID, child.ID, err)
			continue
		}

		processed += childProcessed
		updated += childUpdated

		// 每处理一批用户后休息一下，避免数据库压力过大
		*totalProcessed += childProcessed
		if *totalProcessed%batchSize == 0 {
			l.Infof("已处理 %d 个用户，更新了 %d 个福利池", *totalProcessed, updated)
			time.Sleep(100 * time.Millisecond)
		}
	}

	return processed, updated, nil
}

// processUserHelps 处理单个用户的助力下级
func (l *ProcessUserHelpBenefitsLogic) processUserHelps(userID uint64) (int, int, error) {
	// 查找该用户的直接下级
	var directReferrals []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = ?", userID).Find(&directReferrals).Error; err != nil {
		return 0, 0, err
	}

	// 如果直接下级数量小于BenefitsPoolIncAllTaskCompletedUsers，直接返回
	if len(directReferrals) < BenefitsPoolIncAllTaskCompletedUsers {
		return len(directReferrals), 0, nil
	}

	// 查询已经记录在user_benefits_pool_log中的下级用户ID
	var loggedUserIDs []uint64
	if err := l.svcCtx.GetSlaveDB().Model(&model.UserBenefitsPoolLog{}).
		Where("benefits_user_id = ? AND task_type = ?", userID, BenefitsPollLogHelpType).
		Pluck("user_id", &loggedUserIDs).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return len(directReferrals), 0, err
	}

	// 过滤掉已经记录的下级用户，并检查mark是否为1111
	var validReferrals []model.User
	for _, referral := range directReferrals {
		// 检查是否已记录
		isLogged := false
		for _, loggedID := range loggedUserIDs {
			if uint64(referral.ID) == loggedID {
				isLogged = true
				break
			}
		}
		if isLogged {
			continue
		}

		// 查询用户的动态网络信息
		var userDynamicNetwork model.UserDynamicNetworks
		if err := l.svcCtx.GetSlaveDB().Where("user_id = ?", referral.ID).First(&userDynamicNetwork).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			l.Errorf("查询用户 %d 的动态网络信息失败: %v", referral.ID, err)
			continue
		}

		// 检查mark
		if userDynamicNetwork.Mark == "" {
			continue
		}

		// 使用TaskBitmask解析mark
		taskBitMask := common.NewTaskBitmask(8)
		if err := taskBitMask.LoadFromBinary(userDynamicNetwork.Mark); err != nil {
			l.Errorf("解析用户 %d 的任务标记失败: %v", referral.ID, err)
			continue
		}

		// 检查是否所有4个任务都已完成
		if taskBitMask.IsAllCompleted(4) {
			validReferrals = append(validReferrals, referral)
		}
	}

	// 如果有效下级数量小于BenefitsPoolIncAllTaskCompletedUsers，直接返回
	if len(validReferrals) < BenefitsPoolIncAllTaskCompletedUsers {
		return len(directReferrals), 0, nil
	}

	// 使用事务更新福利池和记录日志
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 查询或创建用户的福利池记录
		var benefitsPool model.UserBenefitsPool
		result := tx.Where("user_id = ? AND type = 3", userID).First(&benefitsPool)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// 创建新的福利池记录
				benefitsPool = model.UserBenefitsPool{
					Type:     3,
					Quantity: BenefitsHelpRewardsNums, // 初始设置为200
					UserID:   userID,
				}
				if err := tx.Create(&benefitsPool).Error; err != nil {
					return err
				}
			} else {
				return result.Error
			}
		} else {
			// 更新现有福利池记录
			if err := tx.Model(&benefitsPool).Update("quantity", gorm.Expr("quantity + ?", BenefitsHelpRewardsNums)).Error; err != nil {
				return err
			}
		}

		// 记录前BenefitsPoolIncAllTaskCompletedUsers个有效下级到日志表
		for i := 0; i < BenefitsPoolIncAllTaskCompletedUsers; i++ {
			log := model.UserBenefitsPoolLog{
				TaskType:       BenefitsPollLogHelpType,
				BenefitsUserId: userID,
				UserID:         uint64(validReferrals[i].ID),
			}
			if err := tx.Create(&log).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return len(directReferrals), 0, err
	}

	return len(directReferrals), 1, nil
}
