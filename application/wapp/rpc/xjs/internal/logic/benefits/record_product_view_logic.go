package benefitslogic

import (
	"context"
	"fmt"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

// 添加Redis缓存相关常量
const (
	// 商品浏览记录缓存前缀
	ProductViewRecordPrefix = "product_view_record:"
	// 商品浏览记录过期时间：7天
	ProductViewRecordExpire = 15 * 60
)

type RecordProductViewLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRecordProductViewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RecordProductViewLogic {
	return &RecordProductViewLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// RecordProductView 记录用户浏览商品信息
func (l *RecordProductViewLogic) RecordProductView(in *pb.RecordProductViewReq) (*pb.RecordProductViewResp, error) {
	l.Infof("记录用户浏览商品信息: userId=%d, productId=%d, shopAppid=%s, poolType=%d",
		in.UserId, in.ProductId, in.ShopAppid, in.PoolType)

	// 参数校验
	if in.UserId <= 0 || in.ProductId <= 0 || in.ShopAppid == "" || in.PoolType <= 0 {
		return &pb.RecordProductViewResp{
			Success: false,
			Message: "参数不完整",
		}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 查询用户信息获取unionid
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return &pb.RecordProductViewResp{
			Success: false,
			Message: fmt.Sprintf("查询用户信息失败: %v", err),
		}, nil
	}

	if user.Unionid == "" {
		l.Errorf("用户unionid为空: userId=%d", in.UserId)
		return &pb.RecordProductViewResp{
			Success: false,
			Message: "用户unionid为空",
		}, nil
	}

	// 使用用户的unionid作为买家标识
	buyerUnionId := user.Unionid

	// 构建缓存键：product_view_record:{buyerUnionId}:{shopAppid}_{productId}
	viewRecordKey := fmt.Sprintf("%s%s:%s_%d",
		ProductViewRecordPrefix,
		buyerUnionId,
		in.ShopAppid,
		in.ProductId)

	// 构建缓存值：用户ID和任务池类型
	viewRecordValue := fmt.Sprintf("%s:%d:%d:%d:%d", buyerUnionId, in.PoolType, in.UserId, in.SellerId, time.Now().Unix())

	// 设置缓存，有效期7天
	err := l.svcCtx.BizRedis.Setex(viewRecordKey, viewRecordValue, ProductViewRecordExpire)
	if err != nil {
		l.Errorf("设置商品浏览记录缓存失败: %v", err)
		return &pb.RecordProductViewResp{
			Success: false,
			Message: fmt.Sprintf("设置缓存失败: %v", err),
		}, nil
	}

	l.Infof("成功记录买家[%s]浏览用户[%d]的商品[%s_%d]，任务池类型: %d",
		buyerUnionId, in.UserId, in.ShopAppid, in.ProductId, in.PoolType)

	return &pb.RecordProductViewResp{
		Success: true,
		Message: "记录成功",
	}, nil
}
