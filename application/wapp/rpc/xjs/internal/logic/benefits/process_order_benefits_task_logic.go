package benefitslogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"strconv"
	"strings"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"

	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProcessOrderBenefitsTaskLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProcessOrderBenefitsTaskLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessOrderBenefitsTaskLogic {
	return &ProcessOrderBenefitsTaskLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ProcessOrderBenefitsTask 处理订单福利任务
// 添加 processOrderCancellationV2 方法
// 修改在 ProcessOrderBenefitsTask 方法中调用逻辑
func (l *ProcessOrderBenefitsTaskLogic) ProcessOrderBenefitsTask(in *pb.ProcessOrderBenefitsTaskReq) (*pb.ProcessOrderBenefitsTaskResp, error) {
	l.Infof("开始处理订单福利任务: orderID=%s", in.OrderId)

	// 参数校验
	if in.OrderId == "" {
		return nil, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "订单ID不能为空")
	}

	// 获取佣金订单信息
	commissionOrderInfo, err := l.getCommissionOrderInfo(in.OrderId)
	if err != nil {
		l.Errorf("获取佣金订单信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("获取佣金订单信息失败: %v", err))
	}
	//var commissionOrderInfo *model.CommissionOrders
	//commissionOrderInfo.OrderID = "3728219079314911232"
	//commissionOrderInfo.SkuID = "3260840546"
	//commissionOrderInfo.OrderStatus = "20"
	//commissionOrderInfo.CommissionStatus = "20"
	//commissionOrderInfo.BuyerUnionID = "oe2nQ653HEvNx9ywl5E_IPd6q0Pk"
	//commissionOrderInfo.OpenTalentID = "ofwT3W6nCsjkEo1z3rYnlzKk6iU0XzEu0fB-CZgtI--r_D6K2p8yCvatfuOzdRPWd3gSNrsG1hww"

	// 根据订单状态和佣金状态处理不同的逻辑
	orderStatus := commissionOrderInfo.OrderStatus
	commissionStatus := commissionOrderInfo.CommissionStatus

	l.Infof("订单状态: orderStatus=%s, commissionStatus=%s", orderStatus, commissionStatus)

	// 如果订单状态为待付款(10)或待发货(20)，处理订单归属逻辑
	if (orderStatus == "10" || orderStatus == "20") && commissionStatus == "20" {
		// 使用新版本的处理方法
		return l.processOrderAssignmentV2(commissionOrderInfo)
	}

	// 如果订单状态为已完成(100)且佣金状态为已结算(20)，处理福利任务完成逻辑
	if orderStatus == "100" && commissionStatus == "20" {
		return l.processCompletedOrderBenefits(commissionOrderInfo)
	}

	// 如果订单状态为取消(200-售后取消或250-未付款取消)，处理恢复福利池逻辑
	if (orderStatus == "200" || orderStatus == "250") && commissionOrderInfo.BenefitsPoolNo > 0 {
		// 使用新版本的处理方法
		return l.processOrderCancellationV2(commissionOrderInfo)
	}

	// 其他状态暂不处理
	l.Infof("订单状态不满足处理条件，跳过处理: orderStatus=%s, commissionStatus=%s", orderStatus, commissionStatus)
	return &pb.ProcessOrderBenefitsTaskResp{
		Success: true,
		Message: "订单状态不满足处理条件，跳过处理",
	}, nil
}

// getCommissionOrderInfo 获取佣金订单信息
func (l *ProcessOrderBenefitsTaskLogic) getCommissionOrderInfo(orderID string) (*model.CommissionOrders, error) {
	var commissionOrder model.CommissionOrders
	err := l.svcCtx.MasterDB.Where("order_id = ?", orderID).First(&commissionOrder).Error
	if err != nil {
		return nil, err
	}
	return &commissionOrder, nil
}

// processCompletedOrderBenefits 处理已完成订单的福利任务
func (l *ProcessOrderBenefitsTaskLogic) processCompletedOrderBenefits(commissionOrder *model.CommissionOrders) (*pb.ProcessOrderBenefitsTaskResp, error) {
	orderID := commissionOrder.OrderID
	l.Infof("处理已完成订单的福利任务: orderID=%s", orderID)

	// 检查佣金订单的买家信息
	if commissionOrder.BuyerUnionID == "" {
		l.Errorf("佣金订单买家UnionID为空: orderID=%s", orderID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "佣金订单买家UnionID为空",
		}, nil
	}

	// 检查benefits_pool_no是否有效
	if commissionOrder.BenefitsPoolNo <= 0 {
		l.Infof("订单未分配福利池: orderID=%s", orderID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: "订单未分配福利池，跳过处理",
		}, nil
	}

	// 查询用户信息和动态网络信息 - 合并查询
	var user model.User
	var userDynamicNetwork model.UserDynamicNetworks

	// 先查询用户信息
	if err := l.svcCtx.GetSlaveDB().Where("unionid = ?", commissionOrder.BuyerUnionID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("未找到买家用户信息: BuyerUnionID=%s", commissionOrder.BuyerUnionID)
			return &pb.ProcessOrderBenefitsTaskResp{
				Success: false,
				Message: "未找到买家用户信息",
			}, nil
		}
		l.Errorf("查询买家用户信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询买家用户信息失败: %v", err))
	}

	// 再查询动态网络信息
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", user.ID, "benefits").First(&userDynamicNetwork).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("未找到买家的动态网络信息: UserID=%d", user.ID)
			return &pb.ProcessOrderBenefitsTaskResp{
				Success: false,
				Message: "未找到买家的动态网络信息",
			}, nil
		}
		l.Errorf("查询买家动态网络信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询买家动态网络信息失败: %v", err))
	}

	// 使用pkg包中的函数还原mark
	currentMark := userDynamicNetwork.Mark
	// 使用common包中的任务位掩码工具，指定位数为8
	taskBitMask := common.NewTaskBitmask(8)
	if len(currentMark) > 0 {
		if err := taskBitMask.LoadFromBitFormat([]byte(currentMark)); err != nil {
			l.Errorf("解析任务标记失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("解析任务标记失败: %v", err))
		}
	}

	// 检查mark是否已完成 (所有4个任务位)
	if taskBitMask.IsAllCompleted(4) {
		l.Infof("用户任务已全部完成: userID=%d, mark=%s", userDynamicNetwork.UserID, currentMark)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: "用户任务已全部完成，跳过处理",
		}, nil
	}

	// 根据benefits_pool_no设置对应的任务位
	taskPosition := commissionOrder.BenefitsPoolNo // 使用实际任务ID
	if taskPosition < 1 || taskPosition > 4 {      // 任务ID范围是1-4
		l.Errorf("无效的福利池编号: BenefitsPoolNo=%d", commissionOrder.BenefitsPoolNo)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("无效的福利池编号: %d", commissionOrder.BenefitsPoolNo),
		}, nil
	}

	// 如果该任务位已经完成，则跳过
	if taskBitMask.IsTaskCompleted(int(taskPosition)) {
		l.Infof("任务%d已完成: userID=%d", commissionOrder.BenefitsPoolNo, userDynamicNetwork.UserID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: fmt.Sprintf("任务%d已完成，跳过处理", commissionOrder.BenefitsPoolNo),
		}, nil
	}

	// 查询用户完成的相同任务订单数量
	var completedOrderCount int64
	err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).
		Where("buyer_union_id = ? AND benefits_pool_no = ? AND commission_status = '20' AND order_status = '100'",
			commissionOrder.BuyerUnionID, taskPosition).
		Count(&completedOrderCount).Error

	if err != nil {
		l.Errorf("查询用户完成的相同任务订单数量失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询用户完成的相同任务订单数量失败: %v", err))
	}

	// 检查是否有足够数量的相同任务订单（>=BenefitsSingleTaskCompletedOrders个）
	if completedOrderCount < BenefitsSingleTaskCompletedOrders {
		l.Infof("用户完成的任务%d订单数量不足: userID=%d, 当前数量=%d, 需要数量=%d",
			taskPosition, userDynamicNetwork.UserID, completedOrderCount, BenefitsSingleTaskCompletedOrders)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: fmt.Sprintf("用户完成的任务%d订单数量不足，需要至少%d个订单", taskPosition, BenefitsSingleTaskCompletedOrders),
		}, nil
	}

	l.Infof("用户已完成足够数量的任务%d订单: userID=%d, 完成数量=%d",
		taskPosition, userDynamicNetwork.UserID, completedOrderCount)

	// 设置任务位为已完成
	taskBitMask.CompleteTask(int(taskPosition))
	// 获取适合MySQL bit(8)类型的字节数组
	newMark := taskBitMask.GetBinaryString()
	// 使用事务更新user_dynamic_networks表
	err = l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 使用原生SQL更新，添加当前mark值的检查
		sql := fmt.Sprintf("UPDATE user_dynamic_networks SET mark = b'%s', updated_at = '%s' WHERE id = %d",
			newMark, time.Now().Format("2006-01-02 15:04:05"), userDynamicNetwork.ID)
		result := tx.Exec(sql)

		if result.Error != nil {
			return result.Error
		}

		if result.RowsAffected == 0 {
			return fmt.Errorf("更新用户动态网络信息失败，可能数据已被修改")
		}

		return nil
	})

	if err != nil {
		l.Errorf("更新用户动态网络信息失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("更新用户动态网络信息失败: %v", err))
	}

	l.Infof("成功更新用户任务状态: userID=%d, oldMark=%s, newMark=%s, taskPosition=%d",
		userDynamicNetwork.UserID, currentMark, newMark, taskPosition)

	// 检查是否所有任务都已完成
	if taskBitMask.IsAllCompleted(4) { // 假设总共有4个任务位
		l.Infof("用户所有任务已完成: userID=%d", userDynamicNetwork.UserID)
		// 发送网体变更消息到队列
		err = l.sendNetworkChangeMessage(uint64(user.ID))
		if err != nil {
			l.Errorf("发送网体变更消息失败: %v", err)
			// 不影响主流程，继续返回成功
		} else {
			l.Infof("已发送网体变更消息到队列: userID=%d", user.ID)
		}
	}

	return &pb.ProcessOrderBenefitsTaskResp{
		Success: true,
		Message: fmt.Sprintf("成功完成任务%d", commissionOrder.BenefitsPoolNo),
	}, nil
}

// processOrderCancellation 处理订单取消，恢复福利池数量
func (l *ProcessOrderBenefitsTaskLogic) processOrderCancellation(commissionOrder *model.CommissionOrders) (*pb.ProcessOrderBenefitsTaskResp, error) {
	orderID := commissionOrder.OrderID
	l.Infof("处理订单取消，恢复福利池数量: orderID=%s", orderID)

	// 只处理福利池3和4的情况
	if commissionOrder.BenefitsPoolNo != 3 && commissionOrder.BenefitsPoolNo != 4 {
		l.Infof("订单不属于福利池3或4，无需恢复: orderID=%s, benefitsPoolNo=%d", orderID, commissionOrder.BenefitsPoolNo)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: "订单不属于福利池3或4，无需恢复",
		}, nil
	}

	// 检查达人ID信息
	if commissionOrder.OpenTalentID == "" {
		l.Errorf("订单缺少达人ID信息，无法恢复福利池: orderID=%s", orderID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "订单缺少达人ID信息，无法恢复福利池",
		}, nil
	}

	// 查找open_talent_id对应的用户
	var talentUser model.User
	if err := l.svcCtx.GetSlaveDB().Where("opentalentid = ?", commissionOrder.OpenTalentID).First(&talentUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("未找到对应的达人用户: OpenTalentID=%s", commissionOrder.OpenTalentID)
			return &pb.ProcessOrderBenefitsTaskResp{
				Success: false,
				Message: "未找到对应的达人用户",
			}, nil
		}
		l.Errorf("查询达人用户失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询达人用户失败: %v", err))
	}

	// 使用事务处理福利池恢复
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 查询用户的福利池
		var benefitsPool model.UserBenefitsPool
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("user_id = ? AND type = ?", talentUser.ID, commissionOrder.BenefitsPoolNo).
			First(&benefitsPool).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果没有找到福利池记录，创建一个新的
				benefitsPool = model.UserBenefitsPool{
					UserID:   uint64(talentUser.ID),
					Type:     int8(commissionOrder.BenefitsPoolNo),
					Quantity: 0, // 将在下面增加
				}
				if err := tx.Create(&benefitsPool).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}

		// 增加福利池数量
		if err := tx.Model(&model.UserBenefitsPool{}).
			Where("id = ?", benefitsPool.ID).
			Update("quantity", gorm.Expr("quantity + 1")).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		l.Errorf("恢复福利池数量失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("恢复福利池数量失败: %v", err))
	}

	l.Infof("成功恢复福利池数量: orderID=%s, benefitsPoolNo=%d, talentUserID=%d",
		orderID, commissionOrder.BenefitsPoolNo, talentUser.ID)

	return &pb.ProcessOrderBenefitsTaskResp{
		Success: true,
		Message: fmt.Sprintf("成功恢复福利池%d数量", commissionOrder.BenefitsPoolNo),
	}, nil
}

// 添加发送网体变更消息的函数
func (l *ProcessOrderBenefitsTaskLogic) sendNetworkChangeMessage(userID uint64) error {
	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		return fmt.Errorf("队列管理器未初始化")
	}

	// 构建消息内容
	msgData := map[string]interface{}{
		"type":    "handle_benefits_tasks",
		"event":   "handle_network_change",
		"user_id": userID,
		"created": time.Now().Unix(),
	}

	// 序列化消息
	msgBytes, err := json.Marshal(msgData)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	// 发送消息到队列
	err = l.svcCtx.QueueManager.PublishToQueue("BenefitsTasks", msgBytes)
	if err != nil {
		return fmt.Errorf("发送消息到RabbitMQ失败: %v", err)
	}

	return nil
}

// 添加一个辅助方法，根据UnionID获取用户信息
func (l *ProcessOrderBenefitsTaskLogic) getUserByUnionID(unionID string) (*model.User, error) {
	var user model.User
	err := l.svcCtx.GetSlaveDB().Where("union_id = ?", unionID).First(&user).Error
	if err != nil {
		return nil, err
	}
	return &user, nil
}

// processOrderAssignmentV2 处理订单归属逻辑的新版本
func (l *ProcessOrderBenefitsTaskLogic) processOrderAssignmentV2(commissionOrder *model.CommissionOrders) (*pb.ProcessOrderBenefitsTaskResp, error) {
	l.Infof("V2-处理订单归属逻辑: orderID=%s, buyerUnionID=%s", commissionOrder.OrderID, commissionOrder.BuyerUnionID)

	// 检查佣金订单的买家信息
	if commissionOrder.BuyerUnionID == "" {
		l.Errorf("佣金订单买家UnionID为空: orderID=%s", commissionOrder.OrderID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "佣金订单买家UnionID为空",
		}, nil
	}

	// 查找商品ID
	productID, err := strconv.ParseInt(commissionOrder.ProductID, 10, 64)
	if err != nil {
		l.Errorf("商品ID转换失败: %v, productId: %s", err, commissionOrder.ProductID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("商品ID转换失败: %v", err),
		}, nil
	}

	// 首先检查是否有商品浏览记录
	viewRecordKey := fmt.Sprintf("%s%s:%s_%d",
		ProductViewRecordPrefix,
		commissionOrder.BuyerUnionID,
		commissionOrder.ShopAppid,
		productID)

	viewRecordValue, err := l.svcCtx.BizRedis.Get(viewRecordKey)

	if err != nil || viewRecordValue == "" {
		// 没有浏览记录，直接返回失败
		l.Infof("未找到买家[%s]浏览商品[%s_%d]的记录",
			commissionOrder.BuyerUnionID, commissionOrder.ShopAppid, productID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "未找到商品浏览记录，无法确定任务池归属",
		}, nil
	}

	// 解析浏览记录值：格式为 "buyerUnionId:poolType:timestamp"
	parts := strings.Split(viewRecordValue, ":")
	if len(parts) < 4 {
		l.Errorf("商品浏览记录格式错误: %s", viewRecordValue)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "商品浏览记录格式错误",
		}, nil
	}

	buyerUnionId, poolTypeStr, buyerUidStr, sellerUidStr := parts[0], parts[1], parts[2], parts[3]
	poolType, err2 := strconv.Atoi(poolTypeStr)
	buyerUid, err3 := strconv.Atoi(buyerUidStr)
	sellerUid, err4 := strconv.Atoi(sellerUidStr)

	if err2 != nil || err3 != nil || err4 != nil || poolType <= 0 || buyerUid <= 0 || sellerUid <= 0 || buyerUnionId != commissionOrder.BuyerUnionID {
		l.Errorf("商品浏览记录解析失败: %v, poolType=%d, buyerUnionId=%s", err2, poolType, buyerUnionId)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "商品浏览记录解析失败",
		}, nil
	}

	l.Infof("找到买家[%s]浏览商品[%s_%d]的记录，任务池类型: %d",
		commissionOrder.BuyerUnionID, commissionOrder.ShopAppid, productID, poolType)

	// 根据任务池类型进行不同的处理
	if poolType == 3 || poolType == 4 {
		// 任务3和4需要验证用户福利池
		return l.processPoolType3And4(commissionOrder, poolType, uint64(sellerUid))
	} else if poolType == 1 || poolType == 2 {
		// 任务1和2需要验证关系链
		// 查找买家用户
		//var buyer model.User
		//if err := l.svcCtx.GetSlaveDB().Where("unionid = ?", commissionOrder.BuyerUnionID).First(&buyer).Error; err != nil {
		//	if errors.Is(err, gorm.ErrRecordNotFound) {
		//		l.Errorf("未找到买家用户: unionid=%s", commissionOrder.BuyerUnionID)
		//		return &pb.ProcessOrderBenefitsTaskResp{
		//			Success: false,
		//			Message: "未找到买家用户",
		//		}, nil
		//	}
		//	l.Errorf("查询买家用户失败: %v", err)
		//	return &pb.ProcessOrderBenefitsTaskResp{
		//		Success: false,
		//		Message: fmt.Sprintf("查询买家用户失败: %v", err),
		//	}, nil
		//}

		// 获取买家的推荐关系链
		//referralChain, err := l.svcCtx.BenefitsRPC.GetUserReferralChain(l.ctx, &pb.GetUserReferralChainReq{
		//	UserId: uint64(buyerUid),
		//})
		//if err != nil {
		//	l.Errorf("获取用户推荐关系链失败: %v", err)
		//	return &pb.ProcessOrderBenefitsTaskResp{
		//		Success: false,
		//		Message: fmt.Sprintf("获取用户推荐关系链失败: %v", err),
		//	}, nil
		//}
		//
		//var relatedUserID uint64
		//var updateReason string
		//
		//// 根据任务池类型检查关系链
		//if poolType == 1 && referralChain.DirectReferrer != nil {
		//	// 任务1：检查直接推荐人
		//	relatedUserID = referralChain.DirectReferrer.Id
		//	updateReason = "订单归属任务1：直接推荐人匹配"
		//} else if poolType == 2 && referralChain.IndirectReferrer != nil {
		//	// 任务2：检查间接推荐人
		//	relatedUserID = referralChain.IndirectReferrer.Id
		//	updateReason = "订单归属任务2：间接推荐人匹配"
		//} else {
		//	l.Infof("用户关系链不符合任务%d要求: userId=%d", poolType, buyerUid)
		//	return &pb.ProcessOrderBenefitsTaskResp{
		//		Success: false,
		//		Message: fmt.Sprintf("用户关系链不符合任务%d要求", poolType),
		//	}, nil
		//}

		// 使用事务更新订单表
		err = l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
			// 更新订单表的benefits_pool_no字段和benefits_refer_seller字段
			result := tx.Model(&model.CommissionOrders{}).
				Where("order_id = ?", commissionOrder.OrderID).
				Updates(map[string]interface{}{
					"benefits_pool_no":      poolType,
					"benefits_refer_seller": sellerUid,
				})

			if result.Error != nil {
				return result.Error
			}

			if result.RowsAffected == 0 {
				return fmt.Errorf("更新订单福利池归属失败，可能订单不存在")
			}

			return nil
		})

		if err != nil {
			l.Errorf("更新订单福利池归属失败: %v", err)
			return &pb.ProcessOrderBenefitsTaskResp{
				Success: false,
				Message: fmt.Sprintf("更新订单福利池归属失败: %v", err),
			}, nil
		}

		l.Infof("成功更新订单福利池归属: orderID=%s, benefitsPoolNo=%d, relatedUserID=%d",
			commissionOrder.OrderID, poolType, sellerUid)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: "订单归属处理成功",
		}, nil
	}

	// 不支持的任务池类型
	l.Errorf("不支持的任务池类型: %d", poolType)
	return &pb.ProcessOrderBenefitsTaskResp{
		Success: false,
		Message: fmt.Sprintf("不支持的任务池类型: %d", poolType),
	}, nil
}

// 处理任务池3和4的逻辑
func (l *ProcessOrderBenefitsTaskLogic) processPoolType3And4(commissionOrder *model.CommissionOrders, poolType int, sellerUid uint64) (*pb.ProcessOrderBenefitsTaskResp, error) {
	// 查询所有type为poolType且quantity>0的福利池记录
	var benefitsPools []model.UserBenefitsPool
	if err := l.svcCtx.GetSlaveDB().Where("quantity > 0 AND type = ?", poolType).Find(&benefitsPools).Error; err != nil {
		l.Errorf("查询福利池记录失败: %v", err)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("查询福利池记录失败: %v", err),
		}, nil
	}

	if len(benefitsPools) == 0 {
		l.Infof("没有找到可用的任务池%d记录", poolType)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("没有找到可用的任务池%d记录", poolType),
		}, nil
	}

	// 查找商品ID
	//productID, err := strconv.ParseInt(commissionOrder.ProductID, 10, 64)
	//if err != nil {
	//	l.Errorf("商品ID转换失败: %v, productId: %s", err, commissionOrder.ProductID)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: fmt.Sprintf("商品ID转换失败: %v", err),
	//	}, nil
	//}

	//// 构建商品浏览记录的Redis键
	//viewRecordKey := fmt.Sprintf("%s%s:%s_%d",
	//	ProductViewRecordPrefix,
	//	commissionOrder.BuyerUnionID,
	//	commissionOrder.ShopAppid,
	//	productID)
	//
	//// 获取浏览记录
	//viewRecordValue, err := l.svcCtx.BizRedis.Get(viewRecordKey)
	//if err != nil || viewRecordValue == "" {
	//	l.Infof("未找到买家[%s]浏览商品[%s_%d]的记录",
	//		commissionOrder.BuyerUnionID, commissionOrder.ShopAppid, productID)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: "未找到商品浏览记录，无法确定任务池归属",
	//	}, nil
	//}

	// 解析浏览记录值：格式为 "buyerUnionId:poolType:userId:timestamp"
	//parts := strings.Split(viewRecordValue, ":")
	//if len(parts) < 3 {
	//	l.Errorf("商品浏览记录格式错误: %s", viewRecordValue)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: "商品浏览记录格式错误",
	//	}, nil
	//}
	//
	//// 获取记录中的任务池类型和用户ID
	//recordPoolTypeStr := parts[1]
	//recordUserIDStr := parts[2]
	//
	//recordPoolType, err := strconv.Atoi(recordPoolTypeStr)
	//if err != nil {
	//	l.Errorf("商品浏览记录中的任务池类型解析失败: %v", err)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: "商品浏览记录中的任务池类型解析失败",
	//	}, nil
	//}
	//
	//if recordPoolType != poolType {
	//	l.Errorf("商品浏览记录中的任务池类型不匹配: 期望=%d, 实际=%d", poolType, recordPoolType)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: fmt.Sprintf("商品浏览记录中的任务池类型不匹配: %d", recordPoolType),
	//	}, nil
	//}

	//recordUserID, err := strconv.ParseUint(recordUserIDStr, 10, 64)
	//if err != nil {
	//	l.Errorf("商品浏览记录中的用户ID解析失败: %v", err)
	//	return &pb.ProcessOrderBenefitsTaskResp{
	//		Success: false,
	//		Message: "商品浏览记录中的用户ID解析失败",
	//	}, nil
	//}
	//
	//l.Infof("找到买家[%s]浏览商品[%s_%d]的记录，任务池类型: %d, 推荐用户ID: %d",
	//	commissionOrder.BuyerUnionID, commissionOrder.ShopAppid, productID, recordPoolType, recordUserID)

	// 查找对应的福利池记录
	var targetPool *model.UserBenefitsPool
	for _, pool := range benefitsPools {
		if pool.UserID == sellerUid {
			targetPool = &pool
			break
		}
	}

	if targetPool == nil {
		l.Errorf("未找到浏览记录中用户[%d]的福利池记录", sellerUid)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("未找到浏览记录中用户的福利池记录"),
		}, nil
	}

	// 使用事务更新福利池和订单
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 再次查询福利池确保数量仍然大于0（使用行锁）
		var lockedPool model.UserBenefitsPool
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("id = ? AND quantity > 0", targetPool.ID).
			First(&lockedPool).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("福利池数量已用完")
			}
			return err
		}

		// 更新佣金订单的benefits_pool_no字段和benefits_refer_seller字段
		updates := map[string]interface{}{
			"benefits_pool_no":      lockedPool.Type,
			"benefits_refer_seller": lockedPool.UserID,
		}
		if err := tx.Model(&model.CommissionOrders{}).
			Where("order_id = ?", commissionOrder.OrderID).
			Updates(updates).Error; err != nil {
			return err
		}

		// 减少福利池数量
		if err := tx.Model(&model.UserBenefitsPool{}).
			Where("id = ?", lockedPool.ID).
			Update("quantity", gorm.Expr("quantity - 1")).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		l.Errorf("更新福利池和订单信息失败: %v", err)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: fmt.Sprintf("更新福利池和订单信息失败: %v", err),
		}, nil
	}

	l.Infof("成功将订单归属到用户的任务池%d: orderID=%s, userId=%d",
		poolType, commissionOrder.OrderID, targetPool.UserID)
	return &pb.ProcessOrderBenefitsTaskResp{
		Success: true,
		Message: fmt.Sprintf("成功将订单归属到用户的任务池%d", poolType),
	}, nil
}

// processOrderCancellationV2 处理订单取消，恢复福利池数量的新版本
func (l *ProcessOrderBenefitsTaskLogic) processOrderCancellationV2(commissionOrder *model.CommissionOrders) (*pb.ProcessOrderBenefitsTaskResp, error) {
	orderID := commissionOrder.OrderID
	l.Infof("V2-处理订单取消，恢复福利池数量: orderID=%s", orderID)

	// 只处理福利池3和4的情况
	if commissionOrder.BenefitsPoolNo != 3 && commissionOrder.BenefitsPoolNo != 4 {
		l.Infof("订单不属于福利池3或4，无需恢复: orderID=%s, benefitsPoolNo=%d", orderID, commissionOrder.BenefitsPoolNo)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: true,
			Message: "订单不属于福利池3或4，无需恢复",
		}, nil
	}

	// 检查benefits_refer_seller信息
	if commissionOrder.BenefitsReferSeller == 0 {
		l.Errorf("订单缺少卖家引荐信息，无法恢复福利池: orderID=%s", orderID)
		return &pb.ProcessOrderBenefitsTaskResp{
			Success: false,
			Message: "订单缺少卖家引荐信息，无法恢复福利池",
		}, nil
	}

	// 使用事务处理福利池恢复
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 查询用户的福利池
		var benefitsPool model.UserBenefitsPool
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
			Where("user_id = ? AND type = ?", commissionOrder.BenefitsReferSeller, commissionOrder.BenefitsPoolNo).
			First(&benefitsPool).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// 如果没有找到福利池记录，创建一个新的
				benefitsPool = model.UserBenefitsPool{
					UserID:   commissionOrder.BenefitsReferSeller,
					Type:     int8(commissionOrder.BenefitsPoolNo),
					Quantity: 0, // 将在下面增加
				}
				if err := tx.Create(&benefitsPool).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}

		// 增加福利池数量
		if err := tx.Model(&model.UserBenefitsPool{}).
			Where("id = ?", benefitsPool.ID).
			Update("quantity", gorm.Expr("quantity + 1")).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		l.Errorf("恢复福利池数量失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("恢复福利池数量失败: %v", err))
	}

	l.Infof("成功恢复福利池数量: orderID=%s, benefitsPoolNo=%d, sellerID=%d",
		orderID, commissionOrder.BenefitsPoolNo, commissionOrder.BenefitsReferSeller)

	return &pb.ProcessOrderBenefitsTaskResp{
		Success: true,
		Message: fmt.Sprintf("成功恢复福利池%d数量", commissionOrder.BenefitsPoolNo),
	}, nil
}

// checkUserWindowProducts 检查用户橱窗中是否包含指定商品
// 返回值: 是否找到商品, 错误信息
//func (l *ProcessOrderBenefitsTaskLogic) checkUserWindowProducts(userID uint64, productID uint64) (bool, error) {
//	// 检查用户的橱窗商品缓存
//	cacheKey := fmt.Sprintf("%s%d", UserWindowProductsCachePrefix, userID)
//	cachedData, err := l.svcCtx.BizRedis.Get(cacheKey)
//	if err != nil || len(cachedData) == 0 {
//		return false, fmt.Errorf("获取用户[%d]橱窗商品缓存失败: %v", userID, err)
//	}
//
//	// 解析缓存数据
//	var cachedProducts []*ecpb.ProductInfo
//	if err = json.Unmarshal([]byte(cachedData), &cachedProducts); err != nil {
//		return false, fmt.Errorf("解析用户[%d]橱窗商品缓存失败: %v", userID, err)
//	}
//
//	// 查找是否包含指定商品
//	for _, product := range cachedProducts {
//		if product.ProductId == productID {
//			return true, nil
//		}
//	}
//
//	return false, nil
//}
