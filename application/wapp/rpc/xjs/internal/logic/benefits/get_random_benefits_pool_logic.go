package benefitslogic

import (
	"context"
	"errors"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"math/rand"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
)

const (
	// Redis键前缀
	redisKeyPrefix = "benefits_pool_exposure:"
	// 曝光记录过期时间（24小时）
	exposureExpireTime = 24 * time.Hour
	// 最大尝试次数
	maxAttempts = 10
)

type GetRandomBenefitsPoolLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetRandomBenefitsPoolLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRandomBenefitsPoolLogic {
	return &GetRandomBenefitsPoolLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetRandomBenefitsPool 获取随机福利池
func (l *GetRandomBenefitsPoolLogic) GetRandomBenefitsPool(in *pb.GetRandomBenefitsPoolReq) (*pb.GetRandomBenefitsPoolResp, error) {
	l.Infof("开始获取随机福利池, userId=%d", in.UserId)

	// 初始化随机数生成器
	rand.Seed(time.Now().UnixNano())

	// 初始化响应
	resp := &pb.GetRandomBenefitsPoolResp{}

	// 获取类型3的福利池
	type3Pool, err := l.getRandomBenefitsPoolByType(3, in.UserId)
	if err != nil {
		l.Errorf("获取类型3福利池失败: %v", err)
		return nil, nil
	}
	if type3Pool != nil {
		resp.Type3Pool = type3Pool
	}

	// 获取类型4的福利池
	type4Pool, err := l.getRandomBenefitsPoolByType(4, in.UserId)
	if err != nil {
		l.Errorf("获取类型4福利池失败: %v", err)
		return nil, nil
	}
	if type4Pool != nil {
		resp.Type4Pool = type4Pool
	}

	return resp, nil
}

// getRandomBenefitsPoolByType 根据类型获取随机福利池
func (l *GetRandomBenefitsPoolLogic) getRandomBenefitsPoolByType(poolType int8, excludeUserId uint64) (*pb.BenefitsPoolInfo, error) {
	// 查询所有符合条件的福利池
	var pools []model.UserBenefitsPool
	query := l.svcCtx.GetSlaveDB().Where("type = ? AND quantity > 0", poolType)

	// 如果需要排除自己
	if excludeUserId > 0 {
		query = query.Where("user_id != ?", excludeUserId)
	}

	if err := query.Find(&pools).Error; err != nil {
		return nil, err
	}

	// 如果没有符合条件的福利池
	if len(pools) == 0 {
		return nil, nil
	}

	// 使用加权随机算法选择福利池
	selectedPool, err := l.weightedRandomSelection(pools)
	if err != nil {
		return nil, err
	}

	// 如果没有选择到福利池
	if selectedPool == nil {
		return nil, nil
	}

	// 查询用户信息
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", selectedPool.UserID).First(&user).Error; err != nil {
		return nil, err
	}

	// 必须填写视频号id
	//if len(user.FinderId) <= 0 {
	//	return nil, nil
	//}

	// 构建响应
	poolInfo := &pb.BenefitsPoolInfo{
		UserId:   selectedPool.UserID,
		Type:     int32(selectedPool.Type),
		Quantity: int32(selectedPool.Quantity),
		UserInfo: &pb.UserInfo{},
	}

	// 复制用户信息
	if err := copier.Copy(poolInfo.UserInfo, &user); err != nil {
		return nil, err
	}

	// 记录曝光
	l.recordExposure(selectedPool.UserID, poolType)

	return poolInfo, nil
}

// weightedRandomSelection 加权随机选择算法
func (l *GetRandomBenefitsPoolLogic) weightedRandomSelection(pools []model.UserBenefitsPool) (*model.UserBenefitsPool, error) {
	// 如果只有一个福利池，直接返回
	if len(pools) == 1 {
		return &pools[0], nil
	}

	// 获取所有福利池的曝光次数
	exposureCounts, err := l.getExposureCounts(pools)
	if err != nil {
		return nil, err
	}

	// 计算权重
	weights := make([]float64, len(pools))
	totalWeight := 0.0

	for i, pool := range pools {
		// 基础权重为福利池数量
		baseWeight := float64(pool.Quantity)

		// 曝光次数越多，权重越低
		exposureWeight := 1.0 / (float64(exposureCounts[pool.UserID]) + 1.0)

		// 最终权重 = 基础权重 * 曝光权重
		weights[i] = baseWeight * exposureWeight
		totalWeight += weights[i]
	}

	// 随机选择
	randomValue := rand.Float64() * totalWeight
	cumulativeWeight := 0.0

	for i, weight := range weights {
		cumulativeWeight += weight
		if randomValue <= cumulativeWeight {
			return &pools[i], nil
		}
	}

	// 如果没有选择到（理论上不会发生），返回第一个
	return &pools[0], nil
}

// getExposureCounts 获取所有福利池的曝光次数
func (l *GetRandomBenefitsPoolLogic) getExposureCounts(pools []model.UserBenefitsPool) (map[uint64]int, error) {
	result := make(map[uint64]int)

	// 初始化所有福利池的曝光次数为0
	for _, pool := range pools {
		result[pool.UserID] = 0
	}

	// 从Redis获取曝光次数
	for _, pool := range pools {
		key := fmt.Sprintf("%s%d", redisKeyPrefix, pool.UserID)
		count, err := l.svcCtx.BizRedis.Get(key)
		if err != nil && !errors.Is(err, redis.Nil) {
			return nil, err
		}

		if count != "" {
			var exposureCount int
			_, err := fmt.Sscanf(count, "%d", &exposureCount)
			if err == nil {
				result[pool.UserID] = exposureCount
			}
		}
	}

	return result, nil
}

// recordExposure 记录曝光
func (l *GetRandomBenefitsPoolLogic) recordExposure(userId uint64, poolType int8) {
	key := fmt.Sprintf("%s%d", redisKeyPrefix, userId)

	// 获取当前曝光次数
	count, err := l.svcCtx.BizRedis.Get(key)
	if err != nil && !errors.Is(err, redis.Nil) {
		l.Errorf("获取曝光次数失败: %v", err)
		return
	}

	var exposureCount int
	if count != "" {
		_, err := fmt.Sscanf(count, "%d", &exposureCount)
		if err != nil {
			l.Errorf("解析曝光次数失败: %v", err)
			return
		}
	}

	// 增加曝光次数
	exposureCount++

	// 更新Redis
	err = l.svcCtx.BizRedis.Setex(key, fmt.Sprintf("%d", exposureCount), int(exposureExpireTime.Seconds()))
	if err != nil {
		l.Errorf("更新曝光次数失败: %v", err)
	}
}
