package benefitslogic

import (
	"context"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type ProcessUserReferralBenefitsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProcessUserReferralBenefitsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessUserReferralBenefitsLogic {
	return &ProcessUserReferralBenefitsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// ProcessUserReferralBenefits 处理用户推荐福利任务
// 福利任务池3的维护
func (l *ProcessUserReferralBenefitsLogic) ProcessUserReferralBenefits(in *pb.ProcessUserReferralBenefitsReq) (*pb.ProcessUserReferralBenefitsResp, error) {
	l.Infof("开始处理用户推荐福利任务")

	// 设置默认批处理大小
	batchSize := 100
	if in.BatchSize > 0 {
		batchSize = int(in.BatchSize)
	}

	// 获取所有根用户（没有上级的用户）
	var rootUsers []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = 0 OR invite_from IS NULL").Find(&rootUsers).Error; err != nil {
		l.Errorf("获取根用户列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("获取根用户列表失败: %v", err))
	}

	processedCount := 0
	updatedCount := 0

	// 从根用户开始，按照网络结构处理
	for _, rootUser := range rootUsers {
		processed, updated, err := l.processUserTreeReferrals(uint64(rootUser.ID), batchSize, &processedCount)
		if err != nil {
			l.Errorf("处理用户 %d 的推荐树失败: %v", rootUser.ID, err)
			continue
		}

		processedCount += processed
		updatedCount += updated

		// 每处理一批用户后休息一下，避免数据库压力过大
		if processedCount%batchSize == 0 {
			l.Infof("已处理 %d 个用户，更新了 %d 个福利池", processedCount, updatedCount)
			time.Sleep(100 * time.Millisecond)
		}
	}

	l.Infof("用户推荐福利任务处理完成，共处理 %d 个用户，更新了 %d 个福利池", processedCount, updatedCount)
	return &pb.ProcessUserReferralBenefitsResp{
		Success:        true,
		Message:        "处理完成",
		ProcessedCount: int32(processedCount),
		UpdatedCount:   int32(updatedCount),
	}, nil
}

// processUserTreeReferrals 递归处理用户及其下级的推荐关系
func (l *ProcessUserReferralBenefitsLogic) processUserTreeReferrals(userID uint64, batchSize int, totalProcessed *int) (int, int, error) {
	// 处理当前用户的直接下级
	processed, updated, err := l.processUserReferrals(userID)
	if err != nil {
		return 0, 0, err
	}

	// 获取当前用户的直接下级
	var directChildren []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = ?", userID).Find(&directChildren).Error; err != nil {
		return processed, updated, err
	}

	// 递归处理每个直接下级
	for _, child := range directChildren {
		childProcessed, childUpdated, err := l.processUserTreeReferrals(uint64(child.ID), batchSize, totalProcessed)
		if err != nil {
			l.Errorf("处理用户 %d 的下级 %d 失败: %v", userID, child.ID, err)
			continue
		}

		processed += childProcessed
		updated += childUpdated

		// 每处理一批用户后休息一下，避免数据库压力过大
		*totalProcessed += childProcessed
		if *totalProcessed%batchSize == 0 {
			l.Infof("已处理 %d 个用户，更新了 %d 个福利池", *totalProcessed, updated)
			time.Sleep(100 * time.Millisecond)
		}
	}

	return processed, updated, nil
}

// processUserReferrals 处理单个用户的推荐关系
func (l *ProcessUserReferralBenefitsLogic) processUserReferrals(userID uint64) (int, int, error) {
	// 查找该用户的直接下级，且openfinderid不为空
	var directReferrals []model.User
	if err := l.svcCtx.GetSlaveDB().Where("invite_from = ? AND openfinderid != ''", userID).Find(&directReferrals).Error; err != nil {
		return 0, 0, err
	}

	// 如果直接下级数量小于BenefitsPoolIncNewSharers，直接返回
	if len(directReferrals) < BenefitsPoolIncNewSharers {
		return len(directReferrals), 0, nil
	}

	// 查询已经记录在user_benefits_pool_log中的下级用户ID
	var loggedUserIDs []uint64
	if err := l.svcCtx.GetSlaveDB().Model(&model.UserBenefitsPoolLog{}).
		Where("benefits_user_id = ? AND task_type = ?", userID, BenefitsPollLogShareType).
		Pluck("user_id", &loggedUserIDs).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return len(directReferrals), 0, err
	}

	// 过滤掉已经记录的下级用户
	var validReferrals []model.User
	for _, referral := range directReferrals {
		isLogged := false
		for _, loggedID := range loggedUserIDs {
			if uint64(referral.ID) == loggedID {
				isLogged = true
				break
			}
		}
		if !isLogged {
			validReferrals = append(validReferrals, referral)
		}
	}

	// 如果有效下级数量小于BenefitsPoolIncSharers，直接返回
	if len(validReferrals) < BenefitsPoolIncNewSharers {
		return len(directReferrals), 0, nil
	}

	// 使用事务更新福利池和记录日志
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 查询或创建用户的福利池记录
		var benefitsPool model.UserBenefitsPool
		result := tx.Where("user_id = ? AND type = 3", userID).First(&benefitsPool)
		if result.Error != nil {
			if errors.Is(result.Error, gorm.ErrRecordNotFound) {
				// 创建新的福利池记录
				benefitsPool = model.UserBenefitsPool{
					Type:     3,
					Quantity: BenefitsReferralRewardsNums, // 初始设置为2
					UserID:   userID,
				}
				if err := tx.Create(&benefitsPool).Error; err != nil {
					return err
				}
			} else {
				return result.Error
			}
		} else {
			// 更新现有福利池记录
			if err := tx.Model(&benefitsPool).Update("quantity", gorm.Expr("quantity + ?", BenefitsReferralRewardsNums)).Error; err != nil {
				return err
			}
		}

		// 记录前BenefitsPoolIncSharers个有效下级到日志表
		for i := 0; i < BenefitsPoolIncNewSharers; i++ {
			log := model.UserBenefitsPoolLog{
				TaskType:       BenefitsPollLogShareType,
				BenefitsUserId: userID,
				UserID:         uint64(validReferrals[i].ID),
			}
			if err := tx.Create(&log).Error; err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return len(directReferrals), 0, err
	}

	return len(directReferrals), 1, nil
}
