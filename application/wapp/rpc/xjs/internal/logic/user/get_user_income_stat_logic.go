package userlogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserIncomeStatLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserIncomeStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserIncomeStatLogic {
	return &GetUserIncomeStatLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户收益统计
func (l *GetUserIncomeStatLogic) GetUserIncomeStat(in *pb.GetUserIncomeStatReq) (*pb.GetUserIncomeStatResp, error) {
	// 初始化响应
	resp := &pb.GetUserIncomeStatResp{}

	// 获取用户余额
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Select("balance, withdraw, sharer_appid").Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Logger.Errorf("获取用户余额失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取用户收益统计失败")
	}
	// 预估收益
	resp.Balance = common.CentToYuanFloat(int64(user.Balance))
	// 可提现余额
	resp.Withdraw = common.CentToYuanFloat(int64(user.Withdraw))
	// 已结算收益
	resp.Settlement = common.CentToYuanFloat(int64(user.Settlement))

	// 获取总数
	var orderTotal int64
	if err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).Where("sharer_appid = ?", user.SharerAppid).Count(&orderTotal).Error; err != nil {
		l.Logger.Errorf("获取用户订单数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取用户订单数失败")
	}
	resp.OrderCount = orderTotal

	// 获取当前日期
	now := time.Now()
	today := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	yesterday := today.AddDate(0, 0, -1)

	// 获取当前月份的起始和结束日期
	currentMonthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	var currentMonthEnd time.Time
	if now.Month() == 12 {
		currentMonthEnd = time.Date(now.Year()+1, 1, 1, 0, 0, 0, 0, now.Location())
	} else {
		currentMonthEnd = time.Date(now.Year(), now.Month()+1, 1, 0, 0, 0, 0, now.Location())
	}

	// 获取上个月的起始和结束日期
	var lastMonthStart time.Time
	var lastMonthEnd time.Time
	if now.Month() == 1 {
		lastMonthStart = time.Date(now.Year()-1, 12, 1, 0, 0, 0, 0, now.Location())
		lastMonthEnd = currentMonthStart
	} else {
		lastMonthStart = time.Date(now.Year(), now.Month()-1, 1, 0, 0, 0, 0, now.Location())
		lastMonthEnd = currentMonthStart
	}

	// 获取今日收益
	var todayIncome float64
	if err := l.svcCtx.GetSlaveDB().Model(&model.TaskSettlementDetail{}).
		Where("user_id = ? AND create_time >= ? AND create_time < ? AND account_type = 'balance'",
			in.UserId, today.Format("2006-01-02 15:04:05"), today.AddDate(0, 0, 1).Format("2006-01-02 15:04:05")).
		Select("COALESCE(SUM(amount_change), 0) as total").
		Scan(&todayIncome).Error; err != nil {
		l.Logger.Errorf("获取今日收益失败: %v", err)
		// 继续执行，不返回错误
	}
	resp.TodayIncome = float64(todayIncome / 100)

	// 获取昨日收益
	var yesterdayIncome float64
	if err := l.svcCtx.GetSlaveDB().Model(&model.TaskSettlementDetail{}).
		Where("user_id = ? AND create_time >= ? AND create_time < ? AND account_type = 'balance'",
			in.UserId, yesterday.Format("2006-01-02 15:04:05"), today.Format("2006-01-02 15:04:05")).
		Select("COALESCE(SUM(amount_change), 0) as total").
		Scan(&yesterdayIncome).Error; err != nil {
		l.Logger.Errorf("获取昨日收益失败: %v", err)
		// 继续执行，不返回错误
	}
	resp.YesterdayIncome = float64(yesterdayIncome / 100)

	// 获取本月收益
	var currentMonthIncome float64
	if err := l.svcCtx.GetSlaveDB().Model(&model.TaskSettlementDetail{}).
		Where("user_id = ? AND create_time >= ? AND create_time < ? AND account_type = 'balance'",
			in.UserId, currentMonthStart.Format("2006-01-02 15:04:05"), currentMonthEnd.Format("2006-01-02 15:04:05")).
		Select("COALESCE(SUM(amount_change), 0) as total").
		Scan(&currentMonthIncome).Error; err != nil {
		l.Logger.Errorf("获取本月收益失败: %v", err)
		// 继续执行，不返回错误
	}
	resp.CurrentMonthIncome = float64(currentMonthIncome / 100)

	// 获取上月收益
	var lastMonthIncome float64
	if err := l.svcCtx.GetSlaveDB().Model(&model.TaskSettlementDetail{}).
		Where("user_id = ? AND create_time >= ? AND create_time < ? AND account_type = 'balance'",
			in.UserId, lastMonthStart.Format("2006-01-02 15:04:05"), lastMonthEnd.Format("2006-01-02 15:04:05")).
		Select("COALESCE(SUM(amount_change), 0) as total").
		Scan(&lastMonthIncome).Error; err != nil {
		l.Logger.Errorf("获取上月收益失败: %v", err)
		// 继续执行，不返回错误
	}
	resp.LastMonthIncome = float64(lastMonthIncome / 100)

	// 获取粉丝总数
	var fansCount int64
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Where("invite_from = ?", in.UserId).
		Count(&fansCount).Error; err != nil {
		l.Logger.Errorf("获取粉丝总数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取用户收益统计失败")
	}
	resp.FansCount = fansCount

	return resp, nil
}
