package userlogic

import (
	"context"
	"time"

	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserFansListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserFansListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserFansListLogic {
	return &GetUserFansListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户粉丝列表
func (l *GetUserFansListLogic) GetUserFansList(in *pb.GetUserFansListReq) (*pb.GetUserFansListResp, error) {
	// 初始化响应
	resp := &pb.GetUserFansListResp{
		List: make([]*pb.UserFansItem, 0),
	}

	// 查询总粉丝数
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Where("invite_from = ?", in.UserId).
		Count(&resp.Total).Error; err != nil {
		l.Logger.Errorf("获取粉丝总数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取粉丝列表失败")
	}

	// 获取今日日期范围
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	todayEnd := todayStart.Add(24 * time.Hour)

	// 查询今日新增粉丝数
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Where("invite_from = ? AND created_at >= ? AND created_at < ?",
			in.UserId, todayStart, todayEnd).
		Count(&resp.TodayCount).Error; err != nil {
		l.Logger.Errorf("获取今日粉丝数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取粉丝列表失败")
	}

	// 获取昨日日期范围
	yesterdayStart := todayStart.Add(-24 * time.Hour)
	yesterdayEnd := todayStart

	// 查询昨日新增粉丝数
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Where("invite_from = ? AND created_at >= ? AND created_at < ?",
			in.UserId, yesterdayStart, yesterdayEnd).
		Count(&resp.YestCount).Error; err != nil {
		l.Logger.Errorf("获取昨日粉丝数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取粉丝列表失败")
	}

	// 分页查询粉丝列表
	offset := int(in.PageRequest.PageSize * (in.PageRequest.PageNo - 1))
	var fans []model.User
	// 构建基础查询
	query := l.svcCtx.GetSlaveDB().
		Select("id, nick_name, avatar, mobile, created_at, level").
		Where("invite_from = ?", in.UserId)

	// 如果提供了关键字，添加手机号筛选条件
	if in.PageRequest != nil && in.PageRequest.Keyword != "" {
		query = query.Where("mobile LIKE ?", "%"+in.PageRequest.Keyword+"%")
	}

	// 执行查询
	if err := query.
		Order("created_at DESC").
		Limit(int(in.PageRequest.PageSize)).
		Offset(offset).
		Find(&fans).Error; err != nil {
		l.Logger.Errorf("获取粉丝列表失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取粉丝列表失败")
	}

	// if err := l.svcCtx.GetSlaveDB().
	// 	Select("id, nick_name, avatar, created_at").
	// 	Where("invite_from = ?", in.UserId).
	// 	Order("created_at DESC").
	// 	Limit(int(in.PageRequest.PageSize)).
	// 	Offset(offset).
	// 	Find(&fans).Error; err != nil {
	// 	l.Logger.Errorf("获取粉丝列表失败: %v", err)
	// 	return nil, xerr.NewErrMsg(l.ctx, "获取粉丝列表失败")
	// }

	// 转换数据格式
	for _, fan := range fans {
		resp.List = append(resp.List, &pb.UserFansItem{
			Id:        uint64(fan.ID),
			NickName:  fan.NickName,
			Avatar:    fan.Avatar,
			Mobile:    fan.Mobile,
			Level:     int64(fan.Level),
			CreatedAt: fan.CreatedAt.Format("2006-01-02 15:04:05"),
		})
	}

	return resp, nil
}
