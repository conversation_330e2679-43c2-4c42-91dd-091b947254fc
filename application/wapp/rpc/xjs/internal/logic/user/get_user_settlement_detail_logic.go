package userlogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserSettlementDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserSettlementDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserSettlementDetailLogic {
	return &GetUserSettlementDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SettlementDetailWithOrder 结算明细与订单关联查询结果
type SettlementDetailWithOrder struct {
	model.TaskSettlementDetail
	OrderID     string `gorm:"column:order_id"`      // 订单ID
	ProductName string `gorm:"column:product_title"` // 商品名称
}

// 获取结算明细
func (l *GetUserSettlementDetailLogic) GetUserSettlementDetail(in *pb.GetUserSettlementDetailReq) (*pb.GetUserSettlementDetailResp, error) {
	// 参数验证
	if in.UserId == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "用户ID不能为空")
	}

	// 开始日期 结束日期 格式化为 2006-01-02 15:04:05
	var startTime, endTime time.Time
	var hasTimeFilter bool

	if in.StartDate != "" && in.EndDate != "" {
		// 解析日期并设置为当天的开始时间（00:00:00）
		startTime, _ = time.ParseInLocation("2006-01-02", in.StartDate, time.Local)
		// 解析日期并设置为当天的结束时间（23:59:59）
		endTimeTemp, _ := time.ParseInLocation("2006-01-02", in.EndDate, time.Local)
		endTime = endTimeTemp.Add(24*time.Hour - time.Second) // 23:59:59
		hasTimeFilter = true
	}

	offset := int(in.PageRequest.PageSize * (in.PageRequest.PageNo - 1))
	var total int64

	// 构建基础查询条件
	baseWhere := l.svcCtx.GetSlaveDB().
		Table("task_settlement_detail as tsd").
		Where("tsd.user_id = ? AND tsd.deleted_at IS NULL AND tsd.account_type='balance'", in.UserId).
		Where("tsd.type IN (?, ?, ?, ?)", 1, 2, 3, 4)

	// 如果有时间过滤条件，则添加时间范围
	if hasTimeFilter {
		baseWhere = baseWhere.Where("tsd.create_time BETWEEN ? AND ?", startTime, endTime)
	}

	if in.Type > 0 {
		baseWhere = baseWhere.Where("tsd.type = ?", in.Type)
	}

	// 获取总数
	if err := baseWhere.Count(&total).Error; err != nil {
		logc.Errorf(l.ctx, "获取结算详情总数失败 err:%s", err.Error())
		return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, "获取结算明细总数失败")
	}

	// 执行联表查询获取详细数据
	var results []SettlementDetailWithOrder
	query := l.svcCtx.GetSlaveDB().
		Table("task_settlement_detail as tsd").
		Select("tsd.*, co.order_id, co.product_title").
		Joins("LEFT JOIN commission_orders as co ON tsd.associated_id = co.order_id").
		Where("tsd.user_id = ? AND tsd.deleted_at IS NULL AND tsd.account_type='balance'", in.UserId).
		Where("tsd.type IN (?, ?, ?, ?)", 1, 2, 3, 4)

	// 如果有时间过滤条件，则添加时间范围
	if hasTimeFilter {
		query = query.Where("tsd.create_time BETWEEN ? AND ?", startTime, endTime)
	}

	// 添加类型过滤条件
	if in.Type > 0 {
		query = query.Where("tsd.type = ?", in.Type)
	}

	query = query.Limit(int(in.PageRequest.PageSize)).
		Offset(offset).
		Order("tsd.create_time DESC")

	if err := query.Find(&results).Error; err != nil {
		logc.Errorf(l.ctx, "获取结算详情列表失败 err:%s", err.Error())
		return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, "获取结算明细列表失败")
	}

	// 计算统计信息 - 基于相同筛选条件查询所有数据
	var summaryResults []struct {
		Type   int8  `gorm:"column:type"`
		Amount int64 `gorm:"column:amount"`
	}

	summaryQuery := l.svcCtx.GetSlaveDB().
		Table("task_settlement_detail as tsd").
		Select("tsd.type, SUM(tsd.amount_change) as amount").
		Where("tsd.user_id = ? AND tsd.deleted_at IS NULL AND tsd.account_type='withdraw'", in.UserId).
		Where("tsd.type IN (?, ?, ?, ?)", 1, 2, 3, 4)

	// 应用相同的时间和类型过滤条件
	if hasTimeFilter {
		summaryQuery = summaryQuery.Where("tsd.create_time BETWEEN ? AND ?", startTime, endTime)
	}
	if in.Type > 0 {
		summaryQuery = summaryQuery.Where("tsd.type = ?", in.Type)
	}

	summaryQuery = summaryQuery.Group("tsd.type")

	if err := summaryQuery.Find(&summaryResults).Error; err != nil {
		logc.Errorf(l.ctx, "获取统计信息失败 err:%s", err.Error())
		// 统计信息失败不影响主流程，继续执行
	}

	// 根据类型计算不同收益
	var totalAmount, directAmount, indirectAmount, agentAmount int64

	for _, summary := range summaryResults {
		amount := summary.Amount
		totalAmount += amount

		// 根据类型分类统计（这里需要根据实际业务逻辑调整）
		switch summary.Type {
		case 1: // 1为直接收益
			directAmount += amount
		case 2: // 2为间接收益
			indirectAmount += amount
		case 3: // 3为代理商收益
			agentAmount += amount
		case 4: // 4为运营商收益
			agentAmount += amount
		default:
			// 其他类型归为直接收益
			directAmount += amount
		}
	}

	// 转换响应
	resp := &pb.GetUserSettlementDetailResp{
		List:  make([]*pb.UserSettlementDetailItem, 0, len(results)),
		Total: uint64(total),
		Summary: &pb.SettlementSummary{
			TotalAmount:    common.CentToYuanFloat(totalAmount),
			DirectAmount:   common.CentToYuanFloat(directAmount),
			IndirectAmount: common.CentToYuanFloat(indirectAmount),
			AgentAmount:    common.CentToYuanFloat(agentAmount),
		},
	}

	for _, result := range results {
		resp.List = append(resp.List, &pb.UserSettlementDetailItem{
			Id:           uint64(result.TaskSettlementDetail.MODEL_BASE.ID),
			Amount:       common.CentToYuanFloat(result.TaskSettlementDetail.Amount),
			AmountChange: common.CentToYuanFloat(result.TaskSettlementDetail.AmountChange),
			Type:         int32(result.TaskSettlementDetail.Type),
			CreateTime:   result.TaskSettlementDetail.CreateTime.Format("2006-01-02 15:04:05"),
			Remark:       result.TaskSettlementDetail.Remark,
			OrderId:      result.OrderID,     // 添加订单ID
			ProductName:  result.ProductName, // 添加商品名称
		})
	}
	return resp, nil
}
