package userlogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"xj-serv/pkg/result/xerr"

	"github.com/gofrs/uuid/v5"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginAndRegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLoginAndRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginAndRegisterLogic {
	return &LoginAndRegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LoginAndRegisterLogic) LoginAndRegister(in *pb.UserLoginReq) (*pb.UserTokeReq, error) {
	// 调用验证方法
	if err := in.Validate(); err != nil {
		return nil, xerr.NewErrCode(xerr.ParamsValidateError)
	}

	var user model.User
	if err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 用户是否存在
		err := tx.Where("mobile = ?", in.Phone).First(&user).Error
		if errors.Is(err, gorm.ErrRecordNotFound) { // 手机号码未注册
			if err = copier.Copy(&user, in); err != nil {
				return err
			}
			user.Mobile = in.Phone
			user.Uuid = uuid.Must(uuid.NewV4())
			user.Avatar = in.Avatar
			user.NickName = in.Nickname
			user.ShareCode = "-"
			user.RegIpv4 = in.Ip
			user.Birthday = time.Now().AddDate(0, 0, -1)
			user.LastLoginTime = uint(time.Now().Unix())
			if err = tx.Model(&model.User{}).Create(&user).Error; err != nil {
				return err
			}

			sCode := common.UidEncode(uint64(user.ID))
			if err = tx.Model(&model.User{}).Where("id = ?", user.ID).Updates(map[string]interface{}{
				"share_code": sCode,
			}).Error; err != nil {
				return err
			}

			// 查找上级确认关系
			// fromOfficial := true // 是否来自官方账号
			var fUser model.User
			if len(in.ShareCode) > 0 {
				if err = tx.Model(&model.User{}).Where("share_code = ?", in.ShareCode).First(&fUser).Error; err == nil {
					// 如果找到上级用户，则是非官方
					// fromOfficial = false

					// 处理首次推荐奖励
					// if err := l.AddFirstReferralRewardPoints(tx, fUser); err != nil {
					// 	return err
					// }
					//
					// 处理分享曝光奖励
					// if err := l.AddExposureOpportunity(tx, uint64(fUser.ID)); err != nil {
					// 	logx.Errorf("处理分享曝光奖励失败: %v", err)
					// }
				} else {
					fUser.ID = int64(l.svcCtx.Config.BLParams.DirectRegisterUserOwner)
				}

			} else {
				fUser.ID = int64(l.svcCtx.Config.BLParams.DirectRegisterUserOwner)
			}

			// if fromOfficial {
			// 找不到推荐码对应的账户，放到官方二级系统账号下面
			// var fUserId int64
			// err = tx.Model(&model.UserVideoAccount{}).Select("user_id").
			// 	Where("is_official = ?", 1).
			// 	Order("id desc").
			// 	Limit(1).
			// 	Scan(&fUserId).Error
			//
			// if err != nil {
			// 	fmt.Println(err.Error())
			// 	return err
			// }
			//
			// fUser.ID = fUserId
			// }
			// 更新用户的邀请人
			err = tx.Model(&model.User{}).Where("id = ?", user.ID).Updates(map[string]interface{}{
				"invite_from": fUser.ID,
			}).Error
			if err != nil {
				fmt.Println(err.Error())
				return err
			}
			// 增加新的分享任务
			// r, err := l.svcCtx.TaskRPC.NewShareTask(l.ctx, &taskPb.NewShareTaskReq{
			// 	UserId:   uint64(user.ID),
			// 	FatherId: uint64(fUser.ID),
			// })
			// if err != nil || r.Success == false {
			// 	return err
			// }

			// 新用户24小时内需完成任务
			// taskResp, err := l.svcCtx.DispatcherRPC.PostDelayedTask(l.ctx, &pfsPb.PostTaskReq{
			// 	TaskName:  "new_user_buy_task_check",
			// 	Name:      "wapp.xjs.Tasks",
			// 	MaxRetry:  3,
			// 	ProcessIn: 3600, // 测试用 实际 3600*24
			// 	Method:    "UserBuyTaskCompletedCheck",
			// 	Params:    fmt.Sprintf("{\"id\": %d}", user.ID),
			// })
			//
			// 新用户注册奖励
			// if err := l.AddNewUserRewardPoints(tx, uint64(user.ID)); err != nil {
			// 	return err
			// }

			// 同步新人福利任务的数据到网络
			// 在事务中发送RabbitMQ消息
			if l.svcCtx.QueueManager != nil {
				// 构建消息内容
				msgData := map[string]interface{}{
					"type":         "benefits_user_network_sync", // 消息通知中的网体类型
					"user_id":      user.ID,
					"father_id":    fUser.ID,
					"network_type": "benefits", // 网络类型为新人福利
					"timestamp":    time.Now().Unix(),
				}

				// 序列化消息
				msgBytes, err := json.Marshal(msgData)
				if err != nil {
					l.Errorf("序列化RabbitMQ消息失败: %v", err)
					return err
				}

				// 使用队列管理器发送消息
				err = l.svcCtx.QueueManager.PublishToQueue(
					"UserNetworkSync", // 队列名称，需要在配置中注册
					msgBytes,
				)
				if err != nil {
					l.Errorf("发送RabbitMQ消息失败: %v", err)
					return err // 返回错误会导致事务回滚
				}

				l.Infof("成功同步用户消息到RabbitMQ，UserId: %s, FatherId: %s", user.ID, fUser.ID)
			} else {
				l.Errorf("队列管理器未初始化，无法发送消息")
			}
		} else if err == nil {
			if err = tx.Model(&model.User{}).Where("id = ?", user.ID).Updates(map[string]interface{}{
				"last_login_time": uint(time.Now().Unix()),
			}).Error; err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.UserServError, "登录失败 err: "+err.Error())
	}

	var resp pb.UserTokeReq
	if err := copier.Copy(&resp, user); err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.UserServError, "拷贝token失败 err: "+err.Error())
	}

	return &resp, nil
}

// 新用户注册赠送爆点
func (l *LoginAndRegisterLogic) AddNewUserRewardPoints(tx *gorm.DB, userID uint64) error {
	exchange := l.svcCtx.Config.NewUserRewards.Point.Register
	if err := tx.Model(&model.User{}).Select("points").Where("id=?", userID).Updates(map[string]interface{}{
		"points": gorm.Expr(fmt.Sprintf("points + %d", exchange)),
	}).Error; err != nil {
		return xerr.NewErrCodeMsg(xerr.DeductionError, "增加新用户注册爆点奖励失败")
	}

	// 记录爆点奖励流水
	if err := tx.Create(&model.TaskUserPointLog{
		UserId: userID,
		Points: uint(exchange),
		Type:   1, // 1表示注册奖励类型
	}).Error; err != nil {
		l.Logger.Errorf("记录新用户注册爆点奖励流水失败: %+v", err)
		return xerr.NewErrCodeMsg(xerr.DeductionError, "记录注册爆点奖励流水失败")
	}

	l.Logger.Infof("新用户[%d]获得注册爆点奖励: %d", userID, exchange)
	return nil
}

// 首次推荐用户奖励
func (l *LoginAndRegisterLogic) AddFirstReferralRewardPoints(tx *gorm.DB, fUser model.User) error {
	// 查找上级用户是否第一次推荐
	var inviteCount int64
	if err := tx.Model(&model.User{}).Where("invite_from = ?", fUser.ID).Count(&inviteCount).Error; err != nil {
		logx.Errorf("查询邀请用户数量失败: %v", err)
		// 如果查询失败，继续执行，默认为首次推荐
	}

	// 如果是第一次推荐，发送爆点奖励
	if inviteCount == 1 {
		rewardPoints := l.svcCtx.Config.NewUserRewards.Point.Share
		if err := tx.Model(&model.User{}).Select("points").Where("id=?", fUser.ID).Updates(map[string]interface{}{
			"points": gorm.Expr(fmt.Sprintf("points + %d", rewardPoints)),
		}).Error; err != nil {
			return xerr.NewErrCodeMsg(xerr.DbError, "增加首次推荐爆点奖励失败")
		}

		// 记录爆点奖励流水
		if err := tx.Create(&model.TaskUserPointLog{
			UserId: uint64(fUser.ID),
			Points: uint(rewardPoints),
			Type:   2, // 2表示首次推荐奖励类型
		}).Error; err != nil {
			l.Logger.Errorf("记录首次推荐爆点奖励流水失败: %+v", err)
			return xerr.NewErrCodeMsg(xerr.DbError, "记录首次推荐爆点奖励流水失败")
		}

		l.Logger.Infof("用户[%d]获得首次推荐爆点奖励: %d", fUser.ID, rewardPoints)
	}
	return nil
}

// 增加用户曝光机会
func (l *LoginAndRegisterLogic) AddExposureOpportunity(tx *gorm.DB, userID uint64) error {
	// 查询用户邀请的新用户数量
	var inviteCount int64
	if err := tx.Model(&model.User{}).Where("invite_from = ?", userID).Count(&inviteCount).Error; err != nil {
		logx.Errorf("查询邀请用户数量失败: %v", err)
		return xerr.NewErrCodeMsg(xerr.DbError, "查询邀请用户数量失败")
	}

	// 每邀请6个新用户，奖励1次新人福利任务曝光机会
	var exposureThreshold int64 = 6
	rewardCount := int(inviteCount / exposureThreshold)

	// 查询已经奖励的分享类型曝光次数
	var rewardedCount int64
	if err := tx.Model(&model.TaskUserExposureLog{}).
		Select("IFNULL(SUM(count), 0)").
		Where("user_id = ? AND exposure_type = ?", userID, 1). // 只查询分享类型(type=1)的记录
		Scan(&rewardedCount).Error; err != nil {
		l.Logger.Errorf("查询已奖励分享类型曝光次数失败: %v", err)
		return xerr.NewErrCodeMsg(xerr.DbError, "查询已奖励分享类型曝光次数失败")
	}

	l.Logger.Infof("用户[%d]已获得分享类型曝光奖励总数: %d", userID, rewardedCount)

	// 计算应该新增的奖励次数
	newRewardCount := rewardCount - int(rewardedCount)

	// 如果有新的奖励需要添加
	if newRewardCount > 0 {
		exposure := model.TaskUserExposure{
			UserId: userID,
			Count:  uint(newRewardCount),
		}

		// upsert
		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "user_id"}},
			DoUpdates: clause.Assignments(map[string]interface{}{"count": gorm.Expr("count + ?", newRewardCount)}),
		}).Create(&exposure).Error; err != nil {
			l.Logger.Errorf("增加用户曝光奖励失败: %+v", err)
			return xerr.NewErrCodeMsg(xerr.DeductionError, "添加曝光奖励失败")
		}
		// 增加曝光日志记录
		exposureLog := model.TaskUserExposureLog{
			UserId:       userID,
			Count:        uint(newRewardCount),
			ExposureType: 1, // 设置为分享类型曝光
		}

		if err := tx.Create(&exposureLog).Error; err != nil {
			l.Logger.Errorf("记录用户曝光日志失败: %+v", err)
			return xerr.NewErrCodeMsg(xerr.DbError, "记录曝光日志失败")
		}

		logx.Infof("用户 %d 获得 %d 次曝光奖励", userID, newRewardCount)
	}

	return nil
}
