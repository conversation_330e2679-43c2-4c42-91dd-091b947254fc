package userlogic

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserDetailLogic {
	return &GetUserDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取用户信息详情
func (l *GetUserDetailLogic) GetUserDetail(in *pb.IDReq) (*pb.UserDetailResp, error) {
	if in.Id == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "用户ID不能为空")
	}

	// 查询用户信息
	var user model.User
	result := l.svcCtx.GetSlaveDB().
		Preload("UserLevel").
		Where("id = ?", in.Id).
		First(&user)

	if result.Error != nil {
		l.Logger.Errorf("查询用户信息失败: %v", result.Error)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	//var resp pb.UserDetailResp
	//if err := copier.Copy(&resp, user); err != nil {
	//	logc.Errorf(l.ctx, "拷贝权限数据失败 err:%s", err.Error())
	//	return nil, xerr.NewErrCodeMsg(xerr.ProjectRuntimeError, "拷贝权限数据失败")
	//}
	//return &resp, nil
	return &pb.UserDetailResp{
		CreatedAt: user.CreatedAt.Format("2006-01-02 15:04:05"),
		//UpdatedAt:      user.UpdatedAt.Format("2006-01-02 15:04:05"),
		Uuid:           user.Uuid.String(),
		Mobile:         user.Mobile,
		NickName:       user.NickName,
		Avatar:         user.Avatar,
		Gender:         uint32(user.Gender),
		Country:        user.Country,
		Province:       user.Province,
		City:           user.City,
		AddressId:      uint32(user.AddressID),
		Balance:        float64(user.Balance),
		Withdraw:       float64(user.Withdraw),
		Points:         uint32(user.Points),
		PayMoney:       float64(user.PayMoney),
		ExpendMoney:    float64(user.ExpendMoney),
		GradeId:        uint32(user.GradeID),
		Platform:       user.Platform,
		LastLoginTime:  uint32(user.LastLoginTime),
		Openid:         user.Openid,
		Unionid:        user.Unionid,
		SharerAppid:    user.SharerAppid,
		ContactName:    user.ContactName,
		ContactNumber:  user.ContactNumber,
		ShareCode:      user.ShareCode,
		ShareCodeImage: user.ShareCodeImage,
		RegIpv4:        user.RegIpv4,
		InviteFrom:     user.InviteFrom,
		RegSource:      user.RegSource,
		Level:          int32(user.Level),
		LevelName: func() string {
			if len(user.UserLevel.Name) > 0 {
				return user.UserLevel.Name
			}
			return "新人用户"
		}(), // 添加等级名称
		IsEnterprise: int32(btoi(user.IsEnterprise)),
		Birthday:     user.Birthday.Format("2006-01-02"),
		RoleId:       int32(user.RoleId),
		FinderId:     user.FinderId,
	}, nil
}

// 辅助函数：bool 转 int
func btoi(b bool) int32 {
	if b {
		return 1
	}
	return 0
}
