package userlogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncUserNetworkLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncUserNetworkLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncUserNetworkLogic {
	return &SyncUserNetworkLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步用户网络关系
func (l *SyncUserNetworkLogic) SyncUserNetwork(in *pb.SyncUserNetworkReq) (*pb.SyncUserNetworkResp, error) {
	// 参数校验
	if in.UserId == 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户ID不能为空")
	}
	if in.FatherId == 0 {
		// 如果上级id为0，则归属到指定上级
		//return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "上级用户ID不能为空")
		in.FatherId = l.svcCtx.Config.BLParams.DirectRegisterUserOwner
	}
	if in.NetworkType == "" {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "网络类型不能为空")
	}

	// 检查用户是否存在
	var userCount int64
	if err := l.svcCtx.MasterDB.Model(&model.User{}).Where("id = ?", in.UserId).Count(&userCount).Error; err != nil {
		l.Errorf("查询用户失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询用户失败")
	}
	if userCount == 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户不存在")
	}

	// 检查上级用户是否存在
	var fatherCount int64
	if err := l.svcCtx.MasterDB.Model(&model.User{}).Where("id = ?", in.FatherId).Count(&fatherCount).Error; err != nil {
		l.Errorf("查询上级用户失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询上级用户失败")
	}
	if fatherCount == 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "上级用户不存在")
	}

	// 检查是否已存在相同的网络关系
	var existCount int64
	if err := l.svcCtx.MasterDB.Model(&model.UserDynamicNetworks{}).
		Where("user_id = ? AND father_id = ? AND network_type = ?", in.UserId, in.FatherId, in.NetworkType).
		Count(&existCount).Error; err != nil {
		l.Errorf("查询网络关系失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询网络关系失败")
	}

	// 如果已存在相同的网络关系，则直接返回成功
	if existCount > 0 {
		l.Infof("用户网络关系已存在: user_id=%d, father_id=%d, network_type=%s", in.UserId, in.FatherId, in.NetworkType)
		return &pb.SyncUserNetworkResp{Success: true}, nil
	}

	// 创建一个空的TaskBitmask并转换为MySQL bit格式
	taskBitMask := common.NewTaskBitmask(8)
	bitMarkBytes := taskBitMask.ToBitFormat()

	// 创建用户网络关系
	network := &model.UserDynamicNetworks{
		UserID:      in.UserId,
		FatherID:    in.FatherId,
		NetworkType: in.NetworkType,
		Mark:        string(bitMarkBytes), // 默认标识符
	}

	// 设置创建时间
	if in.Timestamp > 0 {
		createTime := time.Unix(in.Timestamp, 0)
		network.CreatedAt = createTime
		network.UpdatedAt = createTime
	}

	// 保存到数据库
	if err := l.svcCtx.MasterDB.Create(network).Error; err != nil {
		l.Errorf("创建用户网络关系失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "创建用户网络关系失败")
	}

	l.Infof("成功创建用户网络关系: user_id=%d, father_id=%d, network_type=%s", in.UserId, in.FatherId, in.NetworkType)
	return &pb.SyncUserNetworkResp{Success: true}, nil
}
