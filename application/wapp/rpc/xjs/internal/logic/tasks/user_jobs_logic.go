package taskslogic

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/internal/model"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/result/xerr"

	"gorm.io/gorm"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type UserJobsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUserJobsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserJobsLogic {
	return &UserJobsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UserJobsLogic) UserJobs(in *pb.IDReq) (*pb.UserJobsResp, error) {
	db := l.svcCtx.GetSlaveDB()

	// 检查是否在正常网体中
	var exists int64 = 0
	if err := db.Model(&model.TaskUserStatus{}).Where("user_id = ?", in.Id).Count(&exists).Error; err != nil {
		logc.Errorf(l.ctx, "查询用户网体错误 err:%s", err.Error())
		return nil, xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "查询用户网体错误")
	}

	// 网体中不存在该用户
	if exists == 0 {
		logc.Errorf(l.ctx, "用户不在网体中 err:%s", in.GetId())
		return nil, xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "用户不在网体中")
	}

	var father FatherLevel
	var jobs []*model.TaskUserJobs
	// 1.直接上级任务
	FindDirectFather(db, in.Id, &father)

	var account model.UserVideoAccount
	if err := db.Where("user_id = ?", father.UserId).First(&account).Error; err != nil {
		logc.Errorf(l.ctx, "查询上级节点视频账号错误 err:%s", err.Error())
		return nil, xerr.NewErrMsg(l.ctx, "查询上级节点视频账号错误")
	}

	jobs = append(jobs, &model.TaskUserJobs{
		UserID:             in.Id,
		TaskLevel:          1,
		SellerVideoAccount: account.VideoAccount,
		VideoAccountType:   account.IsOfficial,
		UserVideoAccount:   account,
	})

	// 2.间接上级任务
	// 这里有个判断，如果爸爸节点在第三层，需要直推找原始推荐关系爷爷节点作为间接上级
	if father.UserId > 0 {
		// 如果直接上级是官方节点，则继续在User查上级
		var indirectId uint64
		if account.IsOfficial == 1 {
			// 查询间接上级的账号信息
			if err := db.Model(&model.User{}).Where("id = ?", father.UserId).Select("invite_from").Scan(&indirectId).Error; err != nil {
				logc.Errorf(l.ctx, "查询上级节点账号错误 err:%s", err.Error())
				return nil, xerr.NewErrMsg(l.ctx, "查询上级节点账号错误")
			}
		} else {
			// 否则查询上级节点的jobs level为1的记录，获取上级的直接任务节点
			var sellerVideoAccount string
			if err := db.Model(&model.TaskUserJobs{}).
				Where("user_id = ? AND task_level = ?", father.UserId, 1).
				Select("seller_video_account").
				Limit(1).
				Scan(&sellerVideoAccount).Error; err != nil {
				logc.Errorf(l.ctx, "查询间接上级错误 err:%s", err.Error())
			}
			if sellerVideoAccount != "" {
				var indirectAccount model.UserVideoAccount
				if err := db.Where("video_account = ?", sellerVideoAccount).First(&indirectAccount).Error; err != nil {
					logc.Errorf(l.ctx, "根据视频账号[%s]查询间接上级失败: %v", sellerVideoAccount, err)
				} else {
					indirectId = indirectAccount.UserID
					logc.Infof(l.ctx, "找到间接上级用户ID: %d", indirectId)
				}
			}
		}

		if indirectId > 0 {
			// 查询间接上级的视频账号信息
			var account model.UserVideoAccount
			if err := db.Where("user_id = ?", indirectId).First(&account).Error; err != nil {
				logc.Errorf(l.ctx, "查询上级节点视频账号错误 err:%s", err.Error())
				return nil, xerr.NewErrMsg(l.ctx, "查询上级节点视频账号错误")
			}
			jobs = append(jobs, &model.TaskUserJobs{
				UserID:             in.Id,
				TaskLevel:          2,
				SellerVideoAccount: account.VideoAccount,
				VideoAccountType:   account.IsOfficial,
				UserVideoAccount:   account,
			})
		} else {
			// 如果没找到
			logc.Errorf(l.ctx, "未找到间接上级 fatherId:%s", father.UserId)
		}
	}

	// 3.达人任务池 动态获取
	// 获取有曝光额度的达人账号，且必须在user_video_account表中存在记录
	var exposureUsers []struct {
		UserID uint64
		Count  uint
	}

	if err := db.Table("task_user_exposure tue").
		Select("tue.user_id, tue.count").
		Joins("INNER JOIN user_video_account uva ON tue.user_id = uva.user_id").
		Where("tue.count > ? AND uva.is_official = ?", 0, REGULAR_STORE).
		Order("RAND()").
		Limit(2).
		Find(&exposureUsers).Error; err != nil {
		logc.Errorf(l.ctx, "获取有曝光额度的达人失败 err:%s", err.Error())
		return nil, xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "获取达人任务池失败")
	}

	// 获取这些用户的视频账号
	taskLevel := 3
	for _, exposureUser := range exposureUsers {
		var account model.UserVideoAccount
		if err := db.Where("user_id = ?", exposureUser.UserID).
			First(&account).Error; err != nil {
			logc.Errorf(l.ctx, "获取用户[%d]视频账号失败: %v", exposureUser.UserID, err)
			continue
		}

		jobs = append(jobs, &model.TaskUserJobs{
			UserID:             in.Id,
			TaskLevel:          int8(taskLevel),
			Status:             TASK_UN_FINISHED,
			SellerVideoAccount: account.VideoAccount,
			VideoAccountType:   account.IsOfficial,
			UserVideoAccount:   account,
		})
		taskLevel++
	}

	logc.Infof(l.ctx, "用户[%d]获取到%d个达人任务", in.Id, len(jobs))

	// 4.官方任务...暂时不做处理

	if len(jobs) != 4 {
		logc.Errorf(l.ctx, "获取的任务数量不正确, 需要4个任务, 当前数量: %d", len(jobs))
		return nil, xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "获取的任务数量不正确")
	}

	// 根据jobs生成以tasklevel为下标的map
	jobsMap := make(map[int8]*model.TaskUserJobs)
	for _, job := range jobs {
		jobsMap[job.TaskLevel] = job
	}

	if err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		var existingJobs []*model.TaskUserJobs
		// 检查用户是否已有任务记录
		if err := tx.Where("user_id = ?",
			in.GetId()).Find(&existingJobs).Error; err != nil {
			return xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "查询用户任务失败")
		}

		// 如果没有，初始化任务列表
		if len(existingJobs) == 0 {
			// 记录不存在，创建新任务记录
			logc.Infof(l.ctx, "用户[%d]不存在任务记录，创建新记录", in.GetId())
			if err := tx.Omit("UserVideoAccount").Create(&jobs).Error; err != nil {
				logc.Errorf(l.ctx, "创建用户任务记录失败. err: %s", err.Error())
				return xerr.NewErrCodeMsg(xerr.MiniShopRuntimeError, "创建用户任务记录失败")
			}
		} else {
			// 记录已存在，判断上级任务是否有完成，有完成的则不作变更
			// 一个都未完成，更新最新的任务到任务表
			var completedUpperTasks int64
			// 查询上级任务的完成情况
			err := tx.Model(&model.TaskUserJobs{}).
				Where("user_id = ? AND task_level <= ? AND status = ?",
					in.GetId(), 2, TASK_FINISHED).
				Count(&completedUpperTasks).Error

			if err != nil {
				logc.Errorf(l.ctx, "查询用户上级任务完成情况失败: %v", err)
				return xerr.NewErrCodeMsg(xerr.DbError, "查询用户上级任务完成情况失败")
			}
			// 任务更新
			for _, existingJob := range existingJobs {
				// 如果上级任务一个都未完成(completedUpperTasks=0)，更新上级任务
				if completedUpperTasks == 0 {
					if existingJob.TaskLevel <= 2 {
						// 更新当前任务为最新状态
						existingJob = jobsMap[existingJob.TaskLevel]
						err = tx.Model(&model.TaskUserJobs{}).Where("user_id = ? and task_level = ?", in.GetId(), existingJob.TaskLevel).Updates(existingJob).Error
						if err != nil {
							logc.Errorf(l.ctx, "更新用户任务记录失败: %v", err)
							return xerr.NewErrCodeMsg(xerr.DbError, "更新用户任务记录失败")
						}
					}
				}
				// 达人任务池检查是否有未完成任务，有则更新
				if existingJob.TaskLevel > 2 && existingJob.Status == TASK_UN_FINISHED {
					// 更新当前任务为最新状态
					existingJob = jobsMap[existingJob.TaskLevel]
					err = tx.Model(&model.TaskUserJobs{}).Where("user_id = ? and task_level = ?", in.GetId(), existingJob.TaskLevel).Updates(existingJob).Error
					if err != nil {
						logc.Errorf(l.ctx, "更新用户任务记录失败: %v", err)
						return xerr.NewErrCodeMsg(xerr.DbError, "更新用户任务记录失败")
					}
				}
			}

			// 覆盖jobs
			jobs = existingJobs
		}
		return nil
	}); err != nil {
		return nil, err
	}

	var accounts []*pb.JobsVideoAccount

	for _, v := range jobs {
		// var account pb.VideoAccount
		var job pb.JobsVideoAccount

		if err := copier.Copy(&account, v.UserVideoAccount); err != nil {
			return nil, err
		}
		job.IsComplete = uint64(v.Status)
		job.IsOfficial = uint64(v.VideoAccountType)
		job.Level = uint64(v.TaskLevel)
		// job.Account = &account
		accounts = append(accounts, &job)

	}
	return &pb.UserJobsResp{
		Jobs: accounts,
	}, nil
}
