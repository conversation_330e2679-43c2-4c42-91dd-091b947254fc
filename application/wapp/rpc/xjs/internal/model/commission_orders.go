package model

import (
	"time"
	"xj-serv/pkg/model"
)

type CommissionOrders struct {
	model.MODEL_BASE
	OrderID               string    `json:"order_id" gorm:"column:order_id"`                                 // 订单号
	SkuID                 string    `json:"sku_id" gorm:"column:sku_id"`                                     // 商品SKU ID
	BuyerOpenID           string    `json:"buyer_open_id" gorm:"column:buyer_open_id"`                       // 买家OpenID
	BuyerUnionID          string    `json:"buyer_union_id" gorm:"column:buyer_union_id"`                     // 买家UnionID
	CommissionStatus      string    `json:"commission_status" gorm:"column:commission_status"`               // 佣金单状态：20-未结算，100-已结算，200-取消结算
	OrderStatus           string    `json:"order_status" gorm:"column:order_status"`                         // 订单状态：10-待付款，12-待收礼，20-待发货，21-部分发货，30-待收货，100-完成，200-售后取消，250-未付款取消
	BalanceDealStatus     string    `json:"balance_deal_status" gorm:"column:balance_deal_status"`           // 余额处理状态：0-未处理 1-已处理
	WithdrawDealStatus    string    `json:"withdraw_deal_status" gorm:"column:withdraw_deal_status"`         // 提现余额处理状态：0-未处理 1-已处理
	ShopAppid             string    `json:"shop_appid" gorm:"column:shop_appid"`                             // 商家小店的AppID
	ProductID             string    `json:"product_id" gorm:"column:product_id"`                             // 商品ID
	ProductThumbImg       string    `json:"product_thumb_img" gorm:"column:product_thumb_img"`               // 商品缩略图
	ProductTitle          string    `json:"product_title" gorm:"column:product_title"`                       // 商品标题
	ActualPayment         int       `json:"actual_payment" gorm:"column:actual_payment"`                     // 支付金额，单位分
	ServiceRatio          int       `json:"service_ratio" gorm:"column:service_ratio"`                       // 机构服务费率，范围[0-1000000]，对应0%-100%
	ServiceAmount         int       `json:"service_amount" gorm:"column:service_amount"`                     // 机构服务费金额，单位分
	ProfitShardingSucTime time.Time `json:"profit_sharding_suc_time" gorm:"column:profit_sharding_suc_time"` // 服务费结算成功时间
	PromotionChannel      int       `json:"promotion_channel" gorm:"column:promotion_channel"`               // 推广渠道：0-橱窗带货，1-推客带货
	FinderNickname        string    `json:"finder_nickname" gorm:"column:finder_nickname"`                   // 视频号达人昵称
	FinderRatio           int       `json:"finder_ratio" gorm:"column:finder_ratio"`                         // 视频号达人佣金率，范围[0-1000000]
	FinderAmount          int       `json:"finder_amount" gorm:"column:finder_amount"`                       // 视频号达人佣金金额，单位分
	OpenFinderID          string    `json:"open_finder_id" gorm:"column:open_finder_id"`                     // 视频号openfinderid
	SharerAppid           string    `json:"sharer_appid" gorm:"column:sharer_appid"`                         // 推客AppID
	SharerNickname        string    `json:"sharer_nickname" gorm:"column:sharer_nickname"`                   // 推客昵称
	SharerRatio           int       `json:"sharer_ratio" gorm:"column:sharer_ratio"`                         // 推客佣金率，范围[0-1000000]
	SharerAmount          int       `json:"sharer_amount" gorm:"column:sharer_amount"`                       // 推客佣金金额，单位分
	OpenSharerID          string    `json:"open_sharer_id" gorm:"column:open_sharer_id"`                     // 推客OpenID
	TalentAppid           string    `json:"talent_appid" gorm:"column:talent_appid"`                         // 达人AppID
	TalentNickname        string    `json:"talent_nickname" gorm:"column:talent_nickname"`                   // 达人昵称
	TalentRatio           int       `json:"talent_ratio" gorm:"column:talent_ratio"`                         // 达人佣金率，范围[0-1000000]
	TalentAmount          int       `json:"talent_amount" gorm:"column:talent_amount"`                       // 达人佣金金额，单位分
	OpenTalentID          string    `json:"open_talent_id" gorm:"column:open_talent_id"`                     // 达人OpenID
	AgencyAppid           string    `json:"agency_appid" gorm:"column:agency_appid"`                         // 推客机构AppID
	AgencyNickname        string    `json:"agency_nickname" gorm:"column:agency_nickname"`                   // 推客机构昵称
	OriginResp            string    `json:"origin_resp" gorm:"column:origin_resp"`                           // 订单原始数据
	BenefitsPoolNo        int8      `json:"benefits_pool_no" gorm:"column:benefits_pool_no"`                 // 福利任务池编号
	BenefitsReferSeller   uint64    `json:"benefits_refer_seller" gorm:"column:benefits_refer_seller"`       // 福利任务卖家
}

func (m *CommissionOrders) TableName() string {
	return "commission_orders"
}
