package model

import (
	"xj-serv/pkg/model"
)

type UserBenefitsPool struct {
	model.MODEL_BASE
	Type          int8   `json:"type" gorm:"column:type"`         // 任务池类型
	Quantity      int    `json:"quantity" gorm:"column:quantity"` // 成单额度
	UserID        uint64 `json:"user_id" gorm:"column:user_id"`
	ExposureCount int    `json:"exposure_count" gorm:"column:exposure_count"` // 曝光次数
}

func (m *UserBenefitsPool) TableName() string {
	return "user_benefits_pool"
}
