package svc

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
	"math/rand"
	"xj-serv/application/wapp/rpc/ai/client/scheduler"
	"xj-serv/application/wapp/rpc/ec/client/wxleague"
	"xj-serv/application/wapp/rpc/ec/client/wxminishop"
	"xj-serv/application/wapp/rpc/pfs/client/dispatcher"
	"xj-serv/application/wapp/rpc/pfs/client/wxapp"
	"xj-serv/application/wapp/rpc/xjs/client/benefits"
	"xj-serv/application/wapp/rpc/xjs/client/tasks"
	"xj-serv/application/wapp/rpc/xjs/internal/config"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/rabbitmq"
)

type ServiceContext struct {
	Config        config.Config
	MasterDB      *orm.DB
	SlaveDBS      []*orm.DB
	BizRedis      *redis.Redis
	QueueManager  *rabbitmq.QueueManager
	TaskRPC       tasks.Tasks
	ShopRPC       wxminishop.WxMiniShop
	DispatcherRPC dispatcher.Dispatcher
	WxAppRPC      wxapp.WxApp
	SchedulerRPC  scheduler.Scheduler
	WxLeagueRPC   wxleague.WxLeague
	BenefitsRPC   benefits.Benefits
}

func NewServiceContext(c config.Config) *ServiceContext {
	masterDB := orm.MustNewMysql(&orm.Config{
		DSN:          c.DB.Master.DataSource,
		MaxOpenConns: c.DB.Master.MaxOpenConns,
		MaxIdleCnns:  c.DB.Master.MaxIdleConns,
		MaxLifetime:  c.DB.Master.MaxLifetime,
	})

	// 初始化从库
	var slaveDBs []*orm.DB
	for _, slaveConfig := range c.DB.Slaves {
		slaveDB := orm.MustNewMysql(&orm.Config{
			DSN:          slaveConfig.DataSource,
			MaxOpenConns: slaveConfig.MaxOpenConns,
			MaxIdleCnns:  slaveConfig.MaxIdleConns,
			MaxLifetime:  slaveConfig.MaxLifetime,
		})
		slaveDBs = append(slaveDBs, slaveDB)
	}
	rds := redis.MustNewRedis(redis.RedisConf{
		Host: c.BizRedis.Host,
		Type: c.BizRedis.Type,
		Pass: c.BizRedis.Pass,
	})

	// 初始化RabbitMQ队列管理器
	queueManager, err := rabbitmq.InitQueueManagerFromServiceConfig(c)
	if err != nil {
		logx.Errorf("初始化RabbitMQ队列管理器失败: %v", err)
	} else {
		logx.Info("RabbitMQ队列管理器初始化成功")
	}

	return &ServiceContext{
		Config:        c,
		MasterDB:      masterDB,
		SlaveDBS:      slaveDBs,
		BizRedis:      rds,
		QueueManager:  queueManager,
		TaskRPC:       tasks.NewTasks(zrpc.MustNewClient(c.XjsRPC)),
		BenefitsRPC:   benefits.NewBenefits(zrpc.MustNewClient(c.XjsRPC)),
		ShopRPC:       wxminishop.NewWxMiniShop(zrpc.MustNewClient(c.EcRPC)),
		WxLeagueRPC:   wxleague.NewWxLeague(zrpc.MustNewClient(c.EcRPC)),
		DispatcherRPC: dispatcher.NewDispatcher(zrpc.MustNewClient(c.PfsRPC)),
		WxAppRPC:      wxapp.NewWxApp(zrpc.MustNewClient(c.PfsRPC)),
		SchedulerRPC:  scheduler.NewScheduler(zrpc.MustNewClient(c.AiRPC)),
	}
}

// 随机选择一个从库
func (s *ServiceContext) GetSlaveDB() *orm.DB {
	if len(s.SlaveDBS) == 0 {
		return s.MasterDB // 如果没有从库，则返回主库
	}
	return s.SlaveDBS[rand.Intn(len(s.SlaveDBS))]
}
