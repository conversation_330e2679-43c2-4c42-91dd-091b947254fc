package main

import (
	"flag"
	"fmt"
	"xj-serv/application/wapp/rpc/xjs/internal/config"
	"xj-serv/application/wapp/rpc/xjs/internal/interceptor"
	benefitsServer "xj-serv/application/wapp/rpc/xjs/internal/server/benefits"
	nasServer "xj-serv/application/wapp/rpc/xjs/internal/server/nas"
	ragchatServer "xj-serv/application/wapp/rpc/xjs/internal/server/ragchat"
	tasksServer "xj-serv/application/wapp/rpc/xjs/internal/server/tasks"
	userServer "xj-serv/application/wapp/rpc/xjs/internal/server/user"
	"xj-serv/application/wapp/rpc/xjs/internal/svc"
	"xj-serv/application/wapp/rpc/xjs/pb"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/xjs.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	// 初始化日志配置
	logx.MustSetup(c.Log)
	proc.AddShutdownListener(func() {
		_ = logx.Close()
	})
	logx.DisableStat()

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		pb.RegisterUserServer(grpcServer, userServer.NewUserServer(ctx))
		pb.RegisterTasksServer(grpcServer, tasksServer.NewTasksServer(ctx))
		pb.RegisterRagChatServer(grpcServer, ragchatServer.NewRagChatServer(ctx))
		pb.RegisterNasServer(grpcServer, nasServer.NewNasServer(ctx))
		pb.RegisterBenefitsServer(grpcServer, benefitsServer.NewBenefitsServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	// 添加一元拦截器
	s.AddUnaryInterceptors(interceptor.ErrorInterceptor)

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
