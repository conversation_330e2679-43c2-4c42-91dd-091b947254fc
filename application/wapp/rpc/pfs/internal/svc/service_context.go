package svc

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/hibiken/asynq"
	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
	"time"
	"xj-serv/application/wapp/rpc/ec/client/wxleague"
	"xj-serv/application/wapp/rpc/pfs/client/wxapp"
	"xj-serv/application/wapp/rpc/pfs/internal/config"
	"xj-serv/application/wapp/rpc/pfs/pb"
	"xj-serv/application/wapp/rpc/xjs/client/benefits"
	"xj-serv/application/wapp/rpc/xjs/client/user"
	"xj-serv/pkg/common"
	"xj-serv/pkg/rabbitmq"
)

type ServiceContext struct {
	Config       config.Config
	BizRedis     *redis.Redis
	QueueManager *rabbitmq.QueueManager
	AsynqClient  *asynq.Client
	AsynqServer  *asynq.Server
	WxAppRPC     wxapp.WxApp
	WxLeagueRPC  wxleague.WxLeague
	BenefitsRPC  benefits.Benefits
	UserRPC      user.User
}

func NewServiceContext(c config.Config) *ServiceContext {
	rds := redis.MustNewRedis(redis.RedisConf{
		Host: c.BizRedis.Host,
		Type: c.BizRedis.Type,
		Pass: c.BizRedis.Pass,
	})

	// 初始化 Redis 客户端
	redisOpt := asynq.RedisClientOpt{Addr: c.BizRedis.Host, Password: c.BizRedis.Pass}

	// 初始化RabbitMQ客户端
	// 在初始化QueueManager的地方添加详细日志
	// 初始化RabbitMQ队列管理器
	logx.Info("开始初始化RabbitMQ队列管理器")
	logx.Infof("RabbitMQ配置: Host=%s, Port=%d, Username=%s, VirtualHost=%s",
		c.RabbitMQ.Host, c.RabbitMQ.Port, c.RabbitMQ.Username, c.RabbitMQ.VirtualHost)

	// 打印队列配置
	logx.Infof("RabbitMQ队列配置数量: %d", len(c.RabbitMQ.Queues))
	for queueName, queueConfig := range c.RabbitMQ.Queues {
		logx.Infof("队列配置: 名称=%s, 交换机=%s, 路由键=%s",
			queueName, queueConfig.Exchange, queueConfig.RoutingKey)
	}

	queueManager, err := rabbitmq.InitQueueManagerFromServiceConfig(c)
	if err != nil {
		logx.Errorf("初始化RabbitMQ队列管理器失败: %v", err)
	} else {
		// 打印已注册的队列
		configs := queueManager.GetAllQueueConfigs()
		logx.Infof("成功注册的队列数量: %d", len(configs))
		for queueName := range configs {
			logx.Infof("已成功注册队列: %s", queueName)
		}
		logx.Info("RabbitMQ队列管理器初始化成功")
	}

	// 启动 asynq Worker
	mux := asynq.NewServeMux()
	mux.HandleFunc("new_user_buy_task_check", func(ctx context.Context, task *asynq.Task) error {
		var payload pb.PostTaskReq
		err := json.Unmarshal(task.Payload(), &payload)
		if err != nil {
			return err
		}

		_, err = common.CallServ[interface{}](c.Etcd.Hosts, c.Etcd.Key, payload.Name, payload.Method, payload.Params)
		if err != nil {
			return err
		}

		return nil
	})

	asynqServer := asynq.NewServer(redisOpt, asynq.Config{
		RetryDelayFunc: func(n int, e error, t *asynq.Task) time.Duration {
			return time.Duration(n) * 5 * time.Second // 每次重试间隔增加 5 秒
		}, // 设置自定义重试频次
		Concurrency: 10, // 并发 worker 数量
	})

	// 开启延迟队列
	go func() {
		if err := asynqServer.Run(mux); err != nil {
			fmt.Println("Failed to start asynq server:", err)
		}
	}()

	// 开启定时任务
	if c.EnableCronTask {
		for _, tb := range c.CronTasks {
			// 为每个任务创建一个副本，避免闭包问题
			taskInfo := tb
			go func() {
				ctx := context.Background()
				var hosts []string
				var key string
				switch taskInfo.RpcServer {
				case "xjs":
					hosts = c.XjsRPC.Etcd.Hosts
					key = c.XjsRPC.Etcd.Key
					break
				case "ec":
					hosts = c.EcRPC.Etcd.Hosts
					key = c.EcRPC.Etcd.Key
					break
				default:
					return
				}
				// 定义一个定时任务
				task := func() {
					// 使用recover防止panic导致程序崩溃
					defer func() {
						if r := recover(); r != nil {
							logx.Errorf("定时任务 (%s) 发生panic: %v", taskInfo.Description, r)
						}
					}()

					// 调用RPC服务
					_, err := common.CallServ[interface{}](hosts, key, taskInfo.ServName, taskInfo.ServMethod, taskInfo.ServParams)
					if err != nil {
						logx.Errorf("定时任务 (%s) 失败. 错误: %s", taskInfo.Description, err.Error())
					} else {
						logx.Infof("定时任务 (%s) 成功.", taskInfo.Description)
					}
				}

				cn := cron.New()
				_, err := cn.AddFunc(taskInfo.Expression, task)
				if err != nil {
					logx.Errorf("添加定时任务 (%s) 失败: %v", taskInfo.Description, err)
					return
				}

				// 启动定时任务
				cn.Start()
				logx.Infof("定时任务 (%s) 已启动...", taskInfo.Description)

				// 防止主进程退出
				<-ctx.Done()
			}()
		}
	}

	return &ServiceContext{
		Config:       c,
		BizRedis:     rds,
		QueueManager: queueManager,
		AsynqClient:  asynq.NewClient(redisOpt),
		AsynqServer:  asynqServer,
		WxAppRPC:     wxapp.NewWxApp(zrpc.MustNewClient(c.PfsRPC)),
		WxLeagueRPC:  wxleague.NewWxLeague(zrpc.MustNewClient(c.EcRPC)),
		UserRPC:      user.NewUser(zrpc.MustNewClient(c.XjsRPC)),
		BenefitsRPC:  benefits.NewBenefits(zrpc.MustNewClient(c.XjsRPC)),
	}
}
