package consumer

import (
	"fmt"
	"time"
	xjpb "xj-serv/application/wapp/rpc/xjs/pb"
)

// handleUserNetworkSync 处理用户网络同步消息
func (c *MessageConsumer) handleUserNetworkSync(msgData map[string]interface{}) error {
	// 获取消息类型
	msgType, _ := msgData["type"].(string)
	if msgType != "benefits_user_network_sync" {
		return fmt.Errorf("不支持的消息类型: %s", msgType)
	}

	// 获取用户ID - 简化类型转换
	userIdFloat, ok := msgData["user_id"].(float64)
	if !ok {
		return fmt.Errorf("用户ID不存在或格式错误")
	}
	userId := uint64(userIdFloat)

	// 获取上级用户ID - 简化类型转换
	fatherIdFloat, ok := msgData["father_id"].(float64)
	if !ok {
		return fmt.Errorf("上级用户ID不存在或格式错误")
	}
	fatherId := uint64(fatherIdFloat)

	// 获取网络类型
	networkType, ok := msgData["network_type"].(string)
	if !ok || networkType == "" {
		return fmt.Errorf("网络类型不存在或为空")
	}

	// 获取时间戳
	timestampFloat, ok := msgData["timestamp"].(float64)
	if !ok {
		timestampFloat = float64(time.Now().Unix())
	}
	timestamp := int64(timestampFloat)

	c.logger.Infof("处理用户网络同步: user_id=%d, father_id=%d, network_type=%s", userId, fatherId, networkType)

	// 调用XJS服务的SyncUserNetwork方法
	resp, err := c.svcCtx.UserRPC.SyncUserNetwork(c.ctx, &xjpb.SyncUserNetworkReq{
		UserId:      userId,
		FatherId:    fatherId,
		NetworkType: networkType,
		Timestamp:   timestamp,
	})

	if err != nil {
		c.logger.Errorf("调用用户网络同步RPC失败: %v", err)
		return err
	}

	if !resp.Success {
		c.logger.Errorf("用户网络同步失败")
		return fmt.Errorf("用户网络同步失败")
	}

	c.logger.Infof("用户网络同步成功: user_id=%d, father_id=%d, network_type=%s", userId, fatherId, networkType)
	return nil
}