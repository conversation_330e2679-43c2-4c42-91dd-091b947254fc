package consumer

import (
	"fmt"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// handleCommissionOrder 处理佣金订单消息
func (c *MessageConsumer) handleCommissionOrder(msgData map[string]interface{}) error {
	// 获取订单ID
	orderId, _ := msgData["order_id"].(string)
	if orderId == "" {
		return fmt.Errorf("订单ID不存在或为空")
	}

	c.logger.Infof("处理佣金订单: %s", orderId)

	// 调用RPC处理订单佣金
	resp, err := c.svcCtx.WxLeagueRPC.ProcessOrderCommission(c.ctx, &pb.ProcessOrderCommissionReq{
		OrderId: orderId,
	})

	if err != nil {
		c.logger.Errorf("调用佣金处理RPC失败: %v", err)
		return err
	}

	if !resp.Success {
		c.logger.Infof("佣金处理失败")
		return fmt.Errorf("佣金处理失败")
	}

	c.logger.Infof("佣金处理成功")
	return nil
}