package consumer

import (
	"fmt"
	"strconv"
	"xj-serv/application/wapp/rpc/xjs/pb"
)

// processBenefitsTasksMessage 处理福利任务消息
func (c *MessageConsumer) processBenefitsTasksMessage(message map[string]interface{}) error {
	// 获取消息类型
	msgType, ok := message["type"].(string)
	if !ok {
		return fmt.Errorf("消息类型不存在或类型错误")
	}

	// 只处理handle_benefits_tasks类型的消息
	if msgType != "handle_benefits_tasks" {
		return fmt.Errorf("不支持的消息类型: %s", msgType)
	}

	// 获取事件类型
	eventType, ok := message["event"].(string)
	if !ok {
		return fmt.Errorf("事件类型不存在或类型错误")
	}

	c.logger.Infof("处理福利任务消息: 事件类型=%s", eventType)

	// 根据事件类型处理不同的订单消息
	switch eventType {
	case "handle_network_change":
		c.logger.Infof("处理网体变更消息: 事件类型=%s", eventType)

		userIDFloat, ok := message["user_id"].(float64)
		if !ok {
			// 尝试从字符串转换
			userIDStr, ok := message["user_id"].(string)
			if !ok {
				return fmt.Errorf("用户ID不存在或类型错误")
			}
			userIDInt, err := strconv.ParseUint(userIDStr, 10, 64)
			if err != nil {
				return fmt.Errorf("用户ID格式错误: %v", err)
			}
			return c.processNetworkChangeMessage(userIDInt)
		}
		return c.processNetworkChangeMessage(uint64(userIDFloat))

	case "head_supplier_commission_order_update": // 机构订单同步
		// 直接从消息中获取order_id
		orderID, ok := message["order_id"].(string)
		if !ok {
			return fmt.Errorf("订单ID不存在或类型错误")
		}
		return c.processCommissionOrderUpdateMessage(orderID)
	default:
		return fmt.Errorf("不支持的事件类型: %s", eventType)
	}
}

// processCommissionOrderUpdateMessage 处理机构订单同步消息
func (c *MessageConsumer) processCommissionOrderUpdateMessage(orderID string) error {
	c.logger.Info("处理机构订单同步消息")

	// 处理机构订单同步消息
	resp, err := c.svcCtx.BenefitsRPC.ProcessOrderBenefitsTask(c.ctx, &pb.ProcessOrderBenefitsTaskReq{
		OrderId: orderID,
	})

	if err != nil {
		c.logger.Errorf("调用网体变更RPC失败: %v", err)
		return err
	}

	if !resp.Success {
		c.logger.Infof("网体变更处理失败: %s", resp.Message)
		return fmt.Errorf("网体变更处理失败: %s", resp.Message)
	}

	c.logger.Infof("成功处理机构订单同步消息: orderID=%s", orderID)
	return nil
}

// 添加处理网体变更消息的函数
func (c *MessageConsumer) processNetworkChangeMessage(userID uint64) error {
	c.logger.Infof("处理用户网体变更消息: userID=%d", userID)

	// 调用RPC处理网体变更
	resp, err := c.svcCtx.BenefitsRPC.ProcessNetworkChange(c.ctx, &pb.ProcessNetworkChangeReq{
		UserId: userID,
	})

	if err != nil {
		c.logger.Errorf("调用网体变更RPC失败: %v", err)
		return err
	}

	if !resp.Success {
		c.logger.Infof("网体变更处理失败: %s", resp.Message)
		return fmt.Errorf("网体变更处理失败: %s", resp.Message)
	}

	c.logger.Infof("网体变更处理成功: userID=%d, changedUsers=%v", userID, resp.ChangedUsers)
	return nil
}
