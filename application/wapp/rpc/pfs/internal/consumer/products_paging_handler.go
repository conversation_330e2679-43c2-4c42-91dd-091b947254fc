package consumer

import (
	"encoding/json"
	"fmt"
	"google.golang.org/grpc/metadata"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// 处理统一的商品分页消息
func (c *MessageConsumer) handleProductsPaging(msgData map[string]interface{}) error {
	// 获取消息类型
	msgType, ok := msgData["type"].(string)
	if !ok {
		c.logger.Errorf("消息中缺少type字段或类型错误")
		return fmt.Errorf("消息中缺少type字段或类型错误")
	}

	// 记录消息数据，便于调试
	msgDataJSON, _ := json.Marshal(msgData)
	c.logger.Infof("处理%s类型的商品分页消息: %s", msgType, string(msgDataJSON))

	// 将消息数据序列化为JSON字符串
	msgJSON, err := json.Marshal(msgData)
	if err != nil {
		c.logger.Errorf("序列化消息数据失败: %v", err)
		return err
	}

	// 创建带有消息数据的元数据
	md := metadata.New(map[string]string{
		"x-queue-message": string(msgJSON),
	})

	// 创建带有元数据的上下文
	ctx := metadata.NewOutgoingContext(c.ctx, md)

	// 根据消息类型调用不同的RPC服务
	switch msgType {
	case "live":
		// 处理直播商品分页用于计算最高佣
		_, err = c.svcCtx.WxLeagueRPC.SyncRemoteLiveCommissionRate(ctx, &pb.EmptyRequest{})
		if err != nil {
			c.logger.Errorf("调用SyncRemoteLiveCommissionRate处理消息失败: %v", err)
			return err
		}
		c.logger.Info("成功处理直播商品分页消息")

	case "talent":
		// 处理达人商品分页temp_talent_products
		_, err = c.svcCtx.WxLeagueRPC.SyncRemoteTalentProductListPaged(ctx, &pb.EmptyRequest{})
		if err != nil {
			c.logger.Errorf("调用SyncRemoteTalentProductListPaged处理消息失败: %v", err)
			return err
		}
		c.logger.Info("成功处理达人商品分页消息")

	case "shop_live":
		// 处理店铺直播商品分页用于计算嘴高佣
		_, err = c.svcCtx.WxLeagueRPC.SyncRemoteShopLiveCommissionRate(ctx, &pb.EmptyRequest{})
		if err != nil {
			c.logger.Errorf("调用SyncRemoteShopLiveCommissionRate处理消息失败: %v", err)
			return err
		}
		c.logger.Info("成功处理店铺直播商品分页消息")

	case "promote_product":
		// 处理推客定向商品分页用于计算嘴高佣
		_, err = c.svcCtx.WxLeagueRPC.SyncRemotePromoteProductList(ctx, &pb.EmptyRequest{})
		if err != nil {
			c.logger.Errorf("调用SyncRemotePromoteProductList处理消息失败: %v", err)
			return err
		}
		c.logger.Info("成功处理推客定向商品分页消息")

	default:
		errMsg := fmt.Sprintf("未知的消息类型: %s", msgType)
		c.logger.Errorf(errMsg)
		return fmt.Errorf(errMsg)
	}

	return nil
}
