package consumer

import (
	"github.com/streadway/amqp"
)

// processDeadLetter 处理死信消息
func (c *MessageConsumer) processDeadLetter(msg amqp.Delivery) {
	c.logger.Infof("收到死信消息: %s", string(msg.Body))

	// 获取原始队列名称
	originalQueue := "未知队列"
	if queueName, exists := msg.Headers["x-original-queue"]; exists {
		if name, ok := queueName.(string); ok {
			originalQueue = name
		}
	}

	// 记录失败消息
	c.logger.Errorf("来自队列[%s]的消息处理最终失败，消息内容: %s", originalQueue, string(msg.Body))

	// 实现特殊的处理逻辑，如：
	// 记录到数据库
	// 发送告警通知

	// 确认消息已处理
	if err := msg.Ack(false); err != nil {
		c.logger.Errorf("确认死信消息失败: %v", err)
	}
}