package consumer

import (
	"context"
	"encoding/json"
	"google.golang.org/grpc/metadata"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// processTalentProductsPagingMessage 处理达人商品分页同步消息
func (c *MessageConsumer) processTalentProductsPagingMessage(msgData map[string]interface{}) error {
	c.logger.Infof("处理达人商品分页同步消息: %+v", msgData)

	// 创建上下文
	ctx := context.Background()

	// 序列化消息数据
	msgDataBytes, err := json.Marshal(msgData)
	if err != nil {
		c.logger.Errorf("序列化达人商品分页同步消息数据失败: %v", err)
		return err
	}

	// 创建元数据
	md := metadata.New(map[string]string{
		"x-queue-message": string(msgDataBytes),
	})

	// 将元数据添加到上下文
	ctx = metadata.NewOutgoingContext(ctx, md)

	// 从消息中提取基本参数，用于日志记录
	commissionType := 0
	if ctVal, ok := msgData["commission_type"].(float64); ok {
		commissionType = int(ctVal)
	}

	nextKey := ""
	if nkVal, ok := msgData["next_key"].(string); ok {
		nextKey = nkVal
	}

	pageIndex := 1
	if piVal, ok := msgData["page_index"].(float64); ok {
		pageIndex = int(piVal)
	}

	c.logger.Infof("调用达人商品分页同步服务: CommissionType=%d, PageIndex=%d, NextKey=%s",
		commissionType, pageIndex, nextKey)

	// 调用EC服务的达人商品分页同步接口
	_, err = c.svcCtx.WxLeagueRPC.SyncRemoteTalentProductListPaged(ctx, &pb.EmptyRequest{})
	if err != nil {
		c.logger.Errorf("调用达人商品分页同步服务失败: %v", err)
		return err
	}

	c.logger.Info("处理达人商品分页同步消息成功")
	return nil
}
