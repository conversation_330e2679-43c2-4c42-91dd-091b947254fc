package consumer

import (
	"encoding/json"
	"google.golang.org/grpc/metadata"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// 处理店铺直播商品分页同步消息
func (c *MessageConsumer) handleShopLiveProductsPaging(msgData map[string]interface{}) error {
	c.logger.Info("处理店铺直播商品分页同步消息")

	// 将消息数据序列化为JSON字符串
	msgJSON, err := json.Marshal(msgData)
	if err != nil {
		c.logger.Errorf("序列化消息数据失败: %v", err)
		return err
	}

	// 创建带有消息数据的元数据
	md := metadata.New(map[string]string{
		"x-queue-message": string(msgJSON),
	})

	// 创建带有元数据的上下文
	ctx := metadata.NewOutgoingContext(c.ctx, md)

	// 调用RPC服务处理消息
	_, err = c.svcCtx.WxLeagueRPC.SyncRemoteShopLiveCommissionRate(ctx, &pb.EmptyRequest{})
	if err != nil {
		c.logger.Errorf("调用SyncRemoteShopLiveCommissionRate处理消息失败: %v", err)
		return err
	}

	c.logger.Info("成功处理店铺直播商品分页同步消息")
	return nil
}