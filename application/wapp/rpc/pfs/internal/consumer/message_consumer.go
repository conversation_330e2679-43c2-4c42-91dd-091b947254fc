package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/streadway/amqp"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
	"xj-serv/application/wapp/rpc/pfs/internal/svc"
)

// 检查是否超过最大重试次数（这里设置为3次）
const maxRetries = 3

// MessageConsumer 消息消费者
type MessageConsumer struct {
	svcCtx *svc.ServiceContext
	logger logx.Logger
	ctx    context.Context
	cancel context.CancelFunc
}

// NewMessageConsumer 创建消息消费者
func NewMessageConsumer(svcCtx *svc.ServiceContext) *MessageConsumer {
	ctx, cancel := context.WithCancel(context.Background())
	return &MessageConsumer{
		svcCtx: svcCtx,
		logger: logx.WithContext(ctx),
		ctx:    ctx,
		cancel: cancel,
	}
}

// Start 启动消息消费
func (c *MessageConsumer) Start() {
	// 使用队列管理器替代直接使用RabbitMQ客户端
	if c.svcCtx.QueueManager == nil {
		c.logger.Error("队列管理器未初始化，无法启动消费者")
		return
	}

	// 打印队列管理器中的队列配置
	configs := c.svcCtx.QueueManager.GetAllQueueConfigs()
	c.logger.Infof("队列管理器中的队列配置数量: %d", len(configs))
	for queueName := range configs {
		c.logger.Infof("队列管理器中存在队列配置: %s", queueName)
	}

	// 启动用户网络同步队列消费
	go c.consumeQueue("UserNetworkSync", "pfs-network-consumer")
	c.logger.Info("RabbitMQ 用户网络同步队列消费者已启动")

	// 启动佣金订单队列消费
	go c.consumeQueue("CommissionOrders", "pfs-commission-consumer")
	c.logger.Info("RabbitMQ 佣金订单队列消费者已启动")

	// 启动福利任务队列消费
	go c.consumeQueue("BenefitsTasks", "pfs-benefits-consumer")
	c.logger.Info("RabbitMQ 福利任务队列消费者已启动")

	//// 启动直播商品任务队列消费
	//go c.consumeQueue("LiveProductsPaging", "pfs-live-products-paging")
	//c.logger.Info("RabbitMQ 直播商品队列消费者已启动")
	//
	//// 启动达人商品分页同步队列消费
	//go c.consumeQueue("TalentProductsPaging", "ec-talent-products-paging")
	//c.logger.Info("RabbitMQ 达人商品分页同步队列消费者已启动")
	//
	//// 启动店铺直播商品分页同步队列消费
	//go c.consumeQueue("ShopLiveProductsPaging", "pfs-shop-live-products-paging")
	//c.logger.Info("RabbitMQ 店铺直播商品分页同步队列消费者已启动")
	// 添加统一的商品分页队列消费
	go c.consumeQueue("ProductsPaging", "pfs-products-paging")
	c.logger.Info("RabbitMQ 统一商品分页队列消费者已启动")

	// 启动死信队列消费
	go c.consumeQueue("DeadLetterQueue", "pfs-dlq-consumer")
	c.logger.Info("RabbitMQ 死信队列消费者已启动")
}

// Stop 停止消息消费
func (c *MessageConsumer) Stop() {
	c.cancel()
	c.logger.Info("RabbitMQ消息消费者已停止")
}

// consumeQueue 消费指定队列的消息
func (c *MessageConsumer) consumeQueue(queueName, consumerName string) {
	c.logger.Infof("尝试消费队列: %s, 消费者名称: %s", queueName, consumerName)

	// 检查队列配置是否存在
	if config, exists := c.svcCtx.QueueManager.GetQueueConfig(queueName); exists {
		c.logger.Infof("队列配置存在: %s, 交换机: %s, 路由键: %s",
			queueName, config.Exchange, config.RoutingKey)
	} else {
		c.logger.Errorf("队列配置不存在: %s, QueueManager中的队列数量: %d",
			queueName, len(c.svcCtx.QueueManager.GetAllQueueConfigs()))
	}

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			// 使用队列管理器消费消息
			c.logger.Infof("开始从队列消费消息: %s", queueName)
			msgs, err := c.svcCtx.QueueManager.ConsumeFromQueue(
				queueName,    // 队列名称
				consumerName, // 消费者名称
				false,        // 手动确认
			)
			if err != nil {
				c.logger.Errorf("启动RabbitMQ队列[%s]消费者失败: %v", queueName, err)
				// 等待一段时间后重试
				select {
				case <-c.ctx.Done():
					return
				case <-time.After(5 * time.Second):
					continue
				}
			}

			c.logger.Infof("成功启动队列消费: %s", queueName)

			// 处理消息
			for msg := range msgs {
				c.processQueueMessage(msg, queueName)
			}

			c.logger.Errorf("RabbitMQ队列[%s]消费者通道已关闭，准备重新连接", queueName)
			// 等待一段时间后重试
			select {
			case <-c.ctx.Done():
				return
			case <-time.After(5 * time.Second):
				continue
			}
		}
	}
}

// processQueueMessage 处理队列消息
func (c *MessageConsumer) processQueueMessage(msg amqp.Delivery, queueName string) {
	defer func() {
		if r := recover(); r != nil {
			c.logger.Errorf("处理队列[%s]消息时发生panic: %v", queueName, r)
			c.handleRetryForQueue(msg, fmt.Errorf("panic: %v", r), queueName)
		}
	}()

	// 获取当前重试次数
	retryCount := 0
	if xRetryCount, exists := msg.Headers["x-retry-count"]; exists {
		if count, ok := xRetryCount.(int32); ok {
			retryCount = int(count)
			// 记录当前是第几次重试
			if retryCount > 0 {
				c.logger.Infof("正在处理队列[%s]消息的第 %d 次重试", queueName, retryCount)
			}
		}
	}

	c.logger.Infof("收到队列[%s]消息: %s", queueName, string(msg.Body))

	// 解析消息
	var msgData map[string]interface{}
	if err := json.Unmarshal(msg.Body, &msgData); err != nil {
		c.logger.Errorf("解析队列[%s]消息失败: %v", queueName, err)
		c.handleRetryForQueue(msg, err, queueName)
		return
	}

	// 根据队列类型处理不同的业务逻辑
	var err error
	switch queueName {
	case "UserNetworkSync":
		err = c.handleUserNetworkSync(msgData)
	case "CommissionOrders":
		err = c.handleCommissionOrder(msgData)
	case "BenefitsTasks":
		err = c.processBenefitsTasksMessage(msgData) // 处理福利任务消息
	//case "LiveProductsPaging":
	//	err = c.handleLiveProductsPaging(msgData) // 处理直播间商品佣金同步
	//case "TalentProductsPaging":
	//	err = c.processTalentProductsPagingMessage(msgData)
	//case "ShopLiveProductsPaging":
	//	err = c.handleShopLiveProductsPaging(msgData) // 处理同步店铺合作达人直播间商品佣金率
	case "ProductsPaging":
		c.handleProductsPaging(msgData)
	case "DeadLetterQueue":
		c.processDeadLetter(msg)
		return // 死信队列消息直接确认，不需要重试
	default:
		err = fmt.Errorf("未知的队列类型: %s", queueName)
	}

	if err != nil {
		c.logger.Errorf("处理队列[%s]消息失败: %v", queueName, err)
		c.handleRetryForQueue(msg, err, queueName)
		return
	}

	// 确认消息已处理
	if err := msg.Ack(false); err != nil {
		c.logger.Errorf("确认队列[%s]消息失败: %v", queueName, err)
	}

	c.logger.Infof("成功处理队列[%s]消息: %v", queueName, msgData)
}

// handleRetryForQueue 处理消息重试
func (c *MessageConsumer) handleRetryForQueue(msg amqp.Delivery, err error, queueName string) {
	// 获取当前重试次数
	retryCount := 0
	if xRetryCount, exists := msg.Headers["x-retry-count"]; exists {
		if count, ok := xRetryCount.(int32); ok {
			retryCount = int(count)
		}
	}

	// 增加重试次数
	retryCount++

	if retryCount <= maxRetries {
		// 未超过最大重试次数，重新发布消息到原队列
		c.logger.Infof("队列[%s]消息处理失败，准备第 %d 次重试", queueName, retryCount)

		// 创建新的消息头，包含重试次数
		headers := amqp.Table{}
		if msg.Headers != nil {
			for k, v := range msg.Headers {
				headers[k] = v
			}
		}
		headers["x-retry-count"] = retryCount

		// 使用队列管理器重新发布消息
		err := c.svcCtx.QueueManager.PublishToQueueWithHeaders(
			queueName, // 队列名称
			amqp.Publishing{
				ContentType:  "application/json",
				Body:         msg.Body,
				Headers:      headers,
				DeliveryMode: amqp.Persistent, // 持久化消息
			},
		)

		if err != nil {
			c.logger.Errorf("重新发布消息到队列[%s]失败: %v", queueName, err)
		} else {
			c.logger.Infof("成功重新发布消息到队列[%s]，重试次数: %d", queueName, retryCount)
		}

		// 拒绝原消息，不重新入队（手动已经重新发布）
		if err := msg.Reject(false); err != nil {
			c.logger.Errorf("拒绝消息失败: %v", err)
		} else {
			c.logger.Infof("成功拒绝原消息，等待重新发布的消息被处理")
		}
	} else {
		// 超过最大重试次数，拒绝消息，进入死信队列
		c.logger.Errorf("队列[%s]消息处理失败，已达到最大重试次数 %d，将发送到死信队列", queueName, maxRetries)

		// 记录失败原因
		c.logger.Errorf("消息处理最终失败，原因: %v，消息内容: %s", err, string(msg.Body))

		// 拒绝消息，不重新入队，这会触发死信机制
		if err := msg.Reject(false); err != nil {
			c.logger.Errorf("拒绝消息失败: %v", err)
		}
	}
}
