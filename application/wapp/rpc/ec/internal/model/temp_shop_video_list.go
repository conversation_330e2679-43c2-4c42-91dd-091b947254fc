package model

import (
	"xj-serv/pkg/model"
)

// TempShopVideoList 小店关联账号短视频列表
type TempShopVideoList struct {
	model.MODEL_BASE
	ExportID                string `gorm:"column:export_id;uniqueIndex:idx_shop_promoter_export" json:"export_id"`     // 短视频 ID
	ShopAppid               string `gorm:"column:shop_appid;uniqueIndex:idx_shop_promoter_export" json:"shop_appid"`   // 小店 appid
	PromoterID              string `gorm:"column:promoter_id;uniqueIndex:idx_shop_promoter_export" json:"promoter_id"` // 关联账号的id
	PromoterType            int32  `gorm:"column:promoter_type" json:"promoter_type"`                                  // 关联账号类型
	PredictCommissionAmount int64  `gorm:"column:predict_commission_amount" json:"predict_commission_amount"`          // 预期机构结算金额【单位：分】
	ProductID               uint64 `gorm:"column:product_id" json:"product_id"`                                        // 商品id
	ProductName             string `gorm:"column:product_name" json:"product_name"`                                    // 商品名称
	ProductImgUrl           string `gorm:"column:product_img_url" json:"product_img_url"`                              // 商品头图
	ProductMiniPrice        int64  `gorm:"column:product_mini_price" json:"product_mini_price"`                        // 商品价格【单位：分】
	FeedToken               string `gorm:"column:feed_token" json:"feed_token"`                                        // 内嵌短视频的卡片信息
	PromoterShareLink       string `gorm:"column:promoter_share_link" json:"promoter_share_link"`                      // 推广推客信息
}

// TableName 设置表名
func (TempShopVideoList) TableName() string {
	return "temp_shop_video_list"
}
