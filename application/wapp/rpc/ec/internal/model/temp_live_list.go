package model

import (
	"gorm.io/gorm"
	"time"
	"xj-serv/pkg/model"
)

type TempLiveList struct {
	model.MODEL_BASE
	ExportID                   string      `json:"export_id" gorm:"column:export_id"`                     // 直播id
	Description                string      `json:"description" gorm:"column:description"`                 // 直播描述
	PromoterShareLink          string      `json:"promoter_share_link" gorm:"column:promoter_share_link"` // 推广参数
	PredictMaxCommissionAmount int8        `json:"predict_max_commission_amount" gorm:"column:predict_max_commission_amount"`
	Type                       string      `json:"type" gorm:"column:type"`                   // 直播间类型
	InfoProgress               string      `json:"info_progress" gorm:"column:info_progress"` // 资料状态
	NoticeID                   string      `json:"notice_id" gorm:"column:notice_id"`         // 预约id
	StartTime                  int         `json:"start_time" gorm:"column:start_time"`
	StartIsoTime               string      `gorm:"-"`
	SharerTotal                int         `json:"sharer_total" gorm:"column:sharer_total"`
	TalentAppid                string      `json:"talent_appid" gorm:"column:talent_appid"`
	TempTalents                TempTalents `json:"temp_talents" gorm:"foreignKey:TalentAppid;references:TalentAppid;"`
	TalentNickname             string      `gorm:"-"` // 临时字段，不存储到数据库
	TalentHeadImg              string      `gorm:"-"` // 临时字段，不存储到数据库
	Class                      string      `json:"class" gorm:"column:class"`
	PromoterId                 string      `json:"promoter_id" gorm:"column:promoter_id"`
}

func (m *TempLiveList) TableName() string {
	return "temp_live_list"
}

func (m *TempLiveList) AfterFind(tx *gorm.DB) (err error) {
	// Unix 时间戳（秒级）
	unixTimestamp := int64(m.StartTime)
	// 转换为 time.Time
	t := time.Unix(unixTimestamp, 0)
	// 格式化为字符串
	formatted := t.Format("2006-01-02 15:04:05")
	m.StartIsoTime = formatted
	return
}
