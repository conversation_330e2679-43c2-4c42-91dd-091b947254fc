package model

import (
	"xj-serv/pkg/model"
)

// TempBindShops 绑定小店信息表
type TempBindShops struct {
	model.MODEL_BASE
	ShopAppid  string `gorm:"column:shop_appid" json:"shop_appid"`
	Nickname   string `gorm:"column:nickname" json:"nickname"`
	HeadImgUrl string `gorm:"column:head_img_url" json:"head_img_url"`
	BindTime   int64  `gorm:"column:bind_time" json:"bind_time"`
}

// TableName 设置表名
func (TempBindShops) TableName() string {
	return "temp_bind_shops"
}
