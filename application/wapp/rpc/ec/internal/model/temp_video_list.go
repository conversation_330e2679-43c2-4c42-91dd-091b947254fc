package model

import (
	"xj-serv/pkg/model"
)

type TempVideoList struct {
	model.MODEL_BASE
	ExportID                string `json:"export_id" gorm:"column:export_id"`                                 // 短视频ID
	TalentAppid             string `json:"talent_appid" gorm:"column:talent_appid"`                           // 达人平台appid
	PredictCommissionAmount int64  `json:"predict_commission_amount" gorm:"column:predict_commission_amount"` // 预估佣金金额(分)
	ProductID               uint64 `json:"product_id" gorm:"column:product_id"`                               // 商品ID
	ProductName             string `json:"product_name" gorm:"column:product_name"`                           // 商品名称
	ProductImgUrl           string `json:"product_img_url" gorm:"column:product_img_url"`                     // 商品图片URL
	ProductMiniPrice        int64  `json:"product_mini_price" gorm:"column:product_mini_price"`               // 商品最低价格(分)
	FeedToken               string `json:"feed_token" gorm:"column:feed_token"`                               // 短视频token
	PromoterShareLink       string `json:"promoter_share_link" gorm:"column:promoter_share_link"`             // 推广链接
}

func (m *TempVideoList) TableName() string {
	return "temp_video_list"
}
