package model

import (
	"xj-serv/pkg/model"
)

type ShopProductsCategory struct {
	model.MODEL_BASE
	CatID  uint   `json:"cat_id" gorm:"column:cat_id"`
	FCatID uint   `json:"f_cat_id" gorm:"column:f_cat_id"`
	Name   string `json:"name" gorm:"column:name"`
	Level  int8   `json:"level" gorm:"column:level"`
	Show   int8   `json:"show" gorm:"column:show;default:0"`
}

func (m *ShopProductsCategory) TableName() string {
	return "shop_products_category"
}
