package model

import (
	"time"
	"xj-serv/pkg/model"

	"gorm.io/gorm"
)

// WithdrawStatus 提现状态枚举
type WithdrawStatus int8

const (
	WithdrawStatusPending   WithdrawStatus = 0 // 待处理
	WithdrawStatusProcess   WithdrawStatus = 1 // 处理中
	WithdrawStatusSuccess   WithdrawStatus = 2 // 成功
	WithdrawStatusFailed    WithdrawStatus = 3 // 失败
	WithdrawStatusCancelled WithdrawStatus = 4 // 已取消
)

// ReviewStatus 审核状态枚举
type ReviewStatus int8

const (
	ReviewStatusPending  ReviewStatus = 0 // 待审核
	ReviewStatusApproved ReviewStatus = 1 // 审核通过
	ReviewStatusRejected ReviewStatus = 2 // 审核拒绝
)

// WithdrawRecord 用户提现记录表
type WithdrawRecord struct {
	model.MODEL_BASE

	// 用户信息
	UserID uint64 `json:"user_id" gorm:"column:user_id;not null;index"` // 用户ID

	// 提现金额信息(单位：分)
	Amount       uint64 `json:"amount" gorm:"column:amount;not null"`      // 申请提现金额
	ActualAmount uint64 `json:"actual_amount" gorm:"column:actual_amount"` // 实际到账金额
	Fee          uint64 `json:"fee" gorm:"column:fee;default:0"`           // 手续费

	// 微信支付相关字段
	OutBillNo      string `json:"out_bill_no" gorm:"column:out_bill_no"`           // 商户单号
	TransferBillNo string `json:"transfer_bill_no" gorm:"column:transfer_bill_no"` // 微信单号
	TransferScene  string `json:"transfer_scene" gorm:"column:transfer_scene"`     // 转账场景

	// 状态信息
	Status       WithdrawStatus `json:"status" gorm:"column:status;not null;default:0;index"`         // 提现状态
	ReviewStatus ReviewStatus   `json:"review_status" gorm:"column:review_status;not null;default:1"` // 审核状态（默认为审核通过，普通用户无需审核）
	State        string         `json:"state" gorm:"column:state"`                                    // 单据状态
	FailReason   string         `json:"fail_reason" gorm:"column:fail_reason;type:text"`              // 失败原因

	// 时间记录
	ApplyTime   time.Time  `json:"apply_time" gorm:"column:apply_time;not null"` // 申请时间
	ProcessTime *time.Time `json:"process_time" gorm:"column:process_time"`      // 处理时间
	SuccessTime *time.Time `json:"success_time" gorm:"column:success_time"`      // 成功时间

	// 审核人信息
	ReviewerID uint64 `json:"reviewer_id" gorm:"column:reviewer_id"` // 审核人ID

	// 备注
	Remark string `json:"remark" gorm:"column:remark;type:text"` // 备注

}

// TableName 设置表名
func (w *WithdrawRecord) TableName() string {
	return "user_withdraw_record"
}

// BeforeCreate 创建前处理
func (w *WithdrawRecord) BeforeCreate(tx *gorm.DB) (err error) {
	if w.ApplyTime.IsZero() {
		w.ApplyTime = time.Now()
	}
	return
}
