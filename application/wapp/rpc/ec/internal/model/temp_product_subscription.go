package model

import (
	"xj-serv/pkg/model"
)

// TempProductSubscription 商品订阅历史记录
type TempProductSubscription struct {
	model.MODEL_BASE
	ProductId int64  `gorm:"column:product_id" json:"product_id"` // 商品ID
	ShopAppid string `gorm:"column:shop_appid" json:"shop_appid"` // 店铺AppID
	PlanType  int    `gorm:"column:plan_type" json:"plan_type"`   // 计划类型
	Status    int8   `gorm:"column:status" json:"status"`         // 订阅状态：1-成功，0-失败
	ErrorMsg  string `gorm:"column:error_msg" json:"error_msg"`   // 错误信息
}

// TableName 设置表名
func (TempProductSubscription) TableName() string {
	return "temp_product_subscriptions"
}
