package model

import (
	"xj-serv/pkg/model"
)

// TempShopPromoters 小店关联推客账号信息表
type TempShopPromoters struct {
	model.MODEL_BASE
	ShopAppid    string        `gorm:"column:shop_appid" json:"shop_appid"`
	PromoterID   string        `gorm:"column:promoter_id" json:"promoter_id"`
	PromoterType int32         `gorm:"column:promoter_type" json:"promoter_type"`
	Nickname     string        `gorm:"column:nickname" json:"nickname"`
	HeadImgUrl   string        `gorm:"column:head_img_url" json:"head_img_url"`
	BindShop     TempBindShops `gorm:"foreignKey:ShopAppid;references:ShopAppid" json:"bind_shop"`
}

// TableName 设置表名
func (TempShopPromoters) TableName() string {
	return "temp_shop_promoters"
}
