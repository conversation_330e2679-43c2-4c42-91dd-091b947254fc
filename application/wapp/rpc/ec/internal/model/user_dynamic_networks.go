package model

import (
	"xj-serv/pkg/model"
)

type UserDynamicNetworks struct {
	model.MODEL_BASE
	UserID      uint64 `json:"user_id" gorm:"column:user_id"`           // 用户ID
	FatherID    uint64 `json:"father_id" gorm:"column:father_id"`       // 上级ID
	NetworkType string `json:"network_type" gorm:"column:network_type"` // 网络类型
	Mark        string `json:"mark" gorm:"column:mark"`                 // 标识符(具体视网体而定)
	Label       string `json:"label" gorm:"column:label;default:generic"`
}

func (m *UserDynamicNetworks) TableName() string {
	return "user_dynamic_networks"
}
