package model

import (
	"time"
	"xj-serv/pkg/common"
	"xj-serv/pkg/model"

	"github.com/gofrs/uuid/v5"
	"gorm.io/gorm"
)

type User struct {
	model.MODEL_BASE
	Uuid           uuid.UUID `json:"uuid" gorm:"column:uuid"`
	Mobile         string    `json:"mobile" gorm:"column:mobile"`                   // 用户手机号
	NickName       string    `json:"nick_name" gorm:"column:nick_name"`             // 用户昵称
	Avatar         string    `json:"avatar" gorm:"column:avatar"`                   // 头像文件ID
	Gender         uint8     `json:"gender" gorm:"column:gender"`                   // 性别
	Country        string    `json:"country" gorm:"column:country"`                 // 国家
	Province       string    `json:"province" gorm:"column:province"`               // 省份
	City           string    `json:"city" gorm:"column:city"`                       // 城市
	AddressID      uint      `json:"address_id" gorm:"column:address_id"`           // 默认收货地址
	Balance        uint      `json:"balance" gorm:"column:balance"`                 // 用户可用余额
	Withdraw       uint      `json:"withdraw" gorm:"column:withdraw"`               // 用户可提现金额
	Points         uint      `json:"points" gorm:"column:points"`                   // 用户可用积分
	PayMoney       uint      `json:"pay_money" gorm:"column:pay_money"`             // 用户总支付的金额
	ExpendMoney    uint      `json:"expend_money" gorm:"column:expend_money"`       // 实际消费的金额(不含退款)
	GradeID        uint      `json:"grade_id" gorm:"column:grade_id"`               // 会员等级ID
	Platform       string    `json:"platform" gorm:"column:platform"`               // 注册来源客户端 (APP、H5、小程序等)
	LastLoginTime  uint      `json:"last_login_time" gorm:"column:last_login_time"` // 最后登录时间
	Openid         string    `json:"openid" gorm:"column:openid"`
	Unionid        string    `json:"unionid" gorm:"column:unionid"`
	Openfinderid   string    `json:"openfinderid" gorm:"column:openfinderid"`
	Opentalentid   string    `json:"opentalentid" gorm:"column:opentalentid"`
	SharerAppid    string    `json:"sharer_appid" gorm:"column:sharer_appid"`
	ContactName    string    `json:"contact_name" gorm:"column:contact_name"`
	ContactNumber  string    `json:"contact_number" gorm:"column:contact_number"`
	ShareCode      string    `json:"share_code" gorm:"column:share_code"`
	ShareCodeImage string    `json:"share_code_image" gorm:"column:share_code_image"`
	RegIpv4        string    `json:"reg_ipv4" gorm:"column:reg_ipv4"`
	InviteFrom     uint64    `json:"invite_from" gorm:"column:invite_from"`
	RegSource      string    `json:"reg_source" gorm:"column:reg_source"`
	Level          int8      `json:"level" gorm:"column:level"`
	RoleId         int8      `json:"role_id" gorm:"role_id;default:0"`
	UserLevel      UserLevel `json:"user_level" gorm:"foreignKey:ID;references:Level;"`
	RegTime        string    `json:"reg_time" gorm:"-"`
	Birthday       time.Time `json:"birthday" gorm:"column:birthday"`
	IsEnterprise   bool      `json:"is_enterprise" gorm:"column:is_enterprise" ` // 是否企业用户
}

func (m *User) TableName() string {
	return "user_user"
}

func (m *User) AfterFind(tx *gorm.DB) (err error) {
	m.Mobile = common.InfoMask(m.Mobile) // 手机号脱敏
	m.RegTime = m.CreatedAt.Format("2006-01-02 15:04:05")
	return
}
