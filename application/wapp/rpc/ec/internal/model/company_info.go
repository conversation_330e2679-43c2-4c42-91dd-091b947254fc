package model

import (
	"xj-serv/pkg/model"
)

type CompanyInfo struct {
	model.MODEL_BASE
	UserID      uint64 `json:"user_id" gorm:"column:user_id;not null"`          // 代理商userid
	Name        string `json:"name" gorm:"column:name"`                         // 公司名称
	Address     string `json:"address" gorm:"column:address"`                   // 公司地址
	BankAccount string `json:"bank_account" gorm:"column:bank_account"`         // 银行账号
	BankName    string `json:"bank_name" gorm:"column:bank_name"`               // 开户银行
	TaxNumber   string `json:"tax_number" gorm:"column:tax_number;uniqueIndex"` // 税号
	Phone       string `json:"phone" gorm:"column:phone"`                       // 联系电话
	LicenseImg  string `json:"license_img" gorm:"column:license_img"`           // 营业执照图片
	Status      int32  `json:"status" gorm:"column:status"`                     // 审核状态
	Remark      string `json:"remark" gorm:"column:remark"`                     // 审核结果
}

func (m *CompanyInfo) TableName() string {
	return "company_info"
}
