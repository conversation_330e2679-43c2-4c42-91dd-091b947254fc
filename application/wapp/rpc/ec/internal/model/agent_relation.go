package model

import (
	"time"
	"xj-serv/pkg/model"
)

// AgentRelation 代理商用户关联表
type AgentRelation struct {
	model.MODEL_BASE
	AgentUserID    uint64  `json:"agent_user_id" gorm:"column:agent_user_id;not null"`                                // 代理商userid
	SalesmanUserID uint64  `json:"salesman_user_id" gorm:"column:salesman_user_id;not null"`                          // 业务员userid
	SalesmanRate   float64 `json:"salesman_rate" gorm:"column:salesman_rate;type:decimal(5,2);default:0.00;not null"` // 代理给业务员佣金比例
	SalesmanRemark string  `json:"salesman_remark" gorm:"column:salesman_remark"`                                     // 业务员备注
	IsFullTime     int64   `json:"is_full_time" gorm:"column:is_full_time"`                                           // 全职兼职
	CreatedAt      time.Time
	UpdatedAt      time.Time
	DeletedAt      *time.Time `gorm:"index"`
}

// TableName 返回表名
func (m *AgentRelation) TableName() string {
	return "agent_relation"
}
