package config

import (
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	BizRedis redis.RedisConf
	DB       struct {
		Master struct {
			DataSource   string
			MaxOpenConns int `json:",default=10"`
			MaxIdleConns int `json:",default=100"`
			MaxLifetime  int `json:",default=3600"`
		}
		Slaves []struct {
			DataSource   string
			MaxOpenConns int `json:",default=10"`
			MaxIdleConns int `json:",default=100"`
			MaxLifetime  int `json:",default=3600"`
		}
	}
	// 添加RabbitMQ配置
	RabbitMQ struct {
		Host        string
		Port        int
		Username    string
		Password    string
		VirtualHost string
		// 默认参数
		DefaultParams struct {
			Durable    bool `json:",default=true"`
			AutoDelete bool `json:",default=false"`
			Exclusive  bool `json:",default=false"`
			NoWait     bool `json:",default=false"`
			Mandatory  bool `json:",default=false"`
			Immediate  bool `json:",default=false"`
		}
		// 队列配置
		Queues map[string]struct {
			Exchange   string
			RoutingKey string
			QueueName  string
		}
	}
	JwtAuth struct {
		AccessSecret  string
		AccessExpire  int64
		RefreshSecret string
		RefreshExpire int64
		Issuer        string
	}
	WxApp struct {
		AccessTokenPrefix string
		AppId             string
		AppSecret         string
		AesSecret         string
	}
	WxLeague struct {
		AccessTokenPrefix string
		AppId             string
		AppSecret         string
		AesSecret         string
	}
	WxPay struct {
		MchId                  string
		AppId                  string
		CertificateSerialNo    string
		PrivateKeyPath         string
		WechatPayPublicKeyId   string
		WechatPayPublicKeyPath string

		BaseURL string `json:",default=https://api.mch.weixin.qq.com"`
		Timeout int    `json:",default=60"`

		// 回调配置
		NotifyUrl string // 支付回调通知地址

		// 转账配置
		Transfer struct {
			MaxAmount    int64   `json:",default=*********"`
			MinAmount    int64   `json:",default=30"`
			DefaultScene string  `json:",default=MARKETING_REWARD"`
			FeeRatio     float64 `json:",default=0.1"`
		}

		// 安全配置
		Security struct {
			EncryptionKey    string
			LogSensitiveData bool `json:",default=false"`
		}
	}
	Product struct {
		NeedAudit bool `json:",default=false"`
	}
	XjsRPC zrpc.RpcClientConf
	PfsRPC zrpc.RpcClientConf
	AiRPC  zrpc.RpcClientConf
	EcRPC  zrpc.RpcClientConf
}
