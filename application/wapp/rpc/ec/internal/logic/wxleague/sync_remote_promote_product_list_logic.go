package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"google.golang.org/grpc/metadata"
	"strconv"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemotePromoteProductListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemotePromoteProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemotePromoteProductListLogic {
	return &SyncRemotePromoteProductListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程可推广商品列表
func (l *SyncRemotePromoteProductListLogic) SyncRemotePromoteProductList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 检查是否是消息队列调用
	var isQueueMessage bool
	var msgData map[string]interface{}

	// 从上下文中获取消息数据
	if md, ok := metadata.FromIncomingContext(l.ctx); ok {
		if values := md.Get("x-queue-message"); len(values) > 0 {
			isQueueMessage = true
			if err := json.Unmarshal([]byte(values[0]), &msgData); err != nil {
				l.Errorf("解析队列消息数据失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "解析队列消息数据失败")
			}
		}
	}

	if isQueueMessage {
		// 处理队列消息
		return l.handleQueueMessage(msgData)
	} else {
		// 作为定时任务启动初始消息发送
		return l.startInitialSync()
	}
}

// startInitialSync 启动初始同步
func (l *SyncRemotePromoteProductListLogic) startInitialSync() (*pb.EmptyRequest, error) {
	l.Infof("开始启动可推广商品分页同步任务")

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		l.Errorf("队列管理器未初始化")
		return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "队列管理器未初始化")
	}

	// 发送两条消息，分别对应 plan_type 为 1 和 2
	planTypes := []int{1, 2}
	
	for _, planType := range planTypes {
		// 构建初始消息内容
		msgData := map[string]interface{}{
			"type":       "promote_product", // 添加消息类型标识
			"next_key":   "",                // 初始为空
			"page_index": 1,                 // 初始页数为1
			"plan_type":  planType,          // 计划类型
			"timestamp":  time.Now().Unix(),
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msgData)
		if err != nil {
			l.Errorf("序列化消息失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化消息失败")
		}

		// 发送消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", msgBytes)
		if err != nil {
			l.Errorf("发送消息到队列失败 (plan_type=%d): %v", planType, err)
			return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送消息到队列失败")
		}
		
		l.Infof("成功发送初始同步消息 (plan_type=%d)", planType)
	}

	l.Infof("可推广商品初始同步任务启动完成")
	return &pb.EmptyRequest{}, nil
}

// handleQueueMessage 处理队列消息
func (l *SyncRemotePromoteProductListLogic) handleQueueMessage(msgData map[string]interface{}) (*pb.EmptyRequest, error) {
	// 获取消息参数
	msgType, ok := msgData["type"].(string)
	if !ok || msgType != "promote_product" {
		l.Errorf("消息类型错误或缺失")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "消息类型错误或缺失")
	}

	nextKey, _ := msgData["next_key"].(string)

	pageIndexFloat, ok := msgData["page_index"].(float64)
	if !ok {
		pageIndexFloat = 1
	}
	pageIndex := int(pageIndexFloat)

	planTypeFloat, ok := msgData["plan_type"].(float64)
	if !ok {
		planTypeFloat = 1
	}
	planType := int(planTypeFloat)

	l.Infof("处理可推广商品分页请求: 页数=%d, nextKey=%s, planType=%d",
		pageIndex, nextKey, planType)

	// 创建一个新的上下文，不受原始RPC上下文的超时限制
	bgCtx := context.Background()

	// 将任务放到后台执行
	go func() {
		// 在后台处理商品同步
		l.processProductsInBackground(bgCtx, nextKey, pageIndex, planType)
	}()

	// 立即返回成功
	l.Infof("已将可推广商品同步任务放入后台执行: 页数=%d", pageIndex)
	return &pb.EmptyRequest{}, nil
}

// processProductsInBackground 在后台处理商品同步
func (l *SyncRemotePromoteProductListLogic) processProductsInBackground(ctx context.Context, nextKey string, pageIndex int, planType int) {
	l.Infof("开始在后台处理第 %d 页商品同步", pageIndex)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		// 即使获取令牌失败，也要尝试发送下一页消息，保证链式调用不中断
		l.sendNextPageMessage(nextKey, pageIndex, planType)
		return
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		// 即使获取数据库连接失败，也要尝试发送下一页消息
		l.sendNextPageMessage(nextKey, pageIndex, planType)
		return
	}

	// 构建请求参数
	req := &wxmodels.GetPromoteProductListRequest{
		PlanType: planType,
		PageSize: 20, // 每页获取20条数据
		NextKey:  nextKey,
	}

	// 调用微信API获取商品列表
	resp, err := wxClient.GetPromoteProductList(ctx, req, token)
	if err != nil {
		l.Errorf("获取可推广商品列表失败: %v", err)
		// 即使获取商品列表失败，也要尝试发送下一页消息
		l.sendNextPageMessage(nextKey, pageIndex, planType)
		return
	}

	// 当前页的商品信息
	currentPageProducts := resp.ProductList
	l.Infof("第 %d 页获取到 %d 条商品记录", pageIndex, len(currentPageProducts))

	// 使用并发处理当前页数据
	var wg sync.WaitGroup
	// 限制并发数，避免请求过多导致微信API限流
	concurrencyLimit := 5
	semaphore := make(chan struct{}, concurrencyLimit)

	for _, product := range currentPageProducts {
		productId, _ := strconv.ParseUint(product.ProductId, 10, 64)
		// 确保键格式为 "shopAppid_productId"
		key := fmt.Sprintf("%s_%d", product.ShopAppid, productId)

		wg.Add(1)
		go func(product wxmodels.ProductListItem, productId uint64, key string) {
			defer wg.Done()
			defer func() {
				// 捕获可能的panic，防止影响其他商品处理
				if r := recover(); r != nil {
					l.Errorf("处理商品 %s 时发生panic: %v", key, r)
				}
			}()

			// 获取信号量，限制并发数
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			//// 先尝试订阅商品，如果订阅失败则跳过该商品
			//if !l.trySubscribeProduct(productId, product.ShopAppid, planType, token, wxClient) {
			//	l.Infof("商品 %s 订阅失败，跳过处理", key)
			//	return
			//}

			// 获取商品详情
			detailReq := &wxmodels.GetPromoteProductDetailRequest{
				ProductId: productId,
				ShopAppid: product.ShopAppid,
				PlanType:  planType,
			}
			detailResp, err := wxClient.GetPromoteProductDetail(ctx, detailReq, token)
			if err != nil {
				l.Errorf("获取商品 %s 详情失败: %v", key, err)
				return
			}

			// 将商品基本信息转换为JSON字符串
			headImgsJSON, _ := json.Marshal(detailResp.Product.ProductInfo.HeadImgs)
			descInfoJSON, _ := json.Marshal(detailResp.Product.ProductInfo.DescInfo)
			skusJSON, _ := json.Marshal(detailResp.Product.ProductInfo.Skus)
			commissionInfoJSON, _ := json.Marshal(detailResp.Product.CommissionInfo)

			// 查找该商品是否已存在
			var existingProduct model.TempProduct
			result := db.Where("product_id = ? AND shop_appid = ?", productId, product.ShopAppid).First(&existingProduct)

			// 设置缓存过期时间为24小时
			expiresAt := time.Now().Add(24 * time.Hour)

			// 计算最低价格
			var minPrice int64 = -1
			for _, sku := range detailResp.Product.ProductInfo.Skus {
				if minPrice == -1 || sku.SalePrice < minPrice {
					minPrice = sku.SalePrice
				}
			}

			// 按最低价格计算佣金
			commissionAmount := int64(float64(minPrice) * (float64(detailResp.Product.CommissionInfo.ServiceRatio) / 1000000))

			// 处理分类信息
			cat1, cat2, cat3 := l.processCategoryInfo(detailResp.Product.ProductInfo.CatsV2)

			// 构建商品数据
			tempProduct := model.TempProduct{
				ProductId:        int64(productId),
				ShopAppid:        product.ShopAppid,
				Title:            detailResp.Product.ProductInfo.Title,
				SubTitle:         detailResp.Product.ProductInfo.SubTitle,
				HeadImgs:         string(headImgsJSON),
				DescInfo:         string(descInfoJSON),
				Skus:             string(skusJSON),
				CommissionInfo:   string(commissionInfoJSON),
				CommissionAmount: commissionAmount,
				MinPrice:         minPrice,
				OnSale:           1, // 默认为在售状态
				ExpiresAt:        expiresAt,
				Cat1:             cat1,
				Cat2:             cat2,
				Cat3:             cat3,
			}

			// 如果存在则更新，不存在则创建
			if result.Error == nil {
				// 更新现有记录
				if err := db.Model(&existingProduct).Updates(tempProduct).Error; err != nil {
					l.Errorf("更新商品 %s 失败: %v", key, err)
				}
			} else {
				// 创建新记录
				if err := db.Create(&tempProduct).Error; err != nil {
					l.Errorf("创建商品 %s 失败: %v", key, err)
				}
			}

		}(product, productId, key)
	}

	// 等待所有goroutine完成
	wg.Wait()
	l.Infof("第 %d 页所有商品处理完成", pageIndex)

	// 检查是否有下一页
	if resp.NextKey != "" {
		// 发送下一页消息
		l.sendNextPageMessage(resp.NextKey, pageIndex+1, planType)
	} else {
		l.Infof("没有更多页面，分页获取完成")

		// 最后一页，删除过期的商品记录
		l.deleteExpiredProducts()
	}
}

// sendNextPageMessage 发送下一页消息
func (l *SyncRemotePromoteProductListLogic) sendNextPageMessage(nextKey string, nextPageIndex int, planType int) {
	// 构建下一页的消息
	nextPageMsg := map[string]interface{}{
		"type":       "promote_product",
		"next_key":   nextKey,
		"page_index": nextPageIndex,
		"plan_type":  planType,
		"timestamp":  time.Now().Unix(),
	}

	// 序列化消息
	nextPageMsgBytes, err := json.Marshal(nextPageMsg)
	if err != nil {
		l.Errorf("序列化下一页消息失败: %v", err)
		return
	}

	// 最多重试3次发送消息
	maxRetries := 3
	for i := 0; i < maxRetries; i++ {
		// 发送下一页消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", nextPageMsgBytes)
		if err == nil {
			l.Infof("成功发送下一页消息到队列，nextKey=%s, pageIndex=%d", nextKey, nextPageIndex)
			return
		}

		l.Errorf("发送下一页消息到队列失败(尝试 %d/%d): %v", i+1, maxRetries, err)

		// 如果不是最后一次尝试，等待一段时间后重试
		if i < maxRetries-1 {
			time.Sleep(2 * time.Second)
		}
	}

	l.Errorf("发送下一页消息到队列失败，已达到最大重试次数")
}

// deleteExpiredProducts 删除过期的商品记录
func (l *SyncRemotePromoteProductListLogic) deleteExpiredProducts() {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return
	}

	// 获取当前时间
	now := time.Now()

	// 删除过期的商品记录
	result := db.Unscoped().Where("expires_at < ?", now).Delete(&model.TempProduct{})
	if result.Error != nil {
		l.Errorf("删除过期商品记录失败: %v", result.Error)
		return
	}

	l.Infof("成功删除 %d 条过期商品记录", result.RowsAffected)
}

// processCategoryInfo 处理商品分类信息，返回最多3级分类ID
// 从catsV2中找到ShopProductsCategory表中最接近level=3的记录
// 最大满足返回level=1,2,3的结果
// 如果找到的最大level是2或1，则cat2或cat3可以返回0
func (l *SyncRemotePromoteProductListLogic) processCategoryInfo(catsV2 []wxmodels.CatInfo) (cat1, cat2, cat3 int) {
	if len(catsV2) == 0 {
		return 0, 0, 0
	}

	// 提取分类ID
	var catIDs []uint
	for _, cat := range catsV2 {
		catID, err := strconv.ParseUint(cat.CatId, 10, 32)
		if err == nil {
			catIDs = append(catIDs, uint(catID))
		} else {
			l.Errorf("转换分类ID失败: %v", err)
		}
	}

	if len(catIDs) == 0 {
		return 0, 0, 0
	}

	// 查询分类信息，按level降序排列，优先获取level较高的分类
	var categories []*model.ShopProductsCategory
	if err := l.svcCtx.GetSlaveDB().Where("cat_id IN ?", catIDs).Order("level DESC").Find(&categories).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return 0, 0, 0
	}

	// 如果没有找到任何分类记录
	if len(categories) == 0 {
		return 0, 0, 0
	}

	// 按level分组存储分类
	levelMap := make(map[int8][]*model.ShopProductsCategory)
	maxLevel := int8(0)

	for _, category := range categories {
		levelMap[category.Level] = append(levelMap[category.Level], category)
		if category.Level > maxLevel {
			maxLevel = category.Level
		}
	}

	// 从最高level开始，找到对应的分类ID
	// 如果找到level=3的分类，需要通过FCatID向上查找level=2和level=1的分类
	if maxLevel == 3 && len(levelMap[3]) > 0 {
		level3Cat := levelMap[3][0]
		cat3 = int(level3Cat.CatID)

		// 查找level=2的父分类
		var level2Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 2", level3Cat.FCatID).First(&level2Cat).Error; err == nil {
			cat2 = int(level2Cat.CatID)

			// 查找level=1的父分类
			var level1Cat model.ShopProductsCategory
			if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
				cat1 = int(level1Cat.CatID)
			}
		}
	} else if maxLevel == 2 && len(levelMap[2]) > 0 {
		// 如果最高level是2，直接使用level=2的分类，并查找其父分类
		level2Cat := levelMap[2][0]
		cat2 = int(level2Cat.CatID)

		// 查找level=1的父分类
		var level1Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
			cat1 = int(level1Cat.CatID)
		}
	} else if maxLevel == 1 && len(levelMap[1]) > 0 {
		// 如果最高level是1，直接使用level=1的分类
		cat1 = int(levelMap[1][0].CatID)
	}

	return cat1, cat2, cat3
}

// trySubscribeProduct 尝试订阅商品，返回是否订阅成功
func (l *SyncRemotePromoteProductListLogic) trySubscribeProduct(productId uint64, shopAppid string, planType int, token string, wxClient *client.WechatClient) bool {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return false
	}

	// 检查是否已经订阅过该商品
	var existingSubscription model.TempProductSubscription
	result := db.Where("product_id = ? AND shop_appid = ? AND plan_type = ?",
		productId, shopAppid, planType).
		Order("created_at DESC").
		First(&existingSubscription)

	// 如果已经有订阅记录，无论成功失败，都认为已处理过
	if result.Error == nil {
		// 已经有订阅记录，直接返回true继续处理商品
		return true
	}

	// 构建订阅请求
	req := &wxmodels.SubscribeProductRequest{
		ProductId: productId,
	}

	// 调用微信API订阅商品
	err := wxClient.SubscribeProduct(context.Background(), req, token)
	if err != nil {
		// 订阅失败，记录日志
		l.Errorf("订阅商品失败: ProductId=%d, ShopAppid=%s, Error=%v", productId, shopAppid, err)

		// 创建订阅记录，但不关心status字段
		subscription := model.TempProductSubscription{
			ProductId: int64(productId),
			ShopAppid: shopAppid,
			PlanType:  planType,
		}

		// 保存订阅历史记录
		if dbErr := db.Create(&subscription).Error; dbErr != nil {
			l.Errorf("保存商品订阅历史记录失败: %v", dbErr)
		}

		// 订阅失败，返回false跳过该商品
		return false
	}

	// 订阅成功，记录日志
	l.Infof("成功订阅商品: ProductId=%d", productId)

	// 创建订阅记录，但不关心status字段
	subscription := model.TempProductSubscription{
		ProductId: int64(productId),
		ShopAppid: shopAppid,
		PlanType:  planType,
	}

	// 保存订阅历史记录
	if dbErr := db.Create(&subscription).Error; dbErr != nil {
		l.Errorf("保存商品订阅历史记录失败: %v", dbErr)
	}

	// 订阅成功，返回true继续处理商品
	return true
}
