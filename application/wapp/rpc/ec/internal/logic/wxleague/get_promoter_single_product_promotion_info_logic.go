package wxleaguelogic

import (
	"context"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPromoterSingleProductPromotionInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetPromoterSingleProductPromotionInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPromoterSingleProductPromotionInfoLogic {
	return &GetPromoterSingleProductPromotionInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取某个推客某个商品的内嵌商品卡片
func (l *GetPromoterSingleProductPromotionInfoLogic) GetPromoterSingleProductPromotionInfo(in *pb.GetPromoterSingleProductPromotionInfoReq) (*pb.GetPromoterSingleProductPromotionInfoResp, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetPromoterSingleProductPromotionInfoRequest{
		SharerAppid: in.SharerAppid,
		ProductId:   in.ProductId,
		ShopAppid:   in.ShopAppid,
		//HeadSupplierAppid: l.svcCtx.Config.WxLeague.AppId, // 使用机构的appid
	}

	// 调用微信API
	resp, err := wxClient.GetPromoterSingleProductPromotionInfo(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取商品内嵌卡片失败: %v", err)
		return nil, err
	}

	return &pb.GetPromoterSingleProductPromotionInfoResp{
		ProductPromotionLink: resp.ProductPromotionLink,
	}, nil
}
