package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteTalentProductListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteTalentProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteTalentProductListLogic {
	return &SyncRemoteTalentProductListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteTalentProductList 同步远程可推广商品列表
// Deprecated
func (l *SyncRemoteTalentProductListLogic) SyncRemoteTalentProductList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 获取数据库中现有的所有商品信息
	var existingDbProducts []model.TempTalentProduct
	if err := db.Select("product_id, shop_appid").Find(&existingDbProducts).Error; err != nil {
		l.Errorf("查询数据库 temp_products 失败: %v", err)
		return nil, fmt.Errorf("查询数据库失败")
	}

	// 将数据库中的商品ID和店铺ID存入map，方便查找
	dbProductMap := make(map[string]struct{}, len(existingDbProducts))
	for _, product := range existingDbProducts {
		key := fmt.Sprintf("%s_%d", product.ShopAppid, product.ProductId)
		dbProductMap[key] = struct{}{}
	}
	l.Infof("数据库中现有商品数量: %d", len(dbProductMap))

	// 记录所有API返回的商品，用于最后删除不存在的记录
	allApiProductMap := make(map[string]struct{})

	// 需要获取的佣金类型列表
	commissionTypes := []int{1}

	// 遍历所有佣金类型
	for _, commissionType := range commissionTypes {
		l.Infof("开始获取CommissionType=%d的商品数据", commissionType)

		// 分页循环获取所有商品
		nextKey := ""
		pageIndex := 1

		for {
			l.Infof("CommissionType=%d: 开始获取第 %d 页商品数据", commissionType, pageIndex)

			// 构建请求参数
			req := &wxmodels.GetCooperativeItemListRequest{
				CommissionType: commissionType,
				PageSize:       20, // 每页获取20条数据
				NextKey:        nextKey,
			}

			// 调用微信API获取商品列表
			resp, err := wxClient.GetCooperativeItemList(l.ctx, req, token)
			if err != nil {
				l.Errorf("获取合作商品列表失败 (CommissionType=%d): %v", commissionType, err)
				// 继续下一个佣金类型，而不是直接返回错误
				break
			}

			// 当前页的商品信息
			currentPageProducts := resp.List
			l.Infof("CommissionType=%d, 第 %d 页获取到 %d 条商品记录", commissionType, pageIndex, len(currentPageProducts))

			// 处理当前页数据
			for _, product := range currentPageProducts {
				// 获取商品推广参数详情
				promotionReq := &wxmodels.GetPromotionDetailRequest{
					HeadSupplierItemLink: product.HeadSupplierItemLink,
				}
				promotionResp, err := wxClient.GetPromotionDetail(l.ctx, promotionReq, token)
				if err != nil {
					l.Errorf("获取商品推广参数详情失败: %v", err)
					continue
				} else {
					l.Infof("成功获取商品 %s 的推广参数详情", product.ProductId)
					// 处理推广详情数据
					productId := promotionResp.Item.ProductId
					shopAppid := promotionResp.Item.ShopAppid

					// 确保键格式为 "shopAppid_productId"
					key := fmt.Sprintf("%s_%s", shopAppid, productId)

					// 记录所有API返回的商品
					allApiProductMap[key] = struct{}{}

					// 获取商品详情
					detailReq := &wxmodels.ProductDetailGetRequest{
						ProductId: int64(product.ProductId),
						ShopAppid: shopAppid,
					}
					detailResp, err := wxClient.GetProductDetail(l.ctx, detailReq, token)
					if err != nil {
						l.Errorf("获取商品 %s 详情失败: %v", key, err)
						continue
					}

					// 将商品基本信息转换为JSON字符串
					headImgsJSON, _ := json.Marshal(detailResp.Item.ProductInfo.HeadImgs)
					descInfoJSON, _ := json.Marshal(detailResp.Item.ProductInfo.DescInfo)
					skusJSON, _ := json.Marshal(detailResp.Item.ProductInfo.Skus)
					commissionInfoJSON, _ := json.Marshal(promotionResp.Item.CommissionInfo)

					// 使用哈希表判断商品是否存在
					_, exists := dbProductMap[key]

					// 设置缓存过期时间为24小时
					expiresAt := time.Now().Add(24 * time.Hour)

					// 计算最低价格
					var minPrice int64 = -1
					for _, sku := range detailResp.Item.ProductInfo.Skus {
						if minPrice == -1 || sku.SalePrice < minPrice {
							minPrice = sku.SalePrice
						}
					}

					// 处理分类信息
					cat1, cat2, cat3 := l.processCategoryInfo(detailResp.Item.ProductInfo.CatsV2)
					fmt.Println("cat1:", cat1, "cat2:", cat2, "cat3:", cat3)

					// 构建商品数据
					tempProduct := model.TempTalentProduct{
						ProductId:      int64(product.ProductId),
						ShopAppid:      shopAppid,
						Title:          detailResp.Item.ProductInfo.Title,
						SubTitle:       detailResp.Item.ProductInfo.SubTitle,
						HeadImgs:       string(headImgsJSON),
						DescInfo:       string(descInfoJSON),
						Skus:           string(skusJSON),
						CommissionInfo: string(commissionInfoJSON),
						MinPrice:       minPrice,
						PlanType:       int8(promotionResp.Item.CommissionInfo.PlanType),
						CommissionType: int8(promotionResp.Item.CommissionInfo.CommissionType),
						OnSale:         1, // 默认为在售状态
						Show:           1, // 默认为展示状态
						Cat1:           cat1,
						Cat2:           cat2,
						Cat3:           cat3,
						ExpiresAt:      expiresAt,
					}

					// 如果存在则更新，不存在则创建
					if exists {
						// 更新现有记录
						if err := db.Model(&model.TempTalentProduct{}).Where("product_id = ? AND shop_appid = ?", productId, shopAppid).Updates(tempProduct).Error; err != nil {
							l.Errorf("更新商品 %s 失败: %v", key, err)
						}
					} else {
						// 创建新记录
						if err := db.Create(&tempProduct).Error; err != nil {
							l.Errorf("创建商品 %s 失败: %v", key, err)
						}
					}

					// 避免请求过快，微信API有限流
					time.Sleep(time.Millisecond * 200)
				}
			}

			// 判断是否还有下一页
			if resp.NextKey == "" {
				l.Infof("CommissionType=%d: 没有更多页面，分页获取完成", commissionType)
				break
			}

			// 更新nextKey用于获取下一页
			nextKey = resp.NextKey
			pageIndex++

			// 避免请求过快
			time.Sleep(time.Second * 1)
		}

		// 在不同佣金类型之间增加间隔，避免请求过快
		time.Sleep(time.Second * 2)
	}

	// 所有页面处理完毕后，删除数据库中存在但API返回中不存在的记录
	productsToDelete := make([]string, 0)
	for dbKey := range dbProductMap {
		if _, existsInApi := allApiProductMap[dbKey]; !existsInApi {
			productsToDelete = append(productsToDelete, dbKey)
		}
	}

	// 批量删除过时记录（这里需要解析出product_id和shop_appid）
	if len(productsToDelete) > 0 {
		l.Infof("准备删除 %d 条过时的商品记录", len(productsToDelete))

		for _, key := range productsToDelete {
			// 使用字符串分割替代 fmt.Sscanf
			parts := strings.Split(key, "_")
			if len(parts) != 2 {
				l.Errorf("无法解析键值: %s", key)
				continue
			}

			shopAppid := parts[0]
			productId, err := strconv.ParseInt(parts[1], 10, 64)
			if err != nil {
				l.Errorf("解析商品ID失败: %s, 错误: %v", parts[1], err)
				continue
			}

			l.Infof("尝试删除商品记录: shopAppid=%s, productId=%d", shopAppid, productId)
			if err := db.Unscoped().Where("product_id = ? AND shop_appid = ?", productId, shopAppid).Delete(&model.TempTalentProduct{}).Error; err != nil {
				l.Errorf("删除过时商品记录 %s 失败: %v", key, err)
			}
		}

		l.Infof("成功删除过时商品记录")
	} else {
		l.Infof("没有过时的商品记录需要删除")
	}

	l.Infof("商品列表同步完成，获取了CommissionType=0和CommissionType=1的数据")

	return &pb.EmptyRequest{}, nil
}

// processCategoryInfo 处理商品分类信息，返回最多3级分类ID
// 从catsV2中找到ShopProductsCategory表中最接近level=3的记录
// 最大满足返回level=1,2,3的结果
// 如果找到的最大level是2或1，则cat2或cat3可以返回0
func (l *SyncRemoteTalentProductListLogic) processCategoryInfo(catsV2 []wxmodels.BaseCatInfo) (cat1, cat2, cat3 int) {
	if len(catsV2) == 0 {
		return 0, 0, 0
	}

	// 提取分类ID
	var catIDs []uint64
	for _, cat := range catsV2 {
		catIDs = append(catIDs, cat.CatId)
	}

	if len(catIDs) == 0 {
		return 0, 0, 0
	}

	// 查询分类信息，按level降序排列，优先获取level较高的分类
	var categories []*model.ShopProductsCategory
	if err := l.svcCtx.GetSlaveDB().Where("cat_id IN ?", catIDs).Order("level DESC").Find(&categories).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return 0, 0, 0
	}

	// 如果没有找到任何分类记录
	if len(categories) == 0 {
		return 0, 0, 0
	}

	// 按level分组存储分类
	levelMap := make(map[int8][]*model.ShopProductsCategory)
	maxLevel := int8(0)

	for _, category := range categories {
		levelMap[category.Level] = append(levelMap[category.Level], category)
		if category.Level > maxLevel {
			maxLevel = category.Level
		}
	}

	// 从最高level开始，找到对应的分类ID
	// 如果找到level=3的分类，需要通过FCatID向上查找level=2和level=1的分类
	if maxLevel == 3 && len(levelMap[3]) > 0 {
		level3Cat := levelMap[3][0]
		cat3 = int(level3Cat.CatID)

		// 查找level=2的父分类
		var level2Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 2", level3Cat.FCatID).First(&level2Cat).Error; err == nil {
			cat2 = int(level2Cat.CatID)

			// 查找level=1的父分类
			var level1Cat model.ShopProductsCategory
			if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
				cat1 = int(level1Cat.CatID)
			}
		}
	} else if maxLevel == 2 && len(levelMap[2]) > 0 {
		// 如果最高level是2，直接使用level=2的分类，并查找其父分类
		level2Cat := levelMap[2][0]
		cat2 = int(level2Cat.CatID)

		// 查找level=1的父分类
		var level1Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
			cat1 = int(level1Cat.CatID)
		}
	} else if maxLevel == 1 && len(levelMap[1]) > 0 {
		// 如果最高level是1，直接使用level=1的分类
		cat1 = int(levelMap[1][0].CatID)
	}

	return cat1, cat2, cat3
}
