package wxleaguelogic

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	ec_order "xj-serv/types/order"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SetFanToGroupLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetFanToGroupLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetFanToGroupLogic {
	return &SetFanToGroupLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SetFanToGroup 设置粉丝为团长
// SetFanToGroupReq.SalesmanUserId 现在可以是代理商ID或者业务经理ID
func (l *SetFanToGroupLogic) SetFanToGroup(in *pb.SetFanToGroupReq) (*pb.BoolRequest, error) {
	l.Infof("业务经理ID:%d 设置粉丝ID:%d 为团长", in.SalesmanUserId, in.GroupmanUserId)

	// 参数校验
	if in.SalesmanUserId <= 0 || in.GroupmanUserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证业务经理的角色和状态
	var salesman model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.SalesmanUserId).First(&salesman).Error; err != nil {
		l.Errorf("查询业务经理信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询业务经理信息失败")
	}

	// 验证用户是否为业务经理角色
	if salesman.RoleId != config.RoleSalesman && salesman.RoleId != config.RoleAgent {
		l.Errorf("用户ID %d 不是代理商或业务经理，无法设置团长", in.SalesmanUserId)
		return nil, xerr.NewErrMsg(l.ctx, "只有代理商或业务经理可以设置团长")
	}

	// 检查该粉丝是否已经是团长
	var fan model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.GroupmanUserId).First(&fan).Error; err != nil {
		l.Errorf("查询粉丝信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询粉丝信息失败")
	}

	// 判断用户level是否为5，5表示团长
	if fan.Level == 5 {
		l.Errorf("用户ID %d 已经是团长，无需重复设置", in.GroupmanUserId)
		return nil, xerr.NewErrMsg(l.ctx, "该用户已经是团长")
	}

	// 验证粉丝是否存在且是该业务经理的粉丝(无限级)
	isFan, err := l.isUserInSalesmanDownline(in.SalesmanUserId, in.GroupmanUserId)
	if err != nil {
		l.Errorf("验证粉丝关系失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "验证粉丝关系失败")
	}

	if !isFan {
		l.Errorf("用户ID %d 不是业务经理ID %d 的粉丝", in.GroupmanUserId, in.SalesmanUserId)
		return nil, xerr.NewErrMsg(l.ctx, "该用户不是您的粉丝，无法设置为团长")
	}

	// 调用adjustUserInventory 调整用户库存
	adjustLogic := NewAdjustUserInventoryLogic(l.ctx, l.svcCtx)
	_, err = adjustLogic.AdjustUserInventory(&pb.AdjustUserInventoryReq{
		UserID:          in.SalesmanUserId,
		ProductID:       1,  // 团长产品ID
		Quantity:        -1, // 消耗1个
		Remark:          "设置团长消耗",
		TransactionType: ec_order.TransactionTypeConsumption,
		TccAction:       "",
	})
	if err != nil {
		l.Errorf("调整库存失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "调整库存失败")
	}

	// 开始数据库事务进行角色更新和关系记录
	err = l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 更新用户为团长角色
		if err := tx.Model(&model.User{}).Where("id = ?", in.GroupmanUserId).Update("level", 5).Error; err != nil {
			l.Errorf("更新用户为团长失败: %v", err)
			return xerr.NewErrMsg(l.ctx, "更新用户为团长失败")
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &pb.BoolRequest{Success: true}, nil
}

// isUserInSalesmanDownline 递归检查用户是否在业务经理的下线中
func (l *SetFanToGroupLogic) isUserInSalesmanDownline(salesmanID, userID uint64) (bool, error) {
	// 使用递归CTE查询检查粉丝是否在业务经理的下线中
	query := `WITH RECURSIVE user_upline AS (
		-- 初始查询：获取当前用户
		SELECT id, invite_from
		FROM user_user
		WHERE id = ?

		UNION ALL

		-- 递归查询：获取上级用户
		SELECT u.id, u.invite_from
		FROM user_user u
		INNER JOIN user_upline up ON u.id = up.invite_from
	)
	-- 检查上级链中是否包含业务经理
	SELECT COUNT(*) as is_in_downline
	FROM user_upline
	WHERE id = ?;`

	type Result struct {
		IsInDownline int
	}

	var result Result
	if err := l.svcCtx.GetSlaveDB().Raw(query, userID, salesmanID).Scan(&result).Error; err != nil {
		l.Errorf("查询用户关系失败: %v", err)
		return false, err
	}

	return result.IsInDownline > 0, nil
}
