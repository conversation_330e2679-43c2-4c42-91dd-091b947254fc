package wxleaguelogic

import (
	"context"
	"math/rand"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteShopLiveRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteShopLiveRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteShopLiveRecordListLogic {
	return &SyncRemoteShopLiveRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteShopLiveRecordList 同步店铺直播列表
func (l *SyncRemoteShopLiveRecordListLogic) SyncRemoteShopLiveRecordList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	l.Infof("【调试】开始获取微信访问令牌")
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("【调试】获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}
	l.Infof("【调试】成功获取微信访问令牌: %s", token[:10]+"...")

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("【调试】获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}
	l.Infof("【调试】成功获取数据库连接")

	// 从数据库获取推客信息，并预加载绑定的店铺信息
	l.Infof("【调试】开始查询推客信息并预加载店铺信息")

	// 检查temp_shop_promoters表是否存在
	var count int64
	if err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'temp_shop_promoters'").Count(&count).Error; err != nil {
		l.Errorf("【调试】检查temp_shop_promoters表是否存在失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "检查数据表失败")
	}
	if count == 0 {
		l.Errorf("【调试】temp_shop_promoters表不存在")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "temp_shop_promoters表不存在")
	}
	l.Infof("【调试】temp_shop_promoters表存在")

	// 检查temp_bind_shops表是否存在
	if err := db.Raw("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'temp_bind_shops'").Count(&count).Error; err != nil {
		l.Errorf("【调试】检查temp_bind_shops表是否存在失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "检查数据表失败")
	}
	if count == 0 {
		l.Errorf("【调试】temp_bind_shops表不存在")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "temp_bind_shops表不存在")
	}
	l.Infof("【调试】temp_bind_shops表存在")

	// 检查temp_shop_promoters表中是否有数据
	if err := db.Model(&model.TempShopPromoters{}).Count(&count).Error; err != nil {
		l.Errorf("【调试】检查temp_shop_promoters表数据失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "检查数据失败")
	}
	l.Infof("【调试】temp_shop_promoters表中有 %d 条数据", count)
	if count == 0 {
		l.Errorf("【调试】temp_shop_promoters表中没有数据，请先同步推客数据")
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "没有推客数据，请先同步推客数据")
	}

	// 检查外键关联是否正确
	var promoterSample model.TempShopPromoters
	if err := db.First(&promoterSample).Error; err != nil {
		l.Errorf("【调试】获取推客样本失败: %v", err)
	} else {
		l.Infof("【调试】推客样本: ShopAppid=%s, PromoterID=%s, PromoterType=%d",
			promoterSample.ShopAppid, promoterSample.PromoterID, promoterSample.PromoterType)

		var bindShop model.TempBindShops
		if err := db.Where("shop_appid = ?", promoterSample.ShopAppid).First(&bindShop).Error; err != nil {
			l.Errorf("【调试】根据ShopAppid=%s查询绑定店铺失败: %v", promoterSample.ShopAppid, err)
		} else {
			l.Infof("【调试】找到对应的绑定店铺: ShopAppid=%s, Nickname=%s",
				bindShop.ShopAppid, bindShop.Nickname)
		}
	}

	var promoters []model.TempShopPromoters
	if err := db.Preload("BindShop").Find(&promoters).Error; err != nil {
		l.Errorf("【调试】查询推客信息失败: %v，SQL: %s", err, db.Statement.SQL.String())

		// 尝试不使用Preload直接查询
		l.Infof("【调试】尝试不使用Preload直接查询推客信息")
		if err := db.Find(&promoters).Error; err != nil {
			l.Errorf("【调试】直接查询推客信息也失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询推客信息失败")
		}

		// 手动加载绑定店铺信息
		for i, p := range promoters {
			var bindShop model.TempBindShops
			if err := db.Where("shop_appid = ?", p.ShopAppid).First(&bindShop).Error; err != nil {
				l.Errorf("【调试】手动加载推客[%d]的绑定店铺信息失败: %v", i, err)
			} else {
				promoters[i].BindShop = bindShop
				l.Infof("【调试】手动加载推客[%d]的绑定店铺信息成功: %s", i, bindShop.Nickname)
			}
		}
	}

	l.Infof("【调试】获取到 %d 个推客信息", len(promoters))

	// 检查推客类型分布
	typeCount := make(map[int32]int)
	for _, p := range promoters {
		typeCount[p.PromoterType]++
	}
	for t, c := range typeCount {
		l.Infof("【调试】推客类型 %d 的数量: %d", t, c)
	}

	// 检查是否有视频号类型的推客
	videoPromoterCount := 0
	for _, p := range promoters {
		if p.PromoterType == 1 { // 1表示视频号类型
			videoPromoterCount++
			l.Infof("【调试】找到视频号推客: ID=%s, Nickname=%s, ShopAppid=%s",
				p.PromoterID, p.Nickname, p.ShopAppid)

			// 检查是否有绑定的店铺
			if p.BindShop.ShopAppid == "" {
				l.Errorf("【调试】该视频号推客没有绑定店铺信息")
			} else {
				l.Infof("【调试】该视频号推客绑定的店铺: %s", p.BindShop.Nickname)
			}
		}
	}
	if videoPromoterCount == 0 {
		l.Errorf("【调试】没有找到视频号类型的推客，无法获取直播信息")
	}

	// 记录所有API返回的直播ID，用于最后删除不存在的记录
	allApiLiveIds := make(map[string]struct{})

	// 使用并发处理每个推客的直播列表
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 5) // 限制并发数为5
	var mapMutex sync.Mutex             // 用于保护allApiLiveIds的并发访问

	processedCount := 0
	for _, promoter := range promoters {
		// 检查推客类型是否为视频号
		if promoter.PromoterType != 1 { // 1表示视频号类型
			l.Infof("【调试】跳过非视频号推客: ID=%s, Type=%d", promoter.PromoterID, promoter.PromoterType)
			continue
		}

		// 检查是否有绑定的店铺
		if promoter.BindShop.ShopAppid == "" {
			l.Errorf("【调试】跳过没有绑定店铺的推客: ID=%s", promoter.PromoterID)
			continue
		}

		processedCount++
		l.Infof("【调试】开始处理第 %d 个视频号推客: ID=%s, ShopAppid=%s",
			processedCount, promoter.PromoterID, promoter.BindShop.ShopAppid)

		wg.Add(1)
		go func(p model.TempShopPromoters) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 获取店铺直播列表
			l.syncShopLiveRecords(wxClient, token, p, db, allApiLiveIds, &mapMutex)
		}(promoter)
	}

	// 等待所有goroutine完成
	l.Infof("【调试】等待所有推客的直播信息同步完成")
	wg.Wait()
	l.Infof("【调试】所有推客的直播信息同步已完成")

	// 检查是否获取到任何直播信息
	l.Infof("【调试】共获取到 %d 个直播ID", len(allApiLiveIds))
	if len(allApiLiveIds) == 0 {
		l.Errorf("【调试】没有获取到任何直播信息，请检查API调用是否成功")
	}

	// 删除不存在的直播记录
	l.Infof("【调试】开始处理需要删除的直播记录")
	var existingRecords []model.TempLiveList
	if err := db.Where("class = ?", "shop").Find(&existingRecords).Error; err != nil {
		l.Errorf("【调试】查询现有店铺直播记录失败: %v", err)
	} else {
		l.Infof("【调试】数据库中现有 %d 条店铺直播记录", len(existingRecords))

		var recordsToDelete []uint64
		for _, record := range existingRecords {
			if _, exists := allApiLiveIds[record.ExportID]; !exists {
				recordsToDelete = append(recordsToDelete, uint64(record.ID))
				l.Infof("【调试】标记需要删除的直播记录: ID=%d, ExportID=%s", record.ID, record.ExportID)
			}
		}

		if len(recordsToDelete) > 0 {
			l.Infof("【调试】开始删除 %d 条不存在的直播记录", len(recordsToDelete))
			if err := db.Unscoped().Where("id IN ? AND class = ?", recordsToDelete, "shop").Delete(&model.TempLiveList{}).Error; err != nil {
				l.Errorf("【调试】删除不存在的店铺直播记录失败: %v", err)
			} else {
				l.Infof("【调试】成功删除 %d 个不存在的店铺直播记录", len(recordsToDelete))
			}
		} else {
			l.Infof("【调试】没有需要删除的直播记录")
		}
	}

	l.Infof("【调试】同步店铺直播列表完成")
	return &pb.EmptyRequest{}, nil
}

// syncShopLiveRecords 同步指定推客的店铺直播列表
func (l *SyncRemoteShopLiveRecordListLogic) syncShopLiveRecords(wxClient *client.WechatClient, token string, promoter model.TempShopPromoters, db *orm.DB, allApiLiveIds map[string]struct{}, mapMutex *sync.Mutex) {
	l.Infof("【调试】开始同步推客 %s 的店铺直播列表", promoter.PromoterID)

	// 构建请求参数
	req := &wxmodels.GetShopLiveRecordListRequest{
		ShopAppid:        promoter.BindShop.ShopAppid,
		PromoterId:       promoter.PromoterID,
		PromoterType:     int(promoter.PromoterType),
		MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
	}

	l.Infof("【调试】请求参数: ShopAppid=%s, PromoterId=%s, PromoterType=%d, MiniProgramAppid=%s",
		req.ShopAppid, req.PromoterId, req.PromoterType, req.MiniProgramAppid)

	// 设置请求超时
	ctx, cancel := context.WithTimeout(l.ctx, 10*time.Second)
	defer cancel()

	// 调用微信API获取店铺直播列表
	l.Infof("【调试】开始调用微信API获取店铺直播列表")
	resp, err := wxClient.GetShopLiveRecordList(ctx, req, token)
	if err != nil {
		l.Errorf("【调试】获取店铺 %s 的推客 %s 直播列表失败: %v", promoter.BindShop.ShopAppid, promoter.PromoterID, err)
		return
	}

	l.Infof("【调试】店铺 %s 的推客 %s 获取到 %d 个直播", promoter.BindShop.ShopAppid, promoter.PromoterID, len(resp.LiveRecordList))

	if len(resp.LiveRecordList) == 0 {
		l.Infof("【调试】该推客没有直播记录")
		return
	}

	// 批量处理直播数据
	var livesToCreate []model.TempLiveList
	var livesToUpdate []model.TempLiveList

	for i, live := range resp.LiveRecordList {
		l.Infof("【调试】处理第 %d 个直播: ExportId=%s, Description=%s",
			i+1, live.ExportId, live.Description)

		// 记录API返回的直播ID
		mapMutex.Lock()
		allApiLiveIds[live.ExportId] = struct{}{}
		mapMutex.Unlock()

		// 使用固定的随机种子，确保同一店铺每次生成的随机值相同
		r := rand.New(rand.NewSource(int64(promoter.ID)))
		randomValue := r.Intn(9801) + 200 // 生成200-10000之间的随机数

		// 构建直播数据模型
		tempLive := model.TempLiveList{
			ExportID:          live.ExportId,
			TalentAppid:       promoter.BindShop.ShopAppid, // 使用店铺appid作为talent_appid
			TalentNickname:    promoter.Nickname,
			TalentHeadImg:     promoter.HeadImgUrl,
			Description:       live.Description,
			PromoterId:        promoter.PromoterID,
			PromoterShareLink: live.PromoterShareLink,
			Type:              "live",
			InfoProgress:      "pending",
			Class:             "shop",      // 标记为店铺直播
			SharerTotal:       randomValue, // 添加随机分享数
		}

		// 检查数据库中是否已存在该记录
		var existingRecord model.TempLiveList
		result := db.Where("export_id = ? AND class = ?", live.ExportId, "shop").First(&existingRecord)
		if result.Error == nil {
			l.Infof("【调试】直播记录已存在，将进行更新: ID=%d", existingRecord.ID)
			// 记录已存在，添加到更新列表
			tempLive.ID = existingRecord.ID

			// 如果已有记录中已经有分享数，则保留原有值
			if existingRecord.SharerTotal > 0 {
				tempLive.SharerTotal = existingRecord.SharerTotal
			}

			livesToUpdate = append(livesToUpdate, tempLive)
		} else {
			l.Infof("【调试】直播记录不存在，将创建新记录")
			// 记录不存在，添加到创建列表
			livesToCreate = append(livesToCreate, tempLive)
		}
	}

	// 批量创建新记录
	if len(livesToCreate) > 0 {
		l.Infof("【调试】开始批量创建 %d 条直播记录", len(livesToCreate))
		if err := db.Create(&livesToCreate).Error; err != nil {
			l.Errorf("【调试】批量创建店铺 %s 的推客 %s 直播记录失败: %v", promoter.BindShop.ShopAppid, promoter.PromoterID, err)
		} else {
			l.Infof("【调试】成功批量创建店铺 %s 的推客 %s 的 %d 个直播记录", promoter.BindShop.ShopAppid, promoter.PromoterID, len(livesToCreate))
		}
	}

	// 批量更新现有记录
	for i, live := range livesToUpdate {
		l.Infof("【调试】开始更新第 %d 条直播记录: ID=%d", i+1, live.ID)
		if err := db.Model(&model.TempLiveList{}).Where("id = ?", live.ID).Updates(live).Error; err != nil {
			l.Errorf("【调试】更新店铺 %s 的推客 %s 的直播 %s 记录失败: %v", promoter.BindShop.ShopAppid, promoter.PromoterID, live.ExportID, err)
		} else {
			l.Infof("【调试】成功更新直播记录")
		}
	}

	l.Infof("【调试】完成推客 %s 的店铺直播列表同步", promoter.PromoterID)
}
