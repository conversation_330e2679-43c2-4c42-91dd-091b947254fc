package wxleaguelogic

import (
	"context"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLiveNoticeRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLiveNoticeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLiveNoticeRecordListLogic {
	return &GetLiveNoticeRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取达人平台直播预约列表
func (l *GetLiveNoticeRecordListLogic) GetLiveNoticeRecordList(in *pb.EmptyRequest) (*pb.GetLiveNoticeRecordListResp, error) {
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求
	req := &wxmodels.GetLiveNoticeRecordListRequest{
		TalentAppid:      "wx67bbcff47a5849c9",
		MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
	}

	// 调用微信API
	resp, err := wxClient.GetLiveNoticeRecordList(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取达人平台直播预约列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	var list []*pb.LiveNoticeRecordInfo
	for _, item := range resp.LiveNoticeRecordList {
		list = append(list, &pb.LiveNoticeRecordInfo{
			NoticeId:    item.NoticeId,
			Description: item.Description,
			StartTime:   item.StartTime,
		})
	}

	return &pb.GetLiveNoticeRecordListResp{
		List: list,
	}, nil
}
