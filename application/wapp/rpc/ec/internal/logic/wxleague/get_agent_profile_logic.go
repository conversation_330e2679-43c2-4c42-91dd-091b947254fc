package wxleaguelogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetAgentProfileLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAgentProfileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAgentProfileLogic {
	return &GetAgentProfileLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取代理商/业务员个人主页数据
func (l *GetAgentProfileLogic) GetAgentProfile(in *pb.GetAgentProfileReq) (*pb.GetAgentProfileResp, error) {
	// 使用传入的用户ID
	userId := in.UserId
	l.Infof("获取用户ID为%d的个人主页数据", userId)

	// 查询用户信息
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", userId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 验证用户角色是否为代理商或业务员
	if user.RoleId != config.RoleAgent && user.RoleId != config.RoleSalesman {
		l.Errorf("用户ID %d 既不是代理商也不是业务员，无法获取统计数据", userId)
		return nil, xerr.NewErrMsg(l.ctx, "您不是代理商或业务员，无法获取统计数据")
	}

	// 初始化响应
	resp := &pb.GetAgentProfileResp{
		// 用户基本信息
		NickName: user.NickName,
		Avatar:   user.Avatar,
		Mobile:   user.Mobile,
		IsAgent:  user.RoleId == config.RoleAgent,

		// 统计信息
		TotalCommission:    0,
		SalesmanCommission: 0,
		TodayOrderCount:    0,
		TodayOrderAmount:   0,
		AiPointTotal:       0,
		TeamPackageCount:   0,
	}

	// 查询团长套餐数量（从user_inventory表中获取）
	var teamPackage model.UserInventory
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND product_id = ?", userId, 1).First(&teamPackage).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			l.Errorf("查询团长套餐数量失败: %v", err)
			// 不返回错误，继续执行
		}
		// 如果记录不存在，TeamPackageCount保持为0
	} else {
		// 记录存在，设置团长套餐数量
		resp.TeamPackageCount = int64(teamPackage.Quantity)
	}

	// 如果是业务员，查询业务员相关信息
	if user.RoleId == config.RoleSalesman {
		var agentRelation model.AgentRelation
		if err := l.svcCtx.GetSlaveDB().Where("salesman_user_id = ?", userId).First(&agentRelation).Error; err != nil {
			if err != gorm.ErrRecordNotFound {
				l.Errorf("查询业务员关系失败: %v", err)
				return nil, xerr.NewErrMsg(l.ctx, "查询业务员信息失败")
			}
		} else {
			resp.SalesmanRate = agentRelation.SalesmanRate
			resp.SalesmanRemark = agentRelation.SalesmanRemark
			resp.IsFullTime = agentRelation.IsFullTime
		}
	}

	// 查询公司信息
	var companyInfo model.CompanyInfo
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ?", userId).First(&companyInfo).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			l.Errorf("查询公司信息失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "查询公司信息失败")
		}
		// 记录不存在，表示未填写过公司资料
		resp.HasCompanyInfo = false
	} else {
		// 记录存在，表示已填写过公司资料
		resp.HasCompanyInfo = true
		// 只有当状态为1(已审核通过)时才显示公司名称
		if companyInfo.Status == 1 {
			resp.CompanyName = companyInfo.Name
		}
	}

	// 获取今天的开始时间
	today := time.Now().Format("2006-01-02")
	todayStart, _ := time.Parse("2006-01-02", today)

	// 根据角色ID处理不同的统计逻辑
	if user.RoleId == config.RoleAgent { // 代理商
		// 1. 计算佣金总额
		var totalCommission float64
		if err := l.svcCtx.GetSlaveDB().Model(&model.AgentCommissionDetail{}).
			Where("agent_user_id = ?", userId).
			Select("COALESCE(SUM(agent_amount), 0)").
			Scan(&totalCommission).Error; err != nil {
			l.Errorf("计算佣金总额失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算佣金总额失败")
		}
		resp.TotalCommission = float64(totalCommission) / 100

		// 2. 计算业务经理佣金汇总
		var salesmanCommission float64
		if err := l.svcCtx.GetSlaveDB().Model(&model.AgentCommissionDetail{}).
			Where("agent_user_id = ?", userId).
			Select("COALESCE(SUM(salesman_amount), 0)").
			Scan(&salesmanCommission).Error; err != nil {
			l.Errorf("计算业务经理佣金汇总失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算业务经理佣金汇总失败")
		}
		resp.SalesmanCommission = float64(salesmanCommission) / 100

		sharerAppids, err := l.findAllDownlineSharerAppids(l.svcCtx.GetSlaveDB(), userId)
		if err != nil {
			l.Errorf("查找用户失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "查找用户失败")
		}

		// 如果没有下级用户，直接返回
		if len(sharerAppids) == 0 {
			return resp, nil
		}

		// 4. 计算今日订单数量和总额
		var todayOrderCount int64
		var todayOrderAmount float64
		if err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Count(&todayOrderCount).Error; err != nil {
			l.Errorf("计算今日订单数量失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单数量失败")
		}
		resp.TodayOrderCount = todayOrderCount

		if err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Select("COALESCE(SUM(actual_payment), 0)").
			Scan(&todayOrderAmount).Error; err != nil {
			l.Errorf("计算今日订单总额失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单总额失败")
		}
		resp.TodayOrderAmount = float64(todayOrderAmount) / 100

	} else if user.RoleId == config.RoleSalesman { // 业务员
		// 1. 业务员佣金汇总
		var salesmanCommission float64
		if err := l.svcCtx.GetSlaveDB().Model(&model.AgentCommissionDetail{}).
			Where("salesman_user_id = ?", userId).
			Select("COALESCE(SUM(salesman_amount), 0)").
			Scan(&salesmanCommission).Error; err != nil {
			l.Errorf("计算业务员佣金汇总失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算业务员佣金汇总失败")
		}
		resp.SalesmanCommission = float64(salesmanCommission) / 100

		// 2. 查找所有 sharer_appid
		sharerAppids, err := l.findAllDownlineSharerAppids(l.svcCtx.GetSlaveDB(), userId)
		if err != nil {
			l.Errorf("查找用户失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "查找用户失败")
		}

		// 如果没有用户，直接返回
		if len(sharerAppids) == 0 {
			return resp, nil
		}

		// 3. 计算今日订单数量和总额
		var todayOrderCount int64
		var todayOrderAmount float64
		if err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Count(&todayOrderCount).Error; err != nil {
			l.Errorf("计算今日订单数量失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单数量失败")
		}
		resp.TodayOrderCount = todayOrderCount

		if err := l.svcCtx.GetSlaveDB().Model(&model.CommissionOrders{}).
			Where("sharer_appid IN (?) AND order_status = '100' AND created_at >= ?", sharerAppids, todayStart).
			Select("COALESCE(SUM(actual_payment), 0)").
			Scan(&todayOrderAmount).Error; err != nil {
			l.Errorf("计算今日订单总额失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "计算今日订单总额失败")
		}
		resp.TodayOrderAmount = float64(todayOrderAmount) / 100
	}

	return resp, nil
}

// 递归查找所有用户的 sharer_appid
func (l *GetAgentProfileLogic) findAllDownlineSharerAppids(db *orm.DB, userId uint64) ([]string, error) {
	// 使用 WITH RECURSIVE 递归查询所有用户
	query := `WITH RECURSIVE user_hierarchy AS (
		-- 初始查询：获取当前用户
		SELECT id, invite_from, sharer_appid
		FROM user_user
		WHERE id = ?
		
		UNION ALL
		
		-- 递归查询：获取所有下级用户
		SELECT u.id, u.invite_from, u.sharer_appid
		FROM user_user u
		INNER JOIN user_hierarchy h ON u.invite_from = h.id
	)
	-- 查找所有有 sharer_appid 的用户
	SELECT sharer_appid
	FROM user_hierarchy
	WHERE sharer_appid != '';`

	var sharerAppids []string
	if err := db.Raw(query, userId).Scan(&sharerAppids).Error; err != nil {
		return nil, err
	}

	return sharerAppids, nil
}
