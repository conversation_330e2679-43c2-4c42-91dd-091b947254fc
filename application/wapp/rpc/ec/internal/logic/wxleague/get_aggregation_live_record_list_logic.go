package wxleaguelogic

import (
	"context"
	"fmt"
	"math/rand"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAggregationLiveRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetAggregationLiveRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAggregationLiveRecordListLogic {
	return &GetAggregationLiveRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取直播|预约聚合列表
func (l *GetAggregationLiveRecordListLogic) GetAggregationLiveRecordList(in *pb.GetAggregationLiveRecordListReq) (*pb.GetAggregationLiveRecordListResp, error) {
	// 获取分页参数
	pageNo := int(in.PageReq.PageNo)
	pageSize := int(in.PageReq.PageSize)
	if pageNo <= 0 {
		pageNo = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (pageNo - 1) * pageSize

	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return &pb.GetAggregationLiveRecordListResp{}, nil
	}

	// 构建查询条件
	// 按照优先级排序：
	// type=live 且 info_progress=completed
	// type=notice 且 info_progress=completed
	query := db.Table("temp_live_list")
	
	// 根据class参数进行过滤
	if in.Class != "" && in.Class != "all" {
		// 如果class不为空且不是"all"，则按class过滤
		query = query.Where("temp_live_list.class = ?", in.Class)
		l.Infof("按class=%s过滤直播列表", in.Class)
	} else {
		l.Infof("返回所有class类型的直播列表")
	}
	
	// 根据class类型选择不同的JOIN查询
	query = query.Select("temp_live_list.*")
	
	// 继续原有的过滤条件
	query = query.Where("(temp_live_list.type = ?) OR (temp_live_list.type = ?)",
		"live", "notice")
	
	// 继续原有的排序逻辑
	query = query.Order("CASE WHEN temp_live_list.type = 'live' AND temp_live_list.info_progress = 'completed' THEN 1 " +
		"WHEN temp_live_list.type = 'notice' AND temp_live_list.info_progress = 'completed' THEN 2 " +
		"ELSE 3 END")

	// 获取总记录数
	countQuery := db.Table("temp_live_list")
	
	// 根据class参数进行过滤
	if in.Class != "" && in.Class != "all" {
		// 如果class不为空且不是"all"，则按class过滤
		countQuery = countQuery.Where("temp_live_list.class = ?", in.Class)
	}
	
	// 继续原有的过滤条件
	countQuery = countQuery.Where("(temp_live_list.type = ?) OR (temp_live_list.type = ?)",
		"live", "notice")
	
	var total int64
	if err := countQuery.Count(&total).Error; err != nil {
		l.Errorf("查询总记录数失败: %v", err)
		return &pb.GetAggregationLiveRecordListResp{}, nil
	}

	// 获取分页数据
	dataQuery := db.Table("temp_live_list").Select("temp_live_list.*")
	
	// 根据class参数进行过滤
	if in.Class != "" && in.Class != "all" {
		// 如果class不为空且不是"all"，则按class过滤
		dataQuery = dataQuery.Where("temp_live_list.class = ?", in.Class)
	}
	
	// 继续原有的过滤条件
	dataQuery = dataQuery.Where("(temp_live_list.type = ?) OR (temp_live_list.type = ?)",
		"live", "notice")
	
	// 继续原有的排序逻辑
	dataQuery = dataQuery.Order("CASE WHEN temp_live_list.type = 'live' AND temp_live_list.info_progress = 'completed' THEN 1 " +
		"WHEN temp_live_list.type = 'notice' AND temp_live_list.info_progress = 'completed' THEN 2 " +
		"ELSE 3 END")
	
	var liveRecords []model.TempLiveList
	if err := dataQuery.Limit(pageSize).Offset(offset).Find(&liveRecords).Error; err != nil {
		l.Errorf("查询分页数据失败: %v", err)
		return &pb.GetAggregationLiveRecordListResp{}, nil
	}

	// 构建响应
	resp := &pb.GetAggregationLiveRecordListResp{
		Total: uint64(total),
		List:  make([]*pb.AggregationLiveRecordList, 0, len(liveRecords)),
	}

	// 收集需要查询的talent_appid
	talentAppids := make(map[string]bool)
	shopAppids := make(map[string]bool)
	
	for _, record := range liveRecords {
		if record.Class == "talent" {
			talentAppids[record.TalentAppid] = true
		} else if record.Class == "shop" {
			shopAppids[record.TalentAppid] = true
		}
	}
	
	// 查询talent信息
	talentInfoMap := make(map[string]struct {
		Nickname string
		HeadImg  string
	})
	
	if len(talentAppids) > 0 {
		var talents []model.TempTalents
		talentAppidList := make([]string, 0, len(talentAppids))
		for appid := range talentAppids {
			talentAppidList = append(talentAppidList, appid)
		}
		
		if err := db.Where("talent_appid IN ?", talentAppidList).Find(&talents).Error; err != nil {
			l.Errorf("查询达人信息失败: %v", err)
		} else {
			for _, talent := range talents {
				talentInfoMap[talent.TalentAppid] = struct {
					Nickname string
					HeadImg  string
				}{
					Nickname: talent.TalentNickname,
					HeadImg:  talent.TalentHeadImg,
				}
			}
		}
	}
	
	// 查询shop信息
	shopInfoMap := make(map[string]struct {
		Nickname string
		HeadImg  string
	})
	
	if len(shopAppids) > 0 {
		var shops []model.TempBindShops
		shopAppidList := make([]string, 0, len(shopAppids))
		for appid := range shopAppids {
			shopAppidList = append(shopAppidList, appid)
		}
		
		if err := db.Where("shop_appid IN ?", shopAppidList).Find(&shops).Error; err != nil {
			l.Errorf("查询店铺信息失败: %v", err)
		} else {
			for _, shop := range shops {
				shopInfoMap[shop.ShopAppid] = struct {
					Nickname string
					HeadImg  string
				}{
					Nickname: shop.Nickname,
					HeadImg:  shop.HeadImgUrl,
				}
			}
		}
	}

	// 转换数据
	for _, record := range liveRecords {
		var nickname, headImg string
		
		// 根据class类型获取对应的昵称和头像
		if record.Class == "talent" {
			if info, exists := talentInfoMap[record.TalentAppid]; exists {
				nickname = info.Nickname
				headImg = info.HeadImg
			}
		} else if record.Class == "shop" {
			if info, exists := shopInfoMap[record.TalentAppid]; exists {
				nickname = info.Nickname
				headImg = info.HeadImg
			}
		}
		
		item := &pb.AggregationLiveRecordList{
			ID:                         uint64(record.ID),
			ExportID:                   record.ExportID,
			Description:                record.Description,
			PromoterShareLink:          record.PromoterShareLink,
			TalentAppid:                record.TalentAppid,
			TalentNickname:             nickname,
			TalentHeadImg:              headImg,
			PredictMaxCommissionAmount: int32(record.PredictMaxCommissionAmount),
			Type:                       record.Type,
			InfoProgress:               record.InfoProgress,
			NoticeID:                   record.NoticeID,
			StartTime:                  int64(record.StartTime),
			StartIsoTime:               record.StartIsoTime,
			SharerTotal:                int32(record.SharerTotal),
			SharerHeadImgs:             GetRandomAvatars(10),
			Class:                      record.Class,
		}

		resp.List = append(resp.List, item)
	}

	l.Infof("获取聚合直播列表成功，总记录数: %d, 当前页: %d, 每页大小: %d", total, pageNo, pageSize)
	return resp, nil
}

// GetRandomAvatars 返回指定数量的随机头像URL数组
func GetRandomAvatars(count int) []string {
	// 设置随机种子
	rand.Seed(time.Now().UnixNano())

	// 头像范围：1.jpg 到 5117.jpg
	maxAvatarID := 5117
	minAvatarID := 1

	// 创建结果数组
	result := make([]string, 0, count)

	// 已选择的头像ID，避免重复
	selectedIDs := make(map[int]struct{})

	// 生成指定数量的随机头像
	for i := 0; i < count; i++ {
		// 如果已经选择的头像数量接近最大值，避免无限循环
		if len(selectedIDs) >= maxAvatarID-minAvatarID+1 {
			break
		}

		// 生成随机ID
		var avatarID int
		for {
			avatarID = rand.Intn(maxAvatarID-minAvatarID+1) + minAvatarID
			if _, exists := selectedIDs[avatarID]; !exists {
				selectedIDs[avatarID] = struct{}{}
				break
			}
		}

		// 构建头像URL
		avatarURL := fmt.Sprintf("https://static.xjdy2024.com/v_avatar/%d.jpg", avatarID)
		result = append(result, avatarURL)
	}

	return result
}
