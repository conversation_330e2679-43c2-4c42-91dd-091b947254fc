package wxleaguelogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SyncRemoteTalentProductByNotifyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteTalentProductByNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteTalentProductByNotifyLogic {
	return &SyncRemoteTalentProductByNotifyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteTalentProductByNotify 通过通知同步单个达人商品
func (l *SyncRemoteTalentProductByNotifyLogic) SyncRemoteTalentProductByNotify(in *pb.SyncRemoteTalentProductByNotifyReq) (*pb.BoolRequest, error) {
	// 参数验证
	if in.ProductId <= 0 || in.ShopAppid == "" {
		return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.RequestParamError, "商品ID或ShopAppid无效")
	}

	l.Infof("开始同步单个达人商品: ProductId=%d, ShopAppid=%s", in.ProductId, in.ShopAppid)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(
		l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId,
		l.svcCtx.Config.WxLeague.AppSecret,
		l.svcCtx.Config.WxLeague.AccessTokenPrefix,
	)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("获取微信访问令牌失败: %v", err))
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取商品推广参数详情
	promotionReq := &wxmodels.GetPromotionDetailRequest{
		HeadSupplierItemLink: fmt.Sprintf("https://shop.weixin.qq.com/shop-market/product/detail?product_id=%d&shop_appid=%s", in.ProductId, in.ShopAppid),
	}
	promotionResp, err := wxClient.GetPromotionDetail(l.ctx, promotionReq, token)
	if err != nil {
		l.Errorf("获取商品推广参数详情失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("获取商品推广参数详情失败: %v", err))
	}

	l.Infof("成功获取商品 %d 的推广参数详情", in.ProductId)

	// 处理推广详情数据
	//productId := promotionResp.Item.ProductId
	//shopAppid := promotionResp.Item.ShopAppid

	// 获取商品详情
	detailReq := &wxmodels.ProductDetailGetRequest{
		ProductId: in.ProductId,
		ShopAppid: in.ShopAppid,
	}
	detailResp, err := wxClient.GetProductDetail(l.ctx, detailReq, token)
	if err != nil {
		l.Errorf("获取商品 %d 详情失败: %v", in.ProductId, err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("获取商品详情失败: %v", err))
	}

	// 将商品基本信息转换为JSON字符串
	headImgsJSON, _ := json.Marshal(detailResp.Item.ProductInfo.HeadImgs)
	descInfoJSON, _ := json.Marshal(detailResp.Item.ProductInfo.DescInfo)
	skusJSON, _ := json.Marshal(detailResp.Item.ProductInfo.Skus)
	commissionInfoJSON, _ := json.Marshal(promotionResp.Item.CommissionInfo)

	// 设置缓存过期时间为24小时
	expiresAt := time.Now().Add(24 * time.Hour)

	// 计算最低价格
	var minPrice int64 = -1
	for _, sku := range detailResp.Item.ProductInfo.Skus {
		if minPrice == -1 || sku.SalePrice < minPrice {
			minPrice = sku.SalePrice
		}
	}

	// 获取分佣比例
	ratio := 0.0
	if promotionResp.Item.CommissionInfo.CommissionType == 1 {
		// 机构分佣
		ratio = float64(promotionResp.Item.CommissionInfo.ServiceRatio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
	} else {
		// 店铺分佣
		ratio = float64(promotionResp.Item.CommissionInfo.Ratio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
	}

	// 按最低价格计算佣金
	commissionAmount := int64(float64(minPrice) * ratio)

	// 处理分类信息
	cat1, cat2, cat3 := l.processCategoryInfo(detailResp.Item.ProductInfo.CatsV2)

	// 构建商品数据
	tempProduct := model.TempTalentProduct{
		ProductId:        in.ProductId,
		ShopAppid:        in.ShopAppid,
		Title:            detailResp.Item.ProductInfo.Title,
		SubTitle:         detailResp.Item.ProductInfo.SubTitle,
		HeadImgs:         string(headImgsJSON),
		DescInfo:         string(descInfoJSON),
		Skus:             string(skusJSON),
		CommissionInfo:   string(commissionInfoJSON),
		CommissionAmount: commissionAmount,
		MinPrice:         minPrice,
		PlanType:         int8(promotionResp.Item.CommissionInfo.PlanType),
		CommissionType:   int8(promotionResp.Item.CommissionInfo.CommissionType),
		OnSale:           1, // 默认为在售状态
		Show:             1, // 默认为展示状态
		Cat1:             cat1,
		Cat2:             cat2,
		Cat3:             cat3,
		ExpiresAt:        expiresAt,
		ProductStatus:    int8(detailResp.Item.ProductInfo.Status),
	}

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 查询商品是否已存在
	var existingProduct model.TempTalentProduct
	result := db.Where("shop_appid = ? AND product_id = ?", in.ShopAppid, in.ProductId).First(&existingProduct)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 商品不存在，创建新记录
			if err := db.Create(&tempProduct).Error; err != nil {
				l.Errorf("创建商品 %s_%d 失败: %v", tempProduct.ShopAppid, tempProduct.ProductId, err)
				return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("创建商品失败: %v", err))
			}
			l.Infof("成功创建商品: %s_%d", tempProduct.ShopAppid, tempProduct.ProductId)
		} else {
			// 查询出错
			l.Errorf("查询商品 %s_%d 失败: %v", in.ShopAppid, in.ProductId, result.Error)
			return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("查询商品失败: %v", result.Error))
		}
	} else {
		// 商品已存在，更新记录（包括更新expiresAt字段）
		if err := db.Model(&existingProduct).Updates(tempProduct).Error; err != nil {
			l.Errorf("更新商品 %s_%d 失败: %v", tempProduct.ShopAppid, tempProduct.ProductId, err)
			return &pb.BoolRequest{Success: false}, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("更新商品失败: %v", err))
		}
		l.Infof("成功更新商品: %s_%d", tempProduct.ShopAppid, tempProduct.ProductId)
	}

	return &pb.BoolRequest{Success: true}, nil
}

// processCategoryInfo 处理商品分类信息，返回最多3级分类ID
// 从catsV2中找到ShopProductsCategory表中最接近level=3的记录
// 最大满足返回level=1,2,3的结果
// 如果找到的最大level是2或1，则cat2或cat3可以返回0
func (l *SyncRemoteTalentProductByNotifyLogic) processCategoryInfo(catsV2 []wxmodels.BaseCatInfo) (cat1, cat2, cat3 int) {
	if len(catsV2) == 0 {
		return 0, 0, 0
	}

	// 提取分类ID
	var catIDs []uint64
	for _, cat := range catsV2 {
		catIDs = append(catIDs, cat.CatId)
	}

	if len(catIDs) == 0 {
		return 0, 0, 0
	}

	// 查询分类信息，按level降序排列，优先获取level较高的分类
	var categories []*model.ShopProductsCategory
	if err := l.svcCtx.GetSlaveDB().Where("cat_id IN ?", catIDs).Order("level DESC").Find(&categories).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return 0, 0, 0
	}

	// 如果没有找到任何分类记录
	if len(categories) == 0 {
		return 0, 0, 0
	}

	// 按level分组存储分类
	levelMap := make(map[int8][]*model.ShopProductsCategory)
	maxLevel := int8(0)

	for _, category := range categories {
		levelMap[category.Level] = append(levelMap[category.Level], category)
		if category.Level > maxLevel {
			maxLevel = category.Level
		}
	}

	// 从最高level开始，找到对应的分类ID
	// 如果找到level=3的分类，需要通过FCatID向上查找level=2和level=1的分类
	if maxLevel == 3 && len(levelMap[3]) > 0 {
		level3Cat := levelMap[3][0]
		cat3 = int(level3Cat.CatID)

		// 查找level=2的父分类
		var level2Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 2", level3Cat.FCatID).First(&level2Cat).Error; err == nil {
			cat2 = int(level2Cat.CatID)

			// 查找level=1的父分类
			var level1Cat model.ShopProductsCategory
			if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
				cat1 = int(level1Cat.CatID)
			}
		}
	} else if maxLevel == 2 && len(levelMap[2]) > 0 {
		// 如果最高level是2，直接使用level=2的分类，并查找其父分类
		level2Cat := levelMap[2][0]
		cat2 = int(level2Cat.CatID)

		// 查找level=1的父分类
		var level1Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
			cat1 = int(level1Cat.CatID)
		}
	} else if maxLevel == 1 && len(levelMap[1]) > 0 {
		// 如果最高level是1，直接使用level=1的分类
		cat1 = int(levelMap[1][0].CatID)
	}

	return cat1, cat2, cat3
}
