package wxleaguelogic

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/wxCtl"

	"github.com/zeromicro/go-zero/core/logx"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"
)

// SharerRegisterAndBindStatusLogic 推客注册和绑定状态逻辑
type SharerRegisterAndBindStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

// NewSharerRegisterAndBindStatusLogic 创建推客注册和绑定状态逻辑
func NewSharerRegisterAndBindStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SharerRegisterAndBindStatusLogic {
	return &SharerRegisterAndBindStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取推客的注册和绑定状态
func (l *SharerRegisterAndBindStatusLogic) SharerRegisterAndBindStatus(in *pb.SharerRegisterAndBindStatusReq) (*pb.SharerRegisterAndBindStatusResp, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 从用户表获取openid
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Where("id = ?", in.UserId).
		First(&user).Error; err != nil {
		l.Errorf("查询用户[%d]失败: %v", in.UserId, err)
		return nil, err
	}

	// 构建请求参数
	req := &wxmodels.PromoterRegisterAndBindStatusRequest{
		SharerOpenid:     user.Openid, // 使用从数据库查询到的openid
		IsSimpleRegister: true,        // 默认使用简易注册
	}

	// 调用微信API
	resp, err := wxClient.GetPromoterRegisterAndBindStatus(l.ctx, req, token)
	if err != nil {
		logx.Errorf("获取推客注册和绑定状态失败: %v", err)
		return nil, err
	}

	// 根据条件设置businessType
	var businessType string
	if resp.RegisterStatus == 0 || resp.RegisterStatus == 1 {
		// 如果是未注册或注册中状态，使用注册业务类型
		businessType = resp.RegisterBusinessType
	} else if resp.BindStatus == 0 {
		// 如果未绑定，使用绑定业务类型
		businessType = resp.BindBusinessType
	}
	// 如果已经绑定，将sharer_appid更新到用户表
	if resp.BindStatus == 1 && resp.SharerAppid != "" && user.SharerAppid == "" {
		// 使用主库进行更新操作
		updateResult := l.svcCtx.MasterDB.Model(&model.User{}).Where("id = ?", in.UserId).UpdateColumns(model.User{SharerAppid: resp.SharerAppid, Level: 1, RoleId: 1})
		if updateResult.Error != nil {
			l.Errorf("更新用户[%d]的sharer_appid失败: %v", in.UserId, updateResult.Error)
		} else if updateResult.RowsAffected > 0 {
			l.Infof("成功更新用户[%d]的sharer_appid为: %s", in.UserId, resp.SharerAppid)
		}
	}

	// 构建响应
	return &pb.SharerRegisterAndBindStatusResp{
		BindStatus:        int64(resp.BindStatus),
		RegisterStatus:    int64(resp.RegisterStatus),
		QueryString:       resp.BindQueryString,
		BusinessType:      businessType,
		HeadSupplierAppid: l.svcCtx.Config.WxLeague.AppId,
		CommissionType:    1, // 设置为1，表示机构自己分佣
		CommissionRatio:   0, // 设置为0
	}, nil
}
