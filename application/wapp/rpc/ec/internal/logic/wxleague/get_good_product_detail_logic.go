package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"github.com/zeromicro/go-zero/core/logx"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"
)

type GetGoodProductDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetGoodProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodProductDetailLogic {
	return &GetGoodProductDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// GetGoodProductDetail 获取好物商品详情
func (l *GetGoodProductDetailLogic) GetGoodProductDetail(in *pb.GetGoodProductDetailReq) (*pb.GetGoodProductDetailResp, error) {
	// 参数验证
	if in.ProductId == 0 || in.ShopAppid == "" {
		return nil, fmt.Errorf("商品ID和店铺appid不能为空")
	}

	// 构建缓存键
	cacheKey := fmt.Sprintf("%s_%d", in.ShopAppid, in.ProductId)

	// 尝试从Redis哈希表获取商品详情
	cachedData, err := l.svcCtx.BizRedis.Hget(GoodProductsCache, cacheKey)
	if err == nil && len(cachedData) > 0 {
		// 缓存命中，解析缓存数据
		productInfo := &pb.ProductInfo{}
		if err = json.Unmarshal([]byte(cachedData), productInfo); err == nil {
			// 构建响应
			return &pb.GetGoodProductDetailResp{
				ProductId:            productInfo.ProductId,
				ShopAppid:            productInfo.ShopAppid,
				Title:                productInfo.Title,
				SubTitle:             productInfo.SubTitle,
				HeadImgs:             productInfo.HeadImgs,
				DescInfo:             productInfo.DescInfo,
				Skus:                 productInfo.Skus,
				HeadSupplierItemLink: productInfo.HeadSupplierItemLink,
			}, nil
		}
		// 解析失败时继续从API获取
		l.Errorf("解析缓存数据失败: %v", err)
	}

	// 缓存不存在或解析失败，从微信API获取商品详情
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(
		l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId,
		l.svcCtx.Config.WxLeague.AppSecret,
		l.svcCtx.Config.WxLeague.AccessTokenPrefix,
	)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 调用微信API获取商品详情
	detailReq := &wxmodels.ProductDetailGetRequest{
		ProductId: int64(in.ProductId),
		ShopAppid: in.ShopAppid,
	}

	detailResp, err := wxClient.GetProductDetail(l.ctx, detailReq, token)
	if err != nil {
		l.Errorf("从API获取商品详情失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取商品详情失败")
	}

	// 构建商品信息对象
	productInfo := &pb.ProductInfo{
		ProductId: in.ProductId,
		ShopAppid: in.ShopAppid,
		Title:     detailResp.Item.ProductInfo.Title,
		SubTitle:  detailResp.Item.ProductInfo.SubTitle,
		HeadImgs:  detailResp.Item.ProductInfo.HeadImgs,
		DescInfo: &pb.ProductDescInfo{
			Imgs: detailResp.Item.ProductInfo.DescInfo.Imgs,
			Desc: detailResp.Item.ProductInfo.DescInfo.Desc,
		},
		Skus: make([]*pb.SkuInfo, 0, len(detailResp.Item.ProductInfo.Skus)),
	}

	// 处理SKU信息
	for _, sku := range detailResp.Item.ProductInfo.Skus {
		// 构建SKU对象
		skuInfo := &pb.SkuInfo{
			SkuId:      strconv.FormatUint(sku.SkuId, 10),
			ThumbImg:   sku.ThumbImg,
			SalePrice:  sku.SalePrice,
			StockNum:   sku.StockNum,
			EarnAmount: 0,
			SkuAttrs:   make([]*pb.SkuAttr, 0, len(sku.SkuAttrs)),
		}

		// 处理SKU属性
		for _, attr := range sku.SkuAttrs {
			skuInfo.SkuAttrs = append(skuInfo.SkuAttrs, &pb.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}

		productInfo.Skus = append(productInfo.Skus, skuInfo)
	}

	// 缓存商品信息到Redis哈希表
	productJSON, err := json.Marshal(productInfo)
	if err == nil {
		// 检查哈希表中商品数量是否已达上限
		count, _ := l.svcCtx.BizRedis.Hlen(GoodProductsCache)
		if count < MaxCachedProducts {
			// 存储到哈希表
			err = l.svcCtx.BizRedis.Hset(GoodProductsCache, cacheKey, string(productJSON))
			if err != nil {
				l.Errorf("缓存商品信息失败: %v", err)
			}
			// 设置哈希表的过期时间
			l.svcCtx.BizRedis.Expire(GoodProductsCache, GoodProductsExpire)
		}
	} else {
		l.Errorf("序列化商品信息失败: %v", err)
	}

	// 构建响应
	return &pb.GetGoodProductDetailResp{
		ProductId:            productInfo.ProductId,
		ShopAppid:            productInfo.ShopAppid,
		Title:                productInfo.Title,
		SubTitle:             productInfo.SubTitle,
		HeadImgs:             productInfo.HeadImgs,
		DescInfo:             productInfo.DescInfo,
		Skus:                 productInfo.Skus,
		HeadSupplierItemLink: productInfo.HeadSupplierItemLink,
	}, nil
}
