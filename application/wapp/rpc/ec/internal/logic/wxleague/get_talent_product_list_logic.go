package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTalentProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductListLogic {
	return &GetTalentProductListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取达人合作商品列表，从temp_talent_products表获取
func (l *GetTalentProductListLogic) GetTalentProductList(in *pb.GetTalentProductListReq) (*pb.GetTalentProductListResp, error) {
	// 获取分页参数
	pageNo := int(in.PageReq.PageNo)
	pageSize := int(in.PageReq.PageSize)
	if pageNo <= 0 {
		pageNo = 1
	}
	if pageSize <= 0 {
		pageSize = 10
	}
	offset := (pageNo - 1) * pageSize

	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, nil
	}

	var products []model.TempTalentProduct

	// 构建查询条件
	query := db.Table("temp_talent_products").
		Where("on_sale = ?", 1).
		Where("`show` = ?", 1).
		Where("`is_window` = ?", 1).
		Where("`product_status` = ?", 5)
	// Where("`commission_type` = ?", in.CommissionType)

	// 添加关键词搜索条件
	if in.PageReq.Keyword != "" {
		query = query.Where("title LIKE ?", "%"+in.PageReq.Keyword+"%")
	}

	// 添加分类查询条件
	if in.CatId > 0 {
		query = query.Where("cat1 = ?", in.CatId)
	}

	// 是否推荐
	if in.IsRecommend > 0 {
		query = query.Where("is_recommend = ?", in.IsRecommend)
	}

	var total int64
	if err := query.Count(&total).Error; err != nil {
		l.Errorf("查询数据数量失败: %v", err)
		return &pb.GetTalentProductListResp{}, nil
	}

	// 处理排序
	orderClause := l.buildOrderClause(in.SortReq.SortBy, in.SortReq.Order)
	// 执行查询
	if err := query.Order(orderClause).Limit(pageSize).Offset(offset).Find(&products).Error; err != nil {
		l.Errorf("查询分页数据失败: %v", err)
		return &pb.GetTalentProductListResp{}, nil
	}

	// 获取用户级别
	var userLevel int
	if err := l.svcCtx.MasterDB.Model(&model.User{}).
		Select("level").
		Where("id = ?", in.UserId).
		Scan(&userLevel).Error; err != nil {
		l.Errorf("查询用户级别失败: %v", err)
		userLevel = 1 // 默认为1级
	}

	// 获取用户级别对应的佣金比例
	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.MasterDB.Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", userLevel).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		// 使用默认值
		commissionConfig.CommissionRate = 0
	}

	// 构建响应
	productList := make([]*pb.ProductInfo, 0, len(products))
	for _, product := range products {
		// 创建商品信息对象
		productInfo := &pb.ProductInfo{
			ProductId: uint64(product.ProductId),
			ShopAppid: product.ShopAppid,
			Title:     product.Title,
			SubTitle:  product.SubTitle,
		}

		// 解析JSON字段
		if err := l.parseProductJSON(product, productInfo, commissionConfig.CommissionRate); err != nil {
			l.Errorf("解析商品JSON数据失败: %v", err)
		}

		productList = append(productList, productInfo)
	}

	// 随机打乱productList
	// rand.Shuffle(len(productList), func(i, j int) {
	// 	productList[i], productList[j] = productList[j], productList[i]
	// })

	return &pb.GetTalentProductListResp{
		ProductList: productList,
		Total:       uint64(total),
	}, nil
}

// 构建排序子句
func (l *GetTalentProductListLogic) buildOrderClause(sortBy, order string) string {
	// 默认排序
	if sortBy == "" {
		return "`order`,`id` DESC"
	}

	// 确保排序方向有效
	orderDir := "DESC"
	if strings.ToLower(order) == "asc" {
		orderDir = "ASC"
	}

	// 根据排序字段构建排序子句
	switch strings.ToLower(sortBy) {
	case "price":
		// 按价格排序
		return fmt.Sprintf("`order` DESC, min_price %s", orderDir)
	case "commission":
		// 按佣金金额排序
		return fmt.Sprintf("`order` DESC, commission_amount %s", orderDir)
	default:
		// 默认排序
		return "`order`,`id` DESC"
	}
}

// 解析商品的JSON字段到结构体
func (l *GetTalentProductListLogic) parseProductJSON(product model.TempTalentProduct, productInfo *pb.ProductInfo, commissionRate float64) error {
	// 解析HeadImgs
	var headImgs []string
	if err := json.Unmarshal([]byte(product.HeadImgs), &headImgs); err != nil {
		return err
	}
	productInfo.HeadImgs = headImgs

	// 解析DescInfo
	var descInfo pb.ProductDescInfo
	if err := json.Unmarshal([]byte(product.DescInfo), &descInfo); err != nil {
		return err
	}
	productInfo.DescInfo = &descInfo

	// 解析CommissionInfo，用于计算返利
	var commissionInfo struct {
		Ratio        int64 `json:"ratio"`         // 佣金费率，范围为【100000 - 900000】对应【10%-90%】
		ServiceRatio int64 `json:"service_ratio"` // 服务费率，范围为【100000 - 900000】对应【10%-90%】
	}
	if err := json.Unmarshal([]byte(product.CommissionInfo), &commissionInfo); err != nil {
		l.Errorf("解析CommissionInfo失败: %v", err)
		return err
	}

	// 获取分佣比例
	ratio := 0.0
	if product.CommissionType == 1 {
		// 机构分佣
		ratio = float64(commissionInfo.ServiceRatio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
		ratio = ratio * (commissionRate / 100)
	} else {
		// 店铺分佣
		ratio = float64(commissionInfo.Ratio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
	}

	// 解析Skus
	var skuData []struct {
		SkuId     uint64      `json:"sku_id"`
		ThumbImg  string      `json:"thumb_img"`
		SalePrice json.Number `json:"sale_price"` // 使用json.Number处理数字，更灵活
		StockNum  json.Number `json:"stock_num"`  // 使用json.Number处理数字，更灵活
		SkuAttrs  []struct {
			AttrKey   string `json:"attr_key"`
			AttrValue string `json:"attr_value"`
		} `json:"sku_attrs"`
	}

	if err := json.Unmarshal([]byte(product.Skus), &skuData); err != nil {
		return err
	}

	// 转换解析后的数据到pb结构
	skus := make([]*pb.SkuInfo, 0, len(skuData))
	for _, s := range skuData {
		// 解析销售价格
		salePrice, err := s.SalePrice.Int64()
		if err != nil {
			l.Errorf("解析销售价格失败 %s: %v", s.SkuId, err)
			salePrice = 0
		}

		// 解析库存
		stockNum, err := s.StockNum.Int64()
		if err != nil {
			l.Errorf("解析库存失败 %s: %v", s.SkuId, err)
			stockNum = 0
		}

		// 计算返利金额
		// 1. 获取当前SKU的价格（从分转为元）
		skuPrice := float64(salePrice) / 100

		// 2. 计算返利金额: SKU价格 * 分佣比例
		earnAmount := skuPrice * ratio

		// 3. 保留两位小数
		earnAmount = math.Round(earnAmount*100) / 100

		sku := &pb.SkuInfo{
			SkuId:      strconv.FormatUint(s.SkuId, 10),
			ThumbImg:   s.ThumbImg,
			SalePrice:  salePrice,
			StockNum:   stockNum,
			EarnAmount: earnAmount,
		}

		// 转换SKU属性
		skuAttrs := make([]*pb.SkuAttr, 0, len(s.SkuAttrs))
		for _, attr := range s.SkuAttrs {
			skuAttrs = append(skuAttrs, &pb.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}
		sku.SkuAttrs = skuAttrs

		skus = append(skus, sku)
	}
	productInfo.Skus = skus

	return nil
}
