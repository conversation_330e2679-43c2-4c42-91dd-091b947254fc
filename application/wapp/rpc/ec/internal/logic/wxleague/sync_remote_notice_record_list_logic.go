package wxleaguelogic

import (
	"context"
	"fmt"
	"math/rand"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteNoticeRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteNoticeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteNoticeRecordListLogic {
	return &SyncRemoteNoticeRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程达人平台的直播预约列表
func (l *SyncRemoteNoticeRecordListLogic) SyncRemoteNoticeRecordList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 从temp_talents表查询需要同步的达人记录
	var talentsToSync []model.TempTalents
	if err := db.Find(&talentsToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_talents 失败: %v", err)
		return nil, fmt.Errorf("查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步直播预约信息的达人记录", len(talentsToSync))
	if len(talentsToSync) == 0 {
		l.Infof("没有需要同步的达人记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 设置并发数
	maxConcurrent := 5
	// 创建信号量控制并发
	semaphore := make(chan struct{}, maxConcurrent)
	// 创建等待组
	var wg sync.WaitGroup

	// 创建上下文，设置超时时间为3分钟
	ctx, cancel := context.WithTimeout(l.ctx, 3*time.Minute)
	defer cancel()

	// 遍历每个达人，异步获取其直播预约列表
	for i, talent := range talentsToSync {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			l.Infof("任务超时或被取消，已处理 %d/%d 个达人", i, len(talentsToSync))
			return &pb.EmptyRequest{}, nil
		default:
			// 继续执行
		}

		// 为每个达人创建一个副本，避免闭包问题
		talentCopy := talent
		index := i

		wg.Add(1)
		// 获取信号量
		semaphore <- struct{}{}

		// 异步处理每个达人
		go func() {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			l.Infof("正在处理第 %d/%d 个达人 (talent_appid: %s)",
				index+1, len(talentsToSync), talentCopy.TalentAppid)

			// 构建请求参数
			req := &wxmodels.GetLiveNoticeRecordListRequest{
				TalentAppid:      talentCopy.TalentAppid,
				MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
			}

			// 设置请求超时
			reqCtx, reqCancel := context.WithTimeout(ctx, 10*time.Second)
			defer reqCancel()

			// 调用微信API获取直播预约列表
			resp, err := wxClient.GetLiveNoticeRecordList(reqCtx, req, token)

			// 处理API调用失败或业务错误的情况
			if err != nil || !resp.IsSuccess() || len(resp.LiveNoticeRecordList) == 0 {
				if err != nil {
					l.Infof("获取达人 %s 的直播预约列表失败: %v", talentCopy.TalentAppid, err)
				} else if !resp.IsSuccess() {
					l.Errorf("获取达人 %s 的直播预约列表业务错误: errcode=%d, errmsg=%s",
						talentCopy.TalentAppid, resp.ErrCode, resp.ErrMsg)
				} else {
					l.Infof("达人 %s 没有直播预约记录", talentCopy.TalentAppid)
				}

				// 删除该达人的直播预约记录
				if err := db.Unscoped().Where("talent_appid = ? AND type = ?", talentCopy.TalentAppid, "notice").
					Delete(&model.TempLiveList{}).Error; err != nil {
					l.Errorf("删除达人 %s 的直播预约记录失败: %v", talentCopy.TalentAppid, err)
				} else {
					l.Infof("已删除达人 %s 的直播预约记录", talentCopy.TalentAppid)
				}
				return
			}

			l.Infof("达人 %s 有 %d 条直播预约记录", talentCopy.TalentAppid, len(resp.LiveNoticeRecordList))

			// 先删除该达人的所有旧记录
			if err := db.Unscoped().Where("talent_appid = ? AND type = ?", talentCopy.TalentAppid, "notice").
				Delete(&model.TempLiveList{}).Error; err != nil {
				l.Errorf("删除达人 %s 的旧直播预约记录失败: %v", talentCopy.TalentAppid, err)
				return
			}

			// 准备批量插入的记录
			var newRecords []model.TempLiveList
			for _, noticeRecord := range resp.LiveNoticeRecordList {
				randomValue := rand.Intn(9801) + 200
				newRecords = append(newRecords, model.TempLiveList{
					TalentAppid:  talentCopy.TalentAppid,
					NoticeID:     noticeRecord.NoticeId,
					Description:  noticeRecord.Description,
					StartTime:    int(noticeRecord.StartTime),
					Type:         "notice",
					InfoProgress: "completed",
					SharerTotal:  randomValue,
				})
			}

			// 批量插入新记录
			if err := db.CreateInBatches(newRecords, 100).Error; err != nil {
				l.Errorf("批量插入达人 %s 的新直播预约记录失败: %v", talentCopy.TalentAppid, err)
				return
			}

			l.Infof("成功插入达人 %s 的 %d 条直播预约信息", talentCopy.TalentAppid, len(newRecords))
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()
	l.Infof("所有达人的直播预约信息同步完成")

	return &pb.EmptyRequest{}, nil
}
