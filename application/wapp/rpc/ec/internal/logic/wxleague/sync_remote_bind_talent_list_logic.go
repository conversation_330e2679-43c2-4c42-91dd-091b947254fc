package wxleaguelogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteBindTalentListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteBindTalentListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteBindTalentListLogic {
	return &SyncRemoteBindTalentListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程达人平台列表
func (l *SyncRemoteBindTalentListLogic) SyncRemoteBindTalentList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 获取数据库中现有的所有 talent_appid
	var existingDbTalents []model.TempTalents
	if err := db.Select("talent_appid").Find(&existingDbTalents).Error; err != nil {
		l.Errorf("查询数据库 temp_talents 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	// 将数据库中的 talent_appid 存入 map，方便查找
	dbTalentAppids := make(map[string]struct{}, len(existingDbTalents))
	for _, talent := range existingDbTalents {
		dbTalentAppids[talent.TalentAppid] = struct{}{}
	}
	l.Infof("数据库中现有达人数量: %d", len(dbTalentAppids))

	// 记录所有API返回的talent_appid，用于最后删除不存在的记录
	allApiTalentAppids := make(map[string]struct{})

	// 分页循环获取所有达人
	nextKey := ""
	pageIndex := 1

	for {
		l.Infof("开始获取第 %d 页达人数据", pageIndex)

		// 构建请求参数
		req := &wxmodels.GetBindTalentListReq{
			PageSize: 10, // 保持原有参数值不变
			NextKey:  nextKey,
		}

		// 调用微信API
		resp, err := wxClient.GetBindTalentList(l.ctx, req, token)
		if err != nil {
			l.Errorf("获取合作达人平台列表失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, err.Error())
		}

		// 当前页的达人信息
		var currentPageTalents []wxmodels.TalentInfo
		for _, talent := range resp.TalentList {
			if talent != nil {
				currentPageTalents = append(currentPageTalents, *talent)
			}
		}
		l.Infof("第 %d 页获取到 %d 条达人记录", pageIndex, len(currentPageTalents))

		// 处理当前页数据 - 找出需要插入的记录
		talentsToInsert := make([]model.TempTalents, 0)
		for _, talent := range currentPageTalents {
			// 记录所有API返回的talent_appid
			allApiTalentAppids[talent.TalentAppid] = struct{}{}

			// 如果数据库中不存在此 appid，则准备插入
			if _, existsInDb := dbTalentAppids[talent.TalentAppid]; !existsInDb {
				talentsToInsert = append(talentsToInsert, model.TempTalents{
					TalentAppid:    talent.TalentAppid,
					TalentNickname: talent.TalentNickname,
					TalentHeadImg:  common.SchemaConvert(talent.TalentHeadImg),
				})
			}
		}

		// 批量插入新记录
		if len(talentsToInsert) > 0 {
			l.Infof("第 %d 页：准备插入 %d 条新达人记录", pageIndex, len(talentsToInsert))
			if err := db.CreateInBatches(talentsToInsert, 100).Error; err != nil {
				l.Errorf("批量插入新达人记录失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "插入新达人记录失败")
			}
			l.Infof("第 %d 页：成功插入 %d 条新达人记录", pageIndex, len(talentsToInsert))
		} else {
			l.Infof("第 %d 页：没有新的达人记录需要插入", pageIndex)
		}

		// 判断是否还有下一页
		if !resp.HasMore || resp.NextKey == "" {
			l.Infof("没有更多页面，分页获取完成")
			break
		}

		// 更新nextKey用于获取下一页
		nextKey = resp.NextKey
		pageIndex++

		time.Sleep(time.Second * 1)
	}

	// 所有页面处理完毕后，删除数据库中存在但API返回中不存在的记录
	talentsToDeleteAppids := make([]string, 0)
	for dbAppid := range dbTalentAppids {
		if _, existsInApi := allApiTalentAppids[dbAppid]; !existsInApi {
			talentsToDeleteAppids = append(talentsToDeleteAppids, dbAppid)
		}
	}

	// 批量删除过时记录
	if len(talentsToDeleteAppids) > 0 {
		l.Infof("准备删除 %d 条过时的达人记录", len(talentsToDeleteAppids))
		if err := db.Unscoped().Where("talent_appid IN ?", talentsToDeleteAppids).Delete(&model.TempTalents{}).Error; err != nil {
			l.Errorf("删除过时达人记录失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "删除过时达人记录失败")
		}
		l.Infof("成功删除 %d 条过时达人记录", len(talentsToDeleteAppids))
	} else {
		l.Infof("没有过时的达人记录需要删除")
	}

	l.Infof("达人列表同步完成，总共处理 %d 页数据", pageIndex)

	return &pb.EmptyRequest{}, nil
}
