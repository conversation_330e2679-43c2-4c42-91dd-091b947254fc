package wxleaguelogic

import (
	"context"
	"fmt"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type WithdrawPreviewLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewWithdrawPreviewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawPreviewLogic {
	return &WithdrawPreviewLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 提现预算计算
func (l *WithdrawPreviewLogic) WithdrawPreview(in *pb.WithdrawPreviewReq) (*pb.WithdrawPreviewResp, error) {
	// 参数验证
	if in.UserId == 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	if in.Amount <= 0 {
		return nil, xerr.NewErrMsg(l.ctx, "提现金额必须大于0")
	}

	// 金额转换为分（微信支付使用分为单位）
	amountFen := int64(in.Amount * 100)

	// 金额范围验证
	if amountFen < l.svcCtx.Config.WxPay.Transfer.MinAmount {
		minAmountYuan := float64(l.svcCtx.Config.WxPay.Transfer.MinAmount) / 100
		return nil, xerr.NewErrMsg(l.ctx, fmt.Sprintf("提现金额不能少于%.2f元", minAmountYuan))
	}

	// 查询用户信息
	var user model.User
	if err := l.svcCtx.MasterDB.DB.Select("id", "withdraw", "role_id").
		Where("id = ?", in.UserId).
		First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, xerr.NewErrMsg(l.ctx, "用户不存在")
		}
		logx.WithContext(l.ctx).Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 检查可提现余额
	if uint64(amountFen) > uint64(user.Withdraw) {
		availableAmount := float64(user.Withdraw) / 100
		return nil, xerr.NewErrMsg(l.ctx, fmt.Sprintf("可提现余额不足，当前可提现：%.2f元", availableAmount))
	}

	// 计算手续费和实际到账金额
	feeRatio := l.svcCtx.Config.WxPay.Transfer.FeeRatio
	fee := float64(amountFen) * feeRatio        // 手续费（分）
	actualAmountFen := float64(amountFen) - fee // 实际到账金额（分）

	// 转换为元
	amountYuan := in.Amount                   // 申请金额（元）
	feeYuan := fee / 100                      // 手续费（元）
	actualAmountYuan := actualAmountFen / 100 // 实际到账金额（元）

	// 判断用户是否需要审核
	needsReview := l.userNeedsReview(user.RoleId, amountFen)

	// 格式化手续费率文本
	feeRatioText := fmt.Sprintf("%.0f%%", feeRatio*100)

	logx.WithContext(l.ctx).Infof("提现预算计算: 用户ID=%d, 申请金额=%.2f元, 手续费=%.2f元(费率=%s), 实际到账=%.2f元, 需要审核=%t",
		in.UserId, amountYuan, feeYuan, feeRatioText, actualAmountYuan, needsReview)

	return &pb.WithdrawPreviewResp{
		Amount:       amountYuan,       // 申请提现金额（元）
		ActualAmount: actualAmountYuan, // 实际到账金额（元）
		Fee:          feeYuan,          // 手续费（元）
		FeeRatio:     feeRatio,         // 手续费率（0.1表示10%）
		FeeRatioText: feeRatioText,     // 手续费率显示文本（如"10%"）
		NeedsReview:  needsReview,      // 是否需要审核
	}, nil
}

// 判断用户是否需要审核（复用现有提现逻辑）
func (l *WithdrawPreviewLogic) userNeedsReview(roleId int8, amount int64) bool {
	needsReview := false
	// 代理商(3)和运营商(4)需要审核
	if roleId == config.RoleAgent || roleId == config.RoleOperator {
		logx.WithContext(l.ctx).Infof("用户为代理商或运营商，需要审核")
		needsReview = true
	}
	// 提现金额超过最大值也需要审核
	if amount > l.svcCtx.Config.WxPay.Transfer.MaxAmount {
		logx.WithContext(l.ctx).Infof("提现金额超过最大值，需要审核")
		needsReview = true
	}
	return needsReview
}
