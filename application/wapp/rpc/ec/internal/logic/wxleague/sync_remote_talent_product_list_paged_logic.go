package wxleaguelogic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"google.golang.org/grpc/metadata"
	"gorm.io/gorm"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteTalentProductListPagedLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteTalentProductListPagedLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteTalentProductListPagedLogic {
	return &SyncRemoteTalentProductListPagedLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteTalentProductListPaged 分页同步达人商品列表
func (l *SyncRemoteTalentProductListPagedLogic) SyncRemoteTalentProductListPaged(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 检查是否是消息队列调用
	var isQueueMessage bool
	var msgData map[string]interface{}

	// 从上下文中获取消息数据
	if md, ok := metadata.FromIncomingContext(l.ctx); ok {
		if values := md.Get("x-queue-message"); len(values) > 0 {
			isQueueMessage = true
			if err := json.Unmarshal([]byte(values[0]), &msgData); err != nil {
				l.Errorf("解析队列消息数据失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "解析队列消息数据失败")
			}
		}
	}

	if isQueueMessage {
		// 处理队列消息
		_, err := l.handleQueueMessage(msgData)
		if err != nil {
			l.Errorf("处理队列消息失败: %v", err)
			return nil, err
		}
		return &pb.EmptyRequest{}, nil
	} else {
		// 作为定时任务启动初始消息发送
		_, err := l.startInitialSync()
		if err != nil {
			l.Errorf("启动初始同步失败: %v", err)
			return nil, err
		}
		return &pb.EmptyRequest{}, nil
	}
}

// startInitialSync 启动初始同步
func (l *SyncRemoteTalentProductListPagedLogic) startInitialSync() (*pb.EmptyRequest, error) {
	l.Infof("开始启动达人商品分页同步任务")

	// 注释掉清空表的操作，改为在所有分页处理完成后删除过期商品
	// if err := l.clearTalentProductsTable(); err != nil {
	// 	l.Errorf("清空temp_talent_products表失败: %v", err)
	// 	return nil, err
	// }

	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		l.Errorf("队列管理器未初始化")
		return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "队列管理器未初始化")
	}

	// 需要获取的佣金类型列表
	//commissionTypes := []int{0, 1}
	commissionTypes := []int{1}

	// 为每个佣金类型发送初始同步消息
	for _, commissionType := range commissionTypes {
		// 发送初始消息到队列
		message := map[string]interface{}{
			"type":            "talent", // 添加消息类型标识
			"commission_type": commissionType,
			"next_key":        "",
			"page_index":      1,
			"is_last_page":    false, // 添加是否是最后一页的标志
		}

		// 序列化消息
		messageBytes, err := json.Marshal(message)
		if err != nil {
			l.Errorf("序列化消息失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化消息失败")
		}

		// 发送消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue(
			"ProductsPaging", // 队列名称
			messageBytes,     // 消息内容
		)
		if err != nil {
			l.Errorf("发送消息到队列失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送消息到队列失败")
		}

		l.Infof("已发送CommissionType=%d的初始同步消息到队列", commissionType)
	}

	return &pb.EmptyRequest{}, nil
}

// 添加清空表的方法
func (l *SyncRemoteTalentProductListPagedLogic) clearTalentProductsTable() error {
	// 开始事务
	return l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 使用硬删除清空表
		if err := tx.Exec("DELETE FROM temp_talent_products").Error; err != nil {
			tx.Rollback()
			l.Errorf("清空temp_talent_products表失败: %v", err)
			return err
		}

		// 重置自增ID为1
		if err := tx.Exec("ALTER TABLE temp_talent_products AUTO_INCREMENT = 1").Error; err != nil {
			tx.Rollback()
			l.Errorf("重置temp_talent_products表自增ID失败: %v", err)
			return err
		}
		return nil
	})
}

// handleQueueMessage 处理队列消息
// 处理当前页数据
func (l *SyncRemoteTalentProductListPagedLogic) handleQueueMessage(msgData map[string]interface{}) (*pb.EmptyRequest, error) {
	// 获取参数
	commissionType := 0
	if ctVal, ok := msgData["commission_type"].(float64); ok {
		commissionType = int(ctVal)
	}

	nextKey := ""
	if nkVal, ok := msgData["next_key"].(string); ok {
		nextKey = nkVal
	}

	pageIndex := 1
	if piVal, ok := msgData["page_index"].(float64); ok {
		pageIndex = int(piVal)
	}

	// 获取是否是最后一页的标志
	isLastPage := false
	if ilpVal, ok := msgData["is_last_page"].(bool); ok {
		isLastPage = ilpVal
	}

	l.Infof("处理CommissionType=%d, 第 %d 页的同步任务, nextKey=%s", commissionType, pageIndex, nextKey)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetCooperativeItemListRequest{
		CommissionType: commissionType,
		PageSize:       20, // 每页获取20条数据
		NextKey:        nextKey,
	}

	// 调用微信API获取商品列表
	// 修改调用微信API获取商品列表后的错误处理部分
	resp, err := wxClient.GetCooperativeItemList(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取合作商品列表失败 (CommissionType=%d): %v", commissionType, err)
		// 移除删除过时记录的逻辑
		return &pb.EmptyRequest{}, nil
	}

	// 当前页的商品信息
	currentPageProducts := resp.List
	l.Infof("CommissionType=%d, 第 %d 页获取到 %d 条商品记录", commissionType, pageIndex, len(currentPageProducts))

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 使用并发处理当前页数据
	var wg sync.WaitGroup
	errChan := make(chan error, len(currentPageProducts))

	// 控制并发数量，避免请求过多导致微信API限流
	concurrencyLimit := 5
	sem := make(chan struct{}, concurrencyLimit)

	// 使用互斥锁保护共享的apiProductMap
	var apiProductMapMutex sync.Mutex

	for _, product := range currentPageProducts {
		wg.Add(1)

		// 使用闭包捕获当前循环的product变量
		go func(p wxmodels.ProductInfoItem) {
			defer wg.Done()

			// 获取信号量，控制并发数
			sem <- struct{}{}
			defer func() { <-sem }()

			// 获取商品推广参数详情
			promotionReq := &wxmodels.GetPromotionDetailRequest{
				HeadSupplierItemLink: p.HeadSupplierItemLink,
			}
			promotionResp, err := wxClient.GetPromotionDetail(l.ctx, promotionReq, token)
			if err != nil {
				l.Errorf("获取商品推广参数详情失败: %v", err)
				errChan <- err
				return
			}

			l.Infof("成功获取商品 %s 的推广参数详情", p.ProductId)

			// 先尝试订阅商品，如果订阅失败则跳过该商品
			//_productId, err := strconv.Atoi(promotionResp.Item.ProductId)
			//if err != nil {
			//	return
			//}
			//if !l.trySubscribeProduct(uint64(int64(_productId)), promotionResp.Item.ShopAppid, 1, token, wxClient) {
			//	l.Infof("商品 %s_%s 订阅失败，跳过处理", _productId, p.ProductId)
			//	return
			//}

			// 处理推广详情数据
			productId := promotionResp.Item.ProductId
			shopAppid := promotionResp.Item.ShopAppid

			// 确保键格式为 "shopAppid_productId"
			key := fmt.Sprintf("%s_%s", shopAppid, productId)

			// 使用互斥锁保护对apiProductMap的访问
			apiProductMapMutex.Lock()
			apiProductMapMutex.Unlock()

			// 获取商品详情
			detailReq := &wxmodels.ProductDetailGetRequest{
				ProductId: int64(p.ProductId),
				ShopAppid: shopAppid,
			}
			detailResp, err := wxClient.GetProductDetail(l.ctx, detailReq, token)
			if err != nil {
				l.Errorf("获取商品 %s 详情失败: %v", key, err)
				errChan <- err
				return
			}

			// 将商品基本信息转换为JSON字符串
			headImgsJSON, _ := json.Marshal(detailResp.Item.ProductInfo.HeadImgs)
			descInfoJSON, _ := json.Marshal(detailResp.Item.ProductInfo.DescInfo)
			skusJSON, _ := json.Marshal(detailResp.Item.ProductInfo.Skus)
			commissionInfoJSON, _ := json.Marshal(promotionResp.Item.CommissionInfo)

			// 设置缓存过期时间为24小时
			expiresAt := time.Now().Add(24 * time.Hour)

			// 计算最低价格
			var minPrice int64 = -1
			for _, sku := range detailResp.Item.ProductInfo.Skus {
				if minPrice == -1 || sku.SalePrice < minPrice {
					minPrice = sku.SalePrice
				}
			}

			// 蛋爷需求 大于4900
			isWindow := 0
			if minPrice >= 4900 {
				isWindow = 1
			}

			// 获取分佣比例
			ratio := 0.0
			if promotionResp.Item.CommissionInfo.CommissionType == 1 {
				// 机构分佣
				ratio = float64(promotionResp.Item.CommissionInfo.ServiceRatio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
				// 未算个人比例，每个人级别都不一样，这里只为了实现排序
			} else {
				// 店铺分佣
				ratio = float64(promotionResp.Item.CommissionInfo.Ratio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
			}
			// 按最低价格计算佣金
			commissionAmount := int64(float64(minPrice) * ratio)

			// 处理分类信息
			cat1, cat2, cat3 := l.processCategoryInfo(detailResp.Item.ProductInfo.CatsV2)

			// 构建商品数据
			tempProduct := model.TempTalentProduct{
				ProductId:        int64(p.ProductId),
				ShopAppid:        shopAppid,
				Title:            detailResp.Item.ProductInfo.Title,
				SubTitle:         detailResp.Item.ProductInfo.SubTitle,
				HeadImgs:         string(headImgsJSON),
				DescInfo:         string(descInfoJSON),
				Skus:             string(skusJSON),
				CommissionInfo:   string(commissionInfoJSON),
				CommissionAmount: commissionAmount,
				MinPrice:         minPrice,
				PlanType:         int8(promotionResp.Item.CommissionInfo.PlanType),
				CommissionType:   int8(promotionResp.Item.CommissionInfo.CommissionType),
				OnSale:           1, // 默认为在售状态
				Show:             1, // 默认为展示状态
				Cat1:             cat1,
				Cat2:             cat2,
				Cat3:             cat3,
				ExpiresAt:        expiresAt,
				ProductStatus:    int8(detailResp.Item.ProductInfo.Status),
				IsWindow:         int8(isWindow),
			}

			// 查询商品是否已存在
			var existingProduct model.TempTalentProduct
			result := db.Where("shop_appid = ? AND product_id = ?", shopAppid, int64(p.ProductId)).First(&existingProduct)

			if result.Error != nil {
				if errors.Is(result.Error, gorm.ErrRecordNotFound) {
					// 商品不存在，创建新记录
					if err := db.Create(&tempProduct).Error; err != nil {
						l.Errorf("创建商品 %s_%d 失败: %v", tempProduct.ShopAppid, tempProduct.ProductId, err)
						errChan <- err
					} else {
						l.Infof("成功创建商品: %s_%d", tempProduct.ShopAppid, tempProduct.ProductId)
					}
				} else {
					// 查询出错
					l.Errorf("查询商品 %s_%d 失败: %v", shopAppid, int64(p.ProductId), result.Error)
					errChan <- result.Error
				}
			} else {
				// 商品已存在，更新记录（包括更新expiresAt字段）
				if err := db.Model(&existingProduct).Updates(tempProduct).Error; err != nil {
					l.Errorf("更新商品 %s_%d 失败: %v", tempProduct.ShopAppid, tempProduct.ProductId, err)
					errChan <- err
				} else {
					l.Infof("成功更新商品: %s_%d", tempProduct.ShopAppid, tempProduct.ProductId)
				}
			}

		}(product)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(errChan)

	// 处理错误
	if len(errChan) > 0 {
		l.Errorf("处理商品时有 %d 个错误发生", len(errChan))
	}

	// 判断是否还有下一页
	if resp.NextKey != "" && len(resp.List) > 0 {
		// 发送下一页的消息到队列
		nextMessage := map[string]interface{}{
			"type":            "talent", // 添加消息类型标识
			"commission_type": commissionType,
			"next_key":        resp.NextKey,
			"page_index":      pageIndex + 1,
			"is_last_page":    false,
		}

		// 序列化消息
		nextMessageBytes, err := json.Marshal(nextMessage)
		if err != nil {
			l.Errorf("序列化下一页消息失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化下一页消息失败")
		}

		// 发送下一页消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue(
			"ProductsPaging", // 队列名称
			nextMessageBytes, // 消息内容
		)
		if err != nil {
			l.Errorf("发送下一页消息到队列失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送下一页消息到队列失败")
		}

		l.Infof("CommissionType=%d: 已发送第 %d 页同步消息到队列", commissionType, pageIndex+1)
	} else {
		// 最后一页，发送清理消息
		lastMessage := map[string]interface{}{
			"type":            "talent",
			"commission_type": commissionType,
			"is_last_page":    true,
		}

		// 序列化消息
		lastMessageBytes, err := json.Marshal(lastMessage)
		if err != nil {
			l.Errorf("序列化最后一页消息失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化最后一页消息失败")
		}

		// 发送最后一页消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue(
			"ProductsPaging",
			lastMessageBytes,
		)
		if err != nil {
			l.Errorf("发送最后一页消息到队列失败: %v", err)
			return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送最后一页消息到队列失败")
		}

		l.Infof("CommissionType=%d: 没有更多页面，已发送清理消息", commissionType)
	}

	// 如果是最后一页，清理过期商品
	if isLastPage {
		l.deleteExpiredProducts()
	}

	return &pb.EmptyRequest{}, nil
}

// trySubscribeProduct 尝试订阅商品，返回是否订阅成功
func (l *SyncRemoteTalentProductListPagedLogic) trySubscribeProduct(productId uint64, shopAppid string, planType int, token string, wxClient *client.WechatClient) bool {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return false
	}

	// 检查是否已经订阅过该商品
	var existingSubscription model.TempProductSubscription
	result := db.Where("product_id = ? AND shop_appid = ? AND plan_type = ?",
		productId, shopAppid, planType).
		Order("created_at DESC").
		First(&existingSubscription)

	// 如果已经有订阅记录，无论成功失败，都认为已处理过
	if result.Error == nil {
		// 已经有订阅记录，直接返回true继续处理商品
		return true
	}

	// 构建订阅请求
	req := &wxmodels.SubscribeProductRequest{
		ProductId: productId,
	}

	// 调用微信API订阅商品
	err := wxClient.SubscribeProduct(context.Background(), req, token)
	if err != nil {
		// 订阅失败，记录日志
		l.Errorf("订阅商品失败: ProductId=%d, ShopAppid=%s, Error=%v", productId, shopAppid, err)

		// 创建订阅记录，但不关心status字段
		subscription := model.TempProductSubscription{
			ProductId: int64(productId),
			ShopAppid: shopAppid,
			PlanType:  planType,
		}

		// 保存订阅历史记录
		if dbErr := db.Create(&subscription).Error; dbErr != nil {
			l.Errorf("保存商品订阅历史记录失败: %v", dbErr)
		}

		// 订阅失败，返回false跳过该商品
		return false
	}

	// 订阅成功，记录日志
	l.Infof("成功订阅商品: ProductId=%d", productId)

	// 创建订阅记录，但不关心status字段
	subscription := model.TempProductSubscription{
		ProductId: int64(productId),
		ShopAppid: shopAppid,
		PlanType:  planType,
	}

	// 保存订阅历史记录
	if dbErr := db.Create(&subscription).Error; dbErr != nil {
		l.Errorf("保存商品订阅历史记录失败: %v", dbErr)
	}

	// 订阅成功，返回true继续处理商品
	return true
}

// deleteExpiredProducts 删除过期的商品记录
func (l *SyncRemoteTalentProductListPagedLogic) deleteExpiredProducts() {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return
	}

	// 获取当前时间
	now := time.Now()

	// 删除过期的商品记录
	result := db.Unscoped().Where("expires_at < ?", now).Delete(&model.TempTalentProduct{})
	if result.Error != nil {
		l.Errorf("删除过期商品记录失败: %v", result.Error)
		return
	}

	l.Infof("成功删除 %d 条过期商品记录", result.RowsAffected)
}

// processCategoryInfo 处理商品分类信息，返回最多3级分类ID
// 从catsV2中找到ShopProductsCategory表中最接近level=3的记录
// 最大满足返回level=1,2,3的结果
// 如果找到的最大level是2或1，则cat2或cat3可以返回0
func (l *SyncRemoteTalentProductListPagedLogic) processCategoryInfo(catsV2 []wxmodels.BaseCatInfo) (cat1, cat2, cat3 int) {
	if len(catsV2) == 0 {
		return 0, 0, 0
	}

	// 提取分类ID
	var catIDs []uint64
	for _, cat := range catsV2 {
		catIDs = append(catIDs, cat.CatId)
	}

	if len(catIDs) == 0 {
		return 0, 0, 0
	}

	// 查询分类信息，按level降序排列，优先获取level较高的分类
	var categories []*model.ShopProductsCategory
	if err := l.svcCtx.GetSlaveDB().Where("cat_id IN ?", catIDs).Order("level DESC").Find(&categories).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return 0, 0, 0
	}

	// 如果没有找到任何分类记录
	if len(categories) == 0 {
		return 0, 0, 0
	}

	// 按level分组存储分类
	levelMap := make(map[int8][]*model.ShopProductsCategory)
	maxLevel := int8(0)

	for _, category := range categories {
		levelMap[category.Level] = append(levelMap[category.Level], category)
		if category.Level > maxLevel {
			maxLevel = category.Level
		}
	}

	// 从最高level开始，找到对应的分类ID
	// 如果找到level=3的分类，需要通过FCatID向上查找level=2和level=1的分类
	if maxLevel == 3 && len(levelMap[3]) > 0 {
		level3Cat := levelMap[3][0]
		cat3 = int(level3Cat.CatID)

		// 查找level=2的父分类
		var level2Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 2", level3Cat.FCatID).First(&level2Cat).Error; err == nil {
			cat2 = int(level2Cat.CatID)

			// 查找level=1的父分类
			var level1Cat model.ShopProductsCategory
			if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
				cat1 = int(level1Cat.CatID)
			}
		}
	} else if maxLevel == 2 && len(levelMap[2]) > 0 {
		// 如果最高level是2，直接使用level=2的分类，并查找其父分类
		level2Cat := levelMap[2][0]
		cat2 = int(level2Cat.CatID)

		// 查找level=1的父分类
		var level1Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
			cat1 = int(level1Cat.CatID)
		}
	} else if maxLevel == 1 && len(levelMap[1]) > 0 {
		// 如果最高level是1，直接使用level=1的分类
		cat1 = int(levelMap[1][0].CatID)
	}

	return cat1, cat2, cat3
}
