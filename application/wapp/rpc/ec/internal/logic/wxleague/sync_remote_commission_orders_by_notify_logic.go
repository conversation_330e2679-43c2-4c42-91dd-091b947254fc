package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SyncRemoteCommissionOrdersByNotifyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteCommissionOrdersByNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteCommissionOrdersByNotifyLogic {
	return &SyncRemoteCommissionOrdersByNotifyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 通过通知同步佣金订单
func (l *SyncRemoteCommissionOrdersByNotifyLogic) SyncRemoteCommissionOrdersByNotify(in *pb.SyncRemoteCommissionOrdersByNotifyReq) (*pb.BoolRequest, error) {
	// 参数校验
	if in.OrderID == "" {
		l.Errorf("订单ID不能为空")
		return nil, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "订单ID不能为空")
	}

	if in.SkuID == "" {
		l.Errorf("商品SKU ID不能为空")
		return nil, xerr.NewErrCodeMsg(xerr.ParamsValidateError, "商品SKU ID不能为空")
	}

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 查询订单是否存在
	var existingOrder model.CommissionOrders
	result := db.Where("order_id = ? AND sku_id = ?", in.OrderID, in.SkuID).First(&existingOrder)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetCommissionOrderRequest{
		OrderId: in.OrderID,
		SkuId:   in.SkuID,
	}

	// 调用微信API获取佣金订单详情
	resp, err := wxClient.GetCommissionOrder(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取佣金订单详情失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "获取佣金订单详情失败")
	}

	// 将API返回的原始响应转为JSON字符串保存
	originRespJson, err := json.Marshal(resp)
	if err != nil {
		l.Errorf("序列化原始响应失败: %v", err)
		// 继续执行，不影响主流程
	}

	// 使用事务处理数据库操作和消息发送
	err = db.Transaction(func(tx *gorm.DB) error {
		var isNewOrder bool
		var orderData model.CommissionOrders

		// 如果订单不存在，则需要创建新订单
		if result.Error != nil || result.RowsAffected == 0 {
			l.Infof("订单不存在，开始创建新订单，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)
			isNewOrder = true

			// 创建新的佣金订单记录
			order := resp.CommissionOrder
			newOrder := model.CommissionOrders{
				OrderID:               order.OrderId,
				SkuID:                 order.SkuId,
				BuyerOpenID:           order.OrderDetail.BuyerInfo.Openid,
				BuyerUnionID:          order.OrderDetail.BuyerInfo.Unionid,
				CommissionStatus:      strconv.Itoa(order.Status),
				OrderStatus:           strconv.Itoa(order.OrderDetail.OrderInfo.Status),
				ShopAppid:             order.OrderDetail.ShopInfo.Appid,
				ProductID:             order.OrderDetail.ProductInfo.ProductId,
				ProductThumbImg:       order.OrderDetail.ProductInfo.ThumbImg,
				ProductTitle:          order.OrderDetail.ProductInfo.Title,
				ActualPayment:         int(order.OrderDetail.ProductInfo.ActualPayment),
				ServiceRatio:          int(order.OrderDetail.CommissionInfo.ServiceRatio),
				ServiceAmount:         int(order.OrderDetail.CommissionInfo.ServiceAmount),
				ProfitShardingSucTime: time.Unix(int64(order.OrderDetail.CommissionInfo.ProfitShardingSucTime), 0),
				PromotionChannel:      int(order.OrderDetail.CommissionInfo.PromotionChannel),
				FinderNickname:        "",
				FinderRatio:           0,
				FinderAmount:          0,
				OpenFinderID:          "",
				SharerAppid:           order.OrderDetail.CommissionInfo.SharerInfo.SharerAppid,
				SharerNickname:        order.OrderDetail.CommissionInfo.SharerInfo.Nickname,
				SharerRatio:           int(order.OrderDetail.CommissionInfo.SharerInfo.Ratio),
				SharerAmount:          int(order.OrderDetail.CommissionInfo.SharerInfo.Amount),
				OpenSharerID:          order.OrderDetail.CommissionInfo.SharerInfo.Opensharerid,
				TalentAppid:           order.OrderDetail.CommissionInfo.TalentInfo.TalentAppid,
				TalentNickname:        order.OrderDetail.CommissionInfo.TalentInfo.Nickname,
				TalentRatio:           int(order.OrderDetail.CommissionInfo.TalentInfo.Ratio),
				TalentAmount:          int(order.OrderDetail.CommissionInfo.TalentInfo.Amount),
				OpenTalentID:          order.OrderDetail.CommissionInfo.TalentInfo.Opentalentid,
				AgencyAppid:           order.OrderDetail.CommissionInfo.AgencyInfo.Appid,
				AgencyNickname:        order.OrderDetail.CommissionInfo.AgencyInfo.Nickname,
				OriginResp:            string(originRespJson),
				BalanceDealStatus:     "0",
				WithdrawDealStatus:    "0",
			}

			// 保存到数据库
			if err := tx.Create(&newOrder).Error; err != nil {
				l.Errorf("创建佣金订单失败: %v", err)
				return err
			}

			orderData = newOrder
			l.Infof("成功创建佣金订单，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)
		} else {
			// 订单已存在，更新订单状态和佣金状态
			l.Infof("订单已存在，开始更新订单状态，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)
			isNewOrder = false
			orderData = existingOrder

			// 更新订单状态和佣金状态
			orderDetail := resp.CommissionOrder
			updates := map[string]interface{}{
				"order_status":      strconv.Itoa(orderDetail.OrderDetail.OrderInfo.Status),
				"commission_status": strconv.Itoa(orderDetail.Status),
				"updated_at":        time.Now(),
				"origin_resp":       string(originRespJson),
			}

			// 如果有结算时间，也更新
			if orderDetail.OrderDetail.CommissionInfo.ProfitShardingSucTime > 0 {
				updates["profit_sharding_suc_time"] = time.Unix(int64(orderDetail.OrderDetail.CommissionInfo.ProfitShardingSucTime), 0)
			}

			// 更新数据库
			if err := tx.Model(&model.CommissionOrders{}).Where("order_id = ? AND sku_id = ?", in.OrderID, in.SkuID).Updates(updates).Error; err != nil {
				l.Errorf("更新佣金订单状态失败: %v", err)
				return err
			}

			// 更新内存中的订单数据
			orderData.OrderStatus = strconv.Itoa(orderDetail.OrderDetail.OrderInfo.Status)
			orderData.CommissionStatus = strconv.Itoa(orderDetail.Status)

			l.Infof("成功更新佣金订单状态，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)
		}

		// 在事务中发送RabbitMQ消息
		// 发送佣金订单消息
		if err := l.sendCommissionOrderMessage(orderData, isNewOrder); err != nil {
			l.Errorf("发送佣金订单消息失败: %v", err)
			return err // 返回错误会导致事务回滚
		}
		l.Infof("成功发送佣金订单消息到RabbitMQ，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)

		return nil
	})

	if err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "处理佣金订单失败")
	}

	// 发送福利任务消息
	if err := l.sendBenefitsTaskMessage(in.OrderID, in.SkuID); err != nil {
		l.Errorf("发送福利任务消息失败: %v", err)
		// 不影响主流程，继续执行
	} else {
		l.Infof("成功发送福利任务消息到RabbitMQ，OrderID: %s, SkuID: %s", in.OrderID, in.SkuID)
	}

	return &pb.BoolRequest{
		Success: true,
	}, nil
}

// sendCommissionOrderMessage 发送佣金订单消息到RabbitMQ
func (l *SyncRemoteCommissionOrdersByNotifyLogic) sendCommissionOrderMessage(orderData model.CommissionOrders, isNewOrder bool) error {
	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		return fmt.Errorf("队列管理器未初始化")
	}

	// 构建消息内容
	msgData := map[string]interface{}{
		"type":              "order_notification",
		"order_id":          orderData.OrderID,
		"sku_id":            orderData.SkuID,
		"is_new_order":      isNewOrder,
		"order_status":      orderData.OrderStatus,
		"commission_status": orderData.CommissionStatus,
		"product_id":        orderData.ProductID,
		"timestamp":         time.Now().Unix(),
	}

	// 序列化消息
	msgBytes, err := json.Marshal(msgData)
	if err != nil {
		return fmt.Errorf("序列化RabbitMQ消息失败: %v", err)
	}

	// 使用队列管理器发送消息
	err = l.svcCtx.QueueManager.PublishToQueue(
		"CommissionOrders", // 队列名称，需要在配置中注册
		msgBytes,
	)
	if err != nil {
		return fmt.Errorf("发送消息到RabbitMQ失败: %v", err)
	}

	return nil
}

// sendBenefitsTaskMessage 发送福利任务消息到RabbitMQ
func (l *SyncRemoteCommissionOrdersByNotifyLogic) sendBenefitsTaskMessage(orderID, skuID string) error {
	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		return fmt.Errorf("队列管理器未初始化")
	}

	// 简化消息体，只包含必要的订单ID和事件类型
	messageBody := map[string]interface{}{
		"type":     "handle_benefits_tasks",
		"event":    "head_supplier_commission_order_update",
		"order_id": orderID, // 直接在顶层包含order_id
		"created":  time.Now().Unix(),
	}

	// 序列化消息
	messageBytes, err := json.Marshal(messageBody)
	if err != nil {
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	// 发送消息到队列
	err = l.svcCtx.QueueManager.PublishToQueue("BenefitsTasks", messageBytes)
	if err != nil {
		return fmt.Errorf("发送消息到RabbitMQ失败: %v", err)
	}

	l.Infof("成功发送福利任务消息到RabbitMQ队列: orderID=%s", orderID)
	return nil
}
