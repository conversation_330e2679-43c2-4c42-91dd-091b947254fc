package wxleaguelogic

import (
	"context"
	"fmt"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLiveDetailByTalentAppidLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLiveDetailByTalentAppidLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLiveDetailByTalentAppidLogic {
	return &GetLiveDetailByTalentAppidLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 根据TalentAppid获取直播间详情
func (l *GetLiveDetailByTalentAppidLogic) GetLiveDetailByTalentAppid(in *pb.GetLiveDetailByTalentAppidReq) (*pb.GetLiveDetailByTalentAppidResp, error) {
	// 参数校验
	if in.TalentAppid == "" {
		l.Errorf("TalentAppid不能为空")
		return &pb.GetLiveDetailByTalentAppidResp{}, nil
	}

	if in.Id <= 0 {
		l.Errorf("列表不能为空")
		return &pb.GetLiveDetailByTalentAppidResp{}, nil
	}

	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return &pb.GetLiveDetailByTalentAppidResp{}, nil
	}

	// 初始化响应
	resp := &pb.GetLiveDetailByTalentAppidResp{
		TalentAppid: in.TalentAppid,
		GoodsList:   make([]*pb.LiveGoodsInfo, 0),
	}

	// 查询直播信息
	var liveInfo model.TempLiveList
	if err := db.Where("id = ?", in.Id).First(&liveInfo).Error; err != nil {
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("查询直播信息失败: %v", err))
	}

	// 根据直播分类获取不同的信息
	if liveInfo.Class == "shop" {
		// 如果是shop分类，从temp_bind_shops获取信息
		var shopInfo model.TempBindShops
		if err := db.Where("shop_appid = ?", in.TalentAppid).First(&shopInfo).Error; err != nil {
			l.Errorf("查询店铺信息失败: %v", err)
			return resp, nil
		}

		// 设置店铺信息
		resp.TalentNickname = shopInfo.Nickname
		resp.TalentHeadImg = shopInfo.HeadImgUrl
	} else {
		// 默认为talent分类，从temp_talents获取信息
		var talent model.TempTalents
		if err := db.Where("talent_appid = ?", in.TalentAppid).First(&talent).Error; err != nil {
			l.Errorf("查询达人信息失败: %v", err)
			return resp, nil
		}

		// 设置达人信息
		resp.TalentNickname = talent.TalentNickname
		resp.TalentHeadImg = talent.TalentHeadImg
	}

	// 设置直播描述
	resp.Description = liveInfo.Description
	resp.PromoterShareLink = liveInfo.PromoterShareLink
	resp.ExportId = liveInfo.ExportID
	resp.PredictMaxCommissionAmount = int32(liveInfo.PredictMaxCommissionAmount)
	resp.Type = liveInfo.Type
	resp.StartTime = int64(liveInfo.StartTime)
	resp.StartIsoTime = liveInfo.StartIsoTime
	resp.Class = liveInfo.Class
	resp.PromoterId = liveInfo.PromoterId

	// 查询商品列表
	var goodsList []model.TempLiveGoods
	if err := db.Where("talent_appid = ?", in.TalentAppid).Limit(10).Find(&goodsList).Error; err != nil {
		l.Errorf("查询商品列表失败: %v", err)
		return resp, nil
	}

	// 转换商品信息
	for _, goods := range goodsList {
		resp.GoodsList = append(resp.GoodsList, &pb.LiveGoodsInfo{
			ProductId:               goods.ProductID,
			ProductName:             goods.ProductName,
			ProductImgUrl:           goods.ProductImgUrl,
			ProductPrice:            float64(goods.ProductPrice) / 100.0,
			PredictCommissionAmount: float64(goods.PredictCommissionAmount) / 100.0,
		})
	}

	return resp, nil
}
