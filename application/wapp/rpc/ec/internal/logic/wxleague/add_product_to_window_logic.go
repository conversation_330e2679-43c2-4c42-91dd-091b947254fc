package wxleaguelogic

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	wxClient "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddProductToWindowLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddProductToWindowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddProductToWindowLogic {
	return &AddProductToWindowLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *AddProductToWindowLogic) AddProductToWindow(in *pb.AddProductToWindowReq) (*pb.AddProductToWindowResp, error) {
	l.Infof("添加商品到橱窗请求: %+v", in)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	if in.ProductId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 查询用户信息获取openid和openfinderid
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	if user.Openid == "" {
		l.Errorf("用户没有绑定微信账号: %v", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "您的账号未绑定微信，无法添加商品到橱窗")
	}

	if user.Openfinderid == "" {
		l.Errorf("用户未授权达人橱窗: %v", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "您尚未完成橱窗授权，请先进行授权")
	}

	// 查询商品信息
	var product model.TempTalentProduct
	if err := l.svcCtx.GetSlaveDB().Where("product_id = ?", in.ProductId).First(&product).Error; err != nil {
		l.Errorf("查询商品信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询商品信息失败")
	}

	if product.ShopAppid == "" {
		l.Errorf("商品没有关联店铺: %v", in.ProductId)
		return nil, xerr.NewErrMsg(l.ctx, "商品信息不完整，无法添加到橱窗")
	}

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxclient := wxClient.NewWechatClient()

	// 构建添加商品到橱窗请求
	addRequest := &wxmodels.AddProductToWindowRequest{
		OpenFinderId: user.Openfinderid,
		ProductId:    in.ProductId,
		// ProductLink:  productLink,
		PlanType: 1, // 计划类型，1-普通计划
	}

	// 如果用户有opentalentid，也添加上
	if user.Opentalentid != "" {
		addRequest.OpenTalentId = user.Opentalentid
	}

	// 调用微信API添加商品到橱窗
	addResp, err := wxclient.AddProductToWindow(l.ctx, addRequest, token)
	if err != nil {
		l.Errorf("调用微信API添加商品到橱窗失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "添加商品到橱窗失败")
	}

	// 判断响应状态
	if !addResp.IsSuccess() {
		l.Errorf("添加商品到橱窗失败: %s", addResp.ErrMsg)
		return nil, xerr.NewErrMsg(l.ctx, "添加商品到橱窗失败")
	}

	return &pb.AddProductToWindowResp{
		Success: true,
		Message: "添加商品到橱窗成功",
	}, nil
}
