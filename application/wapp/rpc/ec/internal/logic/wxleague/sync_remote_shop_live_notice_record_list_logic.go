package wxleaguelogic

import (
	"context"
	"gorm.io/gorm"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteShopLiveNoticeRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteShopLiveNoticeRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteShopLiveNoticeRecordListLogic {
	return &SyncRemoteShopLiveNoticeRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteShopLiveNoticeRecordList 同步店铺直播预约列表
func (l *SyncRemoteShopLiveNoticeRecordListLogic) SyncRemoteShopLiveNoticeRecordList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	l.Infof("【调试】开始获取微信访问令牌")
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("【调试】获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}
	l.Infof("【调试】成功获取微信访问令牌: %s", token[:10]+"...")

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("【调试】获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}
	l.Infof("【调试】成功获取数据库连接")

	// 从数据库获取推客信息，并预加载绑定的店铺信息
	l.Infof("【调试】开始查询推客信息并预加载店铺信息")

	// 检查temp_shop_promoters表中是否有数据
	var promoterCount int64
	if err := db.Model(&model.TempShopPromoters{}).Count(&promoterCount).Error; err != nil {
		l.Errorf("【调试】检查temp_shop_promoters表数据失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "检查数据失败")
	}
	l.Infof("【调试】temp_shop_promoters表中有 %d 条数据", promoterCount)
	if promoterCount == 0 {
		l.Errorf("【调试】temp_shop_promoters表中没有数据，请先同步推客数据")
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, "没有推客数据，请先同步推客数据")
	}

	// 获取所有绑定的店铺
	var shops []model.TempBindShops
	if err := db.Find(&shops).Error; err != nil {
		l.Errorf("【调试】获取绑定店铺失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "获取绑定店铺失败")
	}
	l.Infof("【调试】成功获取 %d 个绑定店铺", len(shops))

	// 使用WaitGroup来等待所有goroutine完成
	var wg sync.WaitGroup
	// 使用通道来限制并发数量
	semaphore := make(chan struct{}, 5) // 最多5个并发
	// 使用互斥锁保护共享变量
	var mu sync.Mutex
	var totalNoticeCount int
	var successCount int
	var failCount int

	// 遍历所有店铺，获取直播预约列表
	for _, shop := range shops {
		wg.Add(1)
		semaphore <- struct{}{} // 获取信号量

		go func(shop model.TempBindShops) {
			defer func() {
				<-semaphore // 释放信号量
				wg.Done()
			}()

			// 获取店铺的推客信息 - 使用预加载优化
			var promoters []model.TempShopPromoters
			if err := db.Where("shop_appid = ? AND promoter_type = 1", shop.ShopAppid).Find(&promoters).Error; err != nil {
				l.Errorf("获取店铺 %s 的推客信息失败: %v", shop.ShopAppid, err)
				mu.Lock()
				failCount++
				mu.Unlock()
				return
			}

			if len(promoters) == 0 {
				l.Infof("店铺 %s 没有关联的推客", shop.ShopAppid)
				mu.Lock()
				failCount++
				mu.Unlock()
				return
			}

			// 使用第一个推客信息获取直播预约列表
			promoter := promoters[0]
			l.Infof("开始获取店铺 %s 的直播预约列表，使用推客ID: %s", shop.ShopAppid, promoter.PromoterID)

			// 创建请求上下文
			reqCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			// 获取店铺直播预约列表
			req := &wxmodels.GetShopLiveNoticeRecordListRequest{
				ShopAppid:    shop.ShopAppid,
				PromoterId:   promoter.PromoterID,
				PromoterType: 1, // 固定使用1，表示视频号类型的推客
			}

			resp, err := wxClient.GetShopLiveNoticeRecordList(reqCtx, req, token)
			if err != nil {
				l.Errorf("获取店铺 %s 的直播预约列表失败: %v", shop.ShopAppid, err)
				mu.Lock()
				failCount++
				mu.Unlock()
				return
			}

			l.Infof("成功获取店铺 %s 的直播预约列表，共 %d 条记录", shop.ShopAppid, len(resp.LiveNoticeRecordList))

			// 处理直播预约列表
			mu.Lock()
			totalNoticeCount += len(resp.LiveNoticeRecordList)
			mu.Unlock()

			// 批量处理直播预约记录
			if len(resp.LiveNoticeRecordList) > 0 {
				// 收集所有记录的NoticeID
				var noticeIDs []string
				for _, notice := range resp.LiveNoticeRecordList {
					noticeIDs = append(noticeIDs, notice.NoticeId)
				}

				// 一次性查询所有已存在的记录
				var existingRecords []model.TempLiveList
				if err := db.Where("talent_appid = ? AND notice_id IN ?", shop.ShopAppid, noticeIDs).Find(&existingRecords).Error; err != nil {
					l.Errorf("查询店铺 %s 的已存在直播预约记录失败: %v", shop.ShopAppid, err)
				}

				// 构建已存在记录的映射，便于快速查找
				existingMap := make(map[string]model.TempLiveList)
				for _, record := range existingRecords {
					existingMap[record.NoticeID] = record
				}

				// 处理每条记录
				for _, notice := range resp.LiveNoticeRecordList {
					// 构建推广链接
					//promoterShareLink := ""
					//if notice.NoticeId != "" {
					//	// 这里可能需要调用其他API获取真正的推广链接
					//	// 暂时使用NoticeId作为占位符
					//	promoterShareLink = notice.NoticeId
					//}

					// 创建或更新直播记录
					liveRecord := model.TempLiveList{
						TalentAppid:    shop.ShopAppid,
						TalentNickname: shop.Nickname,
						TalentHeadImg:  shop.HeadImgUrl,
						NoticeID:       notice.NoticeId,
						Description:    notice.Description,
						StartTime:      int(notice.StartTime),
						StartIsoTime:   time.Unix(notice.StartTime, 0).Format(time.RFC3339),
						InfoProgress:   "completed",
						Type:           "notice", // 预约类型
						Class:          "shop",
					}

					// 使用事务保证原子性
					err := db.Transaction(func(tx *gorm.DB) error {
						// 检查记录是否已存在
						if existingRecord, exists := existingMap[notice.NoticeId]; exists {
							// 记录已存在，更新
							liveRecord.ID = existingRecord.ID
							liveRecord.CreatedAt = existingRecord.CreatedAt
							return tx.Model(&model.TempLiveList{}).Where("id = ?", existingRecord.ID).Updates(&liveRecord).Error
						} else {
							// 记录不存在，创建新记录
							return tx.Create(&liveRecord).Error
						}
					})

					if err != nil {
						l.Errorf("保存店铺 %s 的直播预约记录失败: %v", shop.ShopAppid, err)
						mu.Lock()
						failCount++
						mu.Unlock()
					} else {
						mu.Lock()
						successCount++
						mu.Unlock()
					}
				}
			}

			l.Infof("完成处理店铺 %s 的直播预约列表", shop.ShopAppid)
		}(shop)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(semaphore)

	l.Infof("同步店铺直播预约列表完成，总共处理 %d 个店铺，获取 %d 条直播预约记录，成功 %d 条，失败 %d 条",
		len(shops), totalNoticeCount, successCount, failCount)

	return &pb.EmptyRequest{}, nil
}

// 以下是完整的SyncRemoteShopLiveNoticeRecordList方法，不需要修改

// 可以移除或注释掉startInitialSync方法
/*
// 添加初始同步方法，用于队列处理
func (l *SyncRemoteShopLiveNoticeRecordListLogic) startInitialSync() (*pb.EmptyRequest, error) {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 获取所有绑定的店铺，并且只获取有PromoterType为1的推客的店铺
	var shops []model.TempBindShops
	if err := db.Joins("JOIN temp_shop_promoters ON temp_shop_promoters.shop_appid = temp_bind_shops.shop_appid").
		Where("temp_shop_promoters.promoter_type = 1").
		Distinct("temp_bind_shops.*").
		Find(&shops).Error; err != nil {
		l.Errorf("获取绑定店铺失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "获取绑定店铺失败")
	}
	l.Infof("成功获取 %d 个有视频号推客的绑定店铺", len(shops))

	// 遍历每个店铺，发送初始消息到队列
	for _, shop := range shops {
		// 构建消息内容
		msgData := map[string]interface{}{
			"type":       "shop_live_notice", // 添加消息类型标识
			"shop_appid": shop.ShopAppid,
			"timestamp":  time.Now().Unix(),
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msgData)
		if err != nil {
			l.Errorf("序列化消息失败: %v", err)
			continue
		}

		// 发送消息到队列
		queueName := "ProductsPaging" // 使用统一的队列名称
		err = l.svcCtx.QueueManager.PublishToQueue(queueName, msgBytes)
		if err != nil {
			l.Errorf("发送消息到队列[%s]失败: %v", queueName, err)
			continue
		}

		l.Infof("成功发送店铺 %s 的初始直播预约同步消息到队列[%s]", shop.ShopAppid, queueName)
	}

	return &pb.EmptyRequest{}, nil
}
*/
