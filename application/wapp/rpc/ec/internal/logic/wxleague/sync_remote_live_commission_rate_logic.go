package wxleaguelogic

import (
	"context"
	"encoding/json"
	"google.golang.org/grpc/metadata"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteLiveCommissionRateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteLiveCommissionRateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteLiveCommissionRateLogic {
	return &SyncRemoteLiveCommissionRateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程直播的达人分佣信息
func (l *SyncRemoteLiveCommissionRateLogic) SyncRemoteLiveCommissionRate(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 检查是否是消息队列调用
	var isQueueMessage bool
	var msgData map[string]interface{}

	// 从上下文中获取消息数据
	if md, ok := metadata.FromIncomingContext(l.ctx); ok {
		if values := md.Get("x-queue-message"); len(values) > 0 {
			isQueueMessage = true
			if err := json.Unmarshal([]byte(values[0]), &msgData); err != nil {
				l.Errorf("解析队列消息数据失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "解析队列消息数据失败")
			}
		}
	}

	if isQueueMessage {
		// 处理队列消息
		return l.handleQueueMessage(msgData)
	} else {
		// 作为定时任务启动初始消息发送
		return l.startInitialSync()
	}
}

// 处理队列消息
func (l *SyncRemoteLiveCommissionRateLogic) handleQueueMessage(msgData map[string]interface{}) (*pb.EmptyRequest, error) {
	// 获取消息参数
	talentAppidInterface, ok := msgData["talent_appid"]
	if !ok {
		l.Errorf("消息中缺少talent_appid参数")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "消息中缺少talent_appid参数")
	}

	talentAppid, ok := talentAppidInterface.(string)
	if !ok {
		l.Errorf("talent_appid参数类型错误")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "talent_appid参数类型错误")
	}

	recordIDFloat, ok := msgData["record_id"].(float64)
	if !ok {
		l.Errorf("record_id参数不存在或类型错误")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "record_id参数不存在或类型错误")
	}
	recordID := uint(recordIDFloat)

	nextKey, _ := msgData["next_key"].(string)

	pageCountFloat, ok := msgData["page_count"].(float64)
	if !ok {
		pageCountFloat = 0
	}
	pageCount := int(pageCountFloat)

	maxCommissionRateFloat, ok := msgData["max_commission_rate"].(float64)
	if !ok {
		maxCommissionRateFloat = 0
	}
	maxCommissionRate := int8(maxCommissionRateFloat)

	l.Infof("处理达人 %s 的直播商品分页请求: 页数=%d, nextKey=%s, 当前最高佣金率=%d%%",
		talentAppid, pageCount+1, nextKey, maxCommissionRate)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 设置请求超时
	reqCtx, reqCancel := context.WithTimeout(l.ctx, 30*time.Second)
	defer reqCancel()

	// 构建请求参数
	req := &wxmodels.GetLiveCommissionProductListRequest{
		TalentAppid: talentAppid,
		NextKey:     nextKey,
		PageSize:    10, // 每页10条记录
	}

	// 调用微信API获取直播带货商品列表
	resp, err := wxClient.GetLiveCommissionProductList(reqCtx, req, token)

	// 处理API调用失败的情况
	if err != nil {
		l.Errorf("获取达人 %s 的直播带货商品列表失败: %v", talentAppid, err)
		// 如果已经有数据，则更新数据库
		if maxCommissionRate > 0 {
			l.Infof("尽管API调用失败，但已有佣金率数据，更新最终佣金率: %d%%", maxCommissionRate)
			return nil, l.updateFinalCommissionRate(talentAppid, recordID, maxCommissionRate)
		}
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		l.Errorf("获取达人 %s 的直播带货商品列表业务错误: errcode=%d, errmsg=%s",
			talentAppid, resp.ErrCode, resp.ErrMsg)
		// 如果已经有数据，则更新数据库
		if maxCommissionRate > 0 {
			l.Infof("尽管API返回业务错误，但已有佣金率数据，更新最终佣金率: %d%%", maxCommissionRate)
			return nil, l.updateFinalCommissionRate(talentAppid, recordID, maxCommissionRate)
		}
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, resp.ErrMsg)
	}

	// 检查是否有商品记录
	if len(resp.ProductList) == 0 {
		l.Infof("达人 %s 没有直播带货商品记录", talentAppid)
		return nil, l.updateFinalCommissionRate(talentAppid, recordID, maxCommissionRate)
	}

	l.Infof("达人 %s 当前页有 %d 条直播带货商品记录", talentAppid, len(resp.ProductList))

	// 准备批量插入的商品记录
	var goodsToInsert []model.TempLiveGoods

	// 新增：创建一个map来跟踪API返回的商品ID
	currentPageProductIDs := make(map[uint64]struct{})

	// 遍历商品列表，计算最高返佣率
	for _, product := range resp.ProductList {
		// 记录原始数据
		l.Infof("达人 %s 商品ID: %d, 价格: %d分, 佣金: %d分",
			talentAppid, product.ProductId, product.ProductPrice, product.PredictCommissionAmount)

		// 记录当前页的商品ID
		currentPageProductIDs[uint64(product.ProductId)] = struct{}{}

		// 避免除以零错误
		if product.ProductPrice <= 0 {
			l.Infof("达人 %s 商品ID: %d 价格为0或负数，跳过计算", talentAppid, product.ProductId)
			continue
		}

		// 计算返佣率 (佣金/价格)*100，得到百分比
		// 使用float64保留小数点后的精度
		commissionRate := float64(product.PredictCommissionAmount) / float64(product.ProductPrice) * 100
		l.Infof("达人 %s 商品ID: %d 计算的原始佣金率: %.4f%%",
			talentAppid, product.ProductId, commissionRate)

		// 转换为int8前先检查是否大于0
		commissionRateInt8 := int8(commissionRate)
		l.Infof("达人 %s 商品ID: %d 转换为int8后的佣金率: %d%%",
			talentAppid, product.ProductId, commissionRateInt8)

		// 如果实际有佣金但计算结果为0，至少设为1%
		if product.PredictCommissionAmount > 0 && commissionRateInt8 == 0 {
			commissionRateInt8 = 1
			l.Infof("达人 %s 的商品ID: %d 实际佣金率为 %.4f%%, 设置为最小值 1%%",
				talentAppid, product.ProductId, commissionRate)
		}

		// 更新最高返佣率
		if commissionRateInt8 > maxCommissionRate {
			l.Infof("达人 %s 更新最高返佣率: %d%% -> %d%%, 商品ID: %d",
				talentAppid, maxCommissionRate, commissionRateInt8, product.ProductId)
			maxCommissionRate = commissionRateInt8
		}

		// 添加商品记录到批量插入列表前，先检查是否已存在
		var existingProduct model.TempLiveGoods
		result := db.Where("product_id = ? AND talent_appid = ?", product.ProductId, talentAppid).First(&existingProduct)

		// 构建商品数据
		tempProduct := model.TempLiveGoods{
			ProductID:               uint64(product.ProductId),
			ProductName:             product.ProductName,
			ProductImgUrl:           product.ProductImgUrl,
			ProductPrice:            int(product.ProductPrice),
			PredictCommissionAmount: int(product.PredictCommissionAmount),
			TalentAppid:             talentAppid,
		}

		if result.Error == nil {
			// 商品已存在，更新记录
			if err := db.Model(&model.TempLiveGoods{}).
				Where("product_id = ? AND talent_appid = ?", product.ProductId, talentAppid).
				Updates(tempProduct).Error; err != nil {
				l.Errorf("更新达人 %s 的商品记录失败: %v", talentAppid, err)
			} else {
				l.Infof("成功更新达人 %s 的商品记录: 商品ID=%d", talentAppid, product.ProductId)
			}
		} else {
			// 商品不存在，添加到批量插入列表
			goodsToInsert = append(goodsToInsert, tempProduct)
		}
	}

	// 批量插入商品记录
	if len(goodsToInsert) > 0 {
		if err := db.CreateInBatches(goodsToInsert, 100).Error; err != nil {
			l.Errorf("批量插入达人 %s 的商品记录失败: %v", talentAppid, err)
		} else {
			l.Infof("成功插入达人 %s 的 %d 条商品记录", talentAppid, len(goodsToInsert))
		}
	}

	// 增加页数计数
	pageCount++

	// 检查是否还有更多页，如果有则发送下一页请求
	if resp.HasMore {
		// 构建下一页消息
		nextMsgData := map[string]interface{}{
			"type":                "live", // 添加消息类型标识
			"talent_appid":        talentAppid,
			"record_id":           recordID,
			"next_key":            resp.NextKey,
			"max_commission_rate": int(maxCommissionRate),
			"page_count":          pageCount,
			"timestamp":           time.Now().Unix(),
		}

		// 序列化消息
		nextMsgBytes, err := json.Marshal(nextMsgData)
		if err != nil {
			l.Errorf("序列化下一页消息失败: %v", err)
			return nil, err
		}

		// 发送下一页消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", nextMsgBytes)
		if err != nil {
			l.Errorf("发送下一页消息到队列失败: %v", err)
			return nil, err
		}

		l.Infof("成功发送达人 %s 的第 %d 页请求到队列，nextKey=%s", talentAppid, pageCount+1, resp.NextKey)
	} else {
		// 没有更多数据，更新最终佣金率并清理过期商品
		l.Infof("达人 %s 没有更多商品数据，更新最终佣金率: %d%%", talentAppid, maxCommissionRate)

		// 更新最终佣金率
		if err := l.updateFinalCommissionRate(talentAppid, recordID, maxCommissionRate); err != nil {
			l.Errorf("更新最终佣金率失败: %v", err)
			return nil, err
		}

		// 清理过期商品
		if err := l.cleanupExpiredProducts(talentAppid); err != nil {
			l.Errorf("清理过期商品失败: %v", err)
			// 不返回错误，继续执行
		}

		return &pb.EmptyRequest{}, nil
	}

	return &pb.EmptyRequest{}, nil
}

// 清理过期商品
func (l *SyncRemoteLiveCommissionRateLogic) cleanupExpiredProducts(talentAppid string) error {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 计算24小时前的时间点
	expireTime := time.Now().Add(-24 * time.Hour)

	// 删除超过24小时未更新的商品
	result := db.Unscoped().Where("created_at < ?", expireTime).
		Delete(&model.TempLiveGoods{})

	if result.Error != nil {
		l.Errorf("删除达人 %s 的过期商品失败: %v", talentAppid, result.Error)
		return result.Error
	}

	l.Infof("成功删除达人 %s 的过期商品，影响行数: %d", talentAppid, result.RowsAffected)
	return nil
}

// 更新最终佣金率到数据库
func (l *SyncRemoteLiveCommissionRateLogic) updateFinalCommissionRate(talentAppid string, recordID uint, maxCommissionRate int8) error {
	// 只有在有有效佣金率时才更新数据库
	if maxCommissionRate <= 0 {
		l.Errorf("达人 %s 没有有效的佣金率，跳过数据库更新", talentAppid)
		return nil
	}

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 更新数据库记录
	updateData := map[string]interface{}{
		"predict_max_commission_amount": maxCommissionRate,
		"info_progress":                 "completed",
		"updated_at":                    time.Now(),
	}

	l.Infof("准备更新达人 %s 的佣金信息到数据库: %+v", talentAppid, updateData)

	// 执行更新
	result := db.Model(&model.TempLiveList{}).
		Where("id = ?", recordID).
		Updates(updateData)

	if result.Error != nil {
		l.Errorf("更新达人 %s 的分佣信息失败: %v", talentAppid, result.Error)
		return result.Error
	}

	// 检查更新结果
	l.Infof("成功更新达人 %s 的最高返佣率为 %d%%, 影响行数: %d",
		talentAppid, maxCommissionRate, result.RowsAffected)

	// 如果影响行数为0，尝试查询当前记录状态
	if result.RowsAffected == 0 {
		var currentRecord model.TempLiveList
		findResult := db.Where("id = ?", recordID).First(&currentRecord)

		if findResult.Error != nil {
			l.Errorf("查询达人 %s 的当前记录失败: %v", talentAppid, findResult.Error)
		} else {
			l.Infof("达人 %s 的当前记录状态: predict_max_commission_amount=%d, info_progress=%s",
				talentAppid, currentRecord.PredictMaxCommissionAmount, currentRecord.InfoProgress)
		}
	}

	return nil
}

// 启动初始同步
func (l *SyncRemoteLiveCommissionRateLogic) startInitialSync() (*pb.EmptyRequest, error) {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 从temp_live_list表查询需要同步的记录
	var recordsToSync []model.TempLiveList
	if err := db.Where("`type`=?", "live").Find(&recordsToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_live_list 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步直播商品分佣信息的记录", len(recordsToSync))
	if len(recordsToSync) == 0 {
		l.Infof("没有需要同步的记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		l.Errorf("队列管理器未初始化")
		return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "队列管理器未初始化")
	}

	// 遍历每条记录，发送初始消息到队列
	for i, record := range recordsToSync {
		// 为每条记录创建一个副本，避免闭包问题
		recordCopy := record

		// 构建消息内容
		msgData := map[string]interface{}{
			"type":                "live", // 添加消息类型标识
			"talent_appid":        recordCopy.TalentAppid,
			"record_id":           recordCopy.ID,
			"next_key":            "", // 初始为空
			"max_commission_rate": 0,  // 初始最高佣金率为0
			"page_count":          0,  // 初始页数为0
			"timestamp":           time.Now().Unix(),
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msgData)
		if err != nil {
			l.Errorf("序列化消息失败: %v", err)
			continue
		}

		// 发送消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", msgBytes)
		if err != nil {
			l.Errorf("发送消息到队列失败: %v", err)
			continue
		}

		l.Infof("成功发送第 %d/%d 个达人 (talent_appid: %s) 的初始同步请求到队列",
			i+1, len(recordsToSync), recordCopy.TalentAppid)
	}

	l.Infof("所有达人的初始同步请求已发送到队列，任务完成")
	return &pb.EmptyRequest{}, nil
}
