package wxleaguelogic

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetFeedListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetFeedListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFeedListLogic {
	return &GetFeedListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取达人平台推广的短视频信息
func (l *GetFeedListLogic) GetFeedList(in *pb.GetFeedListReq) (*pb.GetFeedListResp, error) {
	// 从用户表中获取推客的appid
	var sharerAppid string
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Select("sharer_appid").
		Where("id = ?", in.UserId).
		Scan(&sharerAppid).Error; err != nil {
		l.Errorf("查询用户[%d]的推客信息失败: %v", in.UserId, err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户推客信息失败")
	}
	// 获取用户级别
	var userLevel int
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Select("level").
		Where("id = ?", in.UserId).
		Scan(&userLevel).Error; err != nil {
		l.Errorf("查询用户级别失败: %v", err)
	}

	// 获取用户级别对应的佣金比例
	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.MasterDB.Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", userLevel).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		// 使用默认值
		commissionConfig.CommissionRate = 0
	}

	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrMsg(l.ctx, "数据库连接失败")
	}

	// 设置分页参数
	pageSize := int(in.PageReq.PageSize)
	if pageSize <= 0 || pageSize > 50 {
		pageSize = 10 // 默认页面大小
	}

	pageNo := int(in.PageReq.PageNo)
	if pageNo <= 0 {
		pageNo = 1 // 默认第一页
	}

	// 计算偏移量
	offset := (pageNo - 1) * pageSize

	// 存储所有短视频信息
	var allFeeds []*pb.FeedInfo

	// 1. 查询达人短视频列表 (TempVideoList)
	var talentVideoList []model.TempVideoList
	talentQuery := db.Model(&model.TempVideoList{}).Order("id DESC")

	// 如果有关键字，添加产品名称搜索条件
	if in.PageReq.Keyword != "" {
		talentQuery = talentQuery.Where("product_name LIKE ?", "%"+in.PageReq.Keyword+"%")
	}

	if err := talentQuery.Find(&talentVideoList).Error; err != nil {
		l.Errorf("查询达人短视频列表失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询达人短视频数据失败")
	}

	// 将达人短视频数据转换为pb.FeedInfo
	for _, video := range talentVideoList {
		// 计算实际佣金金额（基于用户级别的佣金比例）
		var earnAmount float64
		if commissionConfig.CommissionRate > 0 {
			// 使用配置的佣金比例计算实际佣金
			earnAmount = float64(video.PredictCommissionAmount) * float64(commissionConfig.CommissionRate) / 100.0
		} else {
			// 使用预估佣金
			earnAmount = float64(video.PredictCommissionAmount)
		}
		// 转换为元（数据库存储的是分）
		earnAmount = earnAmount / 100
		earnAmountYuan := common.RoundYuan(earnAmount, 2)

		feedInfo := &pb.FeedInfo{
			ExportId:                video.ExportID,
			TalentAppid:             video.TalentAppid,
			PredictCommissionAmount: video.PredictCommissionAmount,
			ProductInfo: &pb.FeedProductInfo{
				ProductId:        video.ProductID,
				ProductName:      video.ProductName,
				ProductImgUrl:    video.ProductImgUrl,
				ProductMiniPrice: video.ProductMiniPrice,
			},
			FeedToken:         video.FeedToken,
			PromoterShareLink: video.PromoterShareLink,
			EarnAmount:        earnAmountYuan,
		}

		allFeeds = append(allFeeds, feedInfo)
	}

	// 2. 查询小店短视频列表 (TempShopVideoList)
	var shopVideoList []model.TempShopVideoList
	shopQuery := db.Model(&model.TempShopVideoList{}).Order("id DESC")

	// 如果有关键字，添加产品名称搜索条件
	if in.PageReq.Keyword != "" {
		shopQuery = shopQuery.Where("product_name LIKE ?", "%"+in.PageReq.Keyword+"%")
	}

	if err := shopQuery.Find(&shopVideoList).Error; err != nil {
		l.Errorf("查询小店短视频列表失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询小店短视频数据失败")
	}

	// 将小店短视频数据转换为pb.FeedInfo
	for _, video := range shopVideoList {
		// 计算实际佣金金额（基于用户级别的佣金比例）
		var earnAmount float64
		if commissionConfig.CommissionRate > 0 {
			// 使用配置的佣金比例计算实际佣金
			earnAmount = float64(video.PredictCommissionAmount) * float64(commissionConfig.CommissionRate) / 100.0
		} else {
			// 使用预估佣金
			earnAmount = float64(video.PredictCommissionAmount)
		}
		// 转换为元（数据库存储的是分）
		earnAmount = earnAmount / 100
		earnAmountYuan := common.RoundYuan(earnAmount, 2)

		feedInfo := &pb.FeedInfo{
			ExportId:                video.ExportID,
			TalentAppid:             "", // 小店短视频没有TalentAppid，设为空字符串
			PredictCommissionAmount: video.PredictCommissionAmount,
			ProductInfo: &pb.FeedProductInfo{
				ProductId:        video.ProductID,
				ProductName:      video.ProductName,
				ProductImgUrl:    video.ProductImgUrl,
				ProductMiniPrice: video.ProductMiniPrice,
			},
			FeedToken:         video.FeedToken,
			PromoterShareLink: video.PromoterShareLink,
			EarnAmount:        earnAmountYuan,
		}

		allFeeds = append(allFeeds, feedInfo)
	}

	l.Infof("查询到达人短视频 %d 条，小店短视频 %d 条，总计 %d 条",
		len(talentVideoList), len(shopVideoList), len(allFeeds))

	// 如果有关键字搜索，记录搜索信息
	if in.PageReq.Keyword != "" {
		l.Infof("使用关键字[%s]搜索，命中达人短视频 %d 条，小店短视频 %d 条",
			in.PageReq.Keyword, len(talentVideoList), len(shopVideoList))
	}

	// 计算总数
	totalCount := int64(len(allFeeds))

	// 应用分页
	var feedList []*pb.FeedInfo
	if offset < len(allFeeds) {
		endIndex := offset + pageSize
		if endIndex > len(allFeeds) {
			endIndex = len(allFeeds)
		}
		feedList = allFeeds[offset:endIndex]
	}

	return &pb.GetFeedListResp{
		FeedList: feedList,
		Total:    totalCount,
		PageNo:   int64(pageNo),
		PageSize: int64(pageSize),
	}, nil
}
