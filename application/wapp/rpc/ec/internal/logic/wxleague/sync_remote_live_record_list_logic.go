package wxleaguelogic

import (
	"context"
	"math/rand"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteLiveRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteLiveRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteLiveRecordListLogic {
	return &SyncRemoteLiveRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程达人平台的直播列表
func (l *SyncRemoteLiveRecordListLogic) SyncRemoteLiveRecordList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 从temp_talents表查询需要同步的达人记录
	var talentsToSync []model.TempTalents
	if err := db.Limit(50).Find(&talentsToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_talents 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步直播信息的达人记录", len(talentsToSync))
	if len(talentsToSync) == 0 {
		l.Infof("没有需要同步的达人记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 设置并发数
	maxConcurrent := 5
	// 创建信号量控制并发
	semaphore := make(chan struct{}, maxConcurrent)
	// 创建等待组
	var wg sync.WaitGroup

	// 创建上下文，设置超时时间为3分钟
	ctx, cancel := context.WithTimeout(l.ctx, 3*time.Minute)
	defer cancel()

	// 遍历每个达人，异步获取其直播列表
	for i, talent := range talentsToSync {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			l.Infof("任务超时或被取消，已处理 %d/%d 个达人", i, len(talentsToSync))
			return &pb.EmptyRequest{}, nil
		default:
			// 继续执行
		}

		// 为每个达人创建一个副本，避免闭包问题
		talentCopy := talent
		index := i

		wg.Add(1)
		// 获取信号量
		semaphore <- struct{}{}

		// 异步处理每个达人
		go func() {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			l.Infof("正在处理第 %d/%d 个达人 (talent_appid: %s)",
				index+1, len(talentsToSync), talentCopy.TalentAppid)

			// 先查询该达人是否已有直播记录
			var existingRecords []model.TempLiveList
			if err := db.Where("talent_appid = ? AND type = ?", talentCopy.TalentAppid, "live").
				Find(&existingRecords).Error; err != nil {
				l.Errorf("查询达人 %s 的现有直播记录失败: %v", talentCopy.TalentAppid, err)
				return
			}

			// 构建请求参数
			req := &wxmodels.GetLiveRecordListRequest{
				TalentAppid:      talentCopy.TalentAppid,
				MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
			}

			// 设置请求超时
			reqCtx, reqCancel := context.WithTimeout(ctx, 10*time.Second)
			defer reqCancel()

			// 调用微信API获取直播列表
			resp, err := wxClient.GetLiveRecordList(reqCtx, req, token)

			// 处理API调用失败或无数据的情况
			if err != nil || !resp.IsSuccess() || len(resp.LiveRecordList) == 0 {
				if err != nil {
					l.Errorf("获取达人 %s 的直播列表失败: %v", talentCopy.TalentAppid, err)
				} else if !resp.IsSuccess() {
					l.Errorf("获取达人 %s 的直播列表业务错误: errcode=%d, errmsg=%s",
						talentCopy.TalentAppid, resp.ErrCode, resp.ErrMsg)
				} else {
					l.Infof("达人 %s 没有直播记录", talentCopy.TalentAppid)
				}

				// 只有在确认没有直播记录时才删除
				if err == nil && resp.IsSuccess() && len(resp.LiveRecordList) == 0 {
					// 删除该达人的直播记录
					if err := db.Unscoped().Where("talent_appid = ? AND type = ?", talentCopy.TalentAppid, "live").
						Delete(&model.TempLiveList{}).Error; err != nil {
						l.Errorf("删除达人 %s 的直播记录失败: %v", talentCopy.TalentAppid, err)
					} else {
						l.Infof("已删除达人 %s 的直播记录", talentCopy.TalentAppid)
					}
				}
				return
			}

			l.Infof("达人 %s 有 %d 条直播记录", talentCopy.TalentAppid, len(resp.LiveRecordList))

			// 获取第一条直播记录（通常是最新的）
			liveRecord := resp.LiveRecordList[0]

			// 检查是否需要更新记录
			needUpdate := true
			var existingRecord model.TempLiveList
			var existingRecordFound bool

			if len(existingRecords) > 0 {
				// 如果已有记录且ExportID相同，则不需要更新
				for _, record := range existingRecords {
					if record.ExportID == liveRecord.ExportId {
						needUpdate = false
						l.Infof("达人 %s 的直播记录已存在，无需更新", talentCopy.TalentAppid)
						break
					}
				}

				// 如果需要更新，且存在记录，则使用第一条记录进行更新
				if needUpdate && len(existingRecords) > 0 {
					existingRecord = existingRecords[0]
					existingRecordFound = true
					l.Infof("达人 %s 的直播记录需要更新，将使用现有记录ID: %d", talentCopy.TalentAppid, existingRecord.ID)
				}
			}

			if needUpdate {
				// 使用固定的随机种子，确保同一达人每次生成的随机值相同
				r := rand.New(rand.NewSource(int64(talentCopy.ID)))
				randomValue := r.Intn(9801) + 200

				if existingRecordFound {
					// 更新现有记录而不是删除后重新创建
					updateData := map[string]interface{}{
						"export_id":           liveRecord.ExportId,
						"description":         liveRecord.Description,
						"promoter_share_link": liveRecord.PromoterShareLink,
						"info_progress":       "pending",
						"updated_at":          time.Now(),
					}

					// 只有在首次创建时才设置随机分享数
					if existingRecord.SharerTotal == 0 {
						updateData["sharer_total"] = randomValue
					}

					if err := db.Model(&model.TempLiveList{}).Where("id = ?", existingRecord.ID).
						Updates(updateData).Error; err != nil {
						l.Errorf("更新达人 %s 的直播记录失败: %v", talentCopy.TalentAppid, err)
						return
					}

					l.Infof("成功更新达人 %s 的直播信息，记录ID: %d", talentCopy.TalentAppid, existingRecord.ID)
				} else {
					// 没有现有记录，创建新记录
					newRecord := &model.TempLiveList{
						TalentAppid:       talentCopy.TalentAppid,
						ExportID:          liveRecord.ExportId,
						Description:       liveRecord.Description,
						PromoterShareLink: liveRecord.PromoterShareLink,
						Type:              "live",
						InfoProgress:      "pending",
						Class:             "talent",
						SharerTotal:       randomValue,
					}

					if err := db.Create(newRecord).Error; err != nil {
						l.Errorf("插入达人 %s 的新直播记录失败: %v", talentCopy.TalentAppid, err)
						return
					}

					l.Infof("成功创建达人 %s 的直播信息，新记录ID: %d", talentCopy.TalentAppid, newRecord.ID)
				}
			}
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	l.Infof("直播列表同步完成")
	return &pb.EmptyRequest{}, nil
}
