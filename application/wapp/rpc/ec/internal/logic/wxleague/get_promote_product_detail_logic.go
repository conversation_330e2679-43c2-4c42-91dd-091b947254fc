package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPromoteProductDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetPromoteProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPromoteProductDetailLogic {
	return &GetPromoteProductDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取可推广的商品详情
func (l *GetPromoteProductDetailLogic) GetPromoteProductDetail(in *pb.GetPromoteProductDetailReq) (*pb.GetPromoteProductDetailResp, error) {
	// 从用户表中获取推客的appid
	var sharerAppid string
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Select("sharer_appid").
		Where("id = ?", in.UserId).
		Scan(&sharerAppid).Error; err != nil {
		l.Errorf("查询用户[%d]的推客信息失败: %v", in.UserId, err)
		return nil, fmt.Errorf("查询用户推客信息失败")
	}

	if sharerAppid == "" {
		l.Errorf("用户[%v]未注册为推客", in.UserId)
	}

	// 查询商品详情
	var product model.TempTalentProduct
	if err := l.svcCtx.GetSlaveDB().Where("product_id = ? AND shop_appid = ?", in.ProductId, in.ShopAppid).First(&product).Error; err != nil {
		l.Errorf("查询商品详情失败: %v", err)
		return nil, err
	}

	// 解析 HeadImgs JSON 字符串
	var headImgs []string
	if err := json.Unmarshal([]byte(product.HeadImgs), &headImgs); err != nil {
		l.Errorf("解析商品主图失败: %v", err)
		headImgs = []string{} // 使用空数组作为默认值
	}

	// 解析 DescInfo JSON 字符串
	var descInfo struct {
		Imgs []string `json:"imgs"`
		Desc string   `json:"desc"`
	}
	if err := json.Unmarshal([]byte(product.DescInfo), &descInfo); err != nil {
		l.Errorf("解析商品描述信息失败: %v", err)
		// 使用默认值
		descInfo.Imgs = []string{}
		descInfo.Desc = ""
	}

	// 解析 Skus JSON 字符串
	var skusData []struct {
		SkuId     uint64 `json:"sku_id"`
		ThumbImg  string `json:"thumb_img"`
		SalePrice int64  `json:"sale_price"`
		StockNum  int64  `json:"stock_num"`
		SkuAttrs  []struct {
			AttrKey   string `json:"attr_key"`
			AttrValue string `json:"attr_value"`
		} `json:"sku_attrs"`
	}
	if err := json.Unmarshal([]byte(product.Skus), &skusData); err != nil {
		l.Errorf("解析商品SKU信息失败: %v", err)
		skusData = []struct {
			SkuId     uint64 `json:"sku_id"`
			ThumbImg  string `json:"thumb_img"`
			SalePrice int64  `json:"sale_price"`
			StockNum  int64  `json:"stock_num"`
			SkuAttrs  []struct {
				AttrKey   string `json:"attr_key"`
				AttrValue string `json:"attr_value"`
			} `json:"sku_attrs"`
		}{} // 使用空数组作为默认值
	}

	// 解析 CommissionInfo JSON 字符串
	var commissionInfo struct {
		ServiceRatio int64 `json:"service_ratio"` // 服务费率
	}
	if err := json.Unmarshal([]byte(product.CommissionInfo), &commissionInfo); err != nil {
		l.Errorf("解析佣金信息失败: %v", err)
		commissionInfo.ServiceRatio = 0 // 使用默认值
	}

	// 构建商品详细信息
	productDetailInfo := &pb.ProductDetailInfo{
		Title:    product.Title,
		SubTitle: product.SubTitle,
		HeadImgs: headImgs,
		DescInfo: &pb.ProductDescInfo{
			Imgs: descInfo.Imgs,
			Desc: descInfo.Desc,
		},
		Skus: make([]*pb.SkuInfo, len(skusData)),
	}

	// 获取用户级别
	var userLevel int
	if err := l.svcCtx.MasterDB.Model(&model.User{}).
		Select("level").
		Where("id = ?", in.UserId).
		Scan(&userLevel).Error; err != nil {
		l.Errorf("查询用户级别失败: %v", err)
		userLevel = 1 // 默认为1级
	}

	// 获取用户级别对应的佣金比例
	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.MasterDB.Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", userLevel).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		// 使用默认值
		commissionConfig.CommissionRate = 0
	}

	// 转换 Skus 数据
	for i, sku := range skusData {
		var skuAttrs []*pb.SkuAttr
		for _, attr := range sku.SkuAttrs {
			skuAttrs = append(skuAttrs, &pb.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}

		// 计算当前SKU的返利金额
		// 1. 获取机构分佣比例
		serviceRatio := float64(commissionInfo.ServiceRatio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】

		// 2. 获取当前SKU的价格（从分转为元）
		skuPrice := float64(sku.SalePrice) / 100

		// 3. 计算返利金额: SKU价格 * 分佣比例 * 用户佣金比例
		earnAmount := skuPrice * serviceRatio * (commissionConfig.CommissionRate / 100)

		// 4. 保留两位小数
		earnAmount = math.Round(earnAmount*100) / 100

		productDetailInfo.Skus[i] = &pb.SkuInfo{
			SkuId:      fmt.Sprintf("%d", sku.SkuId),
			ThumbImg:   sku.ThumbImg,
			SalePrice:  sku.SalePrice,
			StockNum:   sku.StockNum,
			SkuAttrs:   skuAttrs,
			EarnAmount: earnAmount,
		}
	}

	// 获取推广链接
	var productPromotionLink string
	if sharerAppid != "" {
		// 获取微信访问令牌
		token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
			l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
		if err != nil {
			l.Errorf("获取微信访问令牌失败: %v", err)
			return nil, err
		}

		// 初始化微信客户端
		wxClient := client.NewWechatClient()

		// 通过GetPromoterSingleProductPromotionInfo获取ProductPromotionLink
		promotionReq := &wxmodels.GetPromoterSingleProductPromotionInfoRequest{
			SharerAppid: sharerAppid,
			ProductId:   in.ProductId,
			ShopAppid:   in.ShopAppid,
		}

		promotionResp, err := wxClient.GetPromoterSingleProductPromotionInfo(l.ctx, promotionReq, token)
		if err != nil {
			l.Errorf("获取推客商品推广链接失败: %v", err)
			// 如果获取推广链接失败，记录错误但不影响主流程，使用空字符串
			productPromotionLink = ""
		} else if !promotionResp.IsSuccess() {
			l.Errorf("获取推客商品推广链接业务错误: errcode=%d, errmsg=%s", promotionResp.ErrCode, promotionResp.ErrMsg)
			productPromotionLink = ""
		} else {
			productPromotionLink = promotionResp.ProductPromotionLink
			l.Infof("成功获取推客商品推广链接: %s", productPromotionLink)
		}
	}

	return &pb.GetPromoteProductDetailResp{
		Product: &pb.ProductDetail{
			ShopAppid:            product.ShopAppid,
			ProductId:            uint64(product.ProductId),
			ProductPromotionLink: productPromotionLink,
			ProductInfo:          productDetailInfo,
		},
		PublishCoupons:     []*pb.Coupon{}, // 从产品数据库中暂时没有优惠券信息
		CooperativeCoupons: []*pb.Coupon{}, // 从产品数据库中暂时没有优惠券信息
	}, nil
}
