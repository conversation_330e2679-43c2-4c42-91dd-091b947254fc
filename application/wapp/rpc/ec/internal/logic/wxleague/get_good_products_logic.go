package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

// Redis缓存前缀和过期时间
const (
	GoodProductsCache  = "good_products:cache" // 哈希表缓存键
	GoodProductsExpire = 3600                  // 1小时过期
	MaxCachedProducts  = 1000                  // 最多缓存1000个商品
)

type GetGoodProductsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetGoodProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodProductsLogic {
	return &GetGoodProductsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Deprecated
// GetGoodProducts 获取好物商品列表
func (l *GetGoodProductsLogic) GetGoodProducts(in *pb.GetGoodProductsReq) (*pb.GetGoodProductsResp, error) {
	// 参数验证和默认值设置
	pageSize := in.PageSize
	if pageSize <= 0 || pageSize > 20 {
		pageSize = 20
	}

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(
		l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId,
		l.svcCtx.Config.WxLeague.AppSecret,
		l.svcCtx.Config.WxLeague.AccessTokenPrefix,
	)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数调用选品接口
	selectionReq := &wxmodels.GetSelectionProductsListRequest{
		NextKey:   in.NextKey,
		PageSize:  int(pageSize),
		Keyword:   in.Keyword,
		PlanType:  2,
		SpuSource: 1,
	}

	// 添加分类参数（如果有）
	if in.CatId > 0 {
		selectionReq.Category = &wxmodels.CategoryInfoRequest{
			CategoryIds1: []uint64{in.CatId},
		}
	}

	// 调用微信API获取选品列表
	selectionResp, err := wxClient.GetSelectionProductsList(l.ctx, selectionReq, token)
	if err != nil {
		l.Errorf("获取选品商品列表失败: %v", err)
		return nil, err
	}

	// 构建响应对象
	resp := &pb.GetGoodProductsResp{
		ProductList: make([]*pb.ProductInfo, 0, len(selectionResp.ProductList)),
		NextKey:     selectionResp.NextKey,
		HasMore:     len(selectionResp.ProductList) == int(pageSize),
	}

	// 如果没有商品，直接返回空列表
	if len(selectionResp.ProductList) == 0 {
		return resp, nil
	}

	// 使用WaitGroup等待所有goroutine完成
	var wg sync.WaitGroup
	// 使用互斥锁保护结果集的并发写入
	var mu sync.Mutex

	// 并发获取每个商品的详细信息
	for _, product := range selectionResp.ProductList {
		wg.Add(1)
		go func(product wxmodels.SelectionProductInfoItem) {
			defer wg.Done()

			// 构建Redis缓存键（作为哈希表中的field）
			cacheKey := fmt.Sprintf("%s_%s", product.ShopAppid, product.ProductId)

			// 尝试从Redis哈希表获取商品详情
			var productInfo *pb.ProductInfo
			cachedData, err := l.svcCtx.BizRedis.Hget(GoodProductsCache, cacheKey)
			if err == nil && len(cachedData) > 0 {
				// 缓存命中，解析缓存数据
				productInfo = &pb.ProductInfo{}
				if err = json.Unmarshal([]byte(cachedData), productInfo); err != nil {
					l.Errorf("解析缓存商品数据失败: %v", err)
					// 解析失败时继续获取新数据
				} else {
					// 成功从缓存获取商品详情
					mu.Lock()
					resp.ProductList = append(resp.ProductList, productInfo)
					mu.Unlock()
					return
				}
			}

			// 缓存未命中或解析失败，调用微信API获取商品详情
			// 将ProductId从字符串转换为int64
			productId, err := strconv.ParseInt(product.ProductId, 10, 64)
			if err != nil {
				l.Errorf("商品ID格式错误: %s", product.ProductId)
				return
			}

			detailReq := &wxmodels.ProductDetailGetRequest{
				ProductId: productId,
				ShopAppid: product.ShopAppid,
			}

			detailResp, err := wxClient.GetProductDetail(l.ctx, detailReq, token)
			if err != nil {
				l.Errorf("获取商品详情失败: %v", err)
				return
			}

			// 构建商品信息对象
			// 将ProductId从字符串转换为uint64用于响应
			productIdUint, _ := strconv.ParseUint(product.ProductId, 10, 64)
			productInfo = &pb.ProductInfo{
				ProductId: productIdUint,
				ShopAppid: product.ShopAppid,
				Title:     detailResp.Item.ProductInfo.Title,
				SubTitle:  detailResp.Item.ProductInfo.SubTitle,
				HeadImgs:  detailResp.Item.ProductInfo.HeadImgs,
				DescInfo: &pb.ProductDescInfo{
					Imgs: detailResp.Item.ProductInfo.DescInfo.Imgs,
					Desc: detailResp.Item.ProductInfo.DescInfo.Desc,
				},
				Skus:                 make([]*pb.SkuInfo, 0, len(detailResp.Item.ProductInfo.Skus)),
				HeadSupplierItemLink: product.HeadSupplierItemLink,
			}

			// 处理SKU信息
			for _, sku := range detailResp.Item.ProductInfo.Skus {
				// 构建SKU对象
				skuInfo := &pb.SkuInfo{
					SkuId:      strconv.FormatUint(sku.SkuId, 10),
					ThumbImg:   sku.ThumbImg,
					SalePrice:  sku.SalePrice,
					StockNum:   sku.StockNum,
					EarnAmount: 0,
					SkuAttrs:   make([]*pb.SkuAttr, 0, len(sku.SkuAttrs)),
				}

				// 处理SKU属性
				for _, attr := range sku.SkuAttrs {
					skuInfo.SkuAttrs = append(skuInfo.SkuAttrs, &pb.SkuAttr{
						AttrKey:   attr.AttrKey,
						AttrValue: attr.AttrValue,
					})
				}

				productInfo.Skus = append(productInfo.Skus, skuInfo)
			}

			// 将商品信息添加到响应
			mu.Lock()
			resp.ProductList = append(resp.ProductList, productInfo)
			mu.Unlock()

			// 缓存商品信息到Redis哈希表
			if productInfo != nil {
				// 检查哈希表中商品数量是否已达上限
				count, _ := l.svcCtx.BizRedis.Hlen(GoodProductsCache)
				if count < MaxCachedProducts {
					productJSON, err := json.Marshal(productInfo)
					if err != nil {
						l.Errorf("序列化商品信息失败: %v", err)
					} else {
						// 存储到哈希表
						err = l.svcCtx.BizRedis.Hset(GoodProductsCache, cacheKey, string(productJSON))
						if err != nil {
							l.Errorf("缓存商品信息失败: %v", err)
						}
						// 设置哈希表的过期时间
						l.svcCtx.BizRedis.Expire(GoodProductsCache, GoodProductsExpire)
					}
				}
			}
		}(product)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 记录获取到的商品数量
	l.Infof("成功获取到 %d 个商品详情", len(resp.ProductList))

	return resp, nil
}
