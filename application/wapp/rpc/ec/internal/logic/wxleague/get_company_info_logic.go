package wxleaguelogic

import (
	"context"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetCompanyInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyInfoLogic {
	return &GetCompanyInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取公司信息
func (l *GetCompanyInfoLogic) GetCompanyInfo(in *pb.GetCompanyInfoReq) (*pb.CompanyInfoResp, error) {
	l.Infof("获取用户ID为%d的公司信息", in.UserId)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证用户角色是否为代理商
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 检查用户是否为代理商角色
	if user.RoleId != config.RoleAgent {
		l.Errorf("用户ID %d 不是代理商角色，无法获取公司信息", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "只有代理商可以获取公司信息")
	}

	// 查询公司信息
	var companyInfo model.CompanyInfo
	err := l.svcCtx.GetSlaveDB().Where("user_id = ?", in.UserId).First(&companyInfo).Error

	// 如果是记录不存在错误，则返回空结果
	if err != nil {
		if err.Error() == "record not found" {
			l.Infof("用户ID为%d的公司信息不存在", in.UserId)
			return &pb.CompanyInfoResp{}, nil
		}
		l.Errorf("查询公司信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询公司信息失败")
	}

	// 格式化时间
	createdAtStr := companyInfo.CreatedAt.Format("2006-01-02 15:04:05")
	updatedAtStr := companyInfo.UpdatedAt.Format("2006-01-02 15:04:05")

	// 构建响应
	return &pb.CompanyInfoResp{
		Id:          companyInfo.ID,
		Name:        companyInfo.Name,
		Address:     companyInfo.Address,
		BankAccount: companyInfo.BankAccount,
		BankName:    companyInfo.BankName,
		TaxNumber:   companyInfo.TaxNumber,
		Phone:       companyInfo.Phone,
		LicenseImg:  companyInfo.LicenseImg,
		CreatedAt:   createdAtStr,
		UpdatedAt:   updatedAtStr,
		Status:      companyInfo.Status,
		Remark:      companyInfo.Remark,
	}, nil
}
