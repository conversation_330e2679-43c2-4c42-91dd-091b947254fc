package wxleaguelogic

import (
	"context"
	"fmt"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	wxClient "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWindowAuthStatusLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetWindowAuthStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWindowAuthStatusLogic {
	return &GetWindowAuthStatusLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetWindowAuthStatusLogic) GetWindowAuthStatus(in *pb.GetWindowAuthStatusReq) (*pb.GetWindowAuthStatusResp, error) {
	l.Infof("获取达人橱窗授权状态请求: %+v", in)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 查询用户信息获取openid
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	if user.SharerAppid == "" {
		l.Errorf("用户没有绑定推客: %v", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "您的账号未绑定推客，无法获取授权状态")
	}

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxclient := wxClient.NewWechatClient()

	// 步骤1: 先获取授权状态
	statusReq := &wxmodels.GetWindowAuthStatusRequest{
		TalentAppid: user.SharerAppid,
	}

	statusResp, err := wxclient.GetWindowAuthStatus(l.ctx, statusReq, token)
	if err != nil {
		l.Errorf("调用微信API获取达人橱窗授权状态失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取授权状态失败")
	}

	// 如果已经授权，直接返回
	if statusResp.WindowAuthStatus == 1 {
		return &pb.GetWindowAuthStatusResp{
			WindowAuthStatus: int32(statusResp.WindowAuthStatus),
			Openfinderid:     user.Openfinderid,
			Opentalentid:     user.Opentalentid,
		}, nil
	}

	// 步骤2: 先获取授权链接
	authLinkReq := &wxmodels.GetWindowAuthRequest{
		Openid: user.Openid,
	}

	authLinkResp, err := wxclient.GetWindowAuth(l.ctx, authLinkReq, token)
	if err != nil {
		l.Errorf("调用微信API获取达人橱窗授权链接失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.WxError, fmt.Sprintf("获取达人橱窗授权链接失败: %v", err))
	}
	// 如果user.openfinderid为空，更新到user表
	if user.Openfinderid == "" && authLinkResp.OpenFinderId != "" {
		if err := l.svcCtx.GetSlaveDB().Model(&user).Updates(map[string]interface{}{
			"openfinderid": authLinkResp.OpenFinderId,
		}).Error; err != nil {
			l.Errorf("更新openfinderid失败: %v", err)
		}
	}
	// 如果user.opentalentid为空，更新到user表
	if user.Opentalentid == "" && authLinkResp.OpenTalentId != "" {
		if err := l.svcCtx.GetSlaveDB().Model(&user).Updates(map[string]interface{}{
			"opentalentid": authLinkResp.OpenTalentId,
		}).Error; err != nil {
			l.Errorf("更新opentalentid失败: %v", err)
		}
	}
	// 构建响应
	return &pb.GetWindowAuthStatusResp{
		WindowAuthStatus: int32(statusResp.WindowAuthStatus),
		Openfinderid:     statusResp.OpenFinderId,
		Opentalentid:     statusResp.OpenTalentId,
		AuthUrl:          authLinkResp.AuthInfo.AuthUrl,
		AuthWxaPath:      authLinkResp.AuthInfo.AuthWxaPath,
		AuthWxaAppid:     authLinkResp.AuthInfo.AuthWxaAppid,
		AuthWxaUsername:  authLinkResp.AuthInfo.AuthWxaUsername,
	}, nil
}
