package wxleaguelogic

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetTalentProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductDetailLogic {
	return &GetTalentProductDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取达人合作的商品详情
func (l *GetTalentProductDetailLogic) GetTalentProductDetail(in *pb.GetPromoteProductDetailReq) (*pb.GetPromoteProductDetailResp, error) {
	// 获取数据库连接
	db := l.svcCtx.GetSlaveDB()
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, nil
	}

	// 查询商品详情
	var product model.TempTalentProduct
	if err := db.Where("product_id = ? AND shop_appid = ?", in.ProductId, in.ShopAppid).First(&product).Error; err != nil {
		l.Errorf("查询商品详情失败: %v", err)
		return nil, err
	}

	// 获取用户信息
	var user model.User
	if err := l.svcCtx.MasterDB.Model(&model.User{}).
		Where("id = ?", in.UserId).
		First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		user.Level = 1 // 默认为1级
	}
	userLevel := user.Level

	// 获取用户级别对应的佣金比例
	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.MasterDB.Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", userLevel).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		// 使用默认值
		commissionConfig.CommissionRate = 0
	}

	// 获取某个推客某个商品的内嵌商品卡片product_promotion_link
	productPromotionLink := ""
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		return nil, err
	}
	// 初始化微信客户端
	wxClient := client.NewWechatClient()
	// 构建请求参数
	req := &wxmodels.GetPromoterSingleProductPromotionInfoRequest{
		SharerAppid:       user.SharerAppid,
		ProductId:         in.ProductId,
		ShopAppid:         in.ShopAppid,
		HeadSupplierAppid: l.svcCtx.Config.WxLeague.AppId, // 使用机构的appid
	}

	// 调用微信API
	resp, err := wxClient.GetPromoterSingleProductPromotionInfo(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取商品内嵌卡片失败: %v", err)
	} else {
		productPromotionLink = resp.ProductPromotionLink
	}

	// 构建商品详细信息
	productDetailInfo := &pb.ProductDetailInfo{
		Title:    product.Title,
		SubTitle: product.SubTitle,
	}

	// 解析JSON字段
	// 解析HeadImgs
	var headImgs []string
	if err := json.Unmarshal([]byte(product.HeadImgs), &headImgs); err != nil {
		l.Errorf("解析商品主图失败: %v", err)
		return nil, err
	}
	productDetailInfo.HeadImgs = headImgs

	// 解析DescInfo
	var descInfo pb.ProductDescInfo
	if err := json.Unmarshal([]byte(product.DescInfo), &descInfo); err != nil {
		l.Errorf("解析商品描述失败: %v", err)
		return nil, err
	}
	productDetailInfo.DescInfo = &descInfo

	// 解析CommissionInfo
	var commissionInfo struct {
		Ratio        int64 `json:"ratio"`         // 佣金费率，范围为【100000 - 900000】对应【10%-90%】
		ServiceRatio int64 `json:"service_ratio"` // 服务费率，范围为【100000 - 900000】对应【10%-90%】
	}
	if err := json.Unmarshal([]byte(product.CommissionInfo), &commissionInfo); err != nil {
		l.Errorf("解析佣金配置失败: %v", err)
		return nil, err
	}

	// 获取分佣比例
	ratio := 0.0
	if product.CommissionType == 1 {
		ratio = float64(commissionInfo.ServiceRatio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
		ratio = ratio * (commissionConfig.CommissionRate / 100)
	} else {
		// 店铺分佣
		ratio = float64(commissionInfo.Ratio) / 1000000 // 范围为【100000 - 900000】对应【10%-90%】
	}

	// 解析Skus
	var skuData []struct {
		SkuId     uint64      `json:"sku_id"`
		ThumbImg  string      `json:"thumb_img"`
		SalePrice json.Number `json:"sale_price"` // 使用json.Number处理数字，更灵活
		StockNum  json.Number `json:"stock_num"`  // 使用json.Number处理数字，更灵活
		SkuAttrs  []struct {
			AttrKey   string `json:"attr_key"`
			AttrValue string `json:"attr_value"`
		} `json:"sku_attrs"`
	}

	if err := json.Unmarshal([]byte(product.Skus), &skuData); err != nil {
		l.Errorf("解析商品SKU失败: %v", err)
		return nil, err
	}

	// 转换解析后的SKU数据
	skus := make([]*pb.SkuInfo, 0, len(skuData))
	for _, s := range skuData {
		// 解析销售价格
		salePrice, err := s.SalePrice.Int64()
		if err != nil {
			l.Errorf("解析销售价格失败 %d: %v", s.SkuId, err)
			salePrice = 0
		}

		// 解析库存
		stockNum, err := s.StockNum.Int64()
		if err != nil {
			l.Errorf("解析库存失败 %d: %v", s.SkuId, err)
			stockNum = 0
		}

		// 计算返利金额
		// 1. 获取当前SKU的价格（从分转为元）
		skuPrice := float64(salePrice) / 100

		// 2. 计算返利金额: SKU价格 * 分佣比例
		earnAmount := skuPrice * ratio

		// 3. 保留两位小数
		earnAmount = math.Round(earnAmount*100) / 100

		sku := &pb.SkuInfo{
			SkuId:      strconv.FormatUint(s.SkuId, 10),
			ThumbImg:   s.ThumbImg,
			SalePrice:  salePrice,
			StockNum:   stockNum,
			EarnAmount: earnAmount,
		}

		// 转换SKU属性
		skuAttrs := make([]*pb.SkuAttr, 0, len(s.SkuAttrs))
		for _, attr := range s.SkuAttrs {
			skuAttrs = append(skuAttrs, &pb.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}
		sku.SkuAttrs = skuAttrs

		skus = append(skus, sku)
	}
	productDetailInfo.Skus = skus

	// 构建并返回最终响应
	return &pb.GetPromoteProductDetailResp{
		Product: &pb.ProductDetail{
			ShopAppid:            product.ShopAppid,
			ProductId:            uint64(product.ProductId),
			ProductInfo:          productDetailInfo,
			ProductPromotionLink: productPromotionLink,
		},
		PublishCoupons:     make([]*pb.Coupon, 0),
		CooperativeCoupons: make([]*pb.Coupon, 0),
	}, nil
}
