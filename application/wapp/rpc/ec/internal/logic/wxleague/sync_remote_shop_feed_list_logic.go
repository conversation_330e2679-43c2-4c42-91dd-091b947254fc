package wxleaguelogic

import (
	"context"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteShopFeedListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteShopFeedListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteShopFeedListLogic {
	return &SyncRemoteShopFeedListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程小店关联账号的短视频列表
func (l *SyncRemoteShopFeedListLogic) SyncRemoteShopFeedList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 从temp_shop_promoters表查询需要同步的小店关联账号记录
	// 只查询视频号类型(promoter_type = 1)的关联账号
	var shopPromotersToSync []model.TempShopPromoters
	if err := db.Where("promoter_type = ?", 1).Limit(30).Find(&shopPromotersToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_shop_promoters 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步短视频信息的小店关联账号记录", len(shopPromotersToSync))
	if len(shopPromotersToSync) == 0 {
		l.Infof("没有需要同步的小店关联账号记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 设置并发数
	maxConcurrent := 3
	// 创建信号量控制并发
	semaphore := make(chan struct{}, maxConcurrent)
	// 创建等待组
	var wg sync.WaitGroup

	// 创建上下文，设置超时时间为5分钟
	ctx, cancel := context.WithTimeout(l.ctx, 5*time.Minute)
	defer cancel()

	// 遍历每个小店关联账号，异步获取其短视频列表
	for i, shopPromoter := range shopPromotersToSync {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			l.Infof("任务超时或被取消，已处理 %d/%d 个小店关联账号", i, len(shopPromotersToSync))
			return &pb.EmptyRequest{}, nil
		default:
			// 继续执行
		}

		// 为每个小店关联账号创建一个副本，避免闭包问题
		shopPromoterCopy := shopPromoter
		index := i

		wg.Add(1)
		// 获取信号量
		semaphore <- struct{}{}

		// 异步处理每个小店关联账号
		go func() {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			l.Infof("正在处理第 %d/%d 个小店关联账号 (shop_appid: %s, promoter_id: %s)",
				index+1, len(shopPromotersToSync), shopPromoterCopy.ShopAppid, shopPromoterCopy.PromoterID)

			// 处理该小店关联账号的短视频数据
			l.processShopPromoterFeedList(ctx, wxClient, token, shopPromoterCopy, db)
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	l.Infof("小店短视频列表同步完成")
	return &pb.EmptyRequest{}, nil
}

// 处理单个小店关联账号的短视频列表
func (l *SyncRemoteShopFeedListLogic) processShopPromoterFeedList(ctx context.Context, wxClient *client.WechatClient, token string, shopPromoter model.TempShopPromoters, db *orm.DB) {
	// 查询该小店关联账号现有的短视频记录
	var existingRecords []model.TempShopVideoList
	if err := db.Where("shop_appid = ? AND promoter_id = ?", shopPromoter.ShopAppid, shopPromoter.PromoterID).Find(&existingRecords).Error; err != nil {
		l.Errorf("查询小店关联账号 %s-%s 的现有短视频记录失败: %v", shopPromoter.ShopAppid, shopPromoter.PromoterID, err)
		return
	}

	// 创建现有记录的映射，用于快速查找
	existingMap := make(map[string]model.TempShopVideoList)
	for _, record := range existingRecords {
		existingMap[record.ExportID] = record
	}

	// 记录所有API返回的export_id，用于最后删除不存在的记录
	allApiFeedIds := make(map[string]struct{})

	// 分页循环获取所有短视频
	nextKey := ""
	pageIndex := 1
	totalFeeds := 0

	for {
		l.Infof("正在获取小店关联账号 %s-%s 第 %d 页短视频数据", shopPromoter.ShopAppid, shopPromoter.PromoterID, pageIndex)

		// 构建获取小店短视频列表的请求参数
		req := &wxmodels.GetShopFeedListRequest{
			NextKey:      nextKey,
			PageSize:     8,
			ShopAppid:    shopPromoter.ShopAppid,
			PromoterId:   shopPromoter.PromoterID,
			PromoterType: int(shopPromoter.PromoterType),
		}

		// 设置请求超时
		reqCtx, reqCancel := context.WithTimeout(ctx, 20*time.Second)

		// 调用微信API获取小店短视频列表
		resp, err := wxClient.GetShopFeedList(reqCtx, req, token)
		reqCancel() // 立即释放context

		if err != nil || !resp.IsSuccess() {
			if err != nil {
				l.Errorf("获取小店关联账号 %s-%s 的短视频列表失败(第%d页): %v", shopPromoter.ShopAppid, shopPromoter.PromoterID, pageIndex, err)
			} else {
				l.Errorf("获取小店关联账号 %s-%s 的短视频列表业务错误(第%d页): errcode=%d, errmsg=%s",
					shopPromoter.ShopAppid, shopPromoter.PromoterID, pageIndex, resp.ErrCode, resp.ErrMsg)
			}
			return
		}

		currentPageFeeds := len(resp.FeedList)
		totalFeeds += currentPageFeeds
		l.Infof("小店关联账号 %s-%s 第 %d 页获取到 %d 条短视频记录", shopPromoter.ShopAppid, shopPromoter.PromoterID, pageIndex, currentPageFeeds)

		if currentPageFeeds == 0 {
			l.Infof("小店关联账号 %s-%s 第 %d 页没有短视频记录", shopPromoter.ShopAppid, shopPromoter.PromoterID, pageIndex)
			break
		}

		// 获取推广信息
		promotionMap := l.getShopFeedPromotionInfo(reqCtx, wxClient, token, shopPromoter, resp.FeedList)

		// 处理当前页的每个短视频
		for _, feed := range resp.FeedList {
			// 记录所有API返回的export_id
			allApiFeedIds[feed.ExportId] = struct{}{}

			// 获取推广信息
			var feedToken, promoterShareLink string
			if promo, exists := promotionMap[feed.ExportId]; exists {
				feedToken = promo.FeedToken
				promoterShareLink = promo.PromoterShareLink
			}

			// 检查记录是否已存在
			if existingRecord, exists := existingMap[feed.ExportId]; exists {
				// 更新现有记录
				updateData := map[string]interface{}{
					"predict_commission_amount": feed.PredictCommissionAmount,
					"product_id":                feed.ProductInfo.ProductId,
					"product_name":              feed.ProductInfo.ProductName,
					"product_img_url":           feed.ProductInfo.ProductImgUrl,
					"product_mini_price":        feed.ProductInfo.ProductMiniPrice,
					"feed_token":                feedToken,
					"promoter_share_link":       promoterShareLink,
					"updated_at":                time.Now(),
				}

				if err := db.Model(&model.TempShopVideoList{}).Where("id = ?", existingRecord.ID).
					Updates(updateData).Error; err != nil {
					l.Errorf("更新小店关联账号 %s-%s 的短视频记录失败 (export_id: %s): %v",
						shopPromoter.ShopAppid, shopPromoter.PromoterID, feed.ExportId, err)
				} else {
					l.Infof("成功更新小店关联账号 %s-%s 的短视频记录 (export_id: %s)",
						shopPromoter.ShopAppid, shopPromoter.PromoterID, feed.ExportId)
				}
			} else {
				// 创建新记录
				newRecord := &model.TempShopVideoList{
					ExportID:                feed.ExportId,
					ShopAppid:               shopPromoter.ShopAppid,
					PromoterID:              shopPromoter.PromoterID,
					PromoterType:            shopPromoter.PromoterType,
					PredictCommissionAmount: feed.PredictCommissionAmount,
					ProductID:               feed.ProductInfo.ProductId,
					ProductName:             feed.ProductInfo.ProductName,
					ProductImgUrl:           feed.ProductInfo.ProductImgUrl,
					ProductMiniPrice:        feed.ProductInfo.ProductMiniPrice,
					FeedToken:               feedToken,
					PromoterShareLink:       promoterShareLink,
				}

				if err := db.Create(newRecord).Error; err != nil {
					l.Errorf("插入小店关联账号 %s-%s 的新短视频记录失败 (export_id: %s): %v",
						shopPromoter.ShopAppid, shopPromoter.PromoterID, feed.ExportId, err)
				} else {
					l.Infof("成功创建小店关联账号 %s-%s 的短视频记录 (export_id: %s, id: %d)",
						shopPromoter.ShopAppid, shopPromoter.PromoterID, feed.ExportId, newRecord.ID)
				}
			}
		}

		// 判断是否还有下一页
		if !resp.HasMore || resp.NextKey == "" {
			l.Infof("小店关联账号 %s-%s 没有更多页面，分页获取完成", shopPromoter.ShopAppid, shopPromoter.PromoterID)
			break
		}

		// 更新nextKey用于获取下一页
		nextKey = resp.NextKey
		pageIndex++

		// 添加延迟，避免请求过于频繁
		time.Sleep(time.Millisecond * 800)
	}

	l.Infof("小店关联账号 %s-%s 总共获取到 %d 条短视频记录，共 %d 页", shopPromoter.ShopAppid, shopPromoter.PromoterID, totalFeeds, pageIndex)

	// 删除已不存在的记录
	if len(allApiFeedIds) > 0 {
		var currentExportIds []string
		for exportId := range allApiFeedIds {
			currentExportIds = append(currentExportIds, exportId)
		}

		if err := db.Where("shop_appid = ? AND promoter_id = ? AND export_id NOT IN ?", shopPromoter.ShopAppid, shopPromoter.PromoterID, currentExportIds).
			Delete(&model.TempShopVideoList{}).Error; err != nil {
			l.Errorf("删除小店关联账号 %s-%s 的过期短视频记录失败: %v", shopPromoter.ShopAppid, shopPromoter.PromoterID, err)
		} else {
			l.Infof("已删除小店关联账号 %s-%s 的过期短视频记录", shopPromoter.ShopAppid, shopPromoter.PromoterID)
		}
	} else {
		// 如果API没有返回任何短视频，删除该小店关联账号的所有短视频记录
		if err := db.Unscoped().Where("shop_appid = ? AND promoter_id = ?", shopPromoter.ShopAppid, shopPromoter.PromoterID).
			Delete(&model.TempShopVideoList{}).Error; err != nil {
			l.Errorf("删除小店关联账号 %s-%s 的所有短视频记录失败: %v", shopPromoter.ShopAppid, shopPromoter.PromoterID, err)
		} else {
			l.Infof("已删除小店关联账号 %s-%s 的所有短视频记录（API无返回数据）", shopPromoter.ShopAppid, shopPromoter.PromoterID)
		}
	}
}

// 获取小店短视频推广信息
func (l *SyncRemoteShopFeedListLogic) getShopFeedPromotionInfo(ctx context.Context, wxClient *client.WechatClient, token string, shopPromoter model.TempShopPromoters, feedList []wxmodels.ShopFeedInfo) map[string]wxmodels.ShopFeedInfoResp {
	promotionMap := make(map[string]wxmodels.ShopFeedInfoResp)

	// 准备推广请求
	var feedReqs []wxmodels.ShopFeedInfoReq
	for _, feed := range feedList {
		feedReqs = append(feedReqs, wxmodels.ShopFeedInfoReq{
			ExportId: feed.ExportId,
		})
	}

	// 构建获取推广信息的请求
	promotionReq := &wxmodels.GetShopFeedPromotionInfoRequest{
		FeedList:         feedReqs,
		ShopAppid:        shopPromoter.ShopAppid,
		PromoterId:       shopPromoter.PromoterID,
		PromoterType:     int(shopPromoter.PromoterType),
		MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
		SharerAppid:      "", // 这里暂时为空，实际使用时可能需要具体的推客appid
	}

	// 设置请求超时
	reqCtx, reqCancel := context.WithTimeout(ctx, 15*time.Second)
	defer reqCancel()

	// 调用获取推广信息API
	promotionResp, err := wxClient.GetShopFeedPromotionInfo(reqCtx, promotionReq, token)
	if err != nil || !promotionResp.IsSuccess() {
		if err != nil {
			l.Errorf("获取小店短视频推广信息失败(shop_appid=%s, promoter_id=%s): %v", shopPromoter.ShopAppid, shopPromoter.PromoterID, err)
		} else {
			l.Errorf("获取小店短视频推广信息业务错误(shop_appid=%s, promoter_id=%s): errcode=%d, errmsg=%s",
				shopPromoter.ShopAppid, shopPromoter.PromoterID, promotionResp.ErrCode, promotionResp.ErrMsg)
		}
		return promotionMap
	}

	// 将获取到的推广信息添加到映射中
	if promotionResp != nil {
		for _, promo := range promotionResp.FeedList {
			promotionMap[promo.ExportId] = promo
		}
		l.Infof("成功获取小店关联账号 %s-%s 的 %d 条短视频推广信息", shopPromoter.ShopAppid, shopPromoter.PromoterID, len(promotionResp.FeedList))
	}

	return promotionMap
}
