package wxleaguelogic

import (
	"context"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductCategoryListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetProductCategoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductCategoryListLogic {
	return &GetProductCategoryListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取产品分类列表
func (l *GetProductCategoryListLogic) GetProductCategoryList(in *pb.EmptyRequest) (*pb.ProductCategoryListResp, error) {
	var categories []*model.ShopProductsCategory
	err := l.svcCtx.GetSlaveDB().Model(&model.ShopProductsCategory{}).Where("`show` = ?", 1).Where("`f_cat_id` = 0").Order("`order` ASC").Find(&categories).Error
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	list := make([]*pb.ProductCategoryItem, 0, len(categories))
	for _, cat := range categories {
		list = append(list, &pb.ProductCategoryItem{
			CatId: uint64(cat.CatID),
			Name:  cat.Name,
		})
	}

	return &pb.ProductCategoryListResp{
		List:  list,
		Total: uint64(len(list)),
	}, nil
}
