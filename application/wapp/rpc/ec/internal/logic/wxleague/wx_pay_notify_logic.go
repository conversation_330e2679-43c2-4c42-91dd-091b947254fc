package wxleaguelogic

import (
	"context"
	"fmt"
	"time"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxPay/notify"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type WxPayNotifyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewWxPayNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WxPayNotifyLogic {
	return &WxPayNotifyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 微信支付回调
func (l *WxPayNotifyLogic) WxPayNotify(in *pb.WxPayNotifyReq) (*pb.WxPayNotifyResp, error) {
	l.Infof("开始处理微信支付回调: ID=%s, EventType=%s", in.Id, in.EventType)

	// 验证基本参数
	if err := l.validateNotifyRequest(in); err != nil {
		l.Errorf("回调请求验证失败: %v", err)
		return &pb.WxPayNotifyResp{
			Code:    "FAIL",
			Message: "请求参数无效",
		}, nil
	}

	// 解密回调数据
	decryptData, err := l.decryptNotifyData(in.Resource)
	if err != nil {
		l.Errorf("解密回调数据失败: %v", err)
		return &pb.WxPayNotifyResp{
			Code:    "FAIL",
			Message: "数据解密失败",
		}, nil
	}

	l.Infof("解密成功，商户单号: %s, 微信单号: %s, 状态: %s",
		decryptData.OutBillNo, decryptData.TransferBillNo, decryptData.State)

	// 处理业务逻辑
	if err := l.processBusiness(decryptData); err != nil {
		l.Errorf("处理业务逻辑失败: %v", err)
		return &pb.WxPayNotifyResp{
			Code:    "FAIL",
			Message: "业务处理失败",
		}, nil
	}

	l.Infof("微信支付回调处理完成: ID=%s", in.Id)
	return &pb.WxPayNotifyResp{
		Code:    "SUCCESS",
		Message: "处理成功",
	}, nil
}

// validateNotifyRequest 验证回调请求参数
func (l *WxPayNotifyLogic) validateNotifyRequest(in *pb.WxPayNotifyReq) error {
	if in.Id == "" {
		return fmt.Errorf("通知ID不能为空")
	}

	// 使用notify工具类的辅助函数验证
	if !notify.IsTransferNotify(in.EventType) {
		return fmt.Errorf("不支持的事件类型: %s", in.EventType)
	}

	if !notify.IsValidResourceType(in.ResourceType) {
		return fmt.Errorf("不支持的资源类型: %s", in.ResourceType)
	}

	if in.Resource == nil {
		return fmt.Errorf("资源数据不能为空")
	}

	if in.Resource.Algorithm != "AEAD_AES_256_GCM" {
		return fmt.Errorf("不支持的加密算法: %s", in.Resource.Algorithm)
	}

	return nil
}

// decryptNotifyData 解密通知数据（使用notify工具类）
func (l *WxPayNotifyLogic) decryptNotifyData(resource *pb.WxPayNotifyResourceInfo) (*pb.WxPayNotifyDecryptData, error) {
	// 创建notify工具类配置
	config := &notify.NotifyConfig{
		WechatPayPublicKeyId:   l.svcCtx.Config.WxPay.WechatPayPublicKeyId,
		WechatPayPublicKeyPath: l.svcCtx.Config.WxPay.WechatPayPublicKeyPath,
		ApiV3Key:               l.svcCtx.Config.WxPay.Security.EncryptionKey,
	}

	// 创建notify处理器
	notifyHandler, err := notify.NewNotifyHandler(config)
	if err != nil {
		return nil, fmt.Errorf("创建notify处理器失败: %v", err)
	}

	// 转换资源结构
	notifyResource := &notify.NotifyResource{
		OriginalType:   resource.OriginalType,
		Algorithm:      resource.Algorithm,
		Ciphertext:     resource.Ciphertext,
		AssociatedData: resource.AssociatedData,
		Nonce:          resource.Nonce,
	}

	// 解密数据
	transferData, err := notifyHandler.DecryptNotifyData(notifyResource)
	if err != nil {
		return nil, fmt.Errorf("解密回调数据失败: %v", err)
	}

	// 转换为protobuf结构
	return &pb.WxPayNotifyDecryptData{
		OutBillNo:      transferData.OutBillNo,
		TransferBillNo: transferData.TransferBillNo,
		State:          transferData.State,
		MchId:          transferData.MchId,
		TransferAmount: transferData.TransferAmount,
		Openid:         transferData.Openid,
		FailReason:     transferData.FailReason,
		CreateTime:     transferData.CreateTime,
		UpdateTime:     transferData.UpdateTime,
	}, nil
}

// processBusiness 处理业务逻辑
func (l *WxPayNotifyLogic) processBusiness(data *pb.WxPayNotifyDecryptData) error {
	// 根据商户单号查找提现记录
	var withdrawRecord model.WithdrawRecord
	db := l.svcCtx.MasterDB.DB

	err := db.Where("out_bill_no = ?", data.OutBillNo).First(&withdrawRecord).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("未找到对应的提现记录: %s", data.OutBillNo)
			return xerr.NewErrMsg(l.ctx, "未找到对应的提现记录")
		}
		l.Errorf("查询提现记录失败: %v", err)
		return xerr.NewErrMsg(l.ctx, "查询提现记录失败")
	}

	l.Infof("找到提现记录: ID=%d, 用户ID=%d, 当前状态=%d",
		withdrawRecord.ID, withdrawRecord.UserID, withdrawRecord.Status)

	// 幂等性检查：如果已经是终态，则直接返回成功
	if l.isFinalStatus(withdrawRecord.Status) {
		l.Infof("提现记录已处于终态，跳过处理: %d", withdrawRecord.Status)
		return nil
	}

	// 更新提现记录状态
	return l.updateWithdrawRecord(&withdrawRecord, data)
}

// isFinalStatus 检查是否为终态
func (l *WxPayNotifyLogic) isFinalStatus(status model.WithdrawStatus) bool {
	return status == model.WithdrawStatusSuccess ||
		status == model.WithdrawStatusFailed ||
		status == model.WithdrawStatusCancelled
}

// updateWithdrawRecord 更新提现记录
func (l *WxPayNotifyLogic) updateWithdrawRecord(record *model.WithdrawRecord, data *pb.WxPayNotifyDecryptData) error {
	db := l.svcCtx.MasterDB.DB
	now := time.Now()

	// 开启事务
	return db.Transaction(func(tx *gorm.DB) error {
		// 更新基础字段
		updates := map[string]interface{}{
			"transfer_bill_no": data.TransferBillNo,
			"state":            data.State,
			"updated_at":       now,
		}

		// 根据微信支付状态更新本地状态并记录资金明细
		switch data.State {
		case "SUCCESS":
			updates["status"] = model.WithdrawStatusSuccess
			updates["success_time"] = &now
			updates["actual_amount"] = data.TransferAmount
			l.Infof("转账成功: 商户单号=%s, 转账金额=%d", data.OutBillNo, data.TransferAmount)

			// 转账成功时记录资金明细
			if err := l.recordSettlementDetail(tx, record, data, "提现成功"); err != nil {
				l.Errorf("记录成功资金明细失败: %v", err)
				return err
			}

		case "FAIL":
			updates["status"] = model.WithdrawStatusFailed
			updates["fail_reason"] = data.FailReason
			l.Infof("转账失败: 商户单号=%s, 失败原因=%s", data.OutBillNo, data.FailReason)

			// 转账失败时恢复用户余额并记录明细
			if err := l.handleTransferFailure(tx, record, data); err != nil {
				l.Errorf("处理转账失败逻辑失败: %v", err)
				return err
			}

		case "CANCELLED":
			updates["status"] = model.WithdrawStatusCancelled
			updates["fail_reason"] = "转账已撤销"
			l.Infof("转账已撤销: 商户单号=%s", data.OutBillNo)

			// 转账撤销时恢复用户余额并记录明细
			if err := l.handleTransferFailure(tx, record, data); err != nil {
				l.Errorf("处理转账撤销逻辑失败: %v", err)
				return err
			}

		case "PROCESSING", "WAIT_USER_CONFIRM", "TRANSFERING":
			updates["status"] = model.WithdrawStatusProcess
			updates["process_time"] = &now
			l.Infof("转账处理中: 商户单号=%s, 状态=%s", data.OutBillNo, data.State)
			// 处理中状态不记录资金明细，等待最终状态

		default:
			l.Errorf("未知的转账状态: %s", data.State)
			return xerr.NewErrMsg(l.ctx, "未知的转账状态")
		}

		// 执行更新
		result := tx.Model(record).Where("id = ? AND out_bill_no = ?", record.ID, data.OutBillNo).Updates(updates)
		if result.Error != nil {
			l.Errorf("更新提现记录失败: %v", result.Error)
			return xerr.NewErrMsg(l.ctx, "更新提现记录失败")
		}

		if result.RowsAffected == 0 {
			l.Errorf("未找到需要更新的记录: ID=%d, 商户单号=%s", record.ID, data.OutBillNo)
			return xerr.NewErrMsg(l.ctx, "记录不存在或已被其他进程更新")
		}

		l.Infof("提现记录更新成功: ID=%d, 新状态=%v", record.ID, updates["status"])
		return nil
	})
}

// recordSettlementDetail 记录资金明细
func (l *WxPayNotifyLogic) recordSettlementDetail(tx *gorm.DB, record *model.WithdrawRecord, data *pb.WxPayNotifyDecryptData, remark string) error {
	// 查询用户当前可提现余额（用于计算原始余额）
	var user model.User
	if err := tx.Select("withdraw").Where("id = ?", record.UserID).First(&user).Error; err != nil {
		l.Errorf("查询用户余额失败: %v", err)
		return xerr.NewErrMsg(l.ctx, "查询用户余额失败")
	}

	// 计算原始余额（当前余额 + 提现金额）
	originAmount := int64(user.Withdraw) + int64(record.Amount)

	// 创建资金明细记录
	settlementDetail := model.TaskSettlementDetail{
		Amount:       originAmount,
		AmountChange: -int64(record.Amount), // 提现为负数
		Type:         5,                     // 5-提现操作
		AccountType:  "withdraw",            // 可提现余额
		UserID:       record.UserID,
		AssociatedID: record.OutBillNo, // 关联提现订单号
		Remark:       fmt.Sprintf("%s，金额：%.2f元", remark, float64(record.Amount)/100),
		CreateTime:   time.Now(),
	}

	if err := tx.Create(&settlementDetail).Error; err != nil {
		l.Errorf("创建资金明细失败: %v", err)
		return xerr.NewErrMsg(l.ctx, "创建资金明细失败")
	}

	l.Infof("记录资金明细成功: 用户ID=%d, 变动金额=%d", record.UserID, settlementDetail.AmountChange)
	return nil
}

// handleTransferFailure 处理转账失败/撤销，恢复用户余额并记录明细
func (l *WxPayNotifyLogic) handleTransferFailure(tx *gorm.DB, record *model.WithdrawRecord, data *pb.WxPayNotifyDecryptData) error {
	// 恢复用户可提现余额
	updateResult := tx.Model(&model.User{}).
		Where("id = ?", record.UserID).
		Update("withdraw", gorm.Expr("withdraw + ?", record.Amount))

	if updateResult.Error != nil {
		l.Errorf("恢复用户余额失败: %v", updateResult.Error)
		return xerr.NewErrMsg(l.ctx, "恢复用户余额失败")
	}

	if updateResult.RowsAffected == 0 {
		l.Errorf("未找到需要恢复余额的用户: %d", record.UserID)
		return xerr.NewErrMsg(l.ctx, "用户不存在")
	}

	// 查询恢复后的用户余额
	var user model.User
	if err := tx.Select("withdraw").Where("id = ?", record.UserID).First(&user).Error; err != nil {
		l.Errorf("查询恢复后用户余额失败: %v", err)
		return xerr.NewErrMsg(l.ctx, "查询用户余额失败")
	}

	// 记录余额恢复明细
	// settlementDetail := model.TaskSettlementDetail{
	// 	Amount:       int64(user.Withdraw),
	// 	AmountChange: int64(record.Amount), // 恢复为正数
	// 	Type:         6,                    // 6-提现失败恢复
	// 	AccountType:  "withdraw",           // 可提现余额
	// 	UserID:       record.UserID,
	// 	AssociatedID: record.OutBillNo, // 关联提现订单号
	// 	Remark:       fmt.Sprintf("提现失败/撤销，恢复余额：%.2f元，原因：%s", float64(record.Amount)/100, data.FailReason),
	// 	CreateTime:   time.Now(),
	// }
	//
	// if err := tx.Create(&settlementDetail).Error; err != nil {
	// 	l.Errorf("创建余额恢复明细失败: %v", err)
	// 	return xerr.NewErrMsg(l.ctx, "创建余额恢复明细失败")
	// }

	l.Infof("余额恢复成功: 用户ID=%d, 恢复金额=%d", record.UserID, record.Amount)
	return nil
}
