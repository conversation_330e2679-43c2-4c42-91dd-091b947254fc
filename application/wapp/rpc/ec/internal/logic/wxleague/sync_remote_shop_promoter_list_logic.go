package wxleaguelogic

import (
	"context"
	"fmt"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteShopPromoterListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteShopPromoterListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteShopPromoterListLogic {
	return &SyncRemoteShopPromoterListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemoteShopPromoterList 同步店铺关联推客账号
func (l *SyncRemoteShopPromoterListLogic) SyncRemoteShopPromoterList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 同步绑定的店铺列表
	bindShops, err := l.syncBindShopList(wxClient, token, db)
	if err != nil {
		l.Errorf("同步绑定店铺列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ECommerceRuntimeError, fmt.Sprintf("同步绑定店铺列表失败: %v", err))
	}

	// 同步每个店铺的推客列表
	// 记录所有API返回的shop_appid和promoter_id组合，用于最后删除不存在的记录
	allApiShopPromoterPairs := make(map[string]struct{})

	// 使用并发处理小店关联推客信息
	var wg sync.WaitGroup
	semaphore := make(chan struct{}, 5) // 限制并发数为5
	var mapMutex sync.Mutex             // 用于保护allApiShopPromoterPairs的并发访问

	for _, shop := range bindShops {
		wg.Add(1)
		go func(shopAppid string) {
			defer wg.Done()
			semaphore <- struct{}{}        // 获取信号量
			defer func() { <-semaphore }() // 释放信号量

			// 获取小店关联的推客列表
			l.syncShopPromoters(wxClient, token, shopAppid, db, allApiShopPromoterPairs, &mapMutex)
		}(shop.ShopAppid)
	}

	// 等待所有goroutine完成
	wg.Wait()

	// 删除不存在的小店推客关联记录
	var existingRecords []model.TempShopPromoters
	if err := db.Find(&existingRecords).Error; err != nil {
		l.Errorf("查询现有小店推客关联记录失败: %v", err)
	} else {
		var recordsToDelete []uint64
		for _, record := range existingRecords {
			key := fmt.Sprintf("%s_%s", record.ShopAppid, record.PromoterID)
			if _, exists := allApiShopPromoterPairs[key]; !exists {
				recordsToDelete = append(recordsToDelete, uint64(record.ID))
			}
		}

		if len(recordsToDelete) > 0 {
			if err := db.Where("id IN ?", recordsToDelete).Delete(&model.TempShopPromoters{}).Error; err != nil {
				l.Errorf("删除不存在的小店推客关联记录失败: %v", err)
			} else {
				l.Infof("成功删除 %d 个不存在的小店推客关联记录", len(recordsToDelete))
			}
		}
	}

	return &pb.EmptyRequest{}, nil
}

// syncBindShopList 同步绑定的店铺列表
func (l *SyncRemoteShopPromoterListLogic) syncBindShopList(wxClient *client.WechatClient, token string, db *orm.DB) ([]model.TempBindShops, error) {

	// 获取数据库中现有的所有 shop_appid
	var existingDbShops []model.TempBindShops
	if err := db.Select("shop_appid").Find(&existingDbShops).Error; err != nil {
		l.Errorf("查询数据库 temp_bind_shops 失败: %v", err)
		return nil, err
	}

	// 将数据库中的 shop_appid 存入 map，方便查找
	dbShopAppids := make(map[string]struct{}, len(existingDbShops))
	for _, shop := range existingDbShops {
		dbShopAppids[shop.ShopAppid] = struct{}{}
	}
	l.Infof("数据库中现有绑定小店数量: %d", len(dbShopAppids))

	// 记录所有API返回的shop_appid，用于最后删除不存在的记录
	allApiShopAppids := make(map[string]struct{})
	var allBindShops []model.TempBindShops

	// 分页循环获取所有绑定小店
	nextKey := ""
	pageIndex := 1
	pageSize := 30 // 微信API限制最大30

	for {
		l.Infof("开始获取第 %d 页绑定小店数据", pageIndex)

		// 构建请求参数
		req := &wxmodels.GetBindShopListRequest{
			PageSize: pageSize,
			NextKey:  nextKey,
		}

		// 调用微信API获取绑定小店列表
		resp, err := wxClient.GetBindShopList(l.ctx, req, token)
		if err != nil {
			l.Errorf("获取绑定小店列表失败: %v", err)
			return nil, err
		}

		l.Infof("第 %d 页获取到 %d 个绑定小店", pageIndex, len(resp.ShopList))

		// 批量处理小店数据
		var shopsToCreate []model.TempBindShops
		var shopsToUpdate []model.TempBindShops

		for _, shop := range resp.ShopList {
			// 记录API返回的shop_appid
			allApiShopAppids[shop.ShopAppid] = struct{}{}

			// 构建小店数据模型
			tempShop := model.TempBindShops{
				ShopAppid:  shop.ShopAppid,
				Nickname:   shop.ShopNickname,
				HeadImgUrl: common.SchemaConvert(shop.ShopHeadImg),
				BindTime:   shop.BindTime,
			}

			// 添加到返回的绑定店铺列表
			allBindShops = append(allBindShops, tempShop)

			// 检查数据库中是否已存在该小店
			_, exists := dbShopAppids[shop.ShopAppid]
			if exists {
				// 添加到更新列表
				shopsToUpdate = append(shopsToUpdate, tempShop)
			} else {
				// 添加到创建列表
				shopsToCreate = append(shopsToCreate, tempShop)
			}
		}

		// 批量创建新记录
		if len(shopsToCreate) > 0 {
			if err := db.Create(&shopsToCreate).Error; err != nil {
				l.Errorf("批量创建绑定小店失败: %v", err)
			} else {
				l.Infof("成功批量创建 %d 个绑定小店", len(shopsToCreate))
			}
		}

		// 批量更新现有记录
		for _, shop := range shopsToUpdate {
			if err := db.Model(&model.TempBindShops{}).Where("shop_appid = ?", shop.ShopAppid).Updates(shop).Error; err != nil {
				l.Errorf("更新绑定小店 %s 失败: %v", shop.ShopAppid, err)
			}
		}

		// 检查是否还有更多页
		if !resp.HasMore || resp.NextKey == "" {
			l.Infof("没有更多绑定小店数据，同步完成")
			break
		}

		// 更新nextKey和页码
		nextKey = resp.NextKey
		pageIndex++
	}

	// 删除不存在的小店记录
	var shopAppidsToDelete []string
	for dbShopAppid := range dbShopAppids {
		if _, exists := allApiShopAppids[dbShopAppid]; !exists {
			shopAppidsToDelete = append(shopAppidsToDelete, dbShopAppid)
		}
	}

	if len(shopAppidsToDelete) > 0 {
		if err := db.Where("shop_appid IN ?", shopAppidsToDelete).Delete(&model.TempBindShops{}).Error; err != nil {
			l.Errorf("删除不存在的绑定小店记录失败: %v", err)
		} else {
			l.Infof("成功删除 %d 个不存在的绑定小店记录", len(shopAppidsToDelete))
		}
	}

	return allBindShops, nil
}

// syncShopPromoters 同步指定小店的推客列表
func (l *SyncRemoteShopPromoterListLogic) syncShopPromoters(wxClient *client.WechatClient, token string, shopAppid string, db *orm.DB, allApiShopPromoterPairs map[string]struct{}, mapMutex *sync.Mutex) {
	// 分页获取小店关联的推客列表
	nextKey := ""
	pageIndex := 1
	pageSize := 10

	for {
		l.Infof("开始获取小店 %s 的第 %d 页推客数据", shopAppid, pageIndex)

		// 构建请求参数
		req := &wxmodels.GetBindShopPromoterListRequest{
			ShopAppid: shopAppid,
			PageSize:  pageSize,
			NextKey:   nextKey,
		}

		// 设置请求超时
		ctx, cancel := context.WithTimeout(l.ctx, 10*time.Second)
		defer cancel()

		// 调用微信API获取小店关联的推客列表
		resp, err := wxClient.GetBindShopPromoterList(ctx, req, token)
		if err != nil {
			l.Errorf("获取小店 %s 关联的推客列表失败: %v", shopAppid, err)
			return
		}

		l.Infof("小店 %s 第 %d 页获取到 %d 个关联推客", shopAppid, pageIndex, len(resp.PromoterList))

		// 批量处理推客数据
		var promotersToCreate []model.TempShopPromoters
		var promotersToUpdate []model.TempShopPromoters

		for _, promoter := range resp.PromoterList {
			// 记录API返回的shop_appid和promoter_id组合
			key := fmt.Sprintf("%s_%s", shopAppid, promoter.PromoterId)
			mapMutex.Lock()
			allApiShopPromoterPairs[key] = struct{}{}
			mapMutex.Unlock()

			// 构建推客数据模型
			tempPromoter := model.TempShopPromoters{
				ShopAppid:    shopAppid,
				PromoterID:   promoter.PromoterId,
				PromoterType: int32(promoter.PromoterType),
				Nickname:     promoter.PromoterName,
				HeadImgUrl:   common.SchemaConvert(promoter.AvatarImageUrl),
			}

			// 检查数据库中是否已存在该记录
			var existingRecord model.TempShopPromoters
			result := db.Where("shop_appid = ? AND promoter_id = ?", shopAppid, promoter.PromoterId).First(&existingRecord)
			if result.Error == nil {
				// 记录已存在，添加到更新列表
				tempPromoter.ID = existingRecord.ID
				promotersToUpdate = append(promotersToUpdate, tempPromoter)
			} else {
				// 记录不存在，添加到创建列表
				promotersToCreate = append(promotersToCreate, tempPromoter)
			}
		}

		// 批量创建新记录
		if len(promotersToCreate) > 0 {
			if err := db.Create(&promotersToCreate).Error; err != nil {
				l.Errorf("批量创建小店 %s 的推客记录失败: %v", shopAppid, err)
			} else {
				l.Infof("成功批量创建小店 %s 的 %d 个推客记录", shopAppid, len(promotersToCreate))
			}
		}

		// 批量更新现有记录
		for _, promoter := range promotersToUpdate {
			if err := db.Model(&model.TempShopPromoters{}).Where("id = ?", promoter.ID).Updates(promoter).Error; err != nil {
				l.Errorf("更新小店 %s 的推客 %s 记录失败: %v", shopAppid, promoter.PromoterID, err)
			}
		}

		// 检查是否还有更多页
		if !resp.HasMore || resp.NextKey == "" {
			l.Infof("小店 %s 没有更多推客数据，同步完成", shopAppid)
			break
		}

		// 更新nextKey和页码
		nextKey = resp.NextKey
		pageIndex++
	}
}
