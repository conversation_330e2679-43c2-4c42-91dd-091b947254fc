package wxleaguelogic

import (
	"context"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLiveRecordListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetLiveRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLiveRecordListLogic {
	return &GetLiveRecordListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取达人平台的直播列表
// Deprecated: 此方法已废弃，请使用 GetAggregationLiveRecordList 代替
func (l *GetLiveRecordListLogic) GetLiveRecordList(in *pb.EmptyRequest) (*pb.GetLiveRecordListResp, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetLiveRecordListRequest{
		TalentAppid:      "wx67bbcff47a5849c9",
		MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
		// SharerAppid:      in.SharerAppid,
	}

	// 调用微信API
	resp, err := wxClient.GetLiveRecordList(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取达人平台直播列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	var liveRecords []*pb.LiveRecordInfo
	for _, item := range resp.LiveRecordList {
		liveRecords = append(liveRecords, &pb.LiveRecordInfo{
			ExportId:          item.ExportId,
			Description:       item.Description,
			PromoterShareLink: item.PromoterShareLink,
		})
	}

	return &pb.GetLiveRecordListResp{
		LiveRecordList: liveRecords,
	}, nil
}
