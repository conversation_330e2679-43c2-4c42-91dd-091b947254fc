package wxleaguelogic

import (
	"context"
	"sync"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/orm"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteFeedListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteFeedListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteFeedListLogic {
	return &SyncRemoteFeedListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步远程达人平台的短视频列表
func (l *SyncRemoteFeedListLogic) SyncRemoteFeedList(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 从temp_talents表查询需要同步的达人记录
	var talentsToSync []model.TempTalents
	if err := db.Limit(50).Find(&talentsToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_talents 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步短视频信息的达人记录", len(talentsToSync))
	if len(talentsToSync) == 0 {
		l.Infof("没有需要同步的达人记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 设置并发数
	maxConcurrent := 5
	// 创建信号量控制并发
	semaphore := make(chan struct{}, maxConcurrent)
	// 创建等待组
	var wg sync.WaitGroup

	// 创建上下文，设置超时时间为3分钟
	ctx, cancel := context.WithTimeout(l.ctx, 3*time.Minute)
	defer cancel()

	// 遍历每个达人，异步获取其短视频列表
	for i, talent := range talentsToSync {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			l.Infof("任务超时或被取消，已处理 %d/%d 个达人", i, len(talentsToSync))
			return &pb.EmptyRequest{}, nil
		default:
			// 继续执行
		}

		// 为每个达人创建一个副本，避免闭包问题
		talentCopy := talent
		index := i

		wg.Add(1)
		// 获取信号量
		semaphore <- struct{}{}

		// 异步处理每个达人
		go func() {
			defer wg.Done()
			defer func() { <-semaphore }() // 释放信号量

			l.Infof("正在处理第 %d/%d 个达人 (talent_appid: %s)",
				index+1, len(talentsToSync), talentCopy.TalentAppid)

			// 处理该达人的短视频数据
			l.processTalentFeedList(ctx, wxClient, token, talentCopy, db)
		}()
	}

	// 等待所有goroutine完成
	wg.Wait()

	l.Infof("短视频列表同步完成")
	return &pb.EmptyRequest{}, nil
}

// 处理单个达人的短视频列表
func (l *SyncRemoteFeedListLogic) processTalentFeedList(ctx context.Context, wxClient *client.WechatClient, token string, talent model.TempTalents, db *orm.DB) {
	// 查询该达人现有的短视频记录
	var existingRecords []model.TempVideoList
	if err := db.Where("talent_appid = ?", talent.TalentAppid).Find(&existingRecords).Error; err != nil {
		l.Errorf("查询达人 %s 的现有短视频记录失败: %v", talent.TalentAppid, err)
		return
	}

	// 创建现有记录的映射，用于快速查找
	existingMap := make(map[string]model.TempVideoList)
	for _, record := range existingRecords {
		existingMap[record.ExportID] = record
	}

	// 记录所有API返回的export_id，用于最后删除不存在的记录
	allApiFeedIds := make(map[string]struct{})

	// 分页循环获取所有短视频
	nextKey := ""
	pageIndex := 1
	totalFeeds := 0

	for {
		l.Infof("正在获取达人 %s 第 %d 页短视频数据", talent.TalentAppid, pageIndex)

		// 构建获取短视频列表的请求参数
		req := &wxmodels.GetFeedListRequest{
			TalentAppid: talent.TalentAppid,
			NextKey:     nextKey,
			PageSize:    8,
		}

		// 设置请求超时
		reqCtx, reqCancel := context.WithTimeout(ctx, 15*time.Second)

		// 调用微信API获取短视频列表
		resp, err := wxClient.GetFeedList(reqCtx, req, token)
		reqCancel() // 立即释放context

		if err != nil || !resp.IsSuccess() {
			if err != nil {
				l.Errorf("获取达人 %s 的短视频列表失败(第%d页): %v", talent.TalentAppid, pageIndex, err)
			} else {
				l.Errorf("获取达人 %s 的短视频列表业务错误(第%d页): errcode=%d, errmsg=%s",
					talent.TalentAppid, pageIndex, resp.ErrCode, resp.ErrMsg)
			}
			return
		}

		currentPageFeeds := len(resp.FeedList)
		totalFeeds += currentPageFeeds
		l.Infof("达人 %s 第 %d 页获取到 %d 条短视频记录", talent.TalentAppid, pageIndex, currentPageFeeds)

		if currentPageFeeds == 0 {
			l.Infof("达人 %s 第 %d 页没有短视频记录", talent.TalentAppid, pageIndex)
			break
		}

		// 获取推广信息
		promotionMap := l.getFeedPromotionInfo(reqCtx, wxClient, token, talent.TalentAppid, resp.FeedList)

		// 处理当前页的每个短视频
		for _, feed := range resp.FeedList {
			// 记录所有API返回的export_id
			allApiFeedIds[feed.ExportId] = struct{}{}

			// 获取推广信息
			var feedToken, promoterShareLink string
			if promo, exists := promotionMap[feed.ExportId]; exists {
				feedToken = promo.FeedToken
				promoterShareLink = promo.PromoterShareLink
			}

			// 检查记录是否已存在
			if existingRecord, exists := existingMap[feed.ExportId]; exists {
				// 更新现有记录
				updateData := map[string]interface{}{
					"predict_commission_amount": feed.PredictCommissionAmount,
					"product_id":                feed.ProductInfo.ProductId,
					"product_name":              feed.ProductInfo.ProductName,
					"product_img_url":           feed.ProductInfo.ProductImgUrl,
					"product_mini_price":        feed.ProductInfo.ProductMiniPrice,
					"feed_token":                feedToken,
					"promoter_share_link":       promoterShareLink,
					"updated_at":                time.Now(),
				}

				if err := db.Model(&model.TempVideoList{}).Where("id = ?", existingRecord.ID).
					Updates(updateData).Error; err != nil {
					l.Errorf("更新达人 %s 的短视频记录失败 (export_id: %s): %v",
						talent.TalentAppid, feed.ExportId, err)
				} else {
					l.Infof("成功更新达人 %s 的短视频记录 (export_id: %s)",
						talent.TalentAppid, feed.ExportId)
				}
			} else {
				// 创建新记录
				newRecord := &model.TempVideoList{
					ExportID:                feed.ExportId,
					TalentAppid:             talent.TalentAppid,
					PredictCommissionAmount: feed.PredictCommissionAmount,
					ProductID:               feed.ProductInfo.ProductId,
					ProductName:             feed.ProductInfo.ProductName,
					ProductImgUrl:           feed.ProductInfo.ProductImgUrl,
					ProductMiniPrice:        feed.ProductInfo.ProductMiniPrice,
					FeedToken:               feedToken,
					PromoterShareLink:       promoterShareLink,
				}

				if err := db.Create(newRecord).Error; err != nil {
					l.Errorf("插入达人 %s 的新短视频记录失败 (export_id: %s): %v",
						talent.TalentAppid, feed.ExportId, err)
				} else {
					l.Infof("成功创建达人 %s 的短视频记录 (export_id: %s, id: %d)",
						talent.TalentAppid, feed.ExportId, newRecord.ID)
				}
			}
		}

		// 判断是否还有下一页
		if !resp.HasMore || resp.NextKey == "" {
			l.Infof("达人 %s 没有更多页面，分页获取完成", talent.TalentAppid)
			break
		}

		// 更新nextKey用于获取下一页
		nextKey = resp.NextKey
		pageIndex++

		// 添加延迟，避免请求过于频繁
		time.Sleep(time.Millisecond * 500)
	}

	l.Infof("达人 %s 总共获取到 %d 条短视频记录，共 %d 页", talent.TalentAppid, totalFeeds, pageIndex)

	// 删除已不存在的记录
	if len(allApiFeedIds) > 0 {
		var currentExportIds []string
		for exportId := range allApiFeedIds {
			currentExportIds = append(currentExportIds, exportId)
		}

		if err := db.Where("talent_appid = ? AND export_id NOT IN ?", talent.TalentAppid, currentExportIds).
			Delete(&model.TempVideoList{}).Error; err != nil {
			l.Errorf("删除达人 %s 的过期短视频记录失败: %v", talent.TalentAppid, err)
		} else {
			l.Infof("已删除达人 %s 的过期短视频记录", talent.TalentAppid)
		}
	} else {
		// 如果API没有返回任何短视频，删除该达人的所有短视频记录
		if err := db.Unscoped().Where("talent_appid = ?", talent.TalentAppid).
			Delete(&model.TempVideoList{}).Error; err != nil {
			l.Errorf("删除达人 %s 的所有短视频记录失败: %v", talent.TalentAppid, err)
		} else {
			l.Infof("已删除达人 %s 的所有短视频记录（API无返回数据）", talent.TalentAppid)
		}
	}
}

// 获取短视频推广信息
func (l *SyncRemoteFeedListLogic) getFeedPromotionInfo(ctx context.Context, wxClient *client.WechatClient, token string, talentAppid string, feedList []wxmodels.FeedInfo) map[string]wxmodels.FeedInfoResp {
	promotionMap := make(map[string]wxmodels.FeedInfoResp)

	// 准备推广请求
	var feedReqs []wxmodels.FeedInfoReq
	for _, feed := range feedList {
		feedReqs = append(feedReqs, wxmodels.FeedInfoReq{
			ExportId: feed.ExportId,
		})
	}

	// 构建获取推广信息的请求
	promotionReq := &wxmodels.GetFeedPromotionInfoRequest{
		TalentAppid:      talentAppid,
		MiniProgramAppid: l.svcCtx.Config.WxApp.AppId,
		FeedList:         feedReqs,
		SharerAppid:      "", // 这里暂时为空，实际使用时可能需要具体的推客appid
	}

	// 调用获取推广信息API
	promotionResp, err := wxClient.GetFeedPromotionInfo(ctx, promotionReq, token)
	if err != nil {
		l.Errorf("获取短视频推广信息失败(talent_appid=%s): %v", talentAppid, err)
		return promotionMap
	}

	// 将获取到的推广信息添加到映射中
	if promotionResp != nil {
		for _, promo := range promotionResp.FeedList {
			promotionMap[promo.ExportId] = promo
		}
	}

	return promotionMap
}
