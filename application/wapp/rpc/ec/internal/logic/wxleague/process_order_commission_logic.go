package wxleaguelogic

import (
	"context"
	"fmt"
	"time"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"
)

type ProcessOrderCommissionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewProcessOrderCommissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ProcessOrderCommissionLogic {
	return &ProcessOrderCommissionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *ProcessOrderCommissionLogic) ProcessOrderCommission(in *pb.ProcessOrderCommissionReq) (*pb.BoolRequest, error) {
	l.Infof("处理订单佣金，订单ID: %s", in.OrderId)
	// 查询订单信息
	var orderInfo model.CommissionOrders
	if err := l.svcCtx.GetSlaveDB().Where("order_id = ?", in.OrderId).First(&orderInfo).Error; err != nil {
		l.Errorf("查询订单记录失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询订单记录失败: %v", err))
	}

	// 福利订单
	if orderInfo.BenefitsPoolNo > 0 {
		l.ProcessBenefitsOrderCommission(in)
		return &pb.BoolRequest{Success: true}, nil
	}

	// 创建分布式锁，防止同一订单被并发处理
	lockKey := fmt.Sprintf("order_lock:%s", in.OrderId)
	lock := redis.NewRedisLock(l.svcCtx.BizRedis, lockKey)
	// 设置锁超时时间为10秒
	lock.SetExpire(10)

	// 尝试获取锁
	acquired, err := lock.Acquire()
	if err != nil {
		l.Errorf("获取分布式锁失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取分布式锁失败: %v", err))
	}

	if !acquired {
		l.Infof("订单 %s 正在被其他进程处理，请稍后重试", in.OrderId)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("订单 %s 正在被其他进程处理，请稍后重试", in.OrderId))
	}

	// 确保在函数结束时释放锁
	defer func() {
		if released, err := lock.Release(); err != nil {
			l.Errorf("释放分布式锁失败: %v", err)
		} else if !released {
			l.Infof("分布式锁已过期或被其他进程释放")
		}
	}()

	// 查询订单信息
	var commissionOrder model.CommissionOrders
	if err := l.svcCtx.GetSlaveDB().Where("order_id = ?", in.OrderId).First(&commissionOrder).Error; err != nil {
		l.Errorf("查询订单记录失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询订单记录失败: %v", err))
	}

	// 打印订单各个状态
	l.Infof("订单状态: %s", commissionOrder.OrderStatus)
	l.Infof("佣金单状态: %s", commissionOrder.CommissionStatus)
	l.Infof("余额处理状态: %s", commissionOrder.BalanceDealStatus)
	l.Infof("提现余额处理状态: %s", commissionOrder.WithdrawDealStatus)

	sharerAppid := commissionOrder.SharerAppid
	// 获取用户信息
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("sharer_appid = ?", sharerAppid).First(&user).Error; err != nil {
		l.Errorf("获取用户信息失败: %v", err)
		// return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取用户信息失败: %v", err))
		// 没有shareappid则返回，如达人单
		l.Debugf("订单 %s shareAppid为空，不处理佣金", in.OrderId, commissionOrder.OrderStatus)
		return &pb.BoolRequest{Success: true}, nil
	}

	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.GetSlaveDB().Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", user.Level).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取佣金配置失败: %v", err))
	}
	// 当前推客获取佣金
	userCommissionAmount := int64(float64(commissionOrder.ServiceAmount) * (commissionConfig.CommissionRate / 100))
	commissionOrder.SharerRatio = int(commissionConfig.CommissionRate * 10000) // 转为微信的比例
	commissionOrder.SharerAmount = int(userCommissionAmount)                   // 转为分
	// 计算代理商佣金 (15%)
	agentCommissionAmount := int64(float64(commissionOrder.ServiceAmount) * 0.15)
	// 计算运营商佣金 (5%)
	operatorCommissionAmount := int64(float64(commissionOrder.ServiceAmount) * 0.05)

	// 更新订单状态
	if err := l.svcCtx.MasterDB.Model(&commissionOrder).Updates(model.CommissionOrders{
		SharerRatio:  commissionOrder.SharerRatio,
		SharerAmount: commissionOrder.SharerAmount,
	}).Error; err != nil {
		l.Errorf("更新订单状态失败: %v", err)
		return &pb.BoolRequest{Success: true}, nil
	}
	// 检查订单状态是否支持处理
	if commissionOrder.OrderStatus != "20" && commissionOrder.OrderStatus != "200" && commissionOrder.OrderStatus != "250" {
		l.Infof("订单 %s 状态为 %s，不处理佣金", in.OrderId, commissionOrder.OrderStatus)
		return &pb.BoolRequest{Success: true}, nil
	}

	var amountChange int64
	needBalanceDeal := false
	needWithdrawDeal := false
	// 根据订单状态设置处理状态
	if commissionOrder.OrderStatus == "20" { // 付款状态
		amountChange = int64(commissionOrder.SharerAmount)
		if commissionOrder.BalanceDealStatus == "1" {
			// 已经处理了，不做处理
		} else {
			commissionOrder.BalanceDealStatus = "1"
			needBalanceDeal = true
		}
	} else if commissionOrder.OrderStatus == "200" || commissionOrder.OrderStatus == "250" { // 200-售后取消，250-未付款取消
		amountChange = -int64(commissionOrder.SharerAmount)
		if commissionOrder.BalanceDealStatus == "1" { // 原来处理过，现在可以做取消处理
			commissionOrder.BalanceDealStatus = "2"
			needBalanceDeal = true
		}
	}
	// 根据佣金单状态设置处理状态
	if commissionOrder.CommissionStatus == "100" { // 完成状态
		amountChange = int64(commissionOrder.SharerAmount)
		if commissionOrder.WithdrawDealStatus == "1" {
			// 已经处理了，不做处理
		} else {
			commissionOrder.WithdrawDealStatus = "1"
			needWithdrawDeal = true
		}
	} else if commissionOrder.CommissionStatus == "200" { // 200-取消结算
		amountChange = -int64(commissionOrder.SharerAmount)
		if commissionOrder.WithdrawDealStatus == "1" { // 原来处理过，现在可以做取消处理
			commissionOrder.WithdrawDealStatus = "2"
			needWithdrawDeal = true
		}
	}

	// 打印是否需要处理
	l.Infof("是否需要处理余额: %v", needBalanceDeal)
	l.Infof("是否需要处理可提现余额: %v", needWithdrawDeal)

	// 使用事务处理
	if err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		if needBalanceDeal {
			// 用户余额处理
			// 更新用户余额
			// 将分转为元
			// amountChangeYuan := float64(amountChange) / 100.0

			if err := tx.Model(&user).Update("balance", gorm.Expr("balance + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户余额失败: %v", err))
			}

			// 查询用户原始余额
			var originAmount int64 = 0
			var lastBalanceRecord model.TaskSettlementDetail
			if err := tx.Model(&model.TaskSettlementDetail{}).
				Where("user_id = ? AND account_type = ?", user.ID, "balance").
				Order("created_at DESC").
				Limit(1).
				First(&lastBalanceRecord).Error; err == nil {
				// 找到记录，计算原始余额
				originAmount = lastBalanceRecord.Amount + lastBalanceRecord.AmountChange
			} else if err != gorm.ErrRecordNotFound {
				// 查询出错
				l.Errorf("查询用户原始余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询用户原始余额失败: %v", err))
			}

			// 添加结算明细(余额变动)
			settlementDetail := model.TaskSettlementDetail{
				Amount:       originAmount,
				AmountChange: amountChange,
				Type:         1,         // 直接佣金
				AccountType:  "balance", // 类型
				UserID:       uint64(user.ID),
				AssociatedID: in.OrderId,
				Remark:       fmt.Sprintf("直接销售佣金"),
				CreateTime:   time.Now(),
			}

			if err := tx.Create(&settlementDetail).Error; err != nil {
				l.Errorf("创建结算明细失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("创建结算明细失败: %v", err))
			}

			// 处理间接佣金 - 查找上级推客
			uplinePromoter, err := l.findUplinePromoter(tx, uint64(user.ID), 0)
			if err != nil {
				l.Infof("查找上级推客失败，跳过间接佣金处理: %v", err)
			} else if uplinePromoter != nil {
				// 计算间接佣金
				indirectAmount := amountChange // 间接佣金=直接佣金金额

				// 将分转为元
				// indirectAmountYuan := float64(indirectAmount) / 100.0

				// 更新上级推客余额
				if err := tx.Model(uplinePromoter).Update("balance", gorm.Expr("balance + ?", indirectAmount)).Error; err != nil {
					l.Infof("更新上级推客余额失败: %v", err)
				} else {
					// 查询上级推客原始余额
					var uplineOriginAmount int64 = 0
					var uplineLastBalanceRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", uplinePromoter.ID, "balance").
						Order("created_at DESC").
						Limit(1).
						First(&uplineLastBalanceRecord).Error; err == nil {
						// 找到记录，计算原始余额
						uplineOriginAmount = uplineLastBalanceRecord.Amount + uplineLastBalanceRecord.AmountChange
					}

					// 添加上级推客结算明细
					uplineSettlementDetail := model.TaskSettlementDetail{
						Amount:       uplineOriginAmount,
						AmountChange: indirectAmount,
						Type:         2,         // 间接佣金
						AccountType:  "balance", // 类型
						UserID:       uint64(uplinePromoter.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("朋友圈销售佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&uplineSettlementDetail).Error; err != nil {
						l.Infof("创建上级推客结算明细失败: %v", err)
					}
				}
			}

			// 代理商和运营商分佣
			l.Infof("查找上级代理商和运营商")

			// 查找上级代理商
			agentUser, err := l.findUplineUserByRole(tx, uint64(user.ID), config.RoleAgent)
			if err != nil {
				l.Infof("查找上级代理商失败: %v", err)
			} else if agentUser != nil {
				// 确保佣金方向与主佣金一致（正负）
				if amountChange < 0 {
					agentCommissionAmount = -abs(agentCommissionAmount)
				}
				agentCommissionYuan := float64(agentCommissionAmount) / 100.0

				// 更新代理商余额
				updateField := "balance"
				accountType := "balance"
				// if needWithdrawDeal {
				// 	updateField = "withdraw"
				// 	accountType = "withdraw"
				// }

				if err := tx.Model(agentUser).Update(updateField, gorm.Expr(updateField+" + ?", agentCommissionYuan)).Error; err != nil {
					l.Infof("更新代理商%s失败: %v", updateField, err)
				} else {
					// 查询代理商原始余额
					var originAmount int64 = 0
					var lastRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", agentUser.ID, accountType).
						Order("created_at DESC").
						Limit(1).
						First(&lastRecord).Error; err == nil {
						originAmount = lastRecord.Amount + lastRecord.AmountChange
					}

					// 添加代理商结算明细
					agentSettlementDetail := model.TaskSettlementDetail{
						Amount:       originAmount,
						AmountChange: agentCommissionAmount,
						Type:         3, // 代理商佣金
						AccountType:  accountType,
						UserID:       uint64(agentUser.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("代理商佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&agentSettlementDetail).Error; err != nil {
						l.Infof("创建代理商结算明细失败: %v", err)
					}
				}
			}

			// 查找运营商
			operatorUser, err := l.findUplineUserByRole(tx, uint64(user.ID), config.RoleOperator)
			if err != nil {
				l.Infof("查找运营商失败: %v", err)
			} else if operatorUser != nil {
				// 确保佣金方向与主佣金一致（正负）
				if amountChange < 0 {
					operatorCommissionAmount = -abs(operatorCommissionAmount)
				}
				operatorCommissionYuan := float64(operatorCommissionAmount) / 100.0

				// 更新运营商余额
				updateField := "balance"
				accountType := "balance"

				if err := tx.Model(operatorUser).Update(updateField, gorm.Expr(updateField+" + ?", operatorCommissionYuan)).Error; err != nil {
					l.Infof("更新运营商%s失败: %v", updateField, err)
				} else {
					// 查询运营商原始余额
					var originAmount int64 = 0
					var lastRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", operatorUser.ID, accountType).
						Order("created_at DESC").
						Limit(1).
						First(&lastRecord).Error; err == nil {
						originAmount = lastRecord.Amount + lastRecord.AmountChange
					}

					// 添加运营商结算明细
					operatorSettlementDetail := model.TaskSettlementDetail{
						Amount:       originAmount,
						AmountChange: operatorCommissionAmount,
						Type:         4, // 运营商佣金
						AccountType:  accountType,
						UserID:       uint64(operatorUser.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("运营商佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&operatorSettlementDetail).Error; err != nil {
						l.Infof("创建运营商结算明细失败: %v", err)
					}
				}
			}
		}
		if needWithdrawDeal {
			// 用户可提现金额处理
			// 将分转为元
			// amountChangeYuan := float64(amountChange) / 100.0

			// 更新可提现金额
			if err := tx.Model(&user).Update("withdraw", gorm.Expr("withdraw + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户可提现金额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户可提现金额失败: %v", err))
			}
			// 更新结算金额
			if err := tx.Model(&user).Update("settlement", gorm.Expr("settlement + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户已结算金额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户已结算金额失败: %v", err))
			}
			// 查询用户原始可提现金额
			var originWithdrawAmount int64 = 0
			var lastWithdrawRecord model.TaskSettlementDetail
			if err := tx.Model(&model.TaskSettlementDetail{}).
				Where("user_id = ? AND account_type = ?", user.ID, "withdraw").
				Order("created_at DESC").
				Limit(1).
				First(&lastWithdrawRecord).Error; err == nil {
				// 找到记录，计算原始余额
				originWithdrawAmount = lastWithdrawRecord.Amount + lastWithdrawRecord.AmountChange
			} else if err != gorm.ErrRecordNotFound {
				// 查询出错
				l.Errorf("查询用户原始余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询用户原始余额失败: %v", err))
			}
			// 添加结算明细(可提现余额变动)
			settlementDetail := model.TaskSettlementDetail{
				Amount:       originWithdrawAmount,
				AmountChange: amountChange,
				Type:         1,          // 直接佣金
				AccountType:  "withdraw", // 类型
				UserID:       uint64(user.ID),
				AssociatedID: in.OrderId,
				Remark:       fmt.Sprintf("直接销售佣金结算"),
				CreateTime:   time.Now(),
			}

			if err := tx.Create(&settlementDetail).Error; err != nil {
				l.Errorf("创建结算明细(可提现余额变动)失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("创建结算明细(可提现余额变动)失败: %v", err))
			}

			// 处理间接佣金 - 查找上级推客
			uplinePromoter, err := l.findUplinePromoter(tx, uint64(user.ID), 0)
			if err != nil {
				l.Infof("查找上级推客失败，跳过间接佣金处理: %v", err)
			} else if uplinePromoter != nil {
				// 计算间接佣金
				indirectAmount := amountChange // 间接佣金=直接佣金金额

				// 将分转为元
				// indirectAmountYuan := float64(indirectAmount) / 100.0

				// 更新上级推客可提现余额
				if err := tx.Model(uplinePromoter).Update("withdraw", gorm.Expr("withdraw + ?", indirectAmount)).Error; err != nil {
					l.Infof("更新上级推客可提现余额失败: %v", err)
				} else {
					// 查询上级推客原始可提现余额
					var uplineOriginAmount int64 = 0
					var uplineLastWithdrawRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", uplinePromoter.ID, "withdraw").
						Order("created_at DESC").
						Limit(1).
						First(&uplineLastWithdrawRecord).Error; err == nil {
						// 找到记录，计算原始余额
						uplineOriginAmount = uplineLastWithdrawRecord.Amount + uplineLastWithdrawRecord.AmountChange
					}

					// 添加上级推客结算明细
					uplineSettlementDetail := model.TaskSettlementDetail{
						Amount:       uplineOriginAmount,
						AmountChange: indirectAmount,
						Type:         2,          // 间接佣金
						AccountType:  "withdraw", // 类型为可提现余额
						UserID:       uint64(uplinePromoter.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("朋友圈销售佣金结算"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&uplineSettlementDetail).Error; err != nil {
						l.Infof("创建上级推客结算明细(可提现余额)失败: %v", err)
					}
				}
			}

			// 查找上级代理商
			agentUser, err := l.findUplineUserByRole(tx, uint64(user.ID), config.RoleAgent)
			if err != nil {
				l.Infof("查找上级代理商失败: %v", err)
			} else if agentUser != nil {
				// 确保佣金方向与主佣金一致（正负）
				if amountChange < 0 {
					agentCommissionAmount = -abs(agentCommissionAmount)
				}
				agentCommissionYuan := float64(agentCommissionAmount) / 100.0

				// 更新代理商余额
				updateField := "withdraw"
				accountType := "withdraw"

				if err := tx.Model(agentUser).Update(updateField, gorm.Expr(updateField+" + ?", agentCommissionYuan)).Error; err != nil {
					l.Infof("更新代理商%s失败: %v", updateField, err)
				} else {
					// 查询代理商原始余额
					var originAmount int64 = 0
					var lastRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", agentUser.ID, accountType).
						Order("created_at DESC").
						Limit(1).
						First(&lastRecord).Error; err == nil {
						originAmount = lastRecord.Amount + lastRecord.AmountChange
					}

					// 添加代理商结算明细
					agentSettlementDetail := model.TaskSettlementDetail{
						Amount:       originAmount,
						AmountChange: agentCommissionAmount,
						Type:         3, // 代理商佣金
						AccountType:  accountType,
						UserID:       uint64(agentUser.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("代理商佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&agentSettlementDetail).Error; err != nil {
						l.Infof("创建代理商结算明细失败: %v", err)
					}

					// 这里增加代理商分佣给业务员处理(记录下代理商给业务员分佣详情）
					// 检查当前用户是否是业务员
					if user.RoleId == config.RoleSalesman {
						// 查询代理商-业务员关系
						var agentRelation model.AgentRelation
						if err := tx.Where("salesman_user_id = ? AND agent_user_id = ?", user.ID, agentUser.ID).First(&agentRelation).Error; err != nil {
							l.Infof("未找到代理商(%d)与业务员(%d)的关联关系: %v", agentUser.ID, user.ID, err)
						} else {
							// 计算业务员应得佣金(代理商佣金 * 分佣比例)
							salesmanCommissionAmount := int64(float64(agentCommissionAmount) * agentRelation.SalesmanRate / 100)
							l.Infof("业务员佣金计算: agentAmount=%d, rate=%.2f%%, salesmanAmount=%d",
								agentCommissionAmount, agentRelation.SalesmanRate, salesmanCommissionAmount)

							// 创建分佣明细记录
							agentCommissionDetail := model.AgentCommissionDetail{
								OrderID:        in.OrderId,
								OrderAmount:    int64(commissionOrder.ActualPayment),
								AgentAmount:    agentCommissionAmount,
								SalesmanAmount: salesmanCommissionAmount,
								AgentUserID:    uint64(agentUser.ID),
								SalesmanUserID: uint64(user.ID),
								Remark:         fmt.Sprintf("订单分佣 - %s", accountType),
							}

							if err := tx.Create(&agentCommissionDetail).Error; err != nil {
								l.Infof("创建代理商-业务员分佣明细失败: %v", err)
							} else {
								l.Infof("创建代理商-业务员分佣明细成功: 订单ID=%s, 代理商=%d, 业务员=%d",
									in.OrderId, agentUser.ID, user.ID)
							}
						}
					}
				}
			}

			// 查找运营商
			operatorUser, err := l.findUplineUserByRole(tx, uint64(user.ID), config.RoleOperator)
			if err != nil {
				l.Infof("查找运营商失败: %v", err)
			} else if operatorUser != nil {
				// 确保佣金方向与主佣金一致（正负）
				if amountChange < 0 {
					operatorCommissionAmount = -abs(operatorCommissionAmount)
				}
				operatorCommissionYuan := float64(operatorCommissionAmount) / 100.0

				// 更新运营商余额
				updateField := "withdraw"
				accountType := "withdraw"

				if err := tx.Model(operatorUser).Update(updateField, gorm.Expr(updateField+" + ?", operatorCommissionYuan)).Error; err != nil {
					l.Infof("更新运营商%s失败: %v", updateField, err)
				} else {
					// 查询运营商原始余额
					var originAmount int64 = 0
					var lastRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", operatorUser.ID, accountType).
						Order("created_at DESC").
						Limit(1).
						First(&lastRecord).Error; err == nil {
						originAmount = lastRecord.Amount + lastRecord.AmountChange
					}

					// 添加运营商结算明细
					operatorSettlementDetail := model.TaskSettlementDetail{
						Amount:       originAmount,
						AmountChange: operatorCommissionAmount,
						Type:         4, // 运营商佣金
						AccountType:  accountType,
						UserID:       uint64(operatorUser.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("运营商佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&operatorSettlementDetail).Error; err != nil {
						l.Infof("创建运营商结算明细失败: %v", err)
					}
				}
			}
		}

		// 更新订单状态
		if err := tx.Model(&commissionOrder).Updates(model.CommissionOrders{
			WithdrawDealStatus: commissionOrder.WithdrawDealStatus,
			BalanceDealStatus:  commissionOrder.BalanceDealStatus,
			SharerRatio:        commissionOrder.SharerRatio,
			SharerAmount:       commissionOrder.SharerAmount,
		}).Error; err != nil {
			l.Errorf("更新订单状态失败: %v", err)
			return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新订单状态失败: %v", err))
		}

		return nil
	}); err != nil {
		l.Errorf("订单佣金处理失败: %v", err)
		return &pb.BoolRequest{Success: false}, err
	}

	l.Infof("订单佣金处理成功, 订单ID: %s, 用户ID: %d, 状态: %s", in.OrderId, user.ID, commissionOrder.OrderStatus)
	return &pb.BoolRequest{Success: true}, nil
}

func (l *ProcessOrderCommissionLogic) ProcessBenefitsOrderCommission(in *pb.ProcessOrderCommissionReq) (*pb.BoolRequest, error) {
	l.Infof("处理订单佣金，订单ID: %s", in.OrderId)

	// 创建分布式锁，防止同一订单被并发处理
	lockKey := fmt.Sprintf("order_lock:%s", in.OrderId)
	lock := redis.NewRedisLock(l.svcCtx.BizRedis, lockKey)
	// 设置锁超时时间为10秒
	lock.SetExpire(10)

	// 尝试获取锁
	acquired, err := lock.Acquire()
	if err != nil {
		l.Errorf("获取分布式锁失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取分布式锁失败: %v", err))
	}

	if !acquired {
		l.Infof("订单 %s 正在被其他进程处理，请稍后重试", in.OrderId)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("订单 %s 正在被其他进程处理，请稍后重试", in.OrderId))
	}

	// 确保在函数结束时释放锁
	defer func() {
		if released, err := lock.Release(); err != nil {
			l.Errorf("释放分布式锁失败: %v", err)
		} else if !released {
			l.Infof("分布式锁已过期或被其他进程释放")
		}
	}()

	// 查询订单信息
	var commissionOrder model.CommissionOrders
	if err := l.svcCtx.GetSlaveDB().Where("order_id = ?", in.OrderId).First(&commissionOrder).Error; err != nil {
		l.Errorf("查询订单记录失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询订单记录失败: %v", err))
	}

	// 打印订单各个状态
	l.Infof("订单状态: %s", commissionOrder.OrderStatus)
	l.Infof("佣金单状态: %s", commissionOrder.CommissionStatus)
	l.Infof("余额处理状态: %s", commissionOrder.BalanceDealStatus)
	l.Infof("提现余额处理状态: %s", commissionOrder.WithdrawDealStatus)

	// sharerAppid := commissionOrder.SharerAppid
	// 获取用户信息
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ?", commissionOrder.BenefitsReferSeller).First(&user).Error; err != nil {
		l.Errorf("获取用户信息失败: %v", err)
		// return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取用户信息失败: %v", err))
		// 没有shareappid则返回，如达人单
		l.Debugf("订单 %s shareAppid为空，不处理佣金", in.OrderId, commissionOrder.OrderStatus)
		return &pb.BoolRequest{Success: true}, nil
	}

	var commissionConfig model.UserCommissionConfig
	if err := l.svcCtx.GetSlaveDB().Model(&model.UserCommissionConfig{}).
		Where("level = ? AND status = 1", user.Level).
		First(&commissionConfig).Error; err != nil {
		l.Errorf("获取佣金配置失败: %v", err)
		return &pb.BoolRequest{Success: false}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取佣金配置失败: %v", err))
	}
	// 当前推客获取佣金
	userCommissionAmount := int64(float64(commissionOrder.ServiceAmount) * (commissionConfig.CommissionRate / 100))
	commissionOrder.SharerRatio = int(commissionConfig.CommissionRate * 10000) // 转为微信的比例
	commissionOrder.SharerAmount = int(userCommissionAmount)                   // 转为分

	// 更新订单状态
	if err := l.svcCtx.MasterDB.Model(&commissionOrder).Updates(model.CommissionOrders{
		SharerRatio:  commissionOrder.SharerRatio,
		SharerAmount: commissionOrder.SharerAmount,
	}).Error; err != nil {
		l.Errorf("更新订单状态失败: %v", err)
		return &pb.BoolRequest{Success: true}, nil
	}
	// 检查订单状态是否支持处理
	if commissionOrder.OrderStatus != "20" && commissionOrder.OrderStatus != "200" && commissionOrder.OrderStatus != "250" {
		l.Infof("订单 %s 状态为 %s，不处理佣金", in.OrderId, commissionOrder.OrderStatus)
		return &pb.BoolRequest{Success: true}, nil
	}

	var amountChange int64
	needBalanceDeal := false
	needWithdrawDeal := false
	// 根据订单状态设置处理状态
	if commissionOrder.OrderStatus == "20" { // 付款状态
		amountChange = int64(commissionOrder.SharerAmount)
		if commissionOrder.BalanceDealStatus == "1" {
			// 已经处理了，不做处理
		} else {
			commissionOrder.BalanceDealStatus = "1"
			needBalanceDeal = true
		}
	} else if commissionOrder.OrderStatus == "200" || commissionOrder.OrderStatus == "250" { // 200-售后取消，250-未付款取消
		amountChange = -int64(commissionOrder.SharerAmount)
		if commissionOrder.BalanceDealStatus == "1" { // 原来处理过，现在可以做取消处理
			commissionOrder.BalanceDealStatus = "2"
			needBalanceDeal = true
		}
	}
	// 根据佣金单状态设置处理状态
	if commissionOrder.CommissionStatus == "100" { // 完成状态
		amountChange = int64(commissionOrder.SharerAmount)
		if commissionOrder.WithdrawDealStatus == "1" {
			// 已经处理了，不做处理
		} else {
			commissionOrder.WithdrawDealStatus = "1"
			needWithdrawDeal = true
		}
	} else if commissionOrder.CommissionStatus == "200" { // 200-取消结算
		amountChange = -int64(commissionOrder.SharerAmount)
		if commissionOrder.WithdrawDealStatus == "1" { // 原来处理过，现在可以做取消处理
			commissionOrder.WithdrawDealStatus = "2"
			needWithdrawDeal = true
		}
	}

	// 打印是否需要处理
	l.Infof("是否需要处理余额: %v", needBalanceDeal)
	l.Infof("是否需要处理可提现余额: %v", needWithdrawDeal)

	// 使用事务处理
	if err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		if needBalanceDeal {
			// 用户余额处理
			// 更新用户余额
			// 将分转为元
			// amountChangeYuan := float64(amountChange) / 100.0

			if err := tx.Model(&user).Update("balance", gorm.Expr("balance + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户余额失败: %v", err))
			}

			// 查询用户原始余额
			var originAmount int64 = 0
			var lastBalanceRecord model.TaskSettlementDetail
			if err := tx.Model(&model.TaskSettlementDetail{}).
				Where("user_id = ? AND account_type = ?", user.ID, "balance").
				Order("created_at DESC").
				Limit(1).
				First(&lastBalanceRecord).Error; err == nil {
				// 找到记录，计算原始余额
				originAmount = lastBalanceRecord.Amount + lastBalanceRecord.AmountChange
			} else if err != gorm.ErrRecordNotFound {
				// 查询出错
				l.Errorf("查询用户原始余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询用户原始余额失败: %v", err))
			}

			// 添加结算明细(余额变动)
			settlementDetail := model.TaskSettlementDetail{
				Amount:       originAmount,
				AmountChange: amountChange,
				Type:         1,         // 直接佣金
				AccountType:  "balance", // 类型
				UserID:       uint64(user.ID),
				AssociatedID: in.OrderId,
				Remark:       fmt.Sprintf("直接销售佣金"),
				CreateTime:   time.Now(),
			}

			if err := tx.Create(&settlementDetail).Error; err != nil {
				l.Errorf("创建结算明细失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("创建结算明细失败: %v", err))
			}

			// 处理间接佣金 - 查找上级推客
			uplinePromoter, err := l.findUplinePromoter(tx, uint64(user.ID), 0)
			if err != nil {
				l.Infof("查找上级推客失败，跳过间接佣金处理: %v", err)
			} else if uplinePromoter != nil {
				// 计算间接佣金
				indirectAmount := amountChange // 间接佣金=直接佣金金额

				// 将分转为元
				// indirectAmountYuan := float64(indirectAmount) / 100.0

				// 更新上级推客余额
				if err := tx.Model(uplinePromoter).Update("balance", gorm.Expr("balance + ?", indirectAmount)).Error; err != nil {
					l.Infof("更新上级推客余额失败: %v", err)
				} else {
					// 查询上级推客原始余额
					var uplineOriginAmount int64 = 0
					var uplineLastBalanceRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", uplinePromoter.ID, "balance").
						Order("created_at DESC").
						Limit(1).
						First(&uplineLastBalanceRecord).Error; err == nil {
						// 找到记录，计算原始余额
						uplineOriginAmount = uplineLastBalanceRecord.Amount + uplineLastBalanceRecord.AmountChange
					}

					// 添加上级推客结算明细
					uplineSettlementDetail := model.TaskSettlementDetail{
						Amount:       uplineOriginAmount,
						AmountChange: indirectAmount,
						Type:         2,         // 间接佣金
						AccountType:  "balance", // 类型
						UserID:       uint64(uplinePromoter.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("朋友圈销售佣金"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&uplineSettlementDetail).Error; err != nil {
						l.Infof("创建上级推客结算明细失败: %v", err)
					}
				}
			}

		}
		if needWithdrawDeal {
			// 用户可提现金额处理
			// 将分转为元
			// amountChangeYuan := float64(amountChange) / 100.0

			// 更新可提现金额
			if err := tx.Model(&user).Update("withdraw", gorm.Expr("withdraw + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户可提现金额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户可提现金额失败: %v", err))
			}
			// 更新结算金额
			if err := tx.Model(&user).Update("settlement", gorm.Expr("settlement + ?", amountChange)).Error; err != nil {
				l.Errorf("更新用户已结算金额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新用户已结算金额失败: %v", err))
			}
			// 查询用户原始可提现金额
			var originWithdrawAmount int64 = 0
			var lastWithdrawRecord model.TaskSettlementDetail
			if err := tx.Model(&model.TaskSettlementDetail{}).
				Where("user_id = ? AND account_type = ?", user.ID, "withdraw").
				Order("created_at DESC").
				Limit(1).
				First(&lastWithdrawRecord).Error; err == nil {
				// 找到记录，计算原始余额
				originWithdrawAmount = lastWithdrawRecord.Amount + lastWithdrawRecord.AmountChange
			} else if err != gorm.ErrRecordNotFound {
				// 查询出错
				l.Errorf("查询用户原始余额失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("查询用户原始余额失败: %v", err))
			}
			// 添加结算明细(可提现余额变动)
			settlementDetail := model.TaskSettlementDetail{
				Amount:       originWithdrawAmount,
				AmountChange: amountChange,
				Type:         1,          // 直接佣金
				AccountType:  "withdraw", // 类型
				UserID:       uint64(user.ID),
				AssociatedID: in.OrderId,
				Remark:       fmt.Sprintf("直接销售佣金结算"),
				CreateTime:   time.Now(),
			}

			if err := tx.Create(&settlementDetail).Error; err != nil {
				l.Errorf("创建结算明细(可提现余额变动)失败: %v", err)
				return xerr.NewErrMsg(l.ctx, fmt.Sprintf("创建结算明细(可提现余额变动)失败: %v", err))
			}

			// 处理间接佣金 - 查找上级推客
			uplinePromoter, err := l.findUplinePromoter(tx, uint64(user.ID), 0)
			if err != nil {
				l.Infof("查找上级推客失败，跳过间接佣金处理: %v", err)
			} else if uplinePromoter != nil {
				// 计算间接佣金
				indirectAmount := amountChange // 间接佣金=直接佣金金额

				// 将分转为元
				// indirectAmountYuan := float64(indirectAmount) / 100.0

				// 更新上级推客可提现余额
				if err := tx.Model(uplinePromoter).Update("withdraw", gorm.Expr("withdraw + ?", indirectAmount)).Error; err != nil {
					l.Infof("更新上级推客可提现余额失败: %v", err)
				} else {
					// 查询上级推客原始可提现余额
					var uplineOriginAmount int64 = 0
					var uplineLastWithdrawRecord model.TaskSettlementDetail
					if err := tx.Model(&model.TaskSettlementDetail{}).
						Where("user_id = ? AND account_type = ?", uplinePromoter.ID, "withdraw").
						Order("created_at DESC").
						Limit(1).
						First(&uplineLastWithdrawRecord).Error; err == nil {
						// 找到记录，计算原始余额
						uplineOriginAmount = uplineLastWithdrawRecord.Amount + uplineLastWithdrawRecord.AmountChange
					}

					// 添加上级推客结算明细
					uplineSettlementDetail := model.TaskSettlementDetail{
						Amount:       uplineOriginAmount,
						AmountChange: indirectAmount,
						Type:         2,          // 间接佣金
						AccountType:  "withdraw", // 类型为可提现余额
						UserID:       uint64(uplinePromoter.ID),
						FromUser:     uint64(user.ID),
						AssociatedID: in.OrderId,
						Remark:       fmt.Sprintf("朋友圈销售佣金结算"),
						CreateTime:   time.Now(),
					}

					if err := tx.Create(&uplineSettlementDetail).Error; err != nil {
						l.Infof("创建上级推客结算明细(可提现余额)失败: %v", err)
					}
				}
			}
		}

		// 更新订单状态
		if err := tx.Model(&commissionOrder).Updates(model.CommissionOrders{
			WithdrawDealStatus: commissionOrder.WithdrawDealStatus,
			BalanceDealStatus:  commissionOrder.BalanceDealStatus,
			SharerRatio:        commissionOrder.SharerRatio,
			SharerAmount:       commissionOrder.SharerAmount,
		}).Error; err != nil {
			l.Errorf("更新订单状态失败: %v", err)
			return xerr.NewErrMsg(l.ctx, fmt.Sprintf("更新订单状态失败: %v", err))
		}

		return nil
	}); err != nil {
		l.Errorf("订单佣金处理失败: %v", err)
		return &pb.BoolRequest{Success: false}, err
	}

	l.Infof("订单佣金处理成功, 订单ID: %s, 用户ID: %d, 状态: %s", in.OrderId, user.ID, commissionOrder.OrderStatus)
	return &pb.BoolRequest{Success: true}, nil
}

// 递归查询，查找到第一个sharer_appid不为空的用户(间接推客)
func (l *ProcessOrderCommissionLogic) findUplinePromoter(tx *gorm.DB, userId uint64, depth int) (*model.User, error) {
	// 递归查询，使用WITH RECURSIVE语法查找上级推客
	query := `WITH RECURSIVE user_hierarchy AS (
		-- 初始查询：获取当前用户的上级ID
		SELECT id, invite_from, sharer_appid
		FROM user_user
		WHERE id = ?

		UNION ALL

		-- 递归查询：获取上级的上级
		SELECT u.id, u.invite_from, u.sharer_appid
		FROM user_user u
		INNER JOIN user_hierarchy h ON u.id = h.invite_from
	)
	-- 查找第一个sharer_appid不为空的用户
	SELECT id, invite_from, sharer_appid
	FROM user_hierarchy
	WHERE id != ? AND sharer_appid != ''
	LIMIT 1;`

	type Result struct {
		ID          int64
		InviteFrom  int64
		SharerAppid string
	}

	var result Result
	if err := tx.Raw(query, userId, userId).Scan(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有找到符合条件的上级推客
			l.Errorf("没有找到符合条件的上级推客: %v", err)
			return nil, nil
		}
		return nil, err
	}

	// 如果找到了结果，再查询完整的用户信息
	if result.ID > 0 {
		var uplineUser model.User
		if err := tx.First(&uplineUser, result.ID).Error; err != nil {
			return nil, err
		}
		return &uplineUser, nil
	}

	return nil, nil
}

// 递归查询，查找到第一个特定角色的上级用户
func (l *ProcessOrderCommissionLogic) findUplineUserByRole(tx *gorm.DB, userId uint64, roleId int8) (*model.User, error) {
	// 递归查询，使用WITH RECURSIVE语法查找特定角色的上级用户
	query := `WITH RECURSIVE user_hierarchy AS (
		-- 初始查询：获取当前用户的上级ID
		SELECT id, invite_from, role_id
		FROM user_user
		WHERE id = ?

		UNION ALL

		-- 递归查询：获取上级的上级
		SELECT u.id, u.invite_from, u.role_id
		FROM user_user u
		INNER JOIN user_hierarchy h ON u.id = h.invite_from
	)
	-- 查找第一个指定角色的用户
	SELECT id, invite_from, role_id
	FROM user_hierarchy
	WHERE id != ? AND role_id = ?
	LIMIT 1;`

	type Result struct {
		ID         int64
		InviteFrom int64
		RoleId     int
	}

	var result Result
	if err := tx.Raw(query, userId, userId, roleId).Scan(&result).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// 没有找到符合条件的上级用户
			l.Errorf("没有找到角色ID为%d的上级用户: %v", roleId, err)
			return nil, nil
		}
		return nil, err
	}

	// 如果找到了结果，再查询完整的用户信息
	if result.ID > 0 {
		var uplineUser model.User
		if err := tx.First(&uplineUser, result.ID).Error; err != nil {
			return nil, err
		}
		return &uplineUser, nil
	}

	return nil, nil
}

// 辅助函数：取绝对值
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}
