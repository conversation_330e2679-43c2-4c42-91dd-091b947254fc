package wxleaguelogic

import (
	"context"
	"fmt"
	"time"
	"xj-serv/pkg/common"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	wxpayclient "xj-serv/pkg/wxPay/client"
	"xj-serv/pkg/wxPay/models"
	"xj-serv/pkg/wxPay/wxpay_utility"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"
)

type WithdrawLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawLogic {
	return &WithdrawLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 用户提现
func (l *WithdrawLogic) Withdraw(in *pb.WithdrawReq) (*pb.WithdrawResp, error) {
	// 参数验证
	if in.UserId == 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	if in.Amount <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 金额转换为分（微信支付使用分为单位）
	amountFen := int64(in.Amount * 100)

	// 金额范围验证
	if amountFen < l.svcCtx.Config.WxPay.Transfer.MinAmount {
		minAmountYuan := float64(l.svcCtx.Config.WxPay.Transfer.MinAmount) / 100
		return nil, xerr.NewErrMsg(l.ctx, fmt.Sprintf("提现金额不能少于%.2f元", minAmountYuan))
	}

	// 生成订单号
	outBillNo := common.GenerateOrderNo(in.UserId)

	// 创建分布式锁，防止同一用户并发提现
	lockKey := fmt.Sprintf("user_withdraw_lock:%d", in.UserId)
	lock := redis.NewRedisLock(l.svcCtx.BizRedis, lockKey)
	// 设置锁超时时间为10秒
	lock.SetExpire(10)

	// 尝试获取锁
	acquired, err := lock.Acquire()
	if err != nil {
		logx.WithContext(l.ctx).Errorf("获取分布式锁失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "系统繁忙，请稍后重试")
	}

	if !acquired {
		return nil, xerr.NewErrMsg(l.ctx, "您有提现正在处理中，请稍后再试")
	}

	// 确保在函数结束时释放锁
	defer func() {
		if released, err := lock.Release(); err != nil {
			logx.WithContext(l.ctx).Errorf("释放分布式锁失败: %v", err)
		} else if !released {
			logx.WithContext(l.ctx).Infof("分布式锁已过期或被其他进程释放")
		}
	}()

	// 开始数据库事务
	return l.processWithdrawInTransaction(in, outBillNo, amountFen)
}

// 在事务中处理提现
func (l *WithdrawLogic) processWithdrawInTransaction(in *pb.WithdrawReq, outBillNo string, amountFen int64) (*pb.WithdrawResp, error) {
	var result *pb.WithdrawResp
	var txErr error

	// 执行数据库事务
	err := l.svcCtx.MasterDB.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 查询用户信息并加锁
		var user model.User
		if err := tx.Select("id", "withdraw", "openid", "role_id").
			Where("id = ?", in.UserId).
			Set("gorm:query_option", "FOR UPDATE").
			First(&user).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				txErr = xerr.NewErrMsg(l.ctx, "用户不存在")
				return txErr
			}
			logx.WithContext(l.ctx).Errorf("查询用户信息失败: %v", err)
			txErr = xerr.NewErrMsg(l.ctx, "查询用户信息失败")
			return txErr
		}

		// 检查openid是否存在
		if user.Openid == "" {
			txErr = xerr.NewErrMsg(l.ctx, "用户未绑定微信，无法提现")
			return txErr
		}

		// 2. 检查可提现余额
		if uint64(amountFen) > uint64(user.Withdraw) {
			txErr = xerr.NewErrMsg(l.ctx, "可提现余额不足")
			return txErr
		}

		// 3. 扣减用户可提现余额（预扣，锁定资金）
		if err := tx.Model(&user).
			Where("id = ? AND withdraw >= ?", user.ID, amountFen).
			Update("withdraw", gorm.Expr("withdraw - ?", amountFen)).Error; err != nil {
			logx.WithContext(l.ctx).Errorf("扣减用户余额失败: %v", err)
			txErr = xerr.NewErrMsg(l.ctx, "扣减用户余额失败")
			return txErr
		}

		// 检查更新是否成功（防止并发问题）
		var updatedRows int64
		if err := tx.Model(&user).Where("id = ?", user.ID).Count(&updatedRows).Error; err != nil {
			logx.WithContext(l.ctx).Errorf("检查余额更新失败: %v", err)
			txErr = xerr.NewErrMsg(l.ctx, "余额检查失败")
			return txErr
		}

		// 4. 判断用户角色，确定是否需要审核
		needsReview := l.userNeedsReview(user.RoleId, amountFen)

		// 计算手续费和实际到账金额
		feeRatio := l.svcCtx.Config.WxPay.Transfer.FeeRatio
		fee := uint64(float64(amountFen) * feeRatio) // 手续费（分）
		actualAmount := uint64(amountFen) - fee      // 实际到账金额（分）

		logx.WithContext(l.ctx).Infof("手续费计算: 申请金额=%d分, 手续费率=%.2f%%, 手续费=%d分, 实际到账=%d分",
			amountFen, feeRatio*100, fee, actualAmount)

		// 创建提现记录
		withdrawRecord := &model.WithdrawRecord{
			UserID:        uint64(user.ID),
			Amount:        uint64(amountFen), // 申请提现金额
			ActualAmount:  actualAmount,      // 实际到账金额
			Fee:           fee,               // 手续费
			OutBillNo:     outBillNo,
			TransferScene: l.svcCtx.Config.WxPay.Transfer.DefaultScene, // 从配置读取默认转账场景
			Status:        model.WithdrawStatusPending,
			ApplyTime:     time.Now(),
			Remark: fmt.Sprintf("用户申请提现，申请金额：%.2f元，手续费：%.2f元，实际到账：%.2f元",
				float64(amountFen)/100, float64(fee)/100, float64(actualAmount)/100),
		}

		// 根据用户角色设置审核状态
		if needsReview {
			withdrawRecord.ReviewStatus = model.ReviewStatusPending
		} else {
			withdrawRecord.ReviewStatus = model.ReviewStatusApproved
		}

		if err := tx.Create(withdrawRecord).Error; err != nil {
			logx.WithContext(l.ctx).Errorf("创建提现记录失败: %v", err)
			txErr = xerr.NewErrMsg(l.ctx, "创建提现记录失败")
			return txErr
		}

		// 5. 如果需要审核，创建记录后直接返回，等待审核
		if needsReview {
			result = &pb.WithdrawResp{
				OutBillNo:    outBillNo,
				PackageInfo:  "",
				Desc:         "提现申请已提交，等待管理员审核",
				ReviewStatus: int32(withdrawRecord.ReviewStatus),
				Amount:       uint64(amountFen), // 申请提现金额
				ActualAmount: actualAmount,      // 实际到账金额
				Fee:          fee,               // 手续费
				FeeRatio:     feeRatio,          // 手续费率
			}
			logx.WithContext(l.ctx).Infof("用户提现申请已提交等待审核: 用户ID=%d, 申请金额=%d分, 实际到账=%d分, 手续费=%d分, 商户单号=%s",
				in.UserId, amountFen, actualAmount, fee, outBillNo)
			return nil
		}

		// 6. 普通用户直接调用微信支付转账接口（使用实际到账金额）
		transferResp, transferErr := l.callWechatTransfer(user.Openid, outBillNo, int64(actualAmount))
		if transferErr != nil {
			// 转账失败，更新记录状态
			logx.WithContext(l.ctx).Errorf("微信转账失败: %v", transferErr)

			// 构建更新数据，避免访问nil指针
			updateData := map[string]interface{}{
				"status":       model.WithdrawStatusFailed,
				"fail_reason":  transferErr.GetErrMsg(),
				"process_time": time.Now(),
			}

			// 如果transferResp不为nil，则包含state信息
			if transferResp != nil {
				updateData["state"] = transferResp.State
			}

			updateErr := tx.Model(withdrawRecord).Updates(updateData).Error
			if updateErr != nil {
				logx.WithContext(l.ctx).Errorf("更新提现记录状态失败: %v", updateErr)
			}

			txErr = transferErr
			return txErr
		}

		// 7. 转账成功，更新记录
		updateData := map[string]interface{}{
			"status":           model.WithdrawStatusProcess,
			"state":            transferResp.State,
			"transfer_bill_no": *transferResp.TransferBillNo,
			"process_time":     time.Now(),
		}

		if err := tx.Model(withdrawRecord).Updates(updateData).Error; err != nil {
			logx.WithContext(l.ctx).Errorf("更新提现记录失败: %v", err)
			txErr = xerr.NewErrMsg(l.ctx, "更新提现记录失败")
			return txErr
		}

		// 8. 构造返回结果
		result = &pb.WithdrawResp{
			OutBillNo:    outBillNo,
			PackageInfo:  *transferResp.PackageInfo,
			Desc:         "提现处理中",
			ReviewStatus: int32(withdrawRecord.ReviewStatus),
			Amount:       uint64(amountFen), // 申请提现金额
			ActualAmount: actualAmount,      // 实际到账金额
			Fee:          fee,               // 手续费
			FeeRatio:     feeRatio,          // 手续费率
		}

		return nil
	})

	if err != nil {
		if txErr != nil {
			return nil, xerr.NewErrMsg(l.ctx, "提现处理失败，请稍后重试")
		}
		logx.WithContext(l.ctx).Errorf("提现事务执行失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "提现处理失败")
	}

	// 记录成功日志
	logx.WithContext(l.ctx).Infof("用户提现处理成功: 用户ID=%d, 金额=%d分, 商户单号=%s",
		in.UserId, amountFen, outBillNo)

	return result, nil
}

// 判断用户是否需要审核
func (l *WithdrawLogic) userNeedsReview(roleId int8, amount int64) bool {
	needsReview := false
	// 代理商(3)和运营商(4)需要审核
	if roleId == config.RoleAgent || roleId == config.RoleOperator {
		logx.WithContext(l.ctx).Infof("用户为代理商或运营商，需要审核")
		needsReview = true
	}
	// 提现金额超过最大值也需要审核
	if amount > l.svcCtx.Config.WxPay.Transfer.MaxAmount {
		logx.WithContext(l.ctx).Infof("提现金额超过最大值，需要审核")
		needsReview = true
	}
	return needsReview
}

// 调用微信支付转账接口
func (l *WithdrawLogic) callWechatTransfer(openid, outBillNo string, amount int64) (*models.TransferToUserResponse, *xerr.CodeError) {
	// 创建微信支付商户配置
	mchConfig, err := wxpay_utility.CreateMchConfig(
		l.svcCtx.Config.WxPay.MchId,
		l.svcCtx.Config.WxPay.CertificateSerialNo,
		l.svcCtx.Config.WxPay.PrivateKeyPath,
		l.svcCtx.Config.WxPay.WechatPayPublicKeyId,
		l.svcCtx.Config.WxPay.WechatPayPublicKeyPath,
	)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("创建微信支付配置失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "微信支付配置错误")
	}

	// 初始化微信支付客户端
	wxPayClient := wxpayclient.NewWxPayClient(mchConfig)

	// 构造转账请求
	transferReq := &models.TransferToUserRequest{
		Appid:           wxpay_utility.String(l.svcCtx.Config.WxPay.AppId),
		OutBillNo:       wxpay_utility.String(outBillNo),
		TransferSceneId: wxpay_utility.String(models.TransferSceneCommissionReward), // 佣金奖励
		Openid:          wxpay_utility.String(openid),
		TransferAmount:  wxpay_utility.Int64(amount),
		TransferRemark:  wxpay_utility.String("用户提现到微信零钱"),
		NotifyUrl:       wxpay_utility.String(l.svcCtx.Config.WxPay.NotifyUrl),
		TransferSceneReportInfos: []models.TransferSceneReportInfo{
			{
				InfoType:    wxpay_utility.String("岗位类型"),
				InfoContent: wxpay_utility.String("推客"),
			},
			{
				InfoType:    wxpay_utility.String("报酬说明"),
				InfoContent: wxpay_utility.String("佣金报酬"),
			},
		},
	}

	// 记录转账请求详细信息
	logx.WithContext(l.ctx).Infof("微信转账请求: 商户单号=%s, 转账金额=%d分(%.2f元), openid=%s, 场景=%s",
		outBillNo, amount, float64(amount)/100, openid, models.TransferSceneCommissionReward)

	// 调用转账接口
	transferResp, err := wxPayClient.TransferToUser(l.ctx, transferReq)
	if err != nil {
		logx.WithContext(l.ctx).Errorf("微信转账失败详情: 商户单号=%s, 转账金额=%d分, 错误信息=%v",
			outBillNo, amount, err)
		return nil, xerr.NewErrMsg(l.ctx, err.Error())
	}

	logx.WithContext(l.ctx).Infof("微信支付转账成功: 商户单号=%s, 微信单号=%s",
		outBillNo, *transferResp.TransferBillNo)

	return transferResp, nil
}
