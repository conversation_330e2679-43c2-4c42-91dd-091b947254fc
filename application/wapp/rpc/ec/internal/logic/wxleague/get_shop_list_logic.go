package wxleaguelogic

import (
	"context"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShopListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetShopListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShopListLogic {
	return &GetShopListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取合作小店列表
func (l *GetShopListLogic) GetShopList(in *pb.ShopListGetReq) (*pb.ShopListGetResp, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix) // 确认Token来源
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.ShopListGetRequest{
		PageSize: int(in.PageSize),
		NextKey:  in.NextKey,
	}

	// 调用微信API
	resp, err := wxClient.GetShopList(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取合作小店列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	var shopList []*pb.ShopListItemDetail
	for _, shop := range resp.ShopList {
		shopList = append(shopList, &pb.ShopListItemDetail{
			BaseInfo: &pb.BizBaseInfo{ // 假设BizBaseInfo已在proto中定义
				Appid:      shop.BaseInfo.Appid,
				HeadImgUrl: shop.BaseInfo.HeadImgUrl,
				Nickname:   shop.BaseInfo.Nickname,
			},
			Status: int32(shop.Status),
		})
	}

	return &pb.ShopListGetResp{
		ShopList: shopList,
		NextKey:  resp.NextKey,
		HasMore:  resp.HasMore,
	}, nil
}
