package wxleaguelogic

import (
	"context"
	"time"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetSalesmanListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetSalesmanListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSalesmanListLogic {
	return &GetSalesmanListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取员工列表
func (l *GetSalesmanListLogic) GetSalesmanList(in *pb.GetSalesmanListReq) (*pb.GetSalesmanListResp, error) {
	l.Infof("获取代理商ID为%d的员工列表", in.UserId)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证用户角色是否为代理商
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 检查用户是否为代理商角色
	if user.RoleId != config.RoleAgent {
		l.Errorf("用户ID %d 不是代理商角色，无法获取员工列表", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "只有代理商可以获取员工列表")
	}

	// 分页参数
	pageNo := int64(1)
	pageSize := int64(10)
	if in.PageReq != nil {
		pageNo = int64(in.PageReq.PageNo)
		pageSize = int64(in.PageReq.PageSize)
	}
	offset := (pageNo - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// 使用JOIN查询关联User表和AgentRelation表
	db := l.svcCtx.GetSlaveDB().Table("agent_relation").
		Joins("LEFT JOIN user_user ON agent_relation.salesman_user_id = user_user.id").
		Where("agent_relation.agent_user_id = ?", in.UserId)

	// 处理手机号关键词过滤
	if in.PageReq.Keyword != "" {
		db = db.Where("user_user.mobile LIKE ?", "%"+in.PageReq.Keyword+"%")
	}

	// 查询结果结构
	type Result struct {
		model.AgentRelation
		Mobile   string `gorm:"column:mobile"`
		NickName string `gorm:"column:nick_name"`
		Avatar   string `gorm:"column:avatar"`
	}

	var results []Result
	var total int64

	// 查询总数
	if err := db.Count(&total).Error; err != nil {
		l.Errorf("查询员工总数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询员工总数失败")
	}

	// 如果没有数据，直接返回空结果
	if total == 0 {
		return &pb.GetSalesmanListResp{
			List:  []*pb.SalesmanItem{},
			Total: 0,
		}, nil
	}

	// 查询列表
	if err := db.
		Select("agent_relation.*, user_user.mobile, user_user.nick_name, user_user.avatar").
		Order("agent_relation.id DESC").
		Offset(int(offset)).
		Limit(int(pageSize)).
		Find(&results).Error; err != nil {
		l.Errorf("查询员工列表失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询员工列表失败")
	}

	// 构建员工列表
	salesmanList := make([]*pb.SalesmanItem, 0, len(results))
	for _, result := range results {
		// 手动对手机号进行脱敏处理
		maskedMobile := common.InfoMask(result.Mobile)

		salesmanList = append(salesmanList, &pb.SalesmanItem{
			UserId:     result.SalesmanUserID,
			NickName:   result.NickName,
			Avatar:     result.Avatar,
			Mobile:     maskedMobile,
			Rate:       result.SalesmanRate,
			Remark:     result.SalesmanRemark,
			IsFullTime: result.IsFullTime,
			CreatedAt:  result.CreatedAt.Format(time.DateTime),
		})
	}

	return &pb.GetSalesmanListResp{
		List:  salesmanList,
		Total: uint64(total),
	}, nil
}
