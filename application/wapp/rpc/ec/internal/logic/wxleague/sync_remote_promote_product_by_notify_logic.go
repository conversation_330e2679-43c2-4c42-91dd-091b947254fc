package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/common"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemotePromoteProductByNotifyLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemotePromoteProductByNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemotePromoteProductByNotifyLogic {
	return &SyncRemotePromoteProductByNotifyLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// SyncRemotePromoteProductByNotify 通过通知同步单个推广商品
// 机构推广--定向推广--机构|商家分佣
// Deprecated
func (l *SyncRemotePromoteProductByNotifyLogic) SyncRemotePromoteProductByNotify(in *pb.SyncRemotePromoteProductByNotifyReq) (*pb.BoolRequest, error) {
	// 参数校验
	if in.ProductId <= 0 {
		l.Errorf("商品ID不能为空")
		return nil, fmt.Errorf("商品ID不能为空")
	}

	if in.ShopAppid == "" {
		l.Errorf("添加商品时，店铺AppID不能为空")
		return nil, fmt.Errorf("添加商品时，店铺AppID不能为空")
	}

	// 如果消息类型不是我们需要处理的类型，则直接返回成功
	if in.MsgName != "head_supplier_item_update" && in.MsgName != "head_supplier_subscribe_product_planinfo_update" {
		l.Infof("不处理的消息类型: %s, ProductID: %d", in.MsgName, in.ProductId)
		return &pb.BoolRequest{
			Success: true,
		}, nil
	}

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 处理删除操作
	// status字段更新时删除商品
	if in.MsgName == "head_supplier_item_update" && in.EventType == 2 && common.StringInSlice("status", in.UpdateFields) {
		// 调用删除商品的方法
		success, err := l.deleteProduct(int64(in.ProductId))
		if err != nil {
			return nil, err
		}

		return &pb.BoolRequest{
			Success: success,
		}, nil
	}

	// 处理机构推广--普通推广的消息
	if in.MsgName == "head_supplier_subscribe_product_planinfo_update" && in.PlanType == 2 && in.EventType == 2 {
		// 处理机构推广--普通推广--删除(结束)或删除的情况
		if in.PlanStatus == 2 || in.PlanStatus == 4 {
			l.Infof("处理机构推广--普通推广--删除，ProductID: %d, PlanStatus: %d", in.ProductId, in.PlanStatus)
			// 调用删除商品的方法
			success, err := l.deleteProduct(int64(in.ProductId))
			if err != nil {
				return nil, err
			}

			return &pb.BoolRequest{
				Success: success,
			}, nil
		}

		// 处理机构推广--普通推广--新增|编辑的情况
		if in.PlanStatus == 1 {
			l.Infof("处理机构推广--普通推广--新增|编辑，ProductID: %d", in.ProductId)
			// 继续执行下面的添加或更新操作
		}
	}

	// 处理添加或更新操作
	l.Infof("开始添加/更新商品，ProductID: %d, ShopAppID: %s, MsgName: %s, PlanType: %d, PlanStatus: %d",
		in.ProductId, in.ShopAppid, in.MsgName, in.PlanType, in.PlanStatus)

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetPromoteProductDetailRequest{
		ShopAppid:          in.ShopAppid,
		ProductId:          in.ProductId,
		PlanType:           int(in.PlanType), // 使用传入的计划类型
		GetAvailableCoupon: false,
	}

	// 如果未指定计划类型，则默认使用定向计划
	//if in.PlanType == 0 {
	//	req.PlanType = 1 // 商品的计划类型 1：定向计划
	//}

	// 调用微信API获取商品详情
	detailResp, err := wxClient.GetPromoteProductDetail(l.ctx, req, token)
	if err != nil {
		l.Errorf("请求信息：%+v\n", in)
		l.Errorf("获取商品详情失败，ProductID: %s, ShopAppID: %s PlanType: %d 错误: %v", in.ProductId, in.ShopAppid, in.PlanType, err)
		return nil, fmt.Errorf("获取商品详情失败: %v", err)
	}
	// 打印 detailResp
	fmt.Println("detailResp:", detailResp)

	// 检查API响应是否成功
	if !detailResp.IsSuccess() {
		l.Errorf("获取商品详情API返回错误，ProductID: %s, ErrCode: %d, ErrMsg: %s",
			in.ProductId, detailResp.ErrCode, detailResp.ErrMsg)
		return nil, fmt.Errorf("获取商品详情API返回错误: %s", detailResp.ErrMsg)
	}

	// 将商品基本信息转换为JSON字符串
	headImgsJSON, _ := json.Marshal(detailResp.Product.ProductInfo.HeadImgs)
	descInfoJSON, _ := json.Marshal(detailResp.Product.ProductInfo.DescInfo)
	skusJSON, _ := json.Marshal(detailResp.Product.ProductInfo.Skus)
	commissionInfoJSON, _ := json.Marshal(detailResp.Product.CommissionInfo)

	// 查找该商品是否已存在
	var existingProduct model.TempProduct
	result := db.Where("product_id = ? AND shop_appid = ?", in.ProductId, in.ShopAppid).First(&existingProduct)

	// 设置缓存过期时间为24小时
	expiresAt := time.Now().Add(24 * time.Hour)

	// 计算最低价格
	var minPrice int64 = -1
	for _, sku := range detailResp.Product.ProductInfo.Skus {
		if minPrice == -1 || sku.SalePrice < minPrice {
			minPrice = sku.SalePrice
		}
	}

	// 按最低价格计算佣金
	commissionAmount := int64(float64(minPrice) * (float64(detailResp.Product.CommissionInfo.ServiceRatio) / 1000000))

	// 处理分类信息
	cat1, cat2, cat3 := l.processCategoryInfo(detailResp.Product.ProductInfo.CatsV2)
	fmt.Println("cat1:", cat1, "cat2:", cat2, "cat3:", cat3)

	// 构建商品数据
	tempProduct := model.TempProduct{
		ProductId:        int64(in.ProductId),
		ShopAppid:        in.ShopAppid,
		Title:            detailResp.Product.ProductInfo.Title,
		SubTitle:         detailResp.Product.ProductInfo.SubTitle,
		HeadImgs:         string(headImgsJSON),
		DescInfo:         string(descInfoJSON),
		Skus:             string(skusJSON),
		CommissionInfo:   string(commissionInfoJSON),
		CommissionAmount: commissionAmount,
		MinPrice:         minPrice,
		OnSale:           1, // 默认为在售状态
		Cat1:             cat1,
		Cat2:             cat2,
		Cat3:             cat3,
		ExpiresAt:        expiresAt,
		SyncFrom:         "notify",
	}

	// 如果存在则更新，不存在则创建
	if result.Error == nil {
		// 更新现有记录
		if err := db.Model(&existingProduct).Updates(tempProduct).Error; err != nil {
			l.Errorf("更新商品 失败: %v", err)
		} else {
			// 更新分类的显示状态
			l.updateCategoryShowToVisible(cat1)
		}
	} else {
		// 创建新记录
		if err := db.Create(&tempProduct).Error; err != nil {
			l.Errorf("创建商品 失败: %v", err)
		} else {
			// 更新分类的显示状态
			l.updateCategoryShowToVisible(cat1)
		}
	}

	l.Infof("成功同步商品信息，ProductID: %s", in.ProductId)
	return &pb.BoolRequest{
		Success: true,
	}, nil
}

// processCategoryInfo 处理商品分类信息，返回最多3级分类ID
// 从catsV2中找到ShopProductsCategory表中最接近level=3的记录
// 最大满足返回level=1,2,3的结果
// 如果找到的最大level是2或1，则cat2或cat3可以返回0
func (l *SyncRemotePromoteProductByNotifyLogic) processCategoryInfo(catsV2 []wxmodels.CatInfo) (cat1, cat2, cat3 int) {
	if len(catsV2) == 0 {
		return 0, 0, 0
	}

	// 提取分类ID
	var catIDs []uint
	for _, cat := range catsV2 {
		catID, err := strconv.ParseUint(cat.CatId, 10, 32)
		if err == nil {
			catIDs = append(catIDs, uint(catID))
		} else {
			l.Errorf("转换分类ID失败: %v", err)
		}
	}

	if len(catIDs) == 0 {
		return 0, 0, 0
	}

	// 查询分类信息，按level降序排列，优先获取level较高的分类
	var categories []*model.ShopProductsCategory
	if err := l.svcCtx.GetSlaveDB().Where("cat_id IN ?", catIDs).Order("level DESC").Find(&categories).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return 0, 0, 0
	}

	// 如果没有找到任何分类记录
	if len(categories) == 0 {
		return 0, 0, 0
	}

	// 按level分组存储分类
	levelMap := make(map[int8][]*model.ShopProductsCategory)
	maxLevel := int8(0)

	for _, category := range categories {
		levelMap[category.Level] = append(levelMap[category.Level], category)
		if category.Level > maxLevel {
			maxLevel = category.Level
		}
	}

	// 从最高level开始，找到对应的分类ID
	// 如果找到level=3的分类，需要通过FCatID向上查找level=2和level=1的分类
	if maxLevel == 3 && len(levelMap[3]) > 0 {
		level3Cat := levelMap[3][0]
		cat3 = int(level3Cat.CatID)

		// 查找level=2的父分类
		var level2Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 2", level3Cat.FCatID).First(&level2Cat).Error; err == nil {
			cat2 = int(level2Cat.CatID)

			// 查找level=1的父分类
			var level1Cat model.ShopProductsCategory
			if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
				cat1 = int(level1Cat.CatID)
			}
		}
	} else if maxLevel == 2 && len(levelMap[2]) > 0 {
		// 如果最高level是2，直接使用level=2的分类，并查找其父分类
		level2Cat := levelMap[2][0]
		cat2 = int(level2Cat.CatID)

		// 查找level=1的父分类
		var level1Cat model.ShopProductsCategory
		if err := l.svcCtx.GetSlaveDB().Where("cat_id = ? AND level = 1", level2Cat.FCatID).First(&level1Cat).Error; err == nil {
			cat1 = int(level1Cat.CatID)
		}
	} else if maxLevel == 1 && len(levelMap[1]) > 0 {
		// 如果最高level是1，直接使用level=1的分类
		cat1 = int(levelMap[1][0].CatID)
	}

	return cat1, cat2, cat3
}

// updateCategoryShowToVisible 更新分类的显示状态为可见
// 当添加或更新商品时，如果对应的一级分类show=0，则更新为1
func (l *SyncRemotePromoteProductByNotifyLogic) updateCategoryShowToVisible(cat1 int) {
	if cat1 <= 0 {
		return
	}

	db := l.svcCtx.MasterDB
	var category model.ShopProductsCategory

	// 查询分类信息
	if err := db.Where("cat_id = ? AND level = 1", cat1).First(&category).Error; err != nil {
		l.Errorf("查询分类信息失败: %v", err)
		return
	}

	// 如果show=0，则更新为1
	if category.Show == 0 {
		if err := db.Model(&model.ShopProductsCategory{}).Where("cat_id = ? AND level = 1", cat1).Update("show", 1).Error; err != nil {
			l.Errorf("更新分类显示状态失败: %v", err)
		} else {
			l.Infof("成功更新分类显示状态，CatID: %d, Show: 1", cat1)
		}
	}
}

// updateCategoryShowStatus 更新分类的显示状态
// 当删除商品时，检查是否还有其他商品使用该分类，如果没有则将show设为0
func (l *SyncRemotePromoteProductByNotifyLogic) updateCategoryShowStatus(cat1 int) {
	if cat1 <= 0 {
		return
	}

	db := l.svcCtx.MasterDB

	// 检查是否还有其他商品使用该分类
	var count int64
	if err := db.Model(&model.TempProduct{}).Where("cat1 = ?", cat1).Count(&count).Error; err != nil {
		l.Errorf("查询使用分类的商品数量失败: %v", err)
		return
	}

	// 如果没有其他商品使用该分类，则将show设为0
	if count == 0 {
		if err := db.Model(&model.ShopProductsCategory{}).Where("cat_id = ? AND level = 1", cat1).Update("show", 0).Error; err != nil {
			l.Errorf("更新分类显示状态失败: %v", err)
		} else {
			l.Infof("成功更新分类显示状态，CatID: %d, Show: 0", cat1)
		}
	}
}

// deleteProduct 删除商品并更新相关分类状态
func (l *SyncRemotePromoteProductByNotifyLogic) deleteProduct(productId int64) (bool, error) {
	l.Infof("开始删除商品，ProductID: %d", productId)

	db := l.svcCtx.MasterDB

	// 获取要删除的商品信息，用于后续更新分类显示状态
	var productToDelete model.TempProduct
	result := db.Where("product_id = ?", productId).First(&productToDelete)

	// 从数据库中删除商品
	if err := db.Unscoped().Where("product_id = ?", productId).Delete(&model.TempProduct{}).Error; err != nil {
		l.Errorf("删除商品失败，ProductID: %d, 错误: %v", productId, err)
		return false, fmt.Errorf("删除商品失败: %v", err)
	}

	// 如果之前查询到了商品信息，则更新分类显示状态
	if result.Error == nil && productToDelete.Cat1 > 0 {
		l.updateCategoryShowStatus(productToDelete.Cat1)
	}

	l.Infof("成功删除商品，ProductID: %d", productId)
	return true, nil
}
