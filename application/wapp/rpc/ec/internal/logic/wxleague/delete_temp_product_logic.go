package wxleaguelogic

import (
	"context"
	"fmt"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteTempProductLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteTempProductLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTempProductLogic {
	return &DeleteTempProductLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// DeleteTempProduct 删除临时商品（同时删除temp_talent_products和temp_products表中的数据）
func (l *DeleteTempProductLogic) DeleteTempProduct(in *pb.DeleteTalentProductReq) (*pb.BoolRequest, error) {
	// 参数校验
	if in.ShopAppid == "" {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "店铺AppID不能为空")
	}
	if in.ProductId == 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "商品ID不能为空")
	}

	l.Infof("开始删除临时商品: shop_appid=%s, product_id=%d", in.ShopAppid, in.ProductId)

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 删除temp_talent_products表中的商品记录
	talentResult := db.Unscoped().Where("shop_appid = ? AND product_id = ?", in.ShopAppid, in.ProductId).Delete(&model.TempTalentProduct{})
	if talentResult.Error != nil {
		l.Errorf("删除达人商品失败: %v", talentResult.Error)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("删除达人商品失败: %v", talentResult.Error))
	}

	// 删除temp_products表中的商品记录
	tempResult := db.Unscoped().Where("shop_appid = ? AND product_id = ?", in.ShopAppid, in.ProductId).Delete(&model.TempProduct{})
	if tempResult.Error != nil {
		l.Errorf("删除临时商品失败: %v", tempResult.Error)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, fmt.Sprintf("删除临时商品失败: %v", tempResult.Error))
	}

	// 删除temp_product_subscriptions表中的订阅记录
	subscriptionResult := db.Unscoped().Where("shop_appid = ? AND product_id = ?", in.ShopAppid, in.ProductId).Delete(&model.TempProductSubscription{})
	if subscriptionResult.Error != nil {
		l.Errorf("删除商品订阅记录失败: %v", subscriptionResult.Error)
		// 不中断流程，继续执行
	} else {
		l.Infof("删除商品订阅记录: shop_appid=%s, product_id=%d, 影响行数: %d", 
			in.ShopAppid, in.ProductId, subscriptionResult.RowsAffected)
	}

	// 检查是否找到并删除了记录
	totalRowsAffected := talentResult.RowsAffected + tempResult.RowsAffected + subscriptionResult.RowsAffected
	if totalRowsAffected == 0 {
		l.Infof("未找到匹配的商品记录: shop_appid=%s, product_id=%d", in.ShopAppid, in.ProductId)
		// 这里选择返回成功，因为最终目标是确保数据不存在
	} else {
		l.Infof("成功删除商品记录: shop_appid=%s, product_id=%d, 影响行数: %d (达人商品: %d, 临时商品: %d, 订阅记录: %d)",
			in.ShopAppid, in.ProductId, totalRowsAffected, talentResult.RowsAffected, tempResult.RowsAffected, subscriptionResult.RowsAffected)

		// 成功删除商品后，调用微信客户端取消订阅
		// 获取访问令牌
		token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
			l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
		if err != nil {
			l.Errorf("获取微信访问令牌失败: %v", err)
		} else {
			// 初始化微信客户端
			wxClient := client.NewWechatClient()

			// 构建取消订阅请求
			req := &wxmodels.UnsubscribeProductRequest{
				ProductId: uint64(in.ProductId),
			}

			// 调用微信API取消订阅商品
			err := wxClient.UnsubscribeProduct(l.ctx, req, token)
			if err != nil {
				l.Errorf("取消订阅商品失败: shop_appid=%s, product_id=%d, error=%v",
					in.ShopAppid, in.ProductId, err)
			} else {
				l.Infof("成功取消订阅商品: shop_appid=%s, product_id=%d", in.ShopAppid, in.ProductId)
			}
		}
	}

	return &pb.BoolRequest{
		Success: true,
	}, nil
}
