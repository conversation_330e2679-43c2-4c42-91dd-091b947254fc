package wxleaguelogic

import (
	"context"
	"errors"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"sync"
	"time"
	"xj-serv/pkg/common"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// 任务状态常量
const (
	TaskStatusNotStarted = 0 // 未执行
	TaskStatusRunning    = 1 // 执行中
	TaskStatusFailed     = 2 // 执行失败
	TaskStatusCompleted  = 3 // 已完成
)

type TalentUserUpgradeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTalentUserUpgradeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TalentUserUpgradeLogic {
	return &TalentUserUpgradeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// TalentUserUpgrade 用户升级判定服务
func (l *TalentUserUpgradeLogic) TalentUserUpgrade(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 获取主库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 检查今日是否已执行成功
	today := time.Now().Format("2006-01-02")
	var settlementLog model.TaskSettlementLog

	// 查询今日记录
	err := db.Where("date = ? AND type = ?", today, "user_upgrade").First(&settlementLog).Error
	if err == nil && settlementLog.Completed == TaskStatusCompleted {
		// 今日已执行成功，不做处理
		l.Infof("今日(%s)用户升级判定已执行成功，不再重复执行", today)
		return &pb.EmptyRequest{}, nil
	}

	// 创建或更新记录
	if err != nil { // 记录不存在
		settlementLog = model.TaskSettlementLog{
			Date:        today,
			Type:        "user_upgrade",
			Completed:   TaskStatusNotStarted,
			LastUpdated: time.Now(),
		}
		if err := db.Create(&settlementLog).Error; err != nil {
			l.Errorf("创建结算日志记录失败: %v", err)
			return nil, fmt.Errorf("创建结算日志记录失败: %v", err)
		}
	} else if settlementLog.Completed == TaskStatusNotStarted {
		// 更新状态为处理中
		if err := db.Model(&model.TaskSettlementLog{}).
			Where("id = ?", settlementLog.ID).
			Updates(map[string]interface{}{
				"completed":    TaskStatusRunning,
				"last_updated": time.Now(),
			}).Error; err != nil {
			l.Errorf("更新结算日志状态失败: %v", err)
			return nil, fmt.Errorf("更新结算日志状态失败: %v", err)
		}
		settlementLog.Completed = TaskStatusRunning
	}

	// 开始处理用户升级
	err = l.processUserUpgrade(settlementLog.ID)
	if err != nil {
		// 处理失败，更新状态为执行失败
		updateErr := db.Model(&model.TaskSettlementLog{}).
			Where("id = ?", settlementLog.ID).
			Updates(map[string]interface{}{
				"completed":    TaskStatusFailed,
				"err_msg":      err.Error(),
				"last_updated": time.Now(),
			}).Error
		if updateErr != nil {
			l.Errorf("更新结算日志错误信息失败: %v", updateErr)
		}
		return nil, err
	}

	// 处理成功，更新状态为已完成
	if err := db.Model(&model.TaskSettlementLog{}).
		Where("id = ?", settlementLog.ID).
		Updates(map[string]interface{}{
			"completed":    TaskStatusCompleted,
			"last_updated": time.Now(),
		}).Error; err != nil {
		l.Errorf("更新结算日志完成状态失败: %v", err)
		return nil, fmt.Errorf("更新结算日志完成状态失败: %v", err)
	}

	l.Infof("用户升级判定执行成功")
	return &pb.EmptyRequest{}, nil
}

// processUserUpgrade 处理用户升级
func (l *TalentUserUpgradeLogic) processUserUpgrade(logID uint64) error {
	// 查询符合条件的用户（level >= 1）
	var users []model.User
	if err := l.svcCtx.MasterDB.Where("level >= ?", 1).Find(&users).Error; err != nil {
		l.Errorf("查询用户失败: %v", err)
		return fmt.Errorf("查询用户失败: %v", err)
	}

	l.Infof("找到符合条件的用户数量: %d", len(users))
	if len(users) == 0 {
		return nil
	}

	// 使用互斥锁保护错误信息
	var mu sync.Mutex
	var wg sync.WaitGroup
	var errorCount int
	var errorSamples []string
	const maxErrorSamples = 10 // 最多保存10个错误样本

	// 处理每个用户
	for _, user := range users {
		wg.Add(1)
		currentUser := user // 创建副本避免闭包问题

		go func() {
			defer wg.Done()

			// 对单个用户进行升级判定，每个用户使用独立的事务
			err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
				return l.upgradeUserLevel(tx, currentUser)
			})

			if err != nil {
				l.Errorf("用户[%d]升级失败: %v", currentUser.ID, err)
				mu.Lock()
				errorCount++
				// 只保存前几个错误样本
				if len(errorSamples) < maxErrorSamples {
					errorSamples = append(errorSamples, fmt.Sprintf("用户[%d]升级失败: %v", currentUser.ID, err))
				}
				mu.Unlock()
			}
		}()
	}

	// 等待所有任务完成
	wg.Wait()

	// 检查是否有错误
	if errorCount > 0 {
		// 构建有限的错误信息
		var errMsg string
		if errorCount <= maxErrorSamples {
			// 如果错误数量不多，直接显示所有错误
			for i, sample := range errorSamples {
				if i > 0 {
					errMsg += "; "
				}
				errMsg += sample
			}
		} else {
			// 如果错误数量很多，只显示部分样本
			for i, sample := range errorSamples {
				if i > 0 {
					errMsg += "; "
				}
				errMsg += sample
			}
			errMsg += fmt.Sprintf("; 以及其他 %d 个错误", errorCount-maxErrorSamples)
		}

		// 确保错误信息不会太长
		const maxErrLength = 2000 // 最大错误信息长度
		if len(errMsg) > maxErrLength {
			errMsg = errMsg[:maxErrLength-3] + "..."
		}

		return fmt.Errorf("用户升级过程中发生错误: %s", errMsg)
	}

	return nil
}

// upgradeUserLevel 升级用户等级
func (l *TalentUserUpgradeLogic) upgradeUserLevel(tx *gorm.DB, user model.User) error {
	// 使用行锁锁定当前用户记录，防止并发更新
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", user.ID).First(&user).Error; err != nil {
		return fmt.Errorf("锁定用户记录失败: %v", err)
	}

	// 计算直接邀请的体验推客数量
	var level1InviteCount int64
	if err := tx.Model(&model.User{}).
		Where("invite_from = ?", user.ID).
		Where("level = ?", 1).
		Count(&level1InviteCount).Error; err != nil {
		return fmt.Errorf("计算邀请用户数量失败: %v", err)
	}
	// 计算直接邀请的新手推客数量
	var level2InviteCount int64
	if err := tx.Model(&model.User{}).
		Where("invite_from = ?", user.ID).
		Where("level = ?", 2).
		Count(&level2InviteCount).Error; err != nil {
		return fmt.Errorf("计算邀请用户数量失败: %v", err)
	}

	// 计算用户订单数
	var orderCount int64
	if err := tx.Model(&model.CommissionOrders{}).
		Where("sharer_appid = ? AND balance_deal_status = '1' AND withdraw_deal_status = '1'", user.SharerAppid).
		Count(&orderCount).Error; err != nil {
		return fmt.Errorf("计算订单数量失败: %v", err)
	}

	// 判断是否需要升级
	var newLevel int8 = user.Level

	// 等级2条件判断 升级新手推客
	if user.Level < 2 && (level1InviteCount >= 3 || orderCount >= 3) {
		newLevel = 2
	}

	// 等级3条件判断 升级推客
	if user.Level < 3 && (level2InviteCount >= 12 || orderCount >= 24) {
		newLevel = 3
	}

	// 等级4条件判断 升级预备团长
	// 使用pkg包中的函数还原mark
	// 查询用户信息和动态网络信息 - 合并查询
	var newUserTaskFinished bool
	var userDynamicNetwork model.UserDynamicNetworks
	// 再查询动态网络信息
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND network_type = ?", user.ID, "benefits").First(&userDynamicNetwork).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("未找到买家的动态网络信息: UserID=%d", user.ID)
		}
		l.Errorf("查询买家动态网络信息失败: %v", err)
	}
	currentMark := userDynamicNetwork.Mark
	// 使用common包中的任务位掩码工具，指定位数为8
	taskBitMask := common.NewTaskBitmask(8)
	if len(currentMark) > 0 {
		if err := taskBitMask.LoadFromBitFormat([]byte(currentMark)); err != nil {
			l.Errorf("解析任务标记失败: %v", err)
		}
	}

	// 检查mark是否已完成 (所有4个任务位)
	if taskBitMask.IsAllCompleted(4) {
		l.Infof("用户任务已全部完成: userID=%d, mark=%s", userDynamicNetwork.UserID, currentMark)
		newUserTaskFinished = true
	}
	if user.Level < 4 && (level2InviteCount >= 24 || orderCount >= 48 || newUserTaskFinished) {
		newLevel = 4
	}

	// 等级5条件判断 升级团长
	// 判断用户是否买了980套餐
	var hasTeamPackage bool
	if err := tx.Model(&model.UserInventory{}).
		Where("user_id = ? AND product_id = ?", user.ID, 1).
		First(&model.UserInventory{}).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			l.Errorf("查询用户库存失败: %v", err)
		}
	} else {
		hasTeamPackage = true
	}
	if user.Level < 5 && (level2InviteCount >= 48 || orderCount >= 96 || hasTeamPackage) {
		newLevel = 5
	}

	// 如果等级需要更新
	if newLevel > user.Level {
		// 更新用户等级
		if err := tx.Model(&model.User{}).
			Where("id = ?", user.ID).
			Update("level", newLevel).Error; err != nil {
			return fmt.Errorf("更新用户等级失败: %v", err)
		}

		// 记录升级日志
		upgradeLog := model.UserUpgradeLog{
			UserID:      user.ID,
			OldLevel:    user.Level,
			NewLevel:    newLevel,
			InviteCount: int(level1InviteCount),
			OrderCount:  int(orderCount),
		}

		if err := tx.Create(&upgradeLog).Error; err != nil {
			return fmt.Errorf("创建升级日志失败: %v", err)
		}

		l.Infof("用户[%d]从等级%d升级到等级%d", user.ID, user.Level, newLevel)
	}

	return nil
}
