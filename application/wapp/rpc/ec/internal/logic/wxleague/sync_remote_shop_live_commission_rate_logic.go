package wxleaguelogic

import (
	"context"
	"encoding/json"
	"google.golang.org/grpc/metadata"
	"strconv"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type SyncRemoteShopLiveCommissionRateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSyncRemoteShopLiveCommissionRateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncRemoteShopLiveCommissionRateLogic {
	return &SyncRemoteShopLiveCommissionRateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 同步店铺直播最高返佣
func (l *SyncRemoteShopLiveCommissionRateLogic) SyncRemoteShopLiveCommissionRate(in *pb.EmptyRequest) (*pb.EmptyRequest, error) {
	// 检查是否是消息队列调用
	var isQueueMessage bool
	var msgData map[string]interface{}

	// 从上下文中获取消息数据
	if md, ok := metadata.FromIncomingContext(l.ctx); ok {
		if values := md.Get("x-queue-message"); len(values) > 0 {
			isQueueMessage = true
			if err := json.Unmarshal([]byte(values[0]), &msgData); err != nil {
				l.Errorf("解析队列消息数据失败: %v", err)
				return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "解析队列消息数据失败")
			}
		}
	}

	if isQueueMessage {
		// 处理队列消息
		return l.handleQueueMessage(msgData)
	} else {
		// 作为定时任务启动初始消息发送
		return l.startInitialSync()
	}
}

// 处理队列消息
func (l *SyncRemoteShopLiveCommissionRateLogic) handleQueueMessage(msgData map[string]interface{}) (*pb.EmptyRequest, error) {
	// 获取消息参数
	shopAppidInterface, ok := msgData["shop_appid"]
	if !ok {
		l.Errorf("消息中缺少shop_appid参数")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "消息中缺少shop_appid参数")
	}

	shopAppid, ok := shopAppidInterface.(string)
	if !ok {
		l.Errorf("shop_appid参数类型错误")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "shop_appid参数类型错误")
	}

	recordIDFloat, ok := msgData["record_id"].(float64)
	if !ok {
		l.Errorf("record_id参数不存在或类型错误")
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "record_id参数不存在或类型错误")
	}
	recordID := uint(recordIDFloat)

	nextKey, _ := msgData["next_key"].(string)

	pageCountFloat, ok := msgData["page_count"].(float64)
	if !ok {
		pageCountFloat = 0
	}
	pageCount := int(pageCountFloat)

	maxCommissionRateFloat, ok := msgData["max_commission_rate"].(float64)
	if !ok {
		maxCommissionRateFloat = 0
	}
	maxCommissionRate := int64(maxCommissionRateFloat)

	l.Infof("处理店铺 %s 的直播商品分页请求: 页数=%d, nextKey=%s, 当前最高佣金率=%d",
		shopAppid, pageCount+1, nextKey, maxCommissionRate)

	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 查询店铺信息
	var shop model.TempBindShops
	if err := db.Where("shop_appid = ?", shopAppid).First(&shop).Error; err != nil {
		l.Errorf("查询店铺 %s 信息失败: %v", shopAppid, err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询店铺信息失败")
	}

	// 查询店铺关联的推客
	var promoters []model.TempShopPromoters
	if err := db.Where("shop_appid = ?", shop.ShopAppid).Find(&promoters).Error; err != nil {
		l.Errorf("查询店铺 %s 关联的推客失败: %v", shopAppid, err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询店铺关联的推客失败")
	}

	if len(promoters) == 0 {
		l.Errorf("店铺 %s 没有关联的推客", shopAppid)
		// 更新直播记录，标记为已完成但佣金率为0
		l.updateFinalCommissionRate(shopAppid, recordID, 0)
		return &pb.EmptyRequest{}, nil
	}

	// 使用第一个推客获取直播商品列表
	promoter := promoters[0]

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 设置请求超时
	reqCtx, reqCancel := context.WithTimeout(l.ctx, 30*time.Second)
	defer reqCancel()

	// 获取直播商品列表
	req := &wxmodels.GetShopLiveCommissionProductListRequest{
		NextKey:      nextKey,
		PageSize:     10,
		ShopAppid:    shop.ShopAppid,
		PromoterId:   promoter.PromoterID,
		PromoterType: 1, // 视频号
	}

	resp, err := wxClient.GetShopLiveCommissionProductList(reqCtx, req, token)
	if err != nil {
		l.Errorf("获取店铺 %s 的直播商品列表失败: %v", shopAppid, err)
		// 如果API调用失败，仍然更新直播记录，使用当前已知的最高佣金率
		if maxCommissionRate > 0 {
			l.updateFinalCommissionRate(shopAppid, recordID, maxCommissionRate)
		}
		return nil, xerr.NewErrCodeMsg(xerr.ThirdPartPlatformError, "获取直播商品列表失败")
	}

	// 处理返回的商品列表
	var productIds []string // 用于记录API返回的所有商品ID
	for _, product := range resp.ProductList {
		// 更新最高佣金率
		if product.CommissionRate > maxCommissionRate {
			maxCommissionRate = product.CommissionRate
			l.Infof("发现更高的佣金率: %d", maxCommissionRate)
		}

		// 记录商品ID
		productIds = append(productIds, strconv.Itoa(int(product.ProductId)))

		// 同步商品数据到数据库
		err = l.syncProductData(shopAppid, recordID, product)
		if err != nil {
			l.Errorf("同步商品数据失败: %v", err)
			// 继续处理其他商品，不中断流程
		}
	}

	// 删除不存在的商品记录
	if len(productIds) > 0 && nextKey == "" { // 只在最后一页执行删除操作
		l.Infof("开始处理需要删除的商品记录")
		var existingGoods []model.TempLiveGoods
		if err := db.Where("talent_appid = ? AND live_id = ?", shopAppid, recordID).Find(&existingGoods).Error; err != nil {
			l.Errorf("查询现有商品记录失败: %v", err)
		} else {
			l.Infof("数据库中现有 %d 条商品记录", len(existingGoods))

			var goodsToDelete []uint
			for _, goods := range existingGoods {
				found := false
				for _, pid := range productIds {
					if strconv.Itoa(int(goods.ProductID)) == pid {
						found = true
						break
					}
				}

				if !found {
					goodsToDelete = append(goodsToDelete, uint(goods.ID))
					l.Infof("标记需要删除的商品记录: ID=%d, ProductID=%s", goods.ID, goods.ProductID)
				}
			}

			if len(goodsToDelete) > 0 {
				l.Infof("开始删除 %d 条不存在的商品记录", len(goodsToDelete))
				if err := db.Unscoped().Where("id IN ?", goodsToDelete).Delete(&model.TempLiveGoods{}).Error; err != nil {
					l.Errorf("删除不存在的商品记录失败: %v", err)
				} else {
					l.Infof("成功删除 %d 个不存在的商品记录", len(goodsToDelete))
				}
			} else {
				l.Infof("没有需要删除的商品记录")
			}
		}
	}

	// 检查是否有下一页
	if resp.NextKey != "" && pageCount < 10 { // 限制最多查询10页，避免无限循环
		// 构建下一页的消息
		nextPageMsg := map[string]interface{}{
			"type":                "shop_live", // 添加消息类型标识
			"shop_appid":          shopAppid,
			"record_id":           recordID,
			"next_key":            resp.NextKey,
			"page_count":          pageCount + 1,
			"max_commission_rate": maxCommissionRate, // 传递当前找到的最大佣金率
			"timestamp":           time.Now().Unix(),
		}

		// 序列化消息
		nextPageMsgBytes, err := json.Marshal(nextPageMsg)
		if err != nil {
			l.Errorf("序列化下一页消息失败: %v", err)
			// 即使序列化失败，也更新当前已知的最高佣金率
			l.updateFinalCommissionRate(shopAppid, recordID, maxCommissionRate)
			return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "序列化下一页消息失败")
		}

		// 发送下一页消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", nextPageMsgBytes)
		if err != nil {
			l.Errorf("发送下一页消息到队列失败: %v", err)
			// 即使发送失败，也更新当前已知的最高佣金率
			l.updateFinalCommissionRate(shopAppid, recordID, maxCommissionRate)
			return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "发送下一页消息到队列失败")
		}

		l.Infof("成功发送店铺 %s 的下一页消息到队列，nextKey=%s，当前最高佣金率=%d",
			shopAppid, resp.NextKey, maxCommissionRate)
	} else {
		// 没有下一页或已达到最大页数限制，更新最终的佣金率
		l.Infof("店铺 %s 的直播商品分页查询完成，最高佣金率: %d", shopAppid, maxCommissionRate)
		l.updateFinalCommissionRate(shopAppid, recordID, maxCommissionRate)
	}

	return &pb.EmptyRequest{}, nil
}

// 启动初始同步
func (l *SyncRemoteShopLiveCommissionRateLogic) startInitialSync() (*pb.EmptyRequest, error) {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 从temp_live_list表查询需要同步的记录
	var recordsToSync []model.TempLiveList
	if err := db.Where("`type`=? AND `class`=?", "live", "shop").Find(&recordsToSync).Error; err != nil {
		l.Errorf("查询数据库 temp_live_list 失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.DbError, "查询数据库失败")
	}

	l.Infof("找到 %d 条需要同步店铺直播商品分佣信息的记录", len(recordsToSync))
	if len(recordsToSync) == 0 {
		l.Infof("没有需要同步的记录，任务完成")
		return &pb.EmptyRequest{}, nil
	}

	// 检查QueueManager是否初始化
	if l.svcCtx.QueueManager == nil {
		l.Errorf("队列管理器未初始化")
		return nil, xerr.NewErrCodeMsg(xerr.RabbitMQError, "队列管理器未初始化")
	}

	// 遍历每条记录，发送初始消息到队列
	for _, record := range recordsToSync {
		// 构建消息内容
		msgData := map[string]interface{}{
			"type":                "shop_live", // 添加消息类型标识
			"shop_appid":          record.TalentAppid,
			"record_id":           record.ID,
			"next_key":            "", // 初始为空
			"max_commission_rate": 0,  // 初始最高佣金率为0
			"page_count":          0,  // 初始页数为0
			"timestamp":           time.Now().Unix(),
		}

		// 序列化消息
		msgBytes, err := json.Marshal(msgData)
		if err != nil {
			l.Errorf("序列化消息失败: %v", err)
			continue
		}

		// 发送消息到队列
		err = l.svcCtx.QueueManager.PublishToQueue("ProductsPaging", msgBytes)
		if err != nil {
			l.Errorf("发送消息到队列失败: %v", err)
			continue
		}

		l.Infof("成功发送店铺 %s 的初始同步消息到队列", record.TalentAppid)
	}

	l.Infof("店铺直播最高返佣初始同步任务启动完成")
	return &pb.EmptyRequest{}, nil
}

// 更新最终的佣金率
func (l *SyncRemoteShopLiveCommissionRateLogic) updateFinalCommissionRate(shopAppid string, recordID uint, maxCommissionRate int64) error {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		l.Errorf("获取数据库连接失败")
		return xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 更新数据库记录 - 直接使用原始的maxCommissionRate，不进行转换
	updateData := map[string]interface{}{
		"predict_max_commission_amount": maxCommissionRate / 10000, // 直接使用原始值，不转换为百分比
		"info_progress":                 "completed",
	}

	l.Infof("准备更新店铺 %s 的佣金信息到数据库: %+v", shopAppid, updateData)

	// 执行更新
	result := db.Model(&model.TempLiveList{}).
		Where("id = ?", recordID).
		Updates(updateData)

	if result.Error != nil {
		l.Errorf("更新店铺 %s 的分佣信息失败: %v", shopAppid, result.Error)
		return result.Error
	}

	// 检查更新结果
	l.Infof("成功更新店铺 %s 的最高返佣率为 %d, 影响行数: %d",
		shopAppid, maxCommissionRate, result.RowsAffected)

	return nil
}

// 同步商品数据
func (l *SyncRemoteShopLiveCommissionRateLogic) syncProductData(shopAppid string, liveID uint, product wxmodels.ShopLiveProductInfo) error {
	// 获取数据库连接
	db := l.svcCtx.MasterDB
	if db == nil {
		return xerr.NewErrCodeMsg(xerr.DbError, "数据库连接失败")
	}

	// 构建商品数据
	liveGoods := model.TempLiveGoods{
		TalentAppid:             shopAppid,
		ProductID:               uint64(product.ProductId),
		ProductName:             product.ProductName,
		ProductImgUrl:           product.ProductImgUrl,
		ProductPrice:            int(product.ProductPrice),
		PredictCommissionAmount: int(product.PredictCommissionAmount),
	}

	// 检查商品是否已存在
	var existingGoods model.TempLiveGoods
	err := db.Where("talent_appid = ? AND live_id = ? AND product_id = ?",
		shopAppid, liveID, product.ProductId).First(&existingGoods).Error

	if err == nil {
		// 商品已存在，更新
		liveGoods.ID = existingGoods.ID
		liveGoods.CreatedAt = existingGoods.CreatedAt

		err = db.Model(&model.TempLiveGoods{}).Where("id = ?", existingGoods.ID).Updates(&liveGoods).Error
		if err != nil {
			l.Errorf("更新店铺 %s 的直播商品失败: %v", shopAppid, err)
			return err
		}
		l.Infof("更新店铺 %s 的直播商品: %s", shopAppid, product.ProductName)
	} else {
		// 商品不存在，创建
		err = db.Create(&liveGoods).Error
		if err != nil {
			l.Errorf("创建店铺 %s 的直播商品失败: %v", shopAppid, err)
			return err
		}
		l.Infof("创建店铺 %s 的直播商品: %s", shopAppid, product.ProductName)
	}

	return nil
}
