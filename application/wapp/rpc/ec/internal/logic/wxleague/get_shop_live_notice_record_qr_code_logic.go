package wxleaguelogic

import (
	"context"

	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShopLiveNoticeRecordQrCodeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetShopLiveNoticeRecordQrCodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShopLiveNoticeRecordQrCodeLogic {
	return &GetShopLiveNoticeRecordQrCodeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取小店关联账号直播预约推广二维码
func (l *GetShopLiveNoticeRecordQrCodeLogic) GetShopLiveNoticeRecordQrCode(in *pb.GetShopLiveNoticeRecordQrCodeReq) (*pb.GetShopLiveNoticeRecordQrCodeResp, error) {
	// 从用户表中获取推客的appid
	var sharerAppid string
	if err := l.svcCtx.GetSlaveDB().Model(&model.User{}).
		Select("sharer_appid").
		Where("id = ?", in.UserId).
		Scan(&sharerAppid).Error; err != nil {
		l.Errorf("查询用户[%d]的推客信息失败: %v", in.UserId, err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户推客信息失败")
	}

	if sharerAppid == "" {
		return nil, xerr.NewErrMsg(l.ctx, "用户未注册为推客")
	}

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.GetShopLiveNoticeRecordQrCodeRequest{
		ShopAppid:    in.ShopAppid,         // 小店的appid
		PromoterId:   in.PromoterId,        // 关联账号的id
		PromoterType: int(in.PromoterType), // 关联账号类型
		NoticeId:     in.NoticeId,          // 直播预约id
		SharerAppid:  sharerAppid,          // 推客的appid
	}

	// 调用微信API
	resp, err := wxClient.GetShopLiveNoticeRecordQrCode(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取小店关联账号直播预约推广二维码失败: %v", err)
		return nil, err
	}

	// 构建响应
	return &pb.GetShopLiveNoticeRecordQrCodeResp{
		QrcodeUrl: resp.QrcodeUrl,
	}, nil
}
