package wxleaguelogic

import (
	"context"
	"time"
	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetSalesmanPermissionLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetSalesmanPermissionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSalesmanPermissionLogic {
	return &SetSalesmanPermissionLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 设置业务员权限
func (l *SetSalesmanPermissionLogic) SetSalesmanPermission(in *pb.SetSalesmanPermissionReq) (*pb.BoolRequest, error) {
	// / 参数验证
	if in.AgentUserId <= 0 {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "代理商ID不能为空")
	}

	if in.SalesmanUserId <= 0 {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "业务员ID不能为空")
	}

	if in.SalesmanRate < 0 || in.SalesmanRate > 100 {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "佣金比例必须在0-100之间")
	}

	// 验证用户是否为代理商
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ? AND role_id = ?", in.AgentUserId, config.RoleAgent).First(&user).Error; err != nil {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "您不是代理商，无权设置业务员权限")
	}

	// 使用递归SQL查询检查业务员是否在代理商的邀请上
	if !l.isInvitedBySalesman(in.SalesmanUserId, in.AgentUserId) {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "该业务员不在您的邀请上，无法设置权限")
	}

	// 查询是否已存在关联关系
	var existingRelation model.AgentRelation

	err := l.svcCtx.GetSlaveDB().Where("agent_user_id = ? AND salesman_user_id = ? AND deleted_at IS NULL",
		in.AgentUserId, in.SalesmanUserId).First(&existingRelation).Error

	// 如果已存在关联关系，则更新
	if err == nil {
		// 更新现有记录
		existingRelation.SalesmanRate = in.SalesmanRate
		existingRelation.SalesmanRemark = in.SalesmanRemark
		existingRelation.IsFullTime = in.IsFullTime

		if err := l.svcCtx.MasterDB.Save(&existingRelation).Error; err != nil {
			l.Errorf("更新业务员权限失败: %v", err)
			return &pb.BoolRequest{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "更新业务员权限失败")
		}

		return &pb.BoolRequest{
			Success: true,
		}, nil
	}

	// 创建新的关联关系
	newRelation := model.AgentRelation{
		AgentUserID:    in.AgentUserId,
		SalesmanUserID: in.SalesmanUserId,
		SalesmanRate:   in.SalesmanRate,
		SalesmanRemark: in.SalesmanRemark,
		IsFullTime:     in.IsFullTime,
		CreatedAt:      time.Now(),
	}

	if err := l.svcCtx.MasterDB.Create(&newRelation).Error; err != nil {
		// l.Errorf("创建业务员权限失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "创建业务员权限失败")
	}

	// 更新用户为业务员角色
	if err := l.svcCtx.MasterDB.Model(&model.User{}).Where("id = ?", in.SalesmanUserId).Update("role_id", 2).Error; err != nil {
		l.Errorf("更新用户为业务员失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "更新用户为业务员失败")
	}

	return &pb.BoolRequest{
		Success: true,
	}, nil
}

// isInvitedBySalesman 递归检查用户是否在代理商的邀请上
func (l *SetSalesmanPermissionLogic) isInvitedBySalesman(salesmanUserId, agentUserId uint64) bool {
	// 使用WITH RECURSIVE递归SQL查询邀请
	query := `WITH RECURSIVE invite_chain AS (
		-- 初始查询：获取业务员的直接邀请人
		SELECT id, invite_from
		FROM user_user
		WHERE id = ?
		
		UNION ALL
		
		-- 递归查询：获取上级的上级
		SELECT u.id, u.invite_from
		FROM user_user u
		INNER JOIN invite_chain c ON u.id = c.invite_from
		WHERE u.invite_from > 0  -- 确保有邀请人
	)
	-- 检查邀请中是否包含代理商ID
	SELECT COUNT(*) as found
	FROM invite_chain
	WHERE invite_from = ?;`

	var count int
	if err := l.svcCtx.GetSlaveDB().Raw(query, salesmanUserId, agentUserId).Scan(&count).Error; err != nil {
		l.Errorf("递归查询邀请失败: %v", err)
		return false
	}

	return count > 0
}
