package wxleaguelogic

import (
	"context"
	"fmt"
	"gorm.io/gorm"
	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/pkg/result/xerr"
	ec_order "xj-serv/types/order"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetSalesmanTeamPackageLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSetSalesmanTeamPackageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSalesmanTeamPackageLogic {
	return &SetSalesmanTeamPackageLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 代理商给业务经理设置团长套餐数量
func (l *SetSalesmanTeamPackageLogic) SetSalesmanTeamPackage(in *pb.SetSalesmanTeamPackageReq) (*pb.BoolRequest, error) {
	l.Infof("代理商ID:%d 给业务经理ID:%d 设置团长套餐数量:%d", in.AgentUserId, in.SalesmanUserId, in.Number)

	// 参数校验
	if in.AgentUserId <= 0 || in.SalesmanUserId <= 0 || in.Number <= 0 {
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 1. 验证代理商身份
	var agent model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ? AND role_id = ?", in.AgentUserId, config.RoleAgent).First(&agent).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("用户ID %d 不是代理商角色", in.AgentUserId)
			return &pb.BoolRequest{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "您不是代理商，无权设置业务经理团长套餐")
		}
		l.Errorf("查询代理商信息失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询代理商信息失败")
	}

	// 2. 验证业务经理身份及关系
	var salesman model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ? AND role_id = ?", in.SalesmanUserId, config.RoleSalesman).First(&salesman).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("用户ID %d 不是业务经理角色", in.SalesmanUserId)
			return &pb.BoolRequest{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "该用户不是业务经理")
		}
		l.Errorf("查询业务经理信息失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询业务经理信息失败")
	}

	// 3. 验证业务经理是否属于该代理商
	var relation model.AgentRelation
	if err := l.svcCtx.GetSlaveDB().Where("agent_user_id = ? AND salesman_user_id = ? AND deleted_at IS NULL",
		in.AgentUserId, in.SalesmanUserId).First(&relation).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("业务经理ID %d 不属于代理商ID %d", in.SalesmanUserId, in.AgentUserId)
			return &pb.BoolRequest{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "该业务经理不属于您的团队")
		}
		l.Errorf("查询代理商与业务经理关系失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询代理商与业务经理关系失败")
	}

	// 4. 查询代理商的团长套餐库存
	var agentInventory model.UserInventory
	if err := l.svcCtx.GetSlaveDB().Where("user_id = ? AND product_id = ?", in.AgentUserId, 1).First(&agentInventory).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			l.Errorf("代理商ID %d 没有团长套餐库存", in.AgentUserId)
			return &pb.BoolRequest{
				Success: false,
			}, xerr.NewErrMsg(l.ctx, "您没有团长套餐库存")
		}
		l.Errorf("查询代理商团长套餐库存失败: %v", err)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, "查询团长套餐库存失败")
	}

	// 5. 检查代理商的团长套餐库存是否足够
	if agentInventory.Quantity < uint(in.Number) {
		l.Errorf("代理商ID %d 的团长套餐库存不足，当前库存: %d, 需要: %d",
			in.AgentUserId, agentInventory.Quantity, in.Number)
		return &pb.BoolRequest{
			Success: false,
		}, xerr.NewErrMsg(l.ctx, fmt.Sprintf("团长套餐库存不足，当前库存: %d", agentInventory.Quantity))
	}

	// 6. 开始事务，调整库存
	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 6.1 调用adjustUserInventory减少代理商的团长套餐库存
		adjustLogic := NewAdjustUserInventoryLogic(l.ctx, l.svcCtx)
		_, err := adjustLogic.AdjustUserInventory(&pb.AdjustUserInventoryReq{
			UserID:          in.AgentUserId,
			ProductID:       1,                 // 团长产品ID
			Quantity:        -int32(in.Number), // 消耗指定数量
			Remark:          fmt.Sprintf("分配给业务经理ID:%d", in.SalesmanUserId),
			TransactionType: ec_order.TransactionTypeAdjustment,
			TccAction:       "",
		})
		if err != nil {
			l.Errorf("减少代理商团长套餐库存失败: %v", err)
			return xerr.NewErrMsg(l.ctx, "减少代理商团长套餐库存失败")
		}

		// 6.2 调用adjustUserInventory增加业务经理的团长套餐库存
		_, err = adjustLogic.AdjustUserInventory(&pb.AdjustUserInventoryReq{
			UserID:          in.SalesmanUserId,
			ProductID:       1,         // 团长产品ID
			Quantity:        in.Number, // 增加指定数量
			Remark:          fmt.Sprintf("从代理商ID:%d获得", in.AgentUserId),
			TransactionType: ec_order.TransactionTypeAdjustment,
			TccAction:       "",
		})
		if err != nil {
			l.Errorf("增加业务经理团长套餐库存失败: %v", err)
			return xerr.NewErrMsg(l.ctx, "增加业务经理团长套餐库存失败")
		}

		return nil
	})

	if err != nil {
		return &pb.BoolRequest{
			Success: false,
		}, err
	}

	return &pb.BoolRequest{
		Success: true,
	}, nil
}
