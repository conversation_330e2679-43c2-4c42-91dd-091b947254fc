package wxleaguelogic

import (
	"context"
	"xj-serv/pkg/wxCtl"

	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShopDetailLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetShopDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShopDetailLogic {
	return &GetShopDetailLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取合作小店详情
func (l *GetShopDetailLogic) GetShopDetail(in *pb.ShopGetReq) (*pb.ShopGetResp, error) {
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, err
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建请求参数
	req := &wxmodels.ShopGetRequest{
		Appid:    in.Appid,
		PageSize: int(in.PageSize),
	}

	// 调用微信API
	resp, err := wxClient.GetShopDetail(l.ctx, req, token)
	if err != nil {
		l.Errorf("获取合作小店详情失败: %v", err)
		return nil, err
	}

	// 构建响应
	baseInfo := &pb.BizBaseInfo{
		Appid:      resp.ShopDetail.BaseInfo.Appid,
		HeadImgUrl: resp.ShopDetail.BaseInfo.HeadImgUrl,
		Nickname:   resp.ShopDetail.BaseInfo.Nickname,
	}

	dataInfo := &pb.ShopDataInfo{
		Gmv:                    resp.ShopDetail.DataInfo.Gmv,
		ProductNumber:          int32(resp.ShopDetail.DataInfo.ProductNumber),
		SettleAmount:           resp.ShopDetail.DataInfo.SettleAmount,
		UnsettleAmount:         resp.ShopDetail.DataInfo.UnsettleAmount,
		ProductNumberToday:     int32(resp.ShopDetail.DataInfo.ProductNumberToday),
		ProductNumberSoldToday: int32(resp.ShopDetail.DataInfo.ProductNumberSoldToday),
	}

	shopDetail := &pb.ShopDetail{
		BaseInfo:     baseInfo,
		DataInfo:     dataInfo,
		Status:       int32(resp.ShopDetail.Status),
		ApprovedTime: resp.ShopDetail.ApprovedTime,
	}

	return &pb.ShopGetResp{
		ShopDetail: shopDetail,
		NextKey:    resp.NextKey,
		HasMore:    resp.HasMore,
	}, nil
}
