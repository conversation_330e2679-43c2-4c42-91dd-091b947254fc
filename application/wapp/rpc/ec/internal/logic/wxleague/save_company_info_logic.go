package wxleaguelogic

import (
	"context"

	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SaveCompanyInfoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSaveCompanyInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveCompanyInfoLogic {
	return &SaveCompanyInfoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 保存公司信息
func (l *SaveCompanyInfoLogic) SaveCompanyInfo(in *pb.CompanyInfoSaveReq) (*pb.CompanyInfoResp, error) {
	l.Infof("保存用户ID为%d的公司信息", in.UserId)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 验证用户角色是否为代理商
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 检查用户是否为代理商角色
	if user.RoleId != config.RoleAgent {
		l.Errorf("用户ID %d 不是代理商角色，无法保存公司信息", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "只有代理商可以保存公司信息")
	}

	// 检查必填字段
	if in.Name == "" || in.Address == "" || in.BankAccount == "" ||
		in.BankName == "" || in.TaxNumber == "" || in.Phone == "" || in.LicenseImg == "" {
		return nil, xerr.NewErrMsg(l.ctx, "公司信息字段不能为空")
	}

	// 在事务中处理，确保数据一致性
	var companyInfo model.CompanyInfo

	err := l.svcCtx.MasterDB.Transaction(func(tx *gorm.DB) error {
		// 查找是否已存在该用户的公司信息
		var existingCompany model.CompanyInfo
		result := tx.Where("user_id = ?", in.UserId).Order("id DESC").First(&existingCompany)

		if result.Error != nil && result.Error != gorm.ErrRecordNotFound {
			// 查询出错但不是"记录不存在"的错误
			l.Errorf("查询公司信息失败: %v", result.Error)
			return xerr.NewErrMsg(l.ctx, "查询公司信息失败")
		}

		if result.Error == gorm.ErrRecordNotFound {
			// 不存在记录，创建新记录
			l.Infof("创建新的公司信息记录，用户ID: %d", in.UserId)
			companyInfo = model.CompanyInfo{
				UserID:      in.UserId,
				Name:        in.Name,
				Address:     in.Address,
				BankAccount: in.BankAccount,
				BankName:    in.BankName,
				TaxNumber:   in.TaxNumber,
				Phone:       in.Phone,
				LicenseImg:  in.LicenseImg,
			}

			// 创建记录
			if err := tx.Create(&companyInfo).Error; err != nil {
				l.Errorf("创建公司信息失败: %v", err)
				return xerr.NewErrMsg(l.ctx, "创建公司信息失败")
			}
		} else {
			// 已存在记录，更新
			l.Infof("更新现有公司信息记录，用户ID: %d, 记录ID: %d", in.UserId, existingCompany.ID)

			// 更新记录
			if err := tx.Model(&existingCompany).Updates(map[string]interface{}{
				"name":         in.Name,
				"address":      in.Address,
				"bank_account": in.BankAccount,
				"bank_name":    in.BankName,
				"tax_number":   in.TaxNumber,
				"phone":        in.Phone,
				"license_img":  in.LicenseImg,
			}).Error; err != nil {
				l.Errorf("更新公司信息失败: %v", err)
				return xerr.NewErrMsg(l.ctx, "更新公司信息失败")
			}

			// 重新获取更新后的记录
			companyInfo = existingCompany
			if err := tx.Where("id = ?", existingCompany.ID).First(&companyInfo).Error; err != nil {
				l.Errorf("获取更新后的公司信息失败: %v", err)
				return xerr.NewErrMsg(l.ctx, "获取更新后的公司信息失败")
			}
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	// 格式化时间
	createdAtStr := companyInfo.CreatedAt.Format("2006-01-02 15:04:05")
	updatedAtStr := companyInfo.UpdatedAt.Format("2006-01-02 15:04:05")

	// 构建响应
	return &pb.CompanyInfoResp{
		Id:          companyInfo.ID,
		Name:        companyInfo.Name,
		Address:     companyInfo.Address,
		BankAccount: companyInfo.BankAccount,
		BankName:    companyInfo.BankName,
		TaxNumber:   companyInfo.TaxNumber,
		Phone:       companyInfo.Phone,
		LicenseImg:  companyInfo.LicenseImg,
		CreatedAt:   createdAtStr,
		UpdatedAt:   updatedAtStr,
	}, nil
}
