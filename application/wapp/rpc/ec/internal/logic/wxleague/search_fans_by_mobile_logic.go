package wxleaguelogic

import (
	"context"
	"strings"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchFansByMobileLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewSearchFansByMobileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchFansByMobileLogic {
	return &SearchFansByMobileLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 通过手机号搜索粉丝
func (l *SearchFansByMobileLogic) SearchFansByMobile(in *pb.SearchFansByMobileReq) (*pb.SearchFansByMobileResp, error) {
	l.Infof("用户:%d 通过手机号:%s 搜索粉丝", in.UserId, in.Mobile)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	if len(strings.TrimSpace(in.Mobile)) == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "手机号不能为空")
	}

	// 验证用户角色是否存在
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	// 使用递归SQL查询所有层级的粉丝
	query := `WITH RECURSIVE user_tree AS (
		-- 初始查询：获取当前用户
		SELECT id, nick_name, avatar, mobile, level
		FROM user_user
		WHERE id = ?

		UNION ALL

		-- 递归查询：获取下级用户
		SELECT u.id, u.nick_name, u.avatar, u.mobile, u.level
		FROM user_user u
		INNER JOIN user_tree t ON u.invite_from = t.id
	)
	-- 查询所有粉丝，排除自己，并按手机号筛选
	SELECT id, nick_name, avatar, mobile, level
	FROM user_tree
	WHERE id != ? AND mobile LIKE ?
	LIMIT 100;`

	type UserResult struct {
		ID       uint
		NickName string
		Avatar   string
		Mobile   string
		level    uint
	}

	var users []UserResult
	if err := l.svcCtx.GetSlaveDB().Raw(query, in.UserId, in.UserId, "%"+in.Mobile+"%").Scan(&users).Error; err != nil {
		l.Errorf("递归查询粉丝失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询粉丝失败")
	}

	// 构建响应
	resp := &pb.SearchFansByMobileResp{
		List: make([]*pb.FansItem, 0, len(users)),
	}

	// 如果找不到粉丝，直接返回空列表
	if len(users) == 0 {
		return resp, nil
	}

	// 提取所有粉丝ID
	var fanIds []uint64
	for _, user := range users {
		fanIds = append(fanIds, uint64(user.ID))
	}

	// 一次性查询所有代理关系
	var relations []model.AgentRelation
	if err := l.svcCtx.GetSlaveDB().
		Where("agent_user_id = ? AND salesman_user_id IN ?", in.UserId, fanIds).
		Find(&relations).Error; err != nil {
		l.Errorf("批量查询代理关系失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询代理关系失败")
	}

	// 构建映射表，快速判断用户是否为业务员
	salesmanMap := make(map[uint64]bool)
	for _, relation := range relations {
		salesmanMap[uint64(relation.SalesmanUserID)] = true
	}

	// 构建映射表，快速判断用户是否为团长
	groupmanMap := make(map[uint64]bool)
	for _, user := range users {
		if user.level == 5 {
			groupmanMap[uint64(user.ID)] = true
		}
	}

	// 构建响应列表
	for _, user := range users {
		resp.List = append(resp.List, &pb.FansItem{
			UserId:     uint64(user.ID),
			NickName:   user.NickName,
			Avatar:     user.Avatar,
			Mobile:     user.Mobile,
			IsSalesman: salesmanMap[uint64(user.ID)],
			IsGroupman: groupmanMap[uint64(user.ID)],
		})
	}

	return resp, nil
}
