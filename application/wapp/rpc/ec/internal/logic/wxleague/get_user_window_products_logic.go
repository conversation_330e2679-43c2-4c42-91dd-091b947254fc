package wxleaguelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"xj-serv/application/wapp/rpc/ec/internal/model"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/wxCtl"
	client "xj-serv/pkg/wxCtl/wechat/client"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserWindowProductsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetUserWindowProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserWindowProductsLogic {
	return &GetUserWindowProductsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 添加Redis缓存相关常量
//const (
//	// 用户橱窗商品关系缓存前缀
//	UserWindowProductsCachePrefix = "user_window_products:"
//	// 缓存过期时间：15分钟
//	UserWindowProductsCacheExpire = 15 * 60
//)

// GetUserWindowProducts 获取用户橱窗商品列表
func (l *GetUserWindowProductsLogic) GetUserWindowProducts(in *pb.GetUserWindowProductsReq) (*pb.GetUserWindowProductsResp, error) {
	l.Infof("获取用户橱窗商品列表请求: %+v", in)

	// 参数校验
	if in.UserId <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 设置默认分页参数
	pageSize := int(in.PageSize)
	if pageSize <= 0 {
		pageSize = 10
	}

	// 查询用户信息获取openfinderid
	var user model.User
	if err := l.svcCtx.GetSlaveDB().Where("id = ?", in.UserId).First(&user).Error; err != nil {
		l.Errorf("查询用户信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询用户信息失败")
	}

	if user.Openfinderid == "" {
		l.Errorf("用户未授权达人橱窗: %v", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "您尚未完成橱窗授权，请先进行授权")
	}

	if user.SharerAppid == "" {
		l.Errorf("用户未授权机构推客: %v", in.UserId)
		return nil, xerr.NewErrMsg(l.ctx, "您尚未完成机构推客授权，请先进行授权")
	}

	//// 构建Redis缓存键
	//cacheKey := fmt.Sprintf("%s%d", UserWindowProductsCachePrefix, in.UserId)

	// 构建响应
	result := &pb.GetUserWindowProductsResp{
		ProductList: make([]*pb.ProductInfo, 0),
		SharerAppid: user.SharerAppid,
	}

	// 注释掉缓存相关代码
	/*
		// 尝试从缓存获取数据
		cachedData, err := l.svcCtx.BizRedis.Get(cacheKey)
		if err == nil && len(cachedData) > 0 {
			// 缓存命中，解析数据
			var cachedProducts []*pb.ProductInfo
			if err = json.Unmarshal([]byte(cachedData), &cachedProducts); err == nil {
				// 成功解析缓存数据
				result.ProductList = cachedProducts
				l.Infof("从缓存获取用户橱窗商品列表成功: userId=%d, count=%d", in.UserId, len(cachedProducts))
				return result, nil
			}
			// 解析缓存数据失败，继续获取新数据
			l.Warnf("解析缓存数据失败: %v", err)
		}
	*/

	// 获取微信访问令牌
	token, err := wxCtl.GetAccessToken(l.svcCtx.BizRedis,
		l.svcCtx.Config.WxLeague.AppId, l.svcCtx.Config.WxLeague.AppSecret, l.svcCtx.Config.WxLeague.AccessTokenPrefix)
	if err != nil {
		l.Errorf("获取微信访问令牌失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取微信访问令牌失败")
	}

	// 初始化微信客户端
	wxClient := client.NewWechatClient()

	// 构建获取橱窗商品请求
	req := &wxmodels.GetWindowAllRequest{
		Appid:        l.svcCtx.Config.WxLeague.AppId,
		OpenFinderId: user.Openfinderid,
		IsGetAll:     true,
	}

	// 调用微信API获取橱窗商品列表
	resp, err := wxClient.GetWindowProducts(l.ctx, req, token)
	if err != nil {
		l.Errorf("调用微信API获取橱窗商品列表失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "获取橱窗商品列表失败")
	}

	// 检查响应状态
	if !resp.IsSuccess() {
		l.Errorf("获取橱窗商品列表失败: %s", resp.ErrMsg)
		return nil, xerr.NewErrMsg(l.ctx, fmt.Sprintf("获取橱窗商品列表失败: %s", resp.ErrMsg))
	}

	// 更新响应信息
	result.Total = int32(resp.TotalNum)
	result.NextOffset = int32(resp.NextOffset)
	result.HasMore = resp.HaveMore

	// 如果没有商品，直接返回空列表
	if len(resp.List) == 0 {
		//// 缓存空结果
		//l.svcCtx.BizRedis.Setex(cacheKey, "[]", UserWindowProductsCacheExpire)
		return result, nil
	}

	// 收集所有商品ID
	var productIds []int64
	productIdMap := make(map[string]int64)
	for _, item := range resp.List {
		_productId, err := strconv.ParseInt(item.ProductId, 10, 64)
		if err != nil {
			l.Errorf("商品ID转换失败: %v, productId: %s", err, item.ProductId)
			continue
		}
		productIds = append(productIds, _productId)
		productIdMap[item.ProductId] = _productId
	}

	// 从数据库批量查询商品信息 - 修改为查询temp_talent_products表且plan_type=1
	var tempProducts []model.TempTalentProduct
	//if err := l.svcCtx.GetSlaveDB().Where("product_id IN ? AND plan_type = ?", productIds, 1).Find(&tempProducts).Error; err != nil {
	//	l.Errorf("批量查询商品信息失败: %v", err)
	//	return nil, xerr.NewErrMsg(l.ctx, "查询商品信息失败")
	//}
	if err := l.svcCtx.GetSlaveDB().Where("product_id IN ?", productIds).Find(&tempProducts).Error; err != nil {
		l.Errorf("批量查询商品信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "查询商品信息失败")
	}

	// 将查询结果转换为map，方便后续查找
	tempProductMap := make(map[int64]model.TempTalentProduct)
	for _, product := range tempProducts {
		tempProductMap[product.ProductId] = product
	}

	l.Infof("从数据库中找到 %d 个符合条件的达人商品信息(plan_type=1)，橱窗中有 %d 个商品", len(tempProducts), len(resp.List))

	// 构建商品信息
	for _, item := range resp.List {
		productIdStr := item.ProductId
		_productId, exists := productIdMap[productIdStr]
		if !exists {
			continue // 跳过无法转换ID的商品
		}

		// 检查商品是否在数据库中存在
		tempProduct, exists := tempProductMap[_productId]
		if !exists {
			l.Infof("商品ID: %d 在temp_talent_products表中不存在或plan_type不为1，跳过", _productId)
			continue // 跳过不在数据库中的商品
		}

		// 解析JSON字段
		var headImgs []string
		var skusData []map[string]interface{}
		if err := json.Unmarshal([]byte(tempProduct.HeadImgs), &headImgs); err != nil {
			l.Errorf("解析商品头图失败: %v, productId: %d", err, _productId)
			headImgs = []string{}
		}

		if err := json.Unmarshal([]byte(tempProduct.Skus), &skusData); err != nil {
			l.Errorf("解析商品SKU失败: %v, productId: %d", err, _productId)
			skusData = []map[string]interface{}{}
		}

		// 构建SKU信息
		skus := make([]*pb.SkuInfo, 0, len(skusData))
		for _, skuData := range skusData {
			// 提取SKU属性
			skuAttrs := make([]*pb.SkuAttr, 0)
			if skuAttrsData, ok := skuData["sku_attrs"].([]interface{}); ok {
				for _, attrData := range skuAttrsData {
					if attrMap, ok := attrData.(map[string]interface{}); ok {
						skuAttrs = append(skuAttrs, &pb.SkuAttr{
							AttrKey:   attrMap["attr_key"].(string),
							AttrValue: attrMap["attr_value"].(string),
						})
					}
				}
			}

			// 添加SKU信息
			skuId := fmt.Sprintf("%v", skuData["sku_id"])
			thumbImg := ""
			if thumb, ok := skuData["thumb_img"].(string); ok {
				thumbImg = thumb
			}

			var salePrice int64
			if price, ok := skuData["sale_price"].(float64); ok {
				salePrice = int64(price)
			}

			var stockNum int64
			if stock, ok := skuData["stock_num"].(float64); ok {
				stockNum = int64(stock)
			}

			skus = append(skus, &pb.SkuInfo{
				SkuId:     skuId,
				ThumbImg:  thumbImg,
				SalePrice: salePrice,
				StockNum:  stockNum,
				SkuAttrs:  skuAttrs,
			})
		}

		// 添加商品详情到响应
		productInfo := &pb.ProductInfo{
			ShopAppid: tempProduct.ShopAppid,
			ProductId: uint64(_productId),
			Title:     tempProduct.Title,
			SubTitle:  tempProduct.SubTitle,
			HeadImgs:  headImgs,
			Skus:      skus,
		}

		result.ProductList = append(result.ProductList, productInfo)
	}

	// 记录获取到的商品数量
	l.Infof("成功获取到 %d 个商品详情", len(result.ProductList))

	// 缓存查询结果到Redis，无论是否使用缓存数据，都更新缓存
	//if len(result.ProductList) > 0 {
	//	// 序列化商品列表
	//	productListJson, err := json.Marshal(result.ProductList)
	//	if err != nil {
	//		l.Errorf("序列化商品列表失败: %v", err)
	//	}
	//	else {
	//		// 设置缓存，有效期10分钟
	//		err = l.svcCtx.BizRedis.Setex(cacheKey, string(productListJson), UserWindowProductsCacheExpire)
	//		if err != nil {
	//			l.Errorf("设置Redis缓存失败: %v", err)
	//		} else {
	//			l.Infof("成功更新用户[%d]的橱窗商品列表缓存，共%d个商品，有效期%d秒",
	//				in.UserId, len(result.ProductList), UserWindowProductsCacheExpire)
	//		}
	//	}
	//}

	return result, nil
}
