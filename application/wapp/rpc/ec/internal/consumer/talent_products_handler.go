package consumer

import (
	"context"
	"encoding/json"
	"google.golang.org/grpc/metadata"
	"xj-serv/application/wapp/rpc/ec/pb"
)

// processTalentProductsPagingMessage 处理达人商品分页同步消息
func (c *MessageConsumer) processTalentProductsPagingMessage(body []byte) error {
	// 解析消息内容
	var msgData map[string]interface{}
	if err := json.Unmarshal(body, &msgData); err != nil {
		c.logger.Errorf("解析达人商品分页同步消息失败: %v", err)
		return err
	}

	// 创建上下文
	ctx := context.Background()
	
	// 添加消息数据到上下文
	msgDataBytes, err := json.Marshal(msgData)
	if err != nil {
		c.logger.Errorf("序列化消息数据失败: %v", err)
		return err
	}
	
	// 创建元数据
	md := metadata.New(map[string]string{
		"x-queue-message": string(msgDataBytes),
	})
	
	// 将元数据添加到上下文
	ctx = metadata.NewOutgoingContext(ctx, md)

	// 从消息中提取基本参数
	commissionType := 0
	if ctVal, ok := msgData["commission_type"].(float64); ok {
		commissionType = int(ctVal)
	}

	nextKey := ""
	if nkVal, ok := msgData["next_key"].(string); ok {
		nextKey = nkVal
	}

	pageIndex := 1
	if piVal, ok := msgData["page_index"].(float64); ok {
		pageIndex = int(piVal)
	}

	// 创建请求对象
	req := &pb.SyncTalentProductsPagedRequest{
		CommissionType: int32(commissionType),
		NextKey:        nextKey,
		PageIndex:      int32(pageIndex),
	}

	// 调用同步逻辑
	resp, err := c.svcCtx.WxLeagueRPC.SyncRemoteTalentProductListPaged(ctx, req)
	if err != nil {
		c.logger.Errorf("调用达人商品分页同步服务失败: %v", err)
		return err
	}

	if !resp.Success {
		c.logger.Errorf("达人商品分页同步失败: %s", resp.Message)
		return fmt.Errorf(resp.Message)
	}

	c.logger.Info("处理达人商品分页同步消息成功")
	return nil
}