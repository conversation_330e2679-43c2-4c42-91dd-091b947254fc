// user.proto
syntax = "proto3";
package wapp.ec;
option go_package = "./pb";

import "validate/validate.proto";

message EmptyRequest {}

message BoolRequest{
  bool Success = 1;
}

message IDRequest{
  uint64 id = 1 [(validate.rules).uint64 = {gt: 0}];
}

message WxBaseResp {
  // @inject_tag: json:"errcode"
  uint64 Errcode = 1 [json_name = "errcode"];
  // @inject_tag: json:"errmsg"
  string Errmsg = 2 [json_name = "errmsg"];
}

//*********************************微信小店*********************************
service WxMiniShop {
  // 新建微信小店
  rpc NewShop (NewShopReq) returns (BoolRequest);
  // 推荐达人店铺
  rpc RecShop (EmptyRequest) returns (RecShopResp);
  // 通过ID获取达人视频号信息
  rpc GetVideoAccountById (IDRequest) returns (VideoAccount);
  // 推荐达人店铺经营范围
  rpc ShopBusinessCategory(EmptyRequest) returns (ShopBusinessCategoryResp);

  // 小店商品分类列表
  rpc ShopProductCats(EmptyRequest) returns (ShopProductCatsResp);
  // 小店商品列表
  rpc ShopProductList(ShopProductListReq) returns (ShopProductListResp);
  // 小店商品详情
  rpc ShopProductDetail(IDRequest) returns (ShopProductDetailResp);
  // 小店支付成功处理
  rpc PaySuccess (WxShopMsgReq) returns (BoolRequest);
  // 小店取消支付处理
  rpc PayCancel (WxShopMsgReq) returns (BoolRequest);
  // 解密小店消息
  rpc DecryptMiniShopMsg (DecryptMiniShopMsgReq) returns (DecryptMiniShopMsgReq);
  // 同步所有分类数据
  rpc ShopSyncAllCategory(EmptyRequest) returns (BoolRequest);
  // 同步可展示的商品分类信息
  rpc ShopSyncShowCats(EmptyRequest) returns (BoolRequest);
  // 同步小店商品数据
  rpc ShopSyncProductInfo(EmptyRequest) returns (BoolRequest);
  // 微信店铺商品上架
  rpc ShopProductUpload(ShopProductDetailReq) returns (BoolRequest);
  // 微信店铺商品下架
  rpc ShopRemoveProduct(ShopProductDetailReq) returns (BoolRequest);
  // 获取微信店铺商品详情
  rpc ShopRemoteProductDetail(ShopProductDetailReq) returns (ShopProductDetailResp);

  // 获取视频号信息
  rpc GetVideoAccountInfo(IDRequest) returns (VideoAccount);
  // 更新视频号信息
  rpc UpdateVideoAccount(VideoAccountReq) returns (BoolRequest);
}

message NewShopReq {
  // @inject_tag: json:"shop_name"
  string ShopName = 1 [json_name = "shop_name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"shop_app_id"
  string ShopAppId = 2 [json_name = "shop_app_id", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"shop_app_secret"
  string ShopAppSecret = 3 [json_name = "shop_app_secret", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"msg_token"
  string MsgToken = 4 [json_name = "msg_token", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"msg_secret"
  string MsgSecret = 5 [json_name = "msg_secret", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"shop_code"
  string ShopCode = 6 [json_name = "shop_code", (validate.rules).string = {min_len: 1}];
}

message DecryptMiniShopMsgReq {
  // @inject_tag: json:"code"
  string Code = 1 [json_name = "code", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"signature"
  string Signature = 2 [json_name = "Signature", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"signature"
  string Timestamp = 3 [json_name = "timestamp", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"nonce"
  string Nonce = 4 [json_name = "nonce", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"raw_data"
  string RawData = 5 [json_name = "raw_data", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"decrypt_data"
  string DecryptData = 6 [json_name = "decrypt_data"];
  // @inject_tag: json:"app_id"
  string AppId = 7 [json_name = "app_id"];
}

message RecShopResp {
  // @inject_tag: json:"accounts"
  repeated VideoAccount accounts = 1 [json_name = "accounts"];
}

message VideoAccount {
  // @inject_tag: json:"id"
  uint64 ID = 1 [json_name = "id", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"name"
  string Name = 2 [json_name = "name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"logo_url"
  string LogoUrl = 3 [json_name = "logo_url", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"qr_code"
  string QrCode = 4 [json_name = "qr_code", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"cover_url"
  string CoverUrl = 5 [json_name = "cover_url", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"description"
  string Description = 6 [json_name = "description", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"user_video_account_category"
  UserVideoAccountCategory UserVideoAccountCategory = 7 [json_name = "user_video_account_category"];
}

message UserVideoAccountCategory {
  // @inject_tag: json:"id"
  uint64 ID = 8 [json_name = "id", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"name"
  string Name = 9 [json_name = "name", (validate.rules).string = {min_len: 0}];
}


message ShopBusinessCategoryResp {
  repeated UserVideoAccountCategory List = 1;
  uint64 Total = 2;
}

message PageReq {
  // @inject_tag: json:"page_no"
  uint64 PageNo = 1 [json_name = "page_no", (validate.rules).uint64 = {gte: 1, lte: 20}];
  // @inject_tag: json:"page_size"
  uint64 PageSize = 2 [json_name = "page_size", (validate.rules).uint64 = {gte: 1, lte: 15}];
  // @inject_tag: json:"keyword,omitEmptyRequest"
  string Keyword = 3 [json_name = "keyword"];
}
message SortReq {
  // @inject_tag: json:"sort_by,omitempty"
  string sort_by = 4 [
    json_name = "sort_by",
    (validate.rules).string = {
      in: ["price", "sales", "created_at", "popularity", ""],
      ignore_empty: true
    }
  ];
  // @inject_tag: json:"order,omitempty"
  string order = 5 [
    json_name = "order",
    (validate.rules).string = {
      in: ["asc", "desc", ""],
      ignore_empty: true
    }
  ];
}


message ShopProductDetailReq {
  // @inject_tag: json:"app_id"
  string AppId = 1 [json_name = "app_id", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"product_id"
  uint64 ProductId = 2 [json_name = "product_id", (validate.rules).uint64 = {gte: 1}];
}

message ShopProductDetailResp {
  // @inject_tag: json:"id"
  uint64 Id = 1 [json_name = "id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"shop_id"
  uint64 ShopId = 2 [json_name = "shop_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"shop_name"
  string ShopName = 3 [json_name = "shop_name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"shop_qr_code"
  string ShopQrCode = 4 [json_name = "shop_qr_code", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"title"
  string Title = 5 [json_name = "title", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"subtitle"
  string SubTitle = 6 [json_name = "subtitle", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"cats"
  string Cats = 7 [json_name = "cats", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"sales"
  uint64 Sales = 8 [json_name = "sales"];
  // @inject_tag: json:"head_imgs"
  string HeadImgs = 9 [json_name = "head_imgs", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"desc_info"
  string DescInfo = 10 [json_name = "desc_info", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"skus"
  string Skus = 11 [json_name = "skus", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"min_price"
  uint64 MinPrice = 12 [json_name = "min_price", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"on_sale"
  uint64 OnSale = 13 [json_name = "on_sale", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"product_id"
  uint64 ProductId = 14 [json_name = "product_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"cat_info1"
  ShopProductCat CatInfo1 = 15 [json_name = "cat_info1"];
  // @inject_tag: json:"cat_info2"
  ShopProductCat CatInfo2 = 16 [json_name = "cat_info2"];
  // @inject_tag: json:"cat_info3"
  ShopProductCat CatInfo3 = 17 [json_name = "cat_info3"];
}

message ShopProductCat {
  // @inject_tag: json:"id"
  uint64 Id = 1 [json_name = "id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"cat_id"
  uint64 CatId = 2 [json_name = "cat_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"f_cat_id"
  uint64 FCatID = 3 [json_name = "f_cat_id", (validate.rules).uint64 = {gte: 1}];
  // @inject_tag: json:"name"
  string Name = 4 [json_name = "name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"level"
  uint64 Level = 5 [json_name = "level", (validate.rules).uint64 = {gte: 1}];
}

message ShopProductListReq {
  PageReq PageReq = 1;
  // @inject_tag: json:"cat_id"
  uint64 CatId = 2 [json_name = "cat_id", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"order_by"
  string OrderBy = 3 [json_name = "order_by", (validate.rules).string = {in: ["created_at", "order"]}];
  // @inject_tag: json:"desc"
  bool Desc = 4;
}

message ShopProductListResp {
  repeated ShopProductDetailResp List = 1;
  uint64 Total = 2;
}

message ShopProductCatsResp {
  repeated ShopProductCat List = 1;
  uint64 Total = 2;
}

message VideoAccountReq {
  // @inject_tag: json:"name"
  string Name = 1 [json_name = "name", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"logo_url"
  string LogoUrl = 2 [json_name = "logo_url", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"qr_code"
  string QrCode = 3 [json_name = "qr_code", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"cover_url"
  string CoverUrl = 4 [json_name = "cover_url", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"category_id"
  //  uint64 CategoryId = 5 [json_name = "category_id", (validate.rules).uint64 = {gte: 0}];
  // @inject_tag: json:"description"
  string Description = 6 [json_name = "description", (validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"user_id;default=0"
  uint64 UserId = 7 [json_name = "user_id"];
  // @inject_tag: json:"city"
  string City = 8 [json_name = "city"];
  // @inject_tag: json:"user_video_account_category"
  UserVideoAccountCategory UserVideoAccountCategory = 9 [json_name = "user_video_account_category"];

}
message WxShopMsgReq {
  // @inject_tag: json:"app_id"
  string AppId = 1 [json_name = "app_id", (validate.rules).string = {min_len: 1}];
  string ToUserName = 4 [(validate.rules).string = {min_len: 1}];
  string FromUserName = 5 [(validate.rules).string = {min_len: 1}];
  // @inject_tag: json:"order_id"
  string OrderId = 6 [json_name = "order_id"];
}

message GetAggregationLiveRecordListReq{
  PageReq PageReq = 1;
  string Class = 2;
}
message GetAggregationLiveRecordListResp{
  repeated AggregationLiveRecordList List = 1;
  uint64 Total = 2;
}
message AggregationLiveRecordList {
  uint64 ID = 1;                           // 记录ID
  string ExportID = 2;                     // 直播ID
  string Description = 3;                  // 直播描述
  string PromoterShareLink = 4;            // 推广参数
  string TalentAppid = 5;                  // 达人平台的appid
  string TalentNickname = 6;               // 达人昵称
  string TalentHeadImg = 7;                // 达人头像
  int32 PredictMaxCommissionAmount = 8;    // 最高返佣率
  string Type = 9;                         // 直播间类型 (none/notice/live)
  string InfoProgress = 10;                // 资料状态
  string NoticeID = 11;                    // 预约ID
  int64 StartTime = 12;                    // 开播时间
  string StartIsoTime = 13;
  int32 SharerTotal = 14;                  // 分享者总数
  repeated string SharerHeadImgs = 15;     // 分享者头像列表
  string Class = 16;     // 直播分类
}

// 获取直播间详情请求
message GetLiveDetailByTalentAppidReq {
  uint64 id = 1; // 列表id
  string TalentAppid = 2; // 达人平台的appid
}

// 直播商品信息
message LiveGoodsInfo {
  uint64 ProductId = 1;                // 商品id
  string ProductName = 2;              // 商品名称
  string ProductImgUrl = 3;           // 商品的图片url
  double ProductPrice = 4;              // 商品售价【单位：分】
  double PredictCommissionAmount = 5;  // 预估可得佣金金额【单位：分】
}

// 获取直播间详情响应
message GetLiveDetailByTalentAppidResp {
  string TalentAppid = 1;              // 达人平台的appid
  string TalentNickname = 2;           // 达人昵称
  string TalentHeadImg = 3;           // 达人头像
  string Description = 4;               // 直播描述
  string ExportId = 5;                 // 直播ID
  int32 PredictMaxCommissionAmount = 6; // 最高返佣率
  string PromoterShareLink = 7;        // 推广链接
  string Type = 8;                         // 直播间类型 (notice/live)
  int64 StartTime = 9;                    // 开播时间
  string StartIsoTime = 10;
  string Class = 11;
  string PromoterId = 12;
  repeated LiveGoodsInfo GoodsList = 13; // 商品列表
}

message SyncRemotePromoteProductByNotifyReq{
  string ShopAppid = 1;
  uint64 ProductId = 2;
  int32 EventType = 3;
  repeated string UpdateFields = 4;
  string MsgName = 5;  // 消息名称
  int32 PlanType = 6;  // 计划类型
  int32 PlanStatus = 7; // 计划状态
}

message SyncRemoteCommissionOrdersByNotifyReq{
  int32 OrderStatus = 1;
  int32 CommissionOrderStatus = 2;
  string SkuID = 3;
  string OrderID = 4;
}

// 创建订单请求
message CreateOrderReq {
  string BuyerRemark = 1;    // 买家留言
  string PayMethod = 2;      // 支付方式(微信/支付宝/余额)
  string DeliveryType = 3;   // 配送方式(快递配送 门店自提 无需配送)
  string Platform = 4;       // 来源客户端 (小程序,后台等)
  uint64 UserID = 5;         // 用户ID
  uint64 ProductID = 6;      // 产品ID
  uint32 Quantity = 7;       // 购买数量
  string TccAction = 8;     // TCC操作类型: try/confirm/cancel
  string OrderNo = 9;       // 订单号（confirm/cancel时必填）
}

// 创建订单响应
message CreateOrderResp {
  string OrderNo = 1;        // 订单号
  uint32 TotalPrice = 2;     // 商品总金额(不含优惠折扣)
  uint32 OrderPrice = 3;     // 订单金额(含优惠折扣)
  uint32 PayPrice = 4;       // 实际付款金额(包含运费)
  string OrderStatus = 5;    // 订单状态
  bool Success = 6;          // 是否成功
  string Message = 7;        // 消息
}

// 更新订单请求
message UpdateOrderReq {
  string OrderNo = 1;        // 订单号
  uint32 PayPrice = 2;       // 实际付款金额(包含运费)
  uint32 UpdatePrice = 3;    // 后台修改的订单金额(差价)
  string PayStatus = 4;      // 付款状态(未付款 已付款)
  int64 PayTime = 5;         // 付款时间
  string TradeID = 6;        // 第三方交易记录ID
  uint32 ExpressPrice = 7;   // 运费金额
  string DeliveryStatus = 8; // 发货状态(未发货 已发货 部分发货)
  int64 DeliveryTime = 9;    // 发货时间
  string ReceiptStatus = 10; // 收货状态(未收货 已收货)
  int64 ReceiptTime = 11;    // 收货时间
  string OrderStatus = 12;   // 订单状态(进行中 已取消 已关闭 已完成)
  uint32 PointsBonus = 13;   // 赠送的爆点数量
  string MerchantRemark = 14;// 商家备注
  uint32 IsSettled = 15;     // 订单是否已结算(0未结算 1已结算)
  int64 SettledTime = 16;    // 订单结算时间
  string TccAction = 17;     // TCC操作类型: try/confirm/cancel
}

// 更新订单响应
message UpdateOrderResp {
  bool Success = 1;          // 更新是否成功
  string OrderNo = 2;        // 订单号
  string OrderStatus = 3;    // 更新后的订单状态
  string Message = 4;
}

// 根据订单号查询订单请求
message GetOrderByNoReq {
  string OrderNo = 1;        // 订单号
}

// 根据订单号查询订单响应
message GetOrderByNoResp {
  string OrderNo = 1;        // 订单号
  uint32 TotalPrice = 2;     // 总价
  uint32 OrderPrice = 3;     // 订单价格
  uint32 PayPrice = 4;       // 支付价格
  string OrderStatus = 5;    // 订单状态
  string PayStatus = 6;      // 支付状态
  string DeliveryStatus = 7; // 配送状态
  string ReceiptStatus = 8;  // 收货状态
  uint64 UserId = 9;         // 用户ID
  uint32 ProductId = 10;     // 产品ID
  string ProductType = 11;   // 产品类型
}

// 调整用户库存请求
message AdjustUserInventoryReq {
  uint64 UserID = 1 [(validate.rules).uint64 = {gt: 0}];     // 用户ID
  uint64 ProductID = 2 [(validate.rules).uint64 = {gt: 0}];  // 产品ID
  int32 Quantity = 3;                                        // 调整数量(正数增加，负数减少)
  string Remark = 4;                                         // 调整原因备注
  string TransactionType = 5;                                // 调整类型
  string TccAction = 6;                                     // TCC操作类型: try/confirm/cancel
  string OperationId = 7;                                   // 操作ID（confirm/cancel时必填）
}

// 调整用户库存响应
message AdjustUserInventoryResp {
  bool Success = 1;          // 是否成功
  string OperationID = 2;    // 操作ID
  uint32 BeforeQuantity = 3; // 调整前数量
  uint32 AfterQuantity = 4;  // 调整后数量
  string Message = 5;        // 消息
}

// 获取用户橱窗商品列表请求
message GetUserWindowProductsReq {
  uint64 UserId = 1;              // 用户ID
  int32 NextOffset = 2;           // 分页偏移量
  int32 PageSize = 3;             // 每页数量
}

// 获取用户橱窗商品列表响应
message GetUserWindowProductsResp {
  repeated ProductInfo productList = 1; // 商品列表
  int32 Total = 2;                 // 总数
  int32 NextOffset = 3;           // 下一页偏移量
  bool HasMore = 4;               // 是否有更多
  string SharerAppid = 5;
}

// 删除达人商品请求
message DeleteTalentProductReq {
  string shop_appid = 1;  // 商品所属店铺appid
  uint64 product_id = 2;  // 商品ID
}

message SyncRemoteTalentProductByNotifyReq {
  // @inject_tag: json:"product_id"
  int64 ProductId = 1 [json_name = "product_id", (validate.rules).int64 = {gt: 0}];
  // @inject_tag: json:"shop_appid"
  string ShopAppid = 2 [json_name = "shop_appid", (validate.rules).string = {min_len: 1}];
}

//****************************************机构联盟***************************************
service WxLeague {
  // 获取推客的注册和绑定状态
  rpc SharerRegisterAndBindStatus (SharerRegisterAndBindStatusReq) returns (SharerRegisterAndBindStatusResp);
  // 设置推客的分佣模式和分佣比例
  rpc SetSharerCommissionInfo(SetSharerCommissionInfoReq) returns (BoolRequest);
  // 获取直播列表
  rpc GetLiveRecordList(EmptyRequest) returns (GetLiveRecordListResp);
  // 获取直播预约列表
  rpc GetLiveNoticeRecordList(EmptyRequest) returns(GetLiveNoticeRecordListResp);
  // 获取直播|预约聚合列表
  rpc GetAggregationLiveRecordList(GetAggregationLiveRecordListReq) returns(GetAggregationLiveRecordListResp);
  // 根据TalentAppid获取直播间详情
  rpc GetLiveDetailByTalentAppid(GetLiveDetailByTalentAppidReq) returns(GetLiveDetailByTalentAppidResp);
  // 获取指定店铺详情
  rpc GetShopDetail(ShopGetReq) returns (ShopGetResp);
  // 获取可推广的商品列表
  rpc GetPromoteProductList(GetPromoteProductListReq) returns (GetPromoteProductListResp);
  // 获取可推广的商品详情
  rpc GetPromoteProductDetail(GetPromoteProductDetailReq) returns (GetPromoteProductDetailResp);
  // 获取达人合作商品列表
  rpc GetTalentProductList(GetTalentProductListReq) returns (GetTalentProductListResp);
  // 获取合作商品详情
  rpc GetTalentProductDetail(GetPromoteProductDetailReq) returns (GetPromoteProductDetailResp);
  // 获取商店列表
  rpc GetShopList(ShopListGetReq) returns (ShopListGetResp);
  // 获取短视频列表
  rpc GetFeedList(GetFeedListReq) returns (GetFeedListResp);
  // 获取直播推广二维码
  rpc GetLiveRecordQrCode(GetLiveRecordQrCodeReq) returns (GetLiveRecordQrCodeResp);
  // 获取直播预约推广二维码
  rpc GetLiveNoticeRecordQrCode(GetLiveNoticeRecordQrCodeReq) returns (GetLiveNoticeRecordQrCodeResp);
  // 获取推客商品推广二维码
  rpc GetProductQrCode(GetProductQrCodeReq) returns (GetProductQrCodeResp);
  // 获取推客商品推广短链
  rpc GetProductShortLink(GetProductShortLinkReq) returns (GetProductShortLinkResp);

  // 同步店铺关联推客账号
  rpc SyncRemoteShopPromoterList(EmptyRequest) returns (EmptyRequest);
  // 同步店铺直播列表
  rpc SyncRemoteShopLiveRecordList(EmptyRequest) returns (EmptyRequest);
  // 同步店铺直播最高返佣
  rpc SyncRemoteShopLiveCommissionRate(EmptyRequest) returns (EmptyRequest);
  // 同步店铺直播预约列表
  rpc SyncRemoteShopLiveNoticeRecordList(EmptyRequest) returns (EmptyRequest);

  // 同步远程达人平台列表
  rpc SyncRemoteBindTalentList(EmptyRequest) returns (EmptyRequest);
  // 同步远程达人平台的直播列表
  rpc SyncRemoteLiveRecordList(EmptyRequest) returns (EmptyRequest);
  // 同步远程短视频列表
  rpc SyncRemoteFeedList(EmptyRequest) returns (EmptyRequest);
  // 同步远程小店关联账号的短视频列表
  rpc SyncRemoteShopFeedList(EmptyRequest) returns (EmptyRequest);
  // 同步远程直播的达人分佣信息
  rpc SyncRemoteLiveCommissionRate(EmptyRequest) returns (EmptyRequest);
  // 同步远程直播的达人预约直播列表
  rpc SyncRemoteNoticeRecordList(EmptyRequest) returns (EmptyRequest);
  // 同步远程可推荐商品列表
  rpc SyncRemotePromoteProductList(EmptyRequest) returns (EmptyRequest);
  // Deprecated 同步远程达人商品列表
  rpc SyncRemoteTalentProductList(EmptyRequest) returns (EmptyRequest);
  // 分页同步商品
  rpc SyncRemoteTalentProductListPaged(EmptyRequest) returns (EmptyRequest);
  // 同步单个商品
  rpc SyncRemoteTalentProductByNotify(SyncRemoteTalentProductByNotifyReq) returns (BoolRequest);
  // 删除同步商品
  rpc DeleteTempProduct(DeleteTalentProductReq) returns (BoolRequest);
  // 通过消息回调同步远程可推荐商品列表
  rpc SyncRemotePromoteProductByNotify(SyncRemotePromoteProductByNotifyReq) returns (BoolRequest);
  // 通过消息回调同步远程佣单列表
  rpc SyncRemoteCommissionOrdersByNotify(SyncRemoteCommissionOrdersByNotifyReq) returns (BoolRequest);
  // 获取结算明细
  rpc GetSettlementDetail(GetSettlementDetailReq) returns (GetSettlementDetailResp);
  // 获取用户详情
  rpc GetUserInfo(UserInfoReq) returns(UserInfoResp);
  // 用户升级判定服务
  rpc TalentUserUpgrade(EmptyRequest) returns (EmptyRequest);
  // 处理订单佣金结算
  rpc ProcessOrderCommission(ProcessOrderCommissionReq) returns (BoolRequest);
  // 获取产品分类列表
  rpc GetProductCategoryList(EmptyRequest) returns (ProductCategoryListResp);
  // 获取公司信息
  rpc GetCompanyInfo(GetCompanyInfoReq) returns (CompanyInfoResp);
  // 保存公司信息
  rpc SaveCompanyInfo(CompanyInfoSaveReq) returns (CompanyInfoResp);
  // 创建订单
  rpc CreateOrder(CreateOrderReq) returns (CreateOrderResp);
  // 更新订单
  rpc UpdateOrder(UpdateOrderReq) returns (UpdateOrderResp);
  // 根据订单号查询订单
  rpc GetOrderByNo(GetOrderByNoReq) returns (GetOrderByNoResp);
  // 调整用户库存
  rpc AdjustUserInventory(AdjustUserInventoryReq) returns (AdjustUserInventoryResp);
  // 设置业务员权限
  rpc SetSalesmanPermission(SetSalesmanPermissionReq) returns(BoolRequest);
  // 获取员工列表
  rpc GetSalesmanList(GetSalesmanListReq) returns (GetSalesmanListResp);
  // 通过手机号搜索粉丝
  rpc SearchFansByMobile(SearchFansByMobileReq) returns (SearchFansByMobileResp);

  // 设置粉丝为团长
  rpc SetFanToGroup(SetFanToGroupReq) returns (BoolRequest);

  // 获取代理商/业务员个人主页数据
  rpc GetAgentProfile(GetAgentProfileReq) returns (GetAgentProfileResp);
  // 获取员工信息
  rpc GetSalesmanInfo(GetSalesmanInfoReq) returns (GetSalesmanInfoResp);

  // 获取达人橱窗授权状态
  rpc GetWindowAuthStatus(GetWindowAuthStatusReq) returns (GetWindowAuthStatusResp);
  // 添加商品到橱窗
  rpc AddProductToWindow(AddProductToWindowReq) returns (AddProductToWindowResp);
  // 获取用户橱窗商品列表
  rpc GetUserWindowProducts(GetUserWindowProductsReq) returns (GetUserWindowProductsResp);
  // 代理商给业务经理设置团长套餐数量
  rpc SetSalesmanTeamPackage(SetSalesmanTeamPackageReq) returns (BoolRequest);
  // 获取好物商品列表
  rpc GetGoodProducts(GetGoodProductsReq) returns (GetGoodProductsResp);

  // 获取好物商品详情
  rpc GetGoodProductDetail(GetGoodProductDetailReq) returns (GetGoodProductDetailResp);
  // 获取某个推客某个商品的内嵌商品卡片
  rpc GetPromoterSingleProductPromotionInfo(GetPromoterSingleProductPromotionInfoReq) returns (GetPromoterSingleProductPromotionInfoResp);
  // 获取小店关联账号直播推广二维码
  rpc GetShopLiveRecordQrCode(GetShopLiveRecordQrCodeReq) returns (GetShopLiveRecordQrCodeResp);
  // 获取小店关联账号直播预约推广二维码
  rpc GetShopLiveNoticeRecordQrCode(GetShopLiveNoticeRecordQrCodeReq) returns (GetShopLiveNoticeRecordQrCodeResp);
  // 提现预算计算
  rpc WithdrawPreview(WithdrawPreviewReq) returns (WithdrawPreviewResp);
  // 用户提现
  rpc Withdraw(WithdrawReq) returns (WithdrawResp);
  // 微信支付回调
  rpc WxPayNotify(WxPayNotifyReq) returns (WxPayNotifyResp);
}
// 产品分类项
message ProductCategoryItem {
  // @inject_tag: json:"cat_id"
  uint64 cat_id = 1 [json_name = "cat_id"];     // 分类ID
  // @inject_tag: json:"name"
  string name = 2 [json_name = "name"];         // 分类名称
}

// 获取产品分类列表响应
message ProductCategoryListResp {
  // @inject_tag: json:"list"
  repeated ProductCategoryItem list = 1 [json_name = "list"];  // 分类列表
  // @inject_tag: json:"total"
  uint64 total = 2 [json_name = "total"];                    // 总数
}
message SharerRegisterAndBindStatusReq {
  int64 UserId = 1 [(validate.rules).int64 = {gt: 0}];
}

message SharerRegisterAndBindStatusResp {
  // 和机构的绑定状态，0：未绑定 1：已绑定
  int64 BindStatus = 1 [json_name = "bind_status"];
  // 0：未注册 1：注册中，还未完成 2：已完成注册 3:用户未支付实名，需要把微信先支付实名才能继续注册
  int64 RegisterStatus = 2 [json_name = "register_status"];
  // 机构的appid
  string HeadSupplierAppid = 3 [json_name = "head_supplier_appid"];
  // 绑定时需要的queryString参数
  string QueryString = 4 [json_name = "query_string"];
  // bind_business_type
  string BusinessType = 5 [json_name = "business_type"];
  // 分佣类型【0：平台分佣，1:机构自己分佣】
  int64 CommissionType = 6 [json_name = "commission_type"];
  // 分佣比例
  int64 CommissionRatio = 7 [json_name = "commission_ratio"];
}

// 设置推客的分佣模式和分佣比例请求
message SetSharerCommissionInfoReq {
    // 推客在微信电商平台注册的身份标识
    string SharerAppid = 1 [json_name = "sharer_appid"];
    // 分佣类型【0：平台分佣，1:机构自己分佣】
    int64 CommissionType = 2 [json_name = "commission_type"];
    // 平台分佣时的分佣比例，范围为【100000 - 900000】，代表【10%-90%】
    int64 CommissionRatio = 3 [json_name = "commission_ratio"];
}
message GetLiveRecordListReq {
  string talentAppid = 1;        // 达人平台的appid
  string miniProgramAppid = 2;  // 需要挂载的小程序appid
  string sharerAppid = 3;        // 推客appid (可选)
}

message LiveRecordInfo {
  string exportId = 1;            // 直播id
  string description = 2;          // 直播描述
  string promoterShareLink = 3;  // 内嵌直播卡片时需要的推广参数
}

message GetLiveRecordListResp {
  repeated LiveRecordInfo liveRecordList = 1;  // 直播列表
}


// 获取合作小店详情请求
message ShopGetReq {
    string appid = 1;     // 小店appid
    int32 pageSize = 2;  // 获取小店数量（不超过30）
}

// 小店基础信息
message BizBaseInfo {
    string appid = 1;        // 小店appid
    string headImgUrl = 2;  // 小店头像
    string nickname = 3;     // 小店昵称
}

// 小店数据信息
message ShopDataInfo {
    int64 gmv = 1;                         // 合作动销GMV，单位：分
    int32 productNumber = 2;              // 历史合作商品数
    int64 settleAmount = 3;               // 已结算服务费，单位：分
    int64 unsettleAmount = 4;             // 预计待结算服务费，单位：分
    int32 productNumberToday = 5;        // 今日新增合作商品数
    int32 productNumberSoldToday = 6;   // 今日动销商品数
}

// 小店详情
message ShopDetail {
    BizBaseInfo baseInfo = 1;    // 小店基础信息
    ShopDataInfo dataInfo = 2;   // 小店数据信息
    int32 status = 3;             // 合作状态
    int64 approvedTime = 4;      // 开始合作时间戳
}

// 获取合作小店详情响应
message ShopGetResp {
    ShopDetail shopDetail = 1;   // 小店详情
    string nextKey = 2;          // 本次翻页的上下文
    bool hasMore = 3;            // 是否还有剩余小店
}



// 范围定义
message RangeInfo {
    string min = 1; // 区间最小值
    string max = 2; // 区间最大值
}

// 商品查询条件
message SpuItemCondition {
    RangeInfo sellingPriceRange = 1;    // 售卖价区间，单位是分
    RangeInfo monthlySalesRange = 2;    // 月销量区间
    repeated string flags = 3;            // 保障标
    RangeInfo serviceFeeRateRange = 4; // 服务费率区间，单位是10万分之
    RangeInfo commissionRateRange = 5;  // 佣金率区间，单位是10万分之
    RangeInfo promoteTimeRange = 6;     // 推广时间范围
}

// 商品类目查询条件
message CategoryInfo {
    string categoryId = 1;              // 商品类目
    string categoryName = 2;            // 商品类目名称
    repeated string categoryIds1 = 3;  // 一级类目列表
    repeated string categoryIds2 = 4;  // 二级类目列表
    repeated string categoryIds3 = 5;  // 三级类目列表
}

// 商品信息
message ProductInfo {
    string title = 1;                  // 标题
    string subTitle = 2;              // 副标题
    repeated string headImgs = 3;     // 主图
    ProductDescInfo descInfo = 4;     // 商品详情
    repeated CatInfo cats = 5;         // 类目信息
    repeated CatInfo catsV2 = 6;      // 新类目树
    repeated SkuInfo skus = 7;         // sku信息
    uint64 productId = 8;  // 商品id
    string shopAppid = 9; // 商品所属店铺appid
    string HeadSupplierItemLink = 10; // 团长商品链接
    int64 MinPrice = 11; // 商品最小价格
    double EarnAmount = 12; // 佣金
}

// 获取可推广的商品列表请求
message GetPromoteProductListReq {
    PageReq PageReq = 1;
    uint64 CatId = 2;
    int64 UserId = 3;             // 用户ID，用于获取推客appid
    uint64 IsRecommend = 4;
    uint64 IsPreferred = 5;
    uint64 IsWindow = 6;
    SortReq SortReq = 7;
}

// 获取达人合作商品列表请求
message GetTalentProductListReq {
    PageReq PageReq = 1;
    uint64 CatId = 2;
    int64 UserId = 3;             // 用户ID，用于获取推客appid
    int64 CommissionType = 4;     // 佣金分配类型 0：商家指定达人佣金, 1：机构指定达人佣金
    uint64 IsRecommend = 5;       // 是否推荐商品
    SortReq SortReq = 6;          // 排序参数
}
// 获取达人合作商品列表响应
message GetTalentProductListResp {
  repeated ProductInfo productList = 1; // 商品列表
  uint64 Total = 2;
}
// 获取可推广的商品列表响应
message GetPromoteProductListResp {
    repeated ProductInfo productList = 1; // 商品列表
    uint64 Total = 2;
}

// 获取可推广的商品详情请求
message GetPromoteProductDetailReq {
    string shopAppid = 1;           // 团长商品所属小店appid
    uint64 productId = 2;            // 商品id
    int32 planType = 3;             // 商品计划类型
    bool getAvailableCoupon = 4;   // 是否返回可用券
    int64 UserId = 5;             // 用户ID，用于获取推客appid
}

// 获取可推广的商品详情响应
message GetPromoteProductDetailResp {
    ProductDetail product = 1;            // 商品详情
    repeated Coupon publishCoupons = 2;     // 公开机构券
    repeated Coupon cooperativeCoupons = 3; // 定向机构券
}

// 商品详情相关消息定义
message ProductDescInfo {
    repeated string imgs = 1;  // 商品详情图片
    string desc = 2;           // 商品详情文字
}

message SkuAttr {
    string attrKey = 1;    // 属性键
    string attrValue = 2;  // 属性值
}

message CatInfo {
    string catId = 1;     // 类目id
    string catName = 2;   // 类目名称
}

message ProductDetailInfo {
    string title = 1;                  // 标题
    string subTitle = 2;              // 副标题
    repeated string headImgs = 3;     // 主图
    ProductDescInfo descInfo = 4;     // 商品详情
    repeated CatInfo cats = 5;         // 类目信息
    repeated CatInfo catsV2 = 6;      // 新类目树
    repeated SkuInfo skus = 7;         // sku信息
}

message SkuInfo {
    string skuId = 1;  // sku id
    string thumbImg = 2;  // sku 名称
    int64 salePrice = 3;  // sku 价格
    int64 stockNum = 4;  // sku 库存
    repeated SkuAttr skuAttrs = 5;  // sku 属性
    double EarnAmount = 6; // 返利金额
}

message CommissionInfo {
    int32 status = 1;         // 商品带货状态
    int32 serviceRatio = 2;  // 服务费率
    int64 startTime = 3;     // 合作开始时间
    int64 endTime = 4;       // 合作结束时间
}

message Coupon {
    string couponId = 1;  // 券的id
}

message ProductDetail {
    string shopAppid = 1;              // 所属小店appid
    uint64  productId = 2;                // 商品id
    string productPromotionLink = 3;  // 商品卡片透传参数
    ProductDetailInfo productInfo = 4; // 商品信息
}

// 获取合作小店列表请求
message ShopListGetReq {
    int32 pageSize = 1;  // 获取小店数量（不超过30）
    string nextKey = 2;  // 由上次请求返回，顺序翻页时需要传入
}

// 合作小店列表项详情 (复用 BizBaseInfo)
message ShopListItemDetail {
    BizBaseInfo baseInfo = 1; // 小店基础信息
    int32 status = 2;          // 合作状态
}

// 获取合作小店列表响应
message ShopListGetResp {
    repeated ShopListItemDetail shopList = 1; // 小店详情列表
    string nextKey = 2;                       // 本次翻页的上下文
    bool hasMore = 3;                         // 是否还有剩余小店
}

// 短视频商品信息
message FeedProductInfo {
    uint64 productId = 1;
    string productName = 2;
    string productImgUrl = 3;
    int64 productMiniPrice = 4; // 预期是分
}

// 短视频信息
message FeedInfo {
    string exportId = 1;
    string talentAppid = 2;
    int64 predictCommissionAmount = 3; // 预期是分
    FeedProductInfo productInfo = 4;
    string feedToken = 5;          // 内嵌短视频的卡片信息
    string promoterShareLink = 6; // 推客推广信息
    double earnAmount = 7;        // 实际佣金金额（元）
}

// 获取达人平台推广的短视频信息请求
message GetFeedListReq {
    string talentAppid = 1; // 合作的达人平台id (可选)
    PageReq PageReq = 2;    // 分页参数
    int64 UserId = 3;       // 用户ID，用于获取推客appid
}

// 获取达人平台推广的短视频信息响应
message GetFeedListResp {
    repeated FeedInfo feedList = 1; // 可以推广的短视频列表
    int64 Total = 2;                // 总数
    int64 PageNo = 3;               // 当前页
    int64 PageSize = 4;             // 每页大小
}

// 直播预约信息
message LiveNoticeRecordInfo {
    string noticeId = 1;     // 预约id
    string description = 2;   // 预约描述
    int64 startTime = 3;     // 预约对应的开播时间
}

// 获取达人平台直播预约列表响应
message GetLiveNoticeRecordListResp {
    repeated LiveNoticeRecordInfo list = 1; // 直播预约列表
}

// GetLiveRecordQrCodeReq 获取直播推广二维码请求
message GetLiveRecordQrCodeReq {
  string ExportId = 1; // 直播ID
  int64 UserId = 2;    // 用户ID，用于获取推客appid
  string TalentAppid = 3;
}

// GetLiveRecordQrCodeResp 获取直播推广二维码响应
message GetLiveRecordQrCodeResp {
  string QrcodeUrl = 1; // 二维码URL
}

// GetLiveNoticeRecordQrCodeReq 获取直播预约推广二维码请求
message GetLiveNoticeRecordQrCodeReq {
  string NoticeId = 1; // 直播预约ID
  int64 UserId = 2;    // 用户ID，用于获取推客appid
  string TalentAppid = 3;
}

// GetLiveNoticeRecordQrCodeResp 获取直播预约推广二维码响应
message GetLiveNoticeRecordQrCodeResp {
  string QrcodeUrl = 1; // 二维码URL
}

// GetProductQrCodeReq 获取推客商品推广二维码请求
message GetProductQrCodeReq {
  uint64 ProductId = 1; // 商品ID
  string ShopAppid = 2; // 商品所属店铺appid
  int64 UserId = 3;    // 用户ID，用于获取推客appid
}

// GetProductQrCodeResp 获取推客商品推广二维码响应
message GetProductQrCodeResp {
  string QrcodeUrl = 1; // 二维码URL
}

// GetProductShortLinkReq 获取推客商品推广短链请求
message GetProductShortLinkReq {
  uint64 ProductId = 1; // 商品ID
  string ShopAppid = 2; // 商品所属店铺appid
  int64 UserId = 3;    // 用户ID，用于获取推客appid
}

// GetProductShortLinkResp 获取推客商品推广短链响应
message GetProductShortLinkResp {
  string ShortLink = 1; // 推广短链
}

// GetLiveCommissionProductListReq 获取直播带货商品列表请求
message GetLiveCommissionProductListReq {
  string TalentAppid = 1; // 达人平台的appid
  string NextKey = 2;     // 分页参数，第一页为空
  int32 PageSize = 3;     // 一页获取多少个商品，最大10
}

// LiveCommissionProductInfo 直播带货商品信息
message LiveCommissionProductInfo {
  int64 ProductId = 1;                // 商品id
  string ProductName = 2;             // 商品名称
  string ProductImgUrl = 3;           // 商品的图片url
  int64 ProductPrice = 4;             // 商品售价【单位：分】
  int64 PredictCommissionAmount = 5;  // 机构预估可得佣金金额【单位：分】
}

// GetLiveCommissionProductListResp 获取直播带货商品列表响应
message GetLiveCommissionProductListResp {
  string NextKey = 1;                          // 下一页的key内容
  bool HasMore = 2;                            // 是否还有下一页
  repeated LiveCommissionProductInfo ProductList = 3; // 商品列表
}


// 获取结算明细请求
message GetSettlementDetailReq {
  uint64 user_id = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
  string month = 2;    // 月份，格式：2025-05
}

// 结算明细项
message SettlementDetailItem {
  uint64 id = 1;                      // ID
  int64 amount = 2;                   // 金额
  int64 amount_change = 3;            // 变动金额
  int32 type = 4;                     // 明细类型
  string create_time = 5;             // 创建时间
  string remark = 6;                  // 备注
}

// 获取结算明细响应
message GetSettlementDetailResp {
  repeated SettlementDetailItem list = 1;  // 明细列表
}


// 用户详情请求
message UserInfoReq {
  uint64 user_id = 1; // 用户ID
}

// 用户详情响应
message UserInfoResp {
  uint64 id = 1;              // 用户ID
  string created_at = 2;      // 创建时间
  string updated_at = 3;      // 更新时间
  string uuid = 4;            // UUID
  string mobile = 5;          // 手机号
  string nick_name = 6;       // 昵称
  string avatar = 7;          // 头像
  uint32 gender = 8;          // 性别
  string country = 9;         // 国家
  string province = 10;       // 省份
  string city = 11;           // 城市
  uint32 address_id = 12;     // 默认地址ID
  double balance = 13;        // 可用余额
  uint32 points = 14;         // 可用积分
  double pay_money = 15;      // 总支付金额
  double expend_money = 16;   // 实际消费金额
  uint32 grade_id = 17;       // 会员等级ID
  string platform = 18;       // 注册平台
  uint32 last_login_time = 19;// 最后登录时间
  string openid = 20;         // 微信openid
  string unionid = 21;        // 微信unionid
  string sharer_appid = 22;   // 推客appid
  string contact_name = 23;   // 联系人姓名
  string contact_number = 24; // 联系电话
  string share_code = 25;     // 分享码
  string share_code_image = 26;// 分享码图片
  string reg_ipv4 = 27;       // 注册IP
  uint64 invite_from = 28;    // 邀请人
  string reg_source = 29;     // 注册来源
  int32 level = 30;          // 用户等级
  string user_level = 31;  // 添加等级名称字段
  int32 is_enterprise = 32;   // 是否企业用户
  string birthday = 33;       // 生日
}

// 处理订单佣金结算请求
message ProcessOrderCommissionReq {
  string order_id = 1 [(validate.rules).string = {min_len: 1}];  // 订单ID
}

// 获取公司信息请求
message GetCompanyInfoReq {
  // @inject_tag: json:"user_id"
  uint64 user_id = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
}

// 公司信息保存请求
message CompanyInfoSaveReq {
  // @inject_tag: json:"user_id"
  uint64 user_id = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID
  // @inject_tag: json:"name"
  string name = 2 [json_name = "name", (validate.rules).string = {min_len: 1}];           // 公司名称
  // @inject_tag: json:"address"
  string address = 3 [json_name = "address", (validate.rules).string = {min_len: 1}];     // 公司地址
  // @inject_tag: json:"bank_account"
  string bank_account = 4 [json_name = "bank_account", (validate.rules).string = {min_len: 1}]; // 银行账号
  // @inject_tag: json:"bank_name"
  string bank_name = 5 [json_name = "bank_name", (validate.rules).string = {min_len: 1}];  // 开户银行
  // @inject_tag: json:"tax_number"
  string tax_number = 6 [json_name = "tax_number", (validate.rules).string = {min_len: 1}]; // 税号
  // @inject_tag: json:"phone"
  string phone = 7 [json_name = "phone", (validate.rules).string = {min_len: 1}];         // 联系电话
  // @inject_tag: json:"license_img"
  string license_img = 8 [json_name = "license_img", (validate.rules).string = {min_len: 1}]; // 营业执照图片
}

// 公司信息响应
message CompanyInfoResp {
  // @inject_tag: json:"id"
  int64 id = 1 [json_name = "id"];                         // 公司ID
  // @inject_tag: json:"name"
  string name = 2 [json_name = "name"];                    // 公司名称
  // @inject_tag: json:"address"
  string address = 3 [json_name = "address"];              // 公司地址
  // @inject_tag: json:"bank_account"
  string bank_account = 4 [json_name = "bank_account"];    // 银行账号
  // @inject_tag: json:"bank_name"
  string bank_name = 5 [json_name = "bank_name"];          // 开户银行
  // @inject_tag: json:"tax_number"
  string tax_number = 6 [json_name = "tax_number"];        // 税号
  // @inject_tag: json:"phone"
  string phone = 7 [json_name = "phone"];                  // 联系电话
  // @inject_tag: json:"license_img"
  string license_img = 8 [json_name = "license_img"];      // 营业执照图片
  // @inject_tag: json:"created_at"
  string created_at = 9 [json_name = "created_at"];        // 创建时间
  // @inject_tag: json:"updated_at"
  string updated_at = 10 [json_name = "updated_at"];       // 更新时间
  // @inject_tag: json:"status"
  int32 status = 11 [json_name = "status"];                         // 审核状态
  // @inject_tag: json:"remark"
  string remark = 12 [json_name = "remark"];          // 审核结果
}


// 设置业务员权限请求
message SetSalesmanPermissionReq {
  uint64 salesman_user_id = 1;  // 业务员userid
  double salesman_rate = 2;     // 代理给业务员佣金比例
  string salesman_remark = 3;   // 业务员备注
  int64 is_full_time = 4;        // 全职兼职
  uint64 agent_user_id = 5;     // 代理商userid
}

// 获取员工列表请求
message GetSalesmanListReq {
  // @inject_tag: json:"user_id"
  uint64 user_id = 1 [(validate.rules).uint64 = {gt: 0}];  // 用户ID

  PageReq PageReq = 2;

}

// 员工信息
message SalesmanItem {
  // @inject_tag: json:"user_id"
  uint64 user_id = 1;    // 用户ID
  // @inject_tag: json:"nick_name"
  string nick_name = 2;  // 昵称
  // @inject_tag: json:"avatar"
  string avatar = 3;     // 头像
  // @inject_tag: json:"rate"
  double rate = 4;       // 佣金比例
  // @inject_tag: json:"remark"
  string remark = 5;     // 备注
  // @inject_tag: json:"is_full_time"
  int64 is_full_time = 6; // 是否全职
  // @inject_tag: json:"created_at"
  string created_at = 7; // 添加时间
  string mobile = 8;     // 手机号
}

// 获取员工列表响应
message GetSalesmanListResp {
  // @inject_tag: json:"list"
  repeated SalesmanItem list = 1;  // 员工列表
  // @inject_tag: json:"total"
  uint64 total = 2;               // 总数
}

// 通过手机号搜索粉丝请求
message SearchFansByMobileReq {
  // @inject_tag: json:"mobile"
  string mobile = 1 [json_name = "mobile", (validate.rules).string = {min_len: 1}]; // 粉丝手机号
  // @inject_tag: json:"user_id"
  uint64 user_id = 2 [(validate.rules).uint64 = {gt: 0}];  // 代理商用户ID
}

// 粉丝信息项
message FansItem {
  // @inject_tag: json:"user_id"
  uint64 user_id = 1;    // 用户ID
  // @inject_tag: json:"nick_name"
  string nick_name = 2;  // 昵称
  // @inject_tag: json:"avatar"
  string avatar = 3;     // 头像
  // @inject_tag: json:"mobile"
  string mobile = 4;     // 手机号
  // @inject_tag: json:"created_at"
  string created_at = 5; // 注册时间
  // @inject_tag: json:"is_salesman"
  bool is_salesman = 6;  // 是否已经是业务员
  // @inject_tag: json:"is_groupman"
  bool is_groupman = 7;  // 是否已经是团长
}

// 通过手机号搜索粉丝响应
message SearchFansByMobileResp {
  // @inject_tag: json:"list"
  repeated FansItem list = 1;  // 粉丝列表
}


// 设置粉丝为团长请求
message SetFanToGroupReq {
  uint64 salesman_user_id = 1;  // 业务员userid
  uint64 groupman_user_id = 2 ;      // 团长用户ID
}


// 获取代理商/业务员个人主页数据请求
message GetAgentProfileReq {
  uint64 user_id = 1;  // 用户ID
}


// 获取代理商/业务员个人主页数据响应
message GetAgentProfileResp {
  // 用户基本信息
  string nick_name = 1;        // 昵称
  string avatar = 2;           // 头像
  string mobile = 3;           // 手机号
  bool is_agent = 4;           // 是否是代理商
  double salesman_rate = 5;    // 业务员佣金比例（仅业务员有）
  string salesman_remark = 6;  // 业务员备注（仅业务员有）
  int64 is_full_time = 7;      // 全职兼职（仅业务员有）
  string company_name = 8;     // 公司名称
  bool has_company_info = 20;  // 是否填写过公司资料

  // 统计信息（原有的字段）
  double total_commission = 9;     // 佣金总额
  double salesman_commission = 10; // 业务经理佣金汇总
  int64 today_order_count = 11;   // 今日订单数量
  double today_order_amount = 12;  // 今日订单总额
  int64 ai_point_total = 13;      // AI点总数
  int64 team_package_count = 14;  // 团长套餐数量
}

// 获取员工信息请求
message GetSalesmanInfoReq {
  uint64 agent_user_id = 1;     // 代理商用户ID
  uint64 salesman_user_id = 2;  // 业务员用户ID
}

// 获取员工信息响应
message GetSalesmanInfoResp {
  uint64 salesman_user_id = 1;   // 业务员用户ID
  string nick_name = 2;          // 业务员昵称
  string avatar = 3;             // 业务员头像
  string mobile = 4;             // 业务员手机号
  double salesman_rate = 5;      // 代理给业务员佣金比例
  string salesman_remark = 6;    // 业务员备注
  int64 is_full_time = 7;        // 全职兼职
  string created_at = 8;         // 创建时间

  // 收益统计
  double total_commission = 9;     // 佣金总额
  double salesman_commission = 10; // 业务经理佣金汇总
  int64 today_order_count = 11;   // 今日订单数量
  double today_order_amount = 12;  // 今日订单总额
  int64 ai_point_total = 13;      // AI点总数
  int64 team_package_count = 14;  // 团长套餐数量
}

// 获取达人橱窗授权状态请求
message GetWindowAuthStatusReq {
  uint64 user_id = 1;         // 用户ID
}

// 获取达人橱窗授权状态响应
message GetWindowAuthStatusResp {
  int32 window_auth_status = 1;  // 是否授权，0: 未授权, 1: 已授权
  string openfinderid = 2;       // 对应的openfinderid
  string opentalentid = 3;       // 对应的opentalentid
  string auth_url = 4;           // 授权链接
  string auth_wxa_path = 5;      // 授权路径
  string auth_wxa_appid = 6;     // appid
  string auth_wxa_username = 7;  // 小程序name
}

// 添加商品到橱窗请求
message AddProductToWindowReq {
  uint64 user_id = 1;         // 用户ID
  uint64 product_id = 2;      // 商品ID
}

// 添加商品到橱窗响应
message AddProductToWindowResp {
  bool success = 1;           // 是否成功
  string message = 2;         // 消息
}

// 代理商给业务经理设置团长套餐数量请求
message SetSalesmanTeamPackageReq {
    uint64 agent_user_id = 1;    // 代理商用户ID
    uint64 salesman_user_id = 2; // 业务经理用户ID
    int32 number = 3;            // 设置的套餐数量
}

// 好物商品列表请求
message GetGoodProductsReq {
  int32 page_size = 1;       // 一页获取多少个商品，最大 20
  string next_key = 2;       // 分页参数，第一页为空，后面返回前面一页返回的数据
  uint64 cat_id = 3;         // 分类ID
  string keyword = 4;        // 搜索关键词
  int64 user_id = 5;         // 用户ID，用于计算返利
}

// 好物商品列表响应
message GetGoodProductsResp {
  repeated ProductInfo product_list = 1; // 商品列表
  string next_key = 2;                   // 下一页的key
  bool has_more = 3;                     // 是否还有更多数据
}

// 获取好物商品详情请求
message GetGoodProductDetailReq {
  uint64 product_id = 1;       // 商品ID
  string shop_appid = 2;       // 商品所属店铺appid
  int64 user_id = 3;           // 用户ID，用于计算返利
}

// 获取好物商品详情响应
message GetGoodProductDetailResp {
  uint64 product_id = 1;                // 商品id
  string shop_appid = 2;                // 商品所属店铺appid
  string title = 3;                     // 商品标题
  string sub_title = 4;                 // 商品副标题
  repeated string head_imgs = 5;        // 商品主图
  ProductDescInfo desc_info = 6;        // 商品详情
  repeated SkuInfo skus = 7;            // 商品sku列表
  string head_supplier_item_link = 8;   // 团长商品链接
}

// 获取某个推客某个商品的内嵌商品卡片请求
message GetPromoterSingleProductPromotionInfoReq {
  uint64 product_id = 1;       // 商品ID
  string shop_appid = 2;       // 商品所属店铺appid
  string sharer_appid = 3;           // 推客 appid
}

// 获取某个推客某个商品的内嵌商品卡片响应
message GetPromoterSingleProductPromotionInfoResp {
  string product_promotion_link = 1;  // 内嵌商品卡片的推广参数
}

// 获取小店关联账号直播推广二维码请求
message GetShopLiveRecordQrCodeReq {
  uint64 user_id = 1;           // 用户ID，用于获取推客appid
  string shop_appid = 2;        // 小店的appid
  string promoter_id = 3;       // 关联账号的id
  int32 promoter_type = 4;      // 关联账号类型(1:视频号)
  string export_id = 5;         // 直播id
}

// 获取小店关联账号直播推广二维码响应
message GetShopLiveRecordQrCodeResp {
  string qrcode_url = 1;        // 二维码的url
}

// 获取小店关联账号直播预约推广二维码请求
message GetShopLiveNoticeRecordQrCodeReq {
  uint64 user_id = 1;           // 用户ID，用于获取推客appid
  string shop_appid = 2;        // 小店的appid
  string promoter_id = 3;       // 关联账号的id
  int32 promoter_type = 4;      // 关联账号类型(1:视频号)
  string notice_id = 5;         // 直播预约id
}

// 获取小店关联账号直播预约推广二维码响应
message GetShopLiveNoticeRecordQrCodeResp {
  string qrcode_url = 1;        // 二维码的url
}

// 提现预算计算请求
message WithdrawPreviewReq {
  uint64 user_id = 1;
  double amount = 2;
}

// 提现预算计算响应
message WithdrawPreviewResp {
  double amount = 1;            // 申请提现金额（元）
  double actual_amount = 2;     // 实际到账金额（元）
  double fee = 3;               // 手续费（元）
  double fee_ratio = 4;         // 手续费率（0.1表示10%）
  string fee_ratio_text = 5;    // 手续费率显示文本（如"10%"）
  bool needs_review = 6;        // 是否需要审核
}

// 用户提现请求
message WithdrawReq {
  uint64 user_id = 1;
  double amount = 2;
}

// 用户提现响应
message WithdrawResp {
  string out_bill_no = 1;      // 商户单号
  string package_info = 2;     // 微信支付包信息
  string desc = 3;             // 描述信息
  int32 review_status = 4;     // 审核状态
  uint64 amount = 5;           // 申请提现金额（分）
  uint64 actual_amount = 6;    // 实际到账金额（分）
  uint64 fee = 7;              // 手续费（分）
  double fee_ratio = 8;        // 手续费率
}

// 商家转账结果通知
message WxPayNotifyReq {
    string id = 1;            // 通知ID
    string create_time = 2;   // 通知创建时间
    string resource_type = 3; // 通知数据类型
    string event_type = 4;    // 通知类型
    string summary = 5;       // 回调摘要
    WxPayNotifyResourceInfo resource = 6; // 通知数据（加密）
}

// 微信支付回调通知加密资源信息
message WxPayNotifyResourceInfo {
    string original_type = 1;    // 原始类型
    string algorithm = 2;        // 加密算法类型
    string ciphertext = 3;       // 数据密文
    string associated_data = 4;  // 附加数据
    string nonce = 5;            // 随机串
}

// 微信支付回调通知解密后的业务数据
message WxPayNotifyDecryptData {
    string out_bill_no = 1;       // 商户单号
    string transfer_bill_no = 2;  // 商家转账订单号
    string state = 3;             // 单据状态
    string mch_id = 4;            // 商户号
    int64 transfer_amount = 5;    // 转账金额（分）
    string openid = 6;            // 收款用户OpenID
    string fail_reason = 7;       // 失败原因
    string create_time = 8;       // 单据创建时间
    string update_time = 9;       // 最后一次状态变更时间
}

// 回调通知响应
message WxPayNotifyResp {
    string code = 1;    // 返回状态码（SUCCESS为接收成功）
    string message = 2; // 返回信息
}
