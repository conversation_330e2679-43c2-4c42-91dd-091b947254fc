package main

import (
	"flag"
	"fmt"
	"xj-serv/application/wapp/rpc/ec/internal/config"
	"xj-serv/application/wapp/rpc/ec/internal/interceptor"
	wxleagueServer "xj-serv/application/wapp/rpc/ec/internal/server/wxleague"
	wxminishopServer "xj-serv/application/wapp/rpc/ec/internal/server/wxminishop"
	"xj-serv/application/wapp/rpc/ec/internal/svc"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

var configFile = flag.String("f", "etc/ec.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)
	ctx := svc.NewServiceContext(c)

	// 初始化日志配置
	logx.MustSetup(c.Log)
	proc.AddShutdownListener(func() {
		_ = logx.Close()
	})
	logx.DisableStat()

	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		pb.RegisterWxMiniShopServer(grpcServer, wxminishopServer.NewWxMiniShopServer(ctx))
		pb.RegisterWxLeagueServer(grpcServer, wxleagueServer.NewWxLeagueServer(ctx))

		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()

	// 添加一元拦截器
	s.AddUnaryInterceptors(interceptor.ErrorInterceptor)

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
