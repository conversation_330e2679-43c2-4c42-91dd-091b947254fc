syntax = "v1"

type MsgResp {
    Message       string `json:"message"`
}

type IdReq {
   Id  int64 `json:"id" validate:"required,min=1"`
}

type NoReq {
   No  string `json:"no" validate:"required,min=1"`
}


type PageRequest {
	 PageNo   int64  `json:"page_no" validate:"required,min=1,max=100"`
     PageSize int64  `json:"page_size,omitempty" validate:"required,min=1,max=15"`
     Keyword  string `json:"keyword,omitempty" validate:"omitempty,min=10"`
}
type SortRequest {
    SortBy   string `json:"sort_by,optional"`
    Order    string `json:"order,optional"`
}

type PageResponse {
	Total    int64 `json:"total"`
	PageNo   int64 `json:"page"`
	PageSize int64 `json:"page_size"`
}


