syntax = "v1"

import (
	"xj_base_struct.api"
	"non_auth_struct.api"
	"user_struct.api"
	"wx_shop_struct.api"
	"ai_struct.api"
	"xj_callback_struct.api"
    "nas_struct.api"
    "ec_struct.api"
)

@server(
	prefix: /v1/app
	group: non_auth
	middleware: IpLimiter
)
service wapp-api {
    @doc "发送短信"
    @handler SendSms
    post /send_sms (MobileReq)   returns (bool)

    @doc "短信登录"
    @handler SmsLogin
    post /sms_login (SmsLoginReq)   returns (WxLoginResp)

	@doc "微信登录"
    @handler Login
    post /wx_login (WxLoginReq)   returns (WxLoginResp)

	@doc "模拟微信登录"
    @handler SimulateLogin
    post /sim_wx_login (WxSimulateLoginReq)   returns (WxLoginResp)

    @doc "交换TOKEN"
	@handler ExchangeToken
	post /exchange_token (ExchangeTokenReq)   returns (ExchangeTokenReq)

    @doc "根据ID获取视频号信息"
    @handler GetVideoAccountInfoById
    post /video_account_profile_by_id (IdReq)   returns (VideoAccountReq)

    @doc "获取推荐视频号"
    @handler RecShops
    get /rec_shops () returns (RecVideoAccountResp)

    @doc "获取店铺经营类型列表"
    @handler VideoAccountCategoryList
    get /video_account_cats () returns (VideoAccountCategoryListResp)

    @doc "获取商品列表"
    @handler ProductList
    post /products (ProductListReq) returns (ProductListResp)

    @doc "获取商品详情"
    @handler ProductDetail
    post /product (IdReq) returns (ProductDetailResp)

    @doc "获取商品分类列表"
    @handler ProductCats
    get /cats () returns (ProductCatsResp)

    @doc "获取平台配置信息"
    @handler PlatformConfig
    get /platform_config () returns (PlatformConfigResp)
}


@server(
	prefix: /v1/user
	group: user
	jwt: JwtAuth
	middleware: IpLimiter
)
service wapp-api {
   @doc "获取用户信息"
   @handler GetUserInfo
   get /user_info ()   returns (UserInfoResp)


   @doc "获取用户详情"
   @handler GetUserDetail
   get /detail ()   returns (UserDetailResp)


   @doc "获取用户余额明细"
   @handler GetUserSettlementDetail
   post /settlement/detail (GetUserSettlementDetailReq) returns (GetUserSettlementDetailResp)

   @doc "更新用户信息"
   @handler ModifyUserInfo
   put /modify_user_info (UserInfoReq)   returns (bool)

   @doc "获取用户分享码"
   @handler GetUserInvitePicture
   get /get_invite_qrcode ()   returns (string)

   @doc "获取用户任务列表"
   @handler GetUserJobs
   get /jobs ()   returns (UserJobsResp)

   @doc "获取用户下级列表"
   @handler GetUserCustomers
   post /customers (UserCustomersReq)   returns (UserCustomersResp)

   @doc "获取粉丝列表"
   @handler GetUserFansList
   post /fans (UserFansListReq) returns (UserFansListResp)

   @doc " 获取用户收益统计"
   @handler GetUserIncomeStat
   get /income_stat () returns (UserIncomeStatResp)

   @doc "获取用户订单列表"
   @handler UserOrders
   post /orders (UserOrdersReq) returns (UserOrdersResp)

   @doc "获取用户任务池"
   @handler GetUserTaskPools
   post /task_pools (GetUserTaskPoolsReq) returns (GetUserTaskPoolsResp)

   @doc "获取用户任务完成情况"
   @handler GetUserTasksStatus
   get /tasks_status () returns (GetUserTasksStatusResp)

   @doc "获取用户任务完成进度"
   @handler GetUserTasksProgress
   get /tasks_progress () returns (GetUserTasksProgressResp)

   @doc "记录用户浏览商品信息"
   @handler RecordProductView
   post /record_product_view (RecordProductViewReq) returns (RecordProductViewResp)
}

@server(
	prefix: /v1/app
	group: app
	jwt: JwtAuth
	middleware: IpLimiter
)
service wapp-api {
    @doc "参加分红活动"
    @handler JoinBonus
    post /join_bonus (IdReq) returns (bool)

    @doc "创建或更新视频号"
    @handler UpdateVideoAccount
    put /video_account_profile (VideoAccountReq)   returns (string)

    @doc "获取视频号信息"
    @handler GetVideoAccountInfo
    get /video_account_profile ()   returns (VideoAccountReq)

    @doc "上传文件"
    @handler UploadFile
    post /upload (UploadReq) returns (string)
}

@server(
	prefix: /v1/ai
	group: ai
	jwt: JwtAuth
	middleware: IpLimiter
)
service wapp-api {
    @doc "用户创建项目"
    @handler UserNewProject
    post /new_project (UserNewProjectReq) returns (uint64)

    @doc "用户执行项目"
    @handler UserExecProject
    post /exec_project (UserExecProjectReq) returns (bool)

   @doc "项目分类列表"
    @handler CategoryList
    post /category_list (IdReq) returns (CategoryListResp)

    @doc "用户项目列表"
    @handler UserProjectList
    post /projects (UserProjectListReq) returns (UserProjectListResp)

    @doc "更新用户项目详情"
    @handler UpdateUserProjectInfo
    put /project (UpdateUserProjectReq) returns (bool)

    @doc "更新用户项目详情"
    @handler DeleteUserProjectInfo
    delete /project (IdReq) returns (bool)

    @doc "用户项目详情"
    @handler UserProjectInfo
    post /project (IdReq) returns (UserProject)

    @doc "收藏用户项目"
    @handler FavoriteProject
    post /favorite_project (IdReq) returns (bool)

    @doc "用户收藏列表"
    @handler FavoriteList
    post /favorite_list (FavoriteListReq) returns (UserProjectListResp)

    @doc "根据项目ID查询用户收藏状态"
    @handler IsFavorite
    post /is_favorite (IsFavoriteReq) returns (IsFavoriteResp)

    @doc "请求项目资源权限"
    @handler RequestProjectAuthority
    post /request_authority (NoReq) returns (ProductAuthorityResp)

    @doc "请求项目资源上传地址"
    @handler RequestProjectUploadAuth
    post /request_upload_url (RequestProjectUploadAuthReq) returns (RequestProjectUploadAuthResp)

    @doc "确认项目资源权限"
    @handler ConfirmProjectAuthority
    post /confirm_authority (ConfirmProjectAuthorityReq) returns (bool)

   @doc "发布用户项目"
   @handler PublishUserProject
   post /publish (PublishUserProjectReq) returns (bool)

   @doc "取消发布"
   @handler UnPublishUserProject
   delete /publish (IdReq) returns (bool)

   @doc "上传音频文件"
   @handler UploadAudioFile
   post /upload_audio (UploadReq) returns (UploadAudioFileResp)

    @doc "购买产品"
    @handler BuyProjects
    post /projects_buy (BuyProjectsReq) returns (bool)

    @doc "爆点流水"
    @handler IncomeExpenditure
    post /points_txns (IncomeExpenditureReq) returns (IncomeExpenditureResp)
}

@server(
	prefix: /v1/ai
	group: non_auth
    middleware: IpLimiter
)
service wapp-api {
   @doc "社区共享项目列表"
   @handler CommunityList
   post /community_list (CommunityListReq) returns (UserProjectListResp)

   @doc "社区共享项目详情"
   @handler CommunityProjectDetail
   post /community_project_detail (IdReq) returns (UserProject)

   @doc "情绪列表"
   @handler EmotionalTemplates
   get /emotional_templates () returns (EmotionalTemplatesResp)
}

@server(
	prefix: /v1/wx_mini_shop
	group: wx_shop
)
service wapp-api {
	@doc "微信小店支付成功签名回调"
    @handler MsgVerify
    get /msg_notify/:code (DecryptMiniShopMsgReq)   returns (string)

    @doc "微信小店支付成功消息回调"
    @handler MsgNotify
    post /msg_notify/:code (DecryptMiniShopMsgReq)   returns (string)
}

@server(
	prefix: /v1/xj/callback
	group: xj_callback
	middleware: XjSignature
)
service wapp-api {
    @doc "声音训练完成"
    @handler OuterProjectCompleted
    post /outer_project_completed (OuterProjectCompletedReq)   returns (bool)
}
//***************************************NAS相关接口*********************************************
@server(
    prefix: /v1/nas
    group: nas
    jwt: JwtAuth
    middleware: IpLimiter
)
service wapp-api {
    @doc "设置认证密码"
    @handler SetPass
    post /set_pass (SetPassReq) returns (bool)

    @doc "检查密码"
    @handler CheckPass
    post /check_pass (CheckPassReq) returns (CheckPassResp)

    @doc "检查密码是否设置"
    @handler CheckPassSet
    post /check_pass_set () returns (CheckPassSetResp)

    @doc "创建文件夹"
    @handler FolderCreate
    post /folder_create (FolderCreateReq) returns (bool)

    @doc "删除文件夹"
    @handler FolderDelete
    post /folder_delete (FolderDeleteReq) returns (bool)

    @doc "重命名文件夹"
    @handler FolderRename
    post /folder_rename (FolderRenameReq) returns (bool)

    @doc "文件夹信息"
    @handler FolderInfo
    post /folder_info (FolderInfoReq) returns (FolderInfoResp)

    @doc "创建文档"
    @handler DocumentCreate
    post /document_create (DocumentCreateReq) returns (bool)

    @doc "删除文档"
    @handler DocumentDelete
    post /document_delete (DocumentDeleteReq) returns (bool)

    @doc "重命名文档"
    @handler DocumentRename
    post /document_rename (DocumentRenameReq) returns (bool)

    @doc "文档信息"
    @handler DocumentInfo
    post /document_info (DocumentInfoReq) returns (DocumentInfoResp)

    @doc "文档列表"
    @handler DocumentList
    post /document_list (DocumentListReq) returns (DocumentListResp)

    @doc "检查文档是否存在"
    @handler DocumentCheckExist
    post /document_check_exist (DocumentCheckExistReq) returns (DocumentCheckExistResp)

    @doc "获取目录树"
    @handler FolderTree
    post /folder_tree (FolderTreeReq) returns (FolderTreeResp)

    @doc "文档列表APP"
    @handler DocumentListApp
    post /document_list_app (DocumentListAppReq) returns (DocumentListAppResp)

    @doc "移动文档到指定文件夹"
    @handler DocumentMove
    post /document_move (DocumentMoveReq) returns (DocumentMoveResp)
}

//***************************************机构联盟相关接口*********************************************
@server(
    prefix: /v1/ec
    group: ec
    jwt: JwtAuth
    middleware: IpLimiter
)
service wapp-api {
    @doc "获取推客的注册和绑定状态"
    @handler SharerStatus
    post /sharer_status () returns (SharerStatusResp)

    @doc "获取直播推广二维码"
    @handler GetLiveQrCode
    post /live_qrcode (GetLiveQrCodeReq) returns (GetLiveQrCodeResp)

    @doc "获取直播预约推广二维码"
    @handler GetLiveNoticeQrCode
    post /live_notice_qrcode (GetLiveNoticeQrCodeReq) returns (GetLiveNoticeQrCodeResp)

    @doc "获取推客商品推广二维码"
    @handler GetProductQrCode
    post /product_qrcode (GetProductQrCodeReq) returns (GetProductQrCodeResp)

    @doc "获取推客商品推广短链"
    @handler GetProductShortLink
    post /product_short_link (GetProductShortLinkReq) returns (GetProductShortLinkResp)

    @doc "获取小店关联账号直播推广二维码"
    @handler GetShopLiveQrCode
    post /shop_live_qrcode (GetShopLiveQrCodeReq) returns (GetShopLiveQrCodeResp)

    @doc "获取小店关联账号直播预约推广二维码"
    @handler GetShopLiveNoticeQrCode
    post /shop_live_notice_qrcode (GetShopLiveNoticeQrCodeReq) returns (GetShopLiveNoticeQrCodeResp)

    @doc "获取公司信息"
    @handler GetCompanyInfo
    get /company_info () returns (CompanyInfoResp)

    @doc "保存公司信息"
    @handler SaveCompanyInfo
    post /save_company_info (CompanyInfoSaveReq) returns (CompanyInfoResp)

    @doc "获取员工列表"
    @handler GetSalesmanList
    post /salesman_list (GetSalesmanListReq) returns (GetSalesmanListResp)

    @doc "设置业务员权限"
    @handler SetSalesmanPermission
    post /set_salesman_permission (SetSalesmanPermissionReq) returns (bool)

    @doc "通过手机号搜索粉丝"
    @handler SearchFansByMobile
    post /search_fans_by_mobile (SearchFansByMobileReq) returns (SearchFansByMobileResp)

    @doc "设置粉丝为团长"
    @handler SetFanToGroup
    post /set_fan_to_group (SetFanToGroupReq) returns (bool)

    @doc "获取达人橱窗授权状态"
    @handler GetWindowAuthStatus
    post /window/auth_status () returns (GetWindowAuthStatusResp)

    @doc "添加机构商品到橱窗"
    @handler AddProductToWindow
    post /window/add_product (AddProductToWindowReq) returns (bool)

    @doc "获取员工信息"
    @handler GetSalesmanInfo
    post /salesman_info (GetSalesmanInfoReq) returns (GetSalesmanInfoResp)

    @doc "获取代理商/业务员个人主页数据"
    @handler GetAgentProfile
    get /agent/profile () returns (AgentProfileResp)

    @doc "代理商给业务经理设置团长套餐数量"
    @handler SetSalesmanTeamPackage
    post /set_salesman_team_package (SetSalesmanTeamPackageReq) returns (bool)

    @doc "获取用户(绑定达人)橱窗商品列表"
    @handler GetUserWindowProducts
    post /window/products (GetUserWindowProductsReq) returns (GetUserWindowProductsResp)

    @doc "提现预算计算"
    @handler WithdrawPreview
    post /withdraw/preview (WithdrawPreviewReq) returns (WithdrawPreviewResp)

    @doc "用户提现"
    @handler Withdraw
    post /withdraw (WithdrawReq) returns (WithdrawResp)
}
@server(
    prefix: /v1/ec
    group: ec
)
service wapp-api {
    @doc "微信支付回调"
    @handler WxPayNotify
    post /wxpay/notify (WxPayNotifyReq) returns (WxPayNotifyResp)
}
@server(
    prefix: /v1/ec
    group: ec
    middleware: JwtParse,IpLimiter
)
service wapp-api {
    @doc "获取达人平台推广的短视频列表"
    @handler VideoFeedList
    post /video_feed_list (VideoFeedListReq) returns (VideoFeedListResp)

    @doc "获取达人平台直播列表"
    @handler LiveRecordList
    post /live_record_list returns (LiveRecordListResp)

    @doc "获取达人平台直播预约列表"
    @handler LiveNoticeRecordList
    post /live_notice_record_list returns (LiveNoticeRecordListResp)

    @doc "获取达人平台直播和预约聚合列表"
    @handler AggregationLiveRecordList
    post /aggregation_live_record_list (AggregationLiveRecordListReq) returns (AggregationLiveRecordListResp)

    @doc "获取可推广的商品列表"
    @handler GetPromoteProductList
    post /promote_products (GetPromoteProductListReq) returns (GetPromoteProductListResp)

    @doc "获取达人合作商品列表"
    @handler GetTalentProductList
    post /talent_products (GetTalentProductListReq) returns (GetTalentProductListResp)

    @doc "获取产品分类列表"
    @handler GetProductCategoryList
    post /product_categories returns (ProductCategoryListResp)

    @doc "获取直播间详情"
    @handler GetLiveDetailByTalentAppid
    post /live_detail_by_talent_appid (GetLiveDetailByTalentAppidReq) returns (GetLiveDetailByTalentAppidResp)

    @doc "获取可推广的商品详情"
    @handler GetPromoteProductDetail
    post /promote_product_detail (GetPromoteProductDetailReq) returns (GetPromoteProductDetailResp)

    @doc "获取合作商品详情"
    @handler GetTalentProductDetail
    post /talent_product_detail (GetPromoteProductDetailReq) returns (GetPromoteProductDetailResp)

    @doc "获取好物商品列表"
    @handler GetGoodProducts
    post /good_products (GetGoodProductsReq) returns (GetGoodProductsResp)

    @doc "获取好物商品详情"
    @handler GetGoodProductDetail
    post /good_product_detail (GetGoodProductDetailReq) returns (GetGoodProductDetailResp)

    @doc "获取某个推客某个商品的内嵌商品卡片"
    @handler GetPromoterSingleProductPromotionInfo
    post /promoter_single_product_promotion_info (GetPromoterSingleProductPromotionInfoReq) returns (GetPromoterSingleProductPromotionInfoResp)
}
