syntax = "v1"

import "./xj_base_struct.api"

type SharerStatusResp {
    // 和机构的绑定状态，0：未绑定 1：已绑定
    BindStatus int64 `json:"bind_status"`

    // 当前推客的注册状态
    // 0：未注册 1：注册中，还未完成 2：已完成注册 3：用户未支付实名，需要把微信先支付实名才能继续注册
    RegisterStatus int64 `json:"register_status"`

    // 机构的appid
    HeadSupplierAppid string `json:"head_supplier_appid"`

    // 绑定时需要的queryString参数
    QueryString string `json:"query_string"`

    // 绑定业务类型
    BusinessType string `json:"business_type"`

    // 分佣类型
    CommissionType int64 `json:"commission_type"`

    // 分佣比例
    CommissionRatio int64 `json:"commission_radio"`
}

type FeedProductInfo {
    ProductId uint64 `json:"product_id"`
    ProductName string `json:"product_name"`
    ProductImgUrl string `json:"product_img_url"`
    ProductMiniPrice float64 `json:"product_mini_price"`
    EarnAmount float64 `json:"earn_amount"`
}
type VideoFeedItem {
    ExportId string `json:"export_id"`
    TalentAppid string `json:"talent_appid"`
    ProductInfo *FeedProductInfo `json:"product_info"`
    FeedToken string `json:"feed_token"`
    PromoterShareLink string `json:"promoter_share_link"`
}
type VideoFeedListReq {
    TalentAppid string `json:"talent_appid,optional"` // 达人平台id
    PageRequest
}
type VideoFeedListResp {
    List []*VideoFeedItem `json:"list"`
    Total int64 `json:"total"`               // 总数
    PageNo int64 `json:"page_no"`             // 当前页
    PageSize int64 `json:"page_size"`         // 每页大小
}

type LiveRecordItem {
    ExportId string `json:"export_id"` // 直播 id
    Description string `json:"description"` // 直播描述
    PromoterShareLink string `json:"promoter_share_link"` // 内嵌直播卡片时需要的推广参数
}

type LiveRecordListResp {
    List []*LiveRecordItem `json:"list"` // 直播列表
}

// 聚合直播列表请求
type AggregationLiveRecordListReq {
    PageRequest
    Class string `json:"class"`
}

// 聚合直播列表项
type AggregationLiveRecordItem {
    ID                         uint64   `json:"id"`
    ExportID                   string   `json:"export_id"`
    Description                string   `json:"description"`
    PromoterShareLink          string   `json:"promoter_share_link"`
    TalentAppid                string   `json:"talent_appid"`
    TalentNickname             string   `json:"talent_nickname"`
    TalentHeadImg              string   `json:"talent_head_img"`
    PredictMaxCommissionAmount int32    `json:"predict_max_commission_amount"`
    Type                       string   `json:"type"`
    InfoProgress               string   `json:"info_progress"`
    NoticeID                   string   `json:"notice_id"`
    StartTime                  int64    `json:"start_time"`
    StartIsoTime                  string    `json:"start_iso_time"`
    SharerTotal                int32    `json:"sharer_total"`
    SharerHeadImgs             []string `json:"sharer_head_imgs"`
    Class                  string    `json:"class"`
}

// 聚合直播列表响应
type AggregationLiveRecordListResp {
    List  []AggregationLiveRecordItem `json:"list"`
    Total uint64                      `json:"total"`
}

// 获取直播间详情请求
type GetLiveDetailByTalentAppidReq {
    ID uint64 `json:"id" validate:"required"` // 列表id
    TalentAppid string `json:"talent_appid" validate:"required"` // 达人平台的appid
}

// 直播商品信息
type LiveGoodsInfo {
    ProductId               uint64 `json:"product_id"`                // 商品id
    ProductName             string `json:"product_name"`              // 商品名称
    ProductImgUrl           string `json:"product_img_url"`           // 商品的图片url
    ProductPrice            float64 `json:"product_price"`             // 商品售价【单位：分】
    PredictCommissionAmount float64 `json:"predict_commission_amount"` // 预估可得佣金金额【单位：分】
}

// 获取直播间详情响应
type GetLiveDetailByTalentAppidResp {
    TalentAppid     string          `json:"talent_appid"`     // 达人平台的appid
    TalentNickname  string          `json:"talent_nickname"`  // 达人昵称
    TalentHeadImg   string          `json:"talent_head_img"`  // 达人头像
    Description     string          `json:"description"`      // 直播描述
    ExportId string `json:"export_id"` //直播Id
    PredictMaxCommissionAmount  *int8  `json:"predict_max_commission_amount"` //最高返佣率
    PromoterShareLink   string `json:"promoter_share_link"` //推广链接
    Type string `json:"type"` //直播间类型 (notice/live)
    StartTime *int64    `json:"start_time"` //开播时间
    StartIsoTime                  string    `json:"start_iso_time"`
    Class                  string    `json:"class"`
    PromoterId                  string    `json:"promoter_id"`
    GoodsList       []LiveGoodsInfo `json:"goods_list"`       // 商品列表
}

// RangeInfo 区间定义
type RangeInfo {
	Min string `json:"min,omitempty"` // 区间最小值
	Max string `json:"max,omitempty"` // 区间最大值
}

// SpuItemCondition 商品查询条件
type SpuItemCondition {
	SellingPriceRange   *RangeInfo `json:"selling_price_range,omitempty"`    // 售卖价区间，单位是分
	MonthlySalesRange   *RangeInfo `json:"monthly_sales_range,omitempty"`    // 月销量区间
	Flags               []string   `json:"flags,omitempty"`                  // 保障标
	ServiceFeeRateRange *RangeInfo `json:"service_fee_rate_range,omitempty"` // 服务费率区间，单位是10万分之
	CommissionRateRange *RangeInfo `json:"commission_rate_range,omitempty"`  // 佣金率区间，单位是10万分之
	PromoteTimeRange    *RangeInfo `json:"promote_time_range,omitempty"`     // 推广时间范围
}

// CategoryInfo 商品类目查询条件
type CategoryInfo {
	CategoryId   string   `json:"category_id,omitempty"`    // 商品类目
	CategoryName string   `json:"category_name,omitempty"`  // 商品类目名称
	CategoryIds1 []string `json:"category_ids_1,omitempty"` // 一级类目列表
	CategoryIds2 []string `json:"category_ids_2,omitempty"` // 二级类目列表
	CategoryIds3 []string `json:"category_ids_3,omitempty"` // 三级类目列表
}

// GetPromoteProductListReq 获取可推广的商品列表请求
type GetPromoteProductListReq {
    CatId uint `json:"cat_id,optional,default=0"`
    PageRequest
    SortRequest
    IsRecommend uint64 `json:"is_recommend,optional,default=0"`
    IsPreferred uint64 `json:"is_preferred,optional,default=0"`
    IsWindow uint64 `json:"is_window,optional,default=0"`
}

// ProductListItem 商品列表项信息
type ProductListItem {
	ProductId uint64  `json:"product_id"` // 商品id
	ShopAppid string `json:"shop_appid"` // 商品所属店铺appid
	Title string `json:"title"` // 商品标题
	SubTitle string `json:"sub_title"` // 商品副标题
	HeadImgs []string `json:"head_imgs"` // 商品主图
	DescInfo *DescInfo `json:"desc_info"` // 商品详情
	Skus []*SkuInfo `json:"skus"` // 商品sku列表
    HeadSupplierItemLink string `json:"head_supplier_item_link"` // 团长商品链接
    MinPrice   float64         `json:"min_price"`                 // 最低价格
    EarnAmount   float64         `json:"earn_amount"`                 // 佣金
}

// GetPromoteProductListResp 获取可推广的商品列表响应
type GetPromoteProductListResp {
	ProductList []*ProductListItem `json:"product_list"` // 商品列表
	Total     uint64             `json:"total"`     // 分页参数, 下一页请求使用此值
}

// GetPromoteProductDetailReq 获取可推广的商品详情请求
type GetPromoteProductDetailReq {
	ShopAppid          string `json:"shop_appid" validate:"required"`           // 团长商品所属小店appid
	ProductId          uint64  `json:"product_id" validate:"required"`           // 商品id
}

// ProductInfo 商品基本信息
type ProductInfo {
	Title    string   `json:"title"`     // 商品标题
	SubTitle string   `json:"sub_title"` // 商品副标题
	HeadImgs []string `json:"head_imgs"` // 商品主图
	DescInfo *DescInfo `json:"desc_info"` // 商品详情
	Skus     []*SkuInfo `json:"skus"`      // 商品sku列表
}
type SkuInfo {
	SkuId       string          `json:"sku_id"`                     // skuID
	ThumbImg    string          `json:"thumb_img"`                  // sku小图
	SalePrice   float64         `json:"sale_price"`                 // 售卖价格
	StockNum    int64           `json:"stock_num"`                  // 库存
	SkuAttrs    []SkuAttr       `json:"sku_attrs"`                  // sku属性
	DeliverInfo *SkuDeliverInfo `json:"sku_deliver_info,omitempty"` // 预售信息
    EarnAmount  float64         `json:"earn_amount"`       // 返利佣金
}
type SkuAttr {
	AttrKey   string `json:"attr_key"`   // 属性键
	AttrValue string `json:"attr_value"` // 属性值
}
type SkuDeliverInfo {
	StockType                      int   `json:"stock_type"`                         // sku库存情况
	FullPaymentPresaleDeliveryType int   `json:"full_payment_presale_delivery_type"` // sku发货节点
	PresaleBeginTime               int64 `json:"presale_begin_time"`                 // 预售开始时间
	PresaleEndTime                 int64 `json:"presale_end_time"`                   // 预售结束时间
	FullPaymentPresaleDeliveryTime int   `json:"full_payment_presale_delivery_time"` // 发货时效
}

// DescInfo 商品详情信息
type DescInfo {
	Imgs []string `json:"imgs"` // 商品详情图片
	Desc string   `json:"desc"` // 商品详情文字
}


// ProductDetail 商品详情
type ProductDetail {
	ShopAppid            string        `json:"shop_appid"`              // 商品所属小店appid
	ProductId            uint64         `json:"product_id"`              // 商品id
	ProductPromotionLink string        `json:"product_promotion_link"`  // 商品推广链接
	ProductInfo          *ProductInfo   `json:"product_info"`           // 商品基本信息
}

// Coupon 优惠券信息
type Coupon {
	CouponId string `json:"coupon_id"` // 优惠券ID
}

// GetPromoteProductDetailResp 获取可推广的商品详情响应
type GetPromoteProductDetailResp {
	Product            *ProductDetail `json:"product"`             // 商品详情
	PublishCoupons     []*Coupon      `json:"publish_coupons"`     // 公开机构券
	CooperativeCoupons []*Coupon      `json:"cooperative_coupons"` // 定向机构券
}

// LiveNoticeRecordItem 直播预约信息项
type LiveNoticeRecordItem {
    NoticeId    string `json:"notice_id"`    // 预约 id
    Description string `json:"description"`  // 预约描述
    StartTime   int64  `json:"start_time"`   // 预约对应的开播时间
}

// LiveNoticeRecordListResp 获取达人平台直播预约列表响应
type LiveNoticeRecordListResp {
    List []*LiveNoticeRecordItem `json:"list"` // 直播预约列表
}

// GetLiveQrCodeReq 获取直播推广二维码请求
type GetLiveQrCodeReq {
    TalentAppid string `json:"talent_appid"`
    ExportId string `json:"export_id" validate:"required"` // 直播ID
}

// GetLiveQrCodeResp 获取直播推广二维码响应
type GetLiveQrCodeResp {
    QrcodeUrl string `json:"qrcode_url"` // 二维码URL
}

// GetLiveNoticeQrCodeReq 获取直播预约推广二维码请求
type GetLiveNoticeQrCodeReq {
    NoticeId string `json:"notice_id" validate:"required"` // 直播预约ID
    TalentAppid string `json:"talent_appid"`
}

// GetLiveNoticeQrCodeResp 获取直播预约推广二维码响应
type GetLiveNoticeQrCodeResp {
    QrcodeUrl string `json:"qrcode_url"` // 二维码URL
}

// GetProductQrCodeReq 获取推客商品推广二维码请求
type GetProductQrCodeReq {
    ProductId uint64  `json:"product_id" validate:"required"` // 商品ID
    ShopAppid string `json:"shop_appid" validate:"required"` // 商品所属店铺appid
}

// GetProductQrCodeResp 获取推客商品推广二维码响应
type GetProductQrCodeResp {
    QrcodeUrl string `json:"qrcode_url"` // 二维码URL
}

// GetProductShortLinkReq 获取推客商品推广短链请求
type GetProductShortLinkReq {
    ProductId uint64  `json:"product_id" validate:"required"` // 商品ID
    ShopAppid string `json:"shop_appid" validate:"required"` // 商品所属店铺appid
}

// GetProductShortLinkResp 获取推客商品推广短链响应
type GetProductShortLinkResp {
    ShortLink string `json:"short_link"` // 推广短链
}

// 结算明细相关结构
type GetSettlementDetailReq {
    Month string `json:"month,optional"`    // 格式：2025-05
}

type SettlementDetail {
    ID           uint64 `json:"id"`
    Amount       int64  `json:"amount"`         // 金额
    AmountChange int64  `json:"amount_change"`  // 变动金额
    Type         int32  `json:"type"`          // 明细类型
    CreateTime   string `json:"create_time"`    // 创建时间
    Remark       string `json:"remark"`        // 备注
}

type GetSettlementDetailResp {
    List []SettlementDetail `json:"list"`
}

// ProductCategoryItem 产品分类项
type ProductCategoryItem {
    CatId uint64 `json:"cat_id"`   // 分类ID
    Name  string `json:"name"`     // 分类名称
}

// ProductCategoryListResp 获取产品分类列表响应
type ProductCategoryListResp {
    List  []ProductCategoryItem `json:"list"`  // 分类列表
    Total uint64               `json:"total"` // 总数
}

// CompanyInfoSaveReq 公司信息保存请求
type CompanyInfoSaveReq {
    Name        string `json:"name" validate:"required"`           // 公司名称
    Address     string `json:"address" validate:"required"`        // 公司地址
    BankAccount string `json:"bank_account" validate:"required"`   // 银行账号
    BankName    string `json:"bank_name" validate:"required"`      // 开户银行
    TaxNumber   string `json:"tax_number" validate:"required"`     // 税号
    Phone       string `json:"phone" validate:"required"`          // 联系电话
    LicenseImg  string `json:"license_img" validate:"required"`    // 营业执照图片
}

// CompanyInfoResp 公司信息响应
type CompanyInfoResp {
    ID          int64  `json:"id"`                 // 公司ID
    Name        string `json:"name"`               // 公司名称
    Address     string `json:"address"`            // 公司地址
    BankAccount string `json:"bank_account"`       // 银行账号
    BankName    string `json:"bank_name"`          // 开户银行
    TaxNumber   string `json:"tax_number"`         // 税号
    Phone       string `json:"phone"`              // 联系电话
    LicenseImg  string `json:"license_img"`        // 营业执照图片
    CreatedAt   string `json:"created_at"`         // 创建时间
    UpdatedAt   string `json:"updated_at"`         // 更新时间
    Status      int32 `json:"status"`         // 审核状态
    Remark      string `json:"remark"`         // 审核结果
}

// 代理商给业务经理设置团长套餐数量
type SetSalesmanTeamPackageReq {
    SalesmanUserId uint64 `json:"salesman_user_id"` // 业务经理用户ID
    Number         int32  `json:"number"`           // 设置的套餐数量
}



// 设置业务员权限请求
type SetSalesmanPermissionReq {
    SalesmanUserId uint64  `json:"salesman_user_id"` // 业务员userid
    SalesmanRate   float64 `json:"salesman_rate"`    // 代理给业务员佣金比例
    SalesmanRemark string  `json:"salesman_remark"`  // 业务员备注
    IsFullTime     int64    `json:"is_full_time"`     // 全职兼职
}

// 员工信息项
type SalesmanItem {
    UserId    uint64 `json:"user_id"`     // 用户ID
    NickName  string `json:"nick_name"`   // 昵称
    Mobile    string `json:"mobile"`      // 手机号
    Avatar    string `json:"avatar"`      // 头像
    Rate      float64 `json:"rate"`       // 佣金比例
    Remark    string `json:"remark"`      // 备注
    IsFullTime int64 `json:"is_full_time"` // 是否全职
    CreatedAt string `json:"created_at"`  // 添加时间
}

// 获取员工列表请求
type GetSalesmanListReq {
    PageRequest
}

// 获取员工列表响应
type GetSalesmanListResp {
    List  []SalesmanItem `json:"list"`  // 员工列表
    Total uint64         `json:"total"` // 总数
}


// 通过手机号搜索粉丝请求
type SearchFansByMobileReq {
    Mobile string `json:"mobile" validate:"required"` // 粉丝手机号
}

// 粉丝信息项
type FansItem {
    UserId    uint64 `json:"user_id"`     // 用户ID
    NickName  string `json:"nick_name"`   // 昵称
    Avatar    string `json:"avatar"`      // 头像
    Mobile    string `json:"mobile"`      // 手机号
    IsSalesman bool  `json:"is_salesman"` // 是否是业务员
    IsGroupman bool  `json:"is_groupman"` // 是否是团长
}

// 通过手机号搜索粉丝响应
type SearchFansByMobileResp {
    List  []FansItem `json:"list"`  // 粉丝列表
}

// 设置粉丝为团长请求
type SetFanToGroupReq {
    GroupmanId   uint64 `json:"groupman_user_id" validate:"required"`  // 团长用户ID
}


// GetWindowAuthStatusReq 获取达人橱窗授权状态请求
type GetWindowAuthStatusReq {}

// GetWindowAuthStatusResp 获取达人橱窗授权状态响应
type GetWindowAuthStatusResp {
    WindowAuthStatus int    `json:"window_auth_status"` // 是否授权，0: 未授权, 1: 已授权
    OpenFinderId     string `json:"openfinderid"`       // 对应的openfinderid
    OpenTalentId     string `json:"opentalentid"`       // 对应的opentalentid
    AuthUrl          string `json:"auth_url"`           // 授权链接
    AuthWxaPath      string `json:"auth_wxa_path"`      // 授权路径
    AuthWxaAppid    string `json:"auth_wxa_appid"`    // appid
    AuthWxaUsername string `json:"auth_wxa_username"` // 小程序name
}

// 获取员工信息请求
type GetSalesmanInfoReq {
    SalesmanUserId uint64 `json:"salesman_user_id"` // 业务员用户ID
}

// 获取员工信息响应
type GetSalesmanInfoResp {
    SalesmanUserId   uint64  `json:"salesman_user_id"`    // 业务员用户ID
    NickName         string  `json:"nick_name"`           // 业务员昵称
    Avatar           string  `json:"avatar"`              // 业务员头像
    Mobile           string  `json:"mobile"`              // 业务员手机号
    SalesmanRate     float64 `json:"salesman_rate"`       // 代理给业务员佣金比例
    SalesmanRemark   string  `json:"salesman_remark"`     // 业务员备注
    IsFullTime       int64   `json:"is_full_time"`        // 全职兼职
    CreatedAt        string  `json:"created_at"`          // 创建时间

    // 收益统计
    TotalCommission    float64  `json:"total_commission"`    // 佣金总额
    SalesmanCommission float64  `json:"salesman_commission"` // 业务经理佣金汇总
    TodayOrderCount    int64  `json:"today_order_count"`   // 今日订单数量
    TodayOrderAmount   float64  `json:"today_order_amount"`  // 今日订单总额
    AiPointTotal       int64  `json:"ai_point_total"`      // AI点总数
    TeamPackageCount   int64  `json:"team_package_count"`  // 团长套餐数量
}

// 代理商/业务员个人主页数据响应
type AgentProfileResp {
    // 用户基本信息
    NickName        string  `json:"nick_name"`        // 昵称
    Avatar          string  `json:"avatar"`           // 头像
    Mobile          string  `json:"mobile"`           // 手机号
    IsAgent         bool    `json:"is_agent"`         // 是否是代理商
    SalesmanRate    float64 `json:"salesman_rate"`    // 业务员佣金比例（仅业务员有）
    SalesmanRemark  string  `json:"salesman_remark"`  // 业务员备注（仅业务员有）
    IsFullTime      int64   `json:"is_full_time"`     // 全职兼职（仅业务员有）
    CompanyName     string  `json:"company_name"`     // 公司名称
    HasCompanyInfo bool   `json:"has_company_info"` // 是否填写过公司资料
    // 统计信息（原有的字段）
    TotalCommission    float64 `json:"total_commission"`     // 佣金总额
    SalesmanCommission float64 `json:"salesman_commission"` // 业务经理佣金汇总
    TodayOrderCount    int64 `json:"today_order_count"`   // 今日订单数量
    TodayOrderAmount   float64 `json:"today_order_amount"`  // 今日订单总额
    AiPointTotal       int64 `json:"ai_point_total"`      // AI点总数
    TeamPackageCount   int64 `json:"team_package_count"`  // 团长套餐数量
}

// 添加机构商品到橱窗请求体
type AddProductToWindowReq {
    ProductId uint64 `json:"product_id" validate:"required"` // 商品ID
}

// GetGoodProductsReq 获取好物商品列表请求
type GetGoodProductsReq {
    PageSize int32 `json:"page_size,optional,default=20"`  // 一页获取多少个商品，最大 20
    NextKey string `json:"next_key,optional"`             // 分页参数，第一页为空
    CatId uint `json:"cat_id,optional,default=0"`         // 类目ID
    Keyword string `json:"keyword,optional"`               // 搜索关键词
}

// GetGoodProductsResp 获取好物商品列表响应
type GetGoodProductsResp {
    ProductList []*GoodProductsItem `json:"product_list"`  // 商品列表
    NextKey string `json:"next_key"`                      // 下一页的key
    HasMore bool `json:"has_more"`                        // 是否还有更多数据
}
type GoodProductsItem {
    ProductId uint64  `json:"product_id"` // 商品id
    ShopAppid string `json:"shop_appid"` // 商品所属店铺appid
    Title string `json:"title"` // 商品标题
    HeadImg string `json:"head_img"` // 商品主图
    SalePrice   float64         `json:"sale_price"`                 // 售卖价格
    HeadSupplierItemLink string `json:"head_supplier_item_link"` // 团长商品链接
}

// 获取用户橱窗商品列表请求
type GetUserWindowProductsReq {
	UserId uint64 `json:"user_id"`

	// @inject_tag: json:"next_offset" validate:"omitempty,min=0"
	NextOffset int32 `json:"next_offset"` // 下一页偏移量

	// @inject_tag: json:"page_size" validate:"omitempty,min=1,max=50"
	PageSize int32 `json:"page_size,optional"` // 每页数量，默认10
}

// 获取用户橱窗商品列表响应
type GetUserWindowProductsResp {
	// @inject_tag: json:"products"
	Products []ProductListItem `json:"products"` // 商品列表

    // @inject_tag: json:"sharer_appid"
	SharerAppid string `json:"sharer_appid"`

	// @inject_tag: json:"total"
	Total int32 `json:"total"` // 总数

	// @inject_tag: json:"next_offset"
	NextOffset int32 `json:"next_offset"` // 下一页偏移量

	// @inject_tag: json:"has_more"
	HasMore bool `json:"has_more"` // 是否有更多
}

// GetGoodProductDetailReq 获取好物商品详情请求
type GetGoodProductDetailReq {
    ProductId uint64 `json:"product_id" validate:"required"` // 商品ID
    ShopAppid string `json:"shop_appid" validate:"required"` // 商品所属店铺appid
}

// GetGoodProductDetailResp 获取好物商品详情响应
type GetGoodProductDetailResp {
    ProductId uint64  `json:"product_id"` // 商品id
    ShopAppid string `json:"shop_appid"` // 商品所属店铺appid
    Title string `json:"title"` // 商品标题
    SubTitle string `json:"sub_title"` // 商品副标题
    HeadImgs []string `json:"head_imgs"` // 商品主图
    DescInfo *DescInfo `json:"desc_info"` // 商品详情
    Skus []*SkuInfo `json:"skus"` // 商品sku列表
    HeadSupplierItemLink string `json:"head_supplier_item_link"` // 团长商品链接
}

// GetTalentProductListReq 获取达人的商品列表请求
type GetTalentProductListReq {
    CatId uint `json:"cat_id,optional,default=0"`
    CommissionType int64 `json:"commission_type,optional,default=0"`
    PageRequest
    SortRequest
    IsRecommend uint64 `json:"is_recommend,optional,default=0"`
}

// GetTalentProductListResp 获取达人的商品列表响应
type GetTalentProductListResp {
    ProductList []*ProductListItem `json:"product_list"` // 商品列表
    Total     uint64             `json:"total"`     // 分页参数, 下一页请求使用此值
}

// GetPromoterSingleProductPromotionInfoReq 获取某个推客某个商品的内嵌商品卡片请求
type GetPromoterSingleProductPromotionInfoReq {
    ProductId uint64 `json:"product_id" validate:"required"` // 商品ID
    ShopAppid string `json:"shop_appid" validate:"required"` // 商品所属店铺appid
    SharerAppid string `json:"sharer_appid" validate:"required"` // 推客 appid
}

// GetPromoterSingleProductPromotionInfoResp 获取某个推客某个商品的内嵌商品卡片响应
type GetPromoterSingleProductPromotionInfoResp {
    ProductPromotionLink string `json:"product_promotion_link"` // 内嵌商品卡片的推广参数
}

// GetShopLiveQrCodeReq 获取小店关联账号直播推广二维码请求
type GetShopLiveQrCodeReq {
    ShopAppid    string `json:"shop_appid" validate:"required"`    // 小店的appid
    PromoterId   string `json:"promoter_id" validate:"required"`   // 关联账号的id
    PromoterType int    `json:"promoter_type" validate:"required"` // 关联账号类型(1:视频号)
    ExportId     string `json:"export_id" validate:"required"`     // 直播id
}

// GetShopLiveQrCodeResp 获取小店关联账号直播推广二维码响应
type GetShopLiveQrCodeResp {
    QrcodeUrl string `json:"qrcode_url"` // 二维码的url
}

// GetShopLiveNoticeQrCodeReq 获取小店关联账号直播预约推广二维码请求
type GetShopLiveNoticeQrCodeReq {
    ShopAppid    string `json:"shop_appid" validate:"required"`    // 小店的appid
    PromoterId   string `json:"promoter_id" validate:"required"`   // 关联账号的id
    PromoterType int    `json:"promoter_type" validate:"required"` // 关联账号类型(1:视频号)
    NoticeId     string `json:"notice_id" validate:"required"`     // 直播预约id
}

// GetShopLiveNoticeQrCodeResp 获取小店关联账号直播预约推广二维码响应
type GetShopLiveNoticeQrCodeResp {
    QrcodeUrl string `json:"qrcode_url"` // 二维码的url
}

// 提现预算计算请求
type WithdrawPreviewReq {
    Amount float64 `json:"amount" validate:"required,gt=0"` // 提现金额（元）
}

// 提现预算计算响应
type WithdrawPreviewResp {
    Amount       float64 `json:"amount"`        // 申请提现金额（元）
    ActualAmount float64 `json:"actual_amount"` // 实际到账金额（元）
    Fee          float64 `json:"fee"`           // 手续费（元）
    FeeRatio     float64 `json:"fee_ratio"`     // 手续费率（0.1表示10%）
    FeeRatioText string  `json:"fee_ratio_text"` // 手续费率显示文本（如"10%"）
    NeedsReview  bool    `json:"needs_review"`  // 是否需要审核
}

// 用户提现请求
type WithdrawReq {
    Amount float64 `json:"amount" validate:"required,gt=0"`
}

// 用户提现响应
type WithdrawResp {
    OutBillNo string `json:"out_bill_no"` // 商户单号
    PackageInfo string `json:"package_info"` // 微信支付包信息
    Desc string `json:"desc"` // 描述
    ReviewStatus int `json:"review_status"` // 审核状态 0-待审核 1-审核通过 2-审核拒绝
    Amount uint64 `json:"amount"` // 申请提现金额（分）
    ActualAmount uint64 `json:"actual_amount"` // 实际到账金额（分）
    Fee uint64 `json:"fee"` // 手续费（分）
    FeeRatio float64 `json:"fee_ratio"` // 手续费率
}

// 商家转账结果通知
type WxPayNotifyReq {
    Id           string                     `json:"id"`            // 通知ID
    CreateTime   string                     `json:"create_time"`   // 通知创建时间
    ResourceType string                     `json:"resource_type"` // 通知数据类型
    EventType    string                     `json:"event_type"`    // 通知类型
    Summary      string                     `json:"summary"`       // 回调摘要
    Resource     WxPayNotifyResourceInfo    `json:"resource"`      // 通知数据（加密）
}

// 微信支付回调通知加密资源信息
type WxPayNotifyResourceInfo {
    OriginalType    string `json:"original_type"`    // 原始类型
    Algorithm       string `json:"algorithm"`        // 加密算法类型
    Ciphertext      string `json:"ciphertext"`       // 数据密文
    AssociatedData  string `json:"associated_data"`  // 附加数据
    Nonce           string `json:"nonce"`            // 随机串
}

// 微信支付回调通知解密后的业务数据
type WxPayNotifyDecryptData {
    OutBillNo       string `json:"out_bill_no"`       // 商户单号
    TransferBillNo  string `json:"transfer_bill_no"`  // 商家转账订单号
    State           string `json:"state"`             // 单据状态
    MchId           string `json:"mch_id"`            // 商户号
    TransferAmount  int64  `json:"transfer_amount"`   // 转账金额（分）
    Openid          string `json:"openid"`            // 收款用户OpenID
    FailReason      string `json:"fail_reason"`       // 失败原因
    CreateTime      string `json:"create_time"`       // 单据创建时间
    UpdateTime      string `json:"update_time"`       // 最后一次状态变更时间
}

// 回调通知响应
type WxPayNotifyResp {
    Code    string `json:"code"`    // 返回状态码（SUCCESS为接收成功）
    Message string `json:"message"` // 返回信息
}
