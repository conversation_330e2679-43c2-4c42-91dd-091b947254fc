syntax = "v1"

type UploadReq {

}

type UserCustomersReq {
    PageRequest
}

type UserCustomersResp {
    List []Customer `json:"list"`
	PageResponse
}


type Customer {
  Id uint64 `json:"id"`
  Cover string `json:"cover"`
  Nickname string `json:"nickname"`
  Mobile string `json:"mobile"`
  RegTime string `json:"reg_time"`
}

type UserInfoReq {
    NickName  string      `json:"nickname"`
    ShareCode  string      `json:"share_code"`
    Avatar string      `json:"avatar"`
    City string      `json:"city"`
    Birthday string      `json:"birthday"`
    FinderId string      `json:"finder_id"`
}

type UserInfoResp {
    UserId uint64 `json:"user_id"`
    NickName  string      `json:"nickname"`
    ShareCode  string      `json:"share_code"`
    Avatar string      `json:"avatar"`
    City string      `json:"city"`
    Level UserLevel      `json:"level"`
    Birthday string      `json:"birthday"`
    Mobile string      `json:"mobile"`
    Points uint64  `json:"points"`
}

type UserLevel {
  Id uint64 `json:"id"`
  Name string `json:"name"`
}

type UserJobsResp {
    Jobs []*UserJobs `json:"jobs"`
}

type UserJobs {
      IsComplete *int8 `json:"is_complete`
      IsOfficial *int8 `json:"is_official`
      Level *int8 `json:"level`
      Category string `json:"category"`
      Account  *VideoAccount  `json:"account"`
}

type VideoAccount{
      ID uint64 `json:"id"`
      Name string `json:"name"`
      LogoUrl string `json:"logo_url"`
      QrCode string `json:"qr_code"`
      CoverUrl string `json:"cover_url"`
      Description string `json:"description"`
}

type UserDetailResp {
    Id              uint64    `json:"id"`               // 用户ID
    CreatedAt       string    `json:"created_at"`       // 创建时间
    UpdatedAt       string    `json:"updated_at"`       // 更新时间
    Uuid            string    `json:"uuid"`             // UUID
    Mobile          string    `json:"mobile"`           // 手机号
    NickName        string    `json:"nick_name"`        // 昵称
    Avatar          string    `json:"avatar"`           // 头像
    Gender          uint32    `json:"gender"`           // 性别
    Country         string    `json:"country"`          // 国家
    Province        string    `json:"province"`         // 省份
    City            string    `json:"city"`             // 城市
    AddressId       uint32    `json:"address_id"`       // 默认地址ID
    Balance         float64   `json:"balance"`          // 可用余额
    Withdraw        float64   `json:"withdraw"`          // 可提现余额
    Points          uint32    `json:"points"`           // 可用积分
    PayMoney        float64   `json:"pay_money"`        // 总支付金额
    ExpendMoney     float64   `json:"expend_money"`     // 实际消费金额
    GradeId         uint32    `json:"grade_id"`         // 会员等级ID
    Platform        string    `json:"platform"`         // 注册平台
    LastLoginTime   uint32    `json:"last_login_time"`  // 最后登录时间
    Openid          string    `json:"openid"`           // 微信openid
    Unionid         string    `json:"unionid"`          // 微信unionid
    SharerAppid     string    `json:"sharer_appid"`     // 推客appid
    ContactName     string    `json:"contact_name"`     // 联系人姓名
    ContactNumber   string    `json:"contact_number"`   // 联系电话
    ShareCode       string    `json:"share_code"`       // 分享码
    ShareCodeImage  string    `json:"share_code_image"` // 分享码图片
    RegIpv4         string    `json:"reg_ipv4"`        // 注册IP
    InviteFrom      uint64    `json:"invite_from"`      // 邀请人
    RegSource       string    `json:"reg_source"`       // 注册来源
    Level           int32     `json:"level"`            // 用户等级
    LevelName       string    `json:"level_name"`       // 用户等级名称
    IsEnterprise    int32     `json:"is_enterprise"`    // 是否企业用户
    Birthday        string    `json:"birthday"`         // 生日
    RoleId          int32     `json:"role_id"`          // 角色ID
    FinderId        string    `json:"finder_id"`        // 视频号ID
}

// 结算明细相关结构
type GetUserSettlementDetailReq {
    PageRequest
    StartDate string `json:"start_date,optional"` // 开始时间
    EndDate string `json:"end_date,optional"`   // 结束时间
    Type    int32 `json:"type,optional,default=0"` // 明细类型
}

type UserSettlementDetail {
    ID           uint64 `json:"id"`
    Amount       float64  `json:"amount"`         // 金额
    AmountChange float64  `json:"amount_change"`  // 变动金额
    Type         int32  `json:"type"`          // 明细类型
    CreateTime   string `json:"create_time"`    // 创建时间
    Remark       string `json:"remark"`        // 备注
    OrderId      string `json:"order_id"`       // 订单ID
    ProductName  string `json:"product_name"`   // 商品名称
}

type SettlementSummary {
    TotalAmount    float64        `json:"total_amount"` // 总金额
    DirectAmount   float64        `json:"direct_amount"`  // 直接收益
    IndirectAmount float64        `json:"indirect_amount"`  // 间接收益
    AgentAmount    float64        `json:"agent_amount"`   // 代理商｜运营商收益
}
type GetUserSettlementDetailResp {
    List []UserSettlementDetail `json:"list"`
    Total          uint64         `json:"total"`
    Summary        SettlementSummary `json:"summary"`
}

// 粉丝列表请求
type UserFansListReq {
    PageRequest
}

// 粉丝列表响应
type UserFansListResp {
    List       []UserFansItem `json:"list"`
    Total      int64          `json:"total"`       // 总粉丝数
    TodayCount int64          `json:"today_count"` // 今日新增粉丝数
    YestCount  int64          `json:"yest_count"`  // 昨日新增粉丝数
    PageResponse
}

// 粉丝项
type UserFansItem {
    ID        uint64 `json:"id"`         // 用户ID
    NickName  string `json:"nick_name"`  // 昵称
    Avatar    string `json:"avatar"`     // 头像
    Mobile string      `json:"mobile"`
    Level    int32 `json:"level"`
    CreatedAt string `json:"created_at"` // 创建时间
}

// 用户收益统计响应
type UserIncomeStatResp {
    Balance           float64 `json:"balance"`             // 我的余额（分）
    TodayIncome       float64 `json:"today_income"`        // 今日收益（分）
    YesterdayIncome   float64 `json:"yesterday_income"`    // 昨日收益（分）
    CurrentMonthIncome float64 `json:"current_month_income"` // 本月收益（分）
    LastMonthIncome   float64 `json:"last_month_income"`   // 上月收益（分）
    OrderCount        int64 `json:"order_count"`         // 订单总数
    FansCount         int64 `json:"fans_count"`          // 粉丝总数
    Settlement        float64 `json:"settlement"`        // 已结算金额（分）
    Withdraw        float64 `json:"withdraw"`          // 可提现金额（分）
}

type UserOrdersReq {
    PageRequest
    CommissionStatus *int `json:"commission_status,omitempty"` // 佣金单状态：20-未结算，100-已结算，200-取消结算，不传则查询全部
}

type OrderItem {
    OrderID           string  `json:"order_id"`            // 订单号
    ProductTitle      string  `json:"product_title"`       // 商品标题
    ProductThumbImg   string  `json:"product_thumb_img"`   // 商品缩略图
    ActualPayment     float64 `json:"actual_payment"`      // 支付金额(元)
    SharerAmount      float64 `json:"sharer_amount"`       // 预估收益(元)
    OrderStatus       string  `json:"order_status"`        // 订单状态
    CommissionStatus  string  `json:"commission_status"`   // 佣金单状态
    CreateTime        string  `json:"create_time"`         // 下单时间
    SettlementTime    string  `json:"settlement_time"`     // 预估结算时间
}

type UserOrdersResp {
    List []OrderItem `json:"list"`
    PageResponse
}

// 获取用户任务池请求
type GetUserTaskPoolsReq {
}

// 任务池用户信息
type TaskPoolUserInfo {
    UserId uint64 `json:"user_id"`           // 用户ID
    FinderId string `json:"finder_id"`  // 视频号id
    Nickname string `json:"nickname"`        // 用户昵称
    Avatar string `json:"avatar"`            // 用户头像
    Mobile string `json:"mobile,omitempty"`  // 手机号
}

// 获取用户任务池响应
type GetUserTaskPoolsResp {
    DirectReferrer TaskPoolUserInfo `json:"direct_referrer"`       // 直接推荐人(任务池1)
    IndirectReferrer TaskPoolUserInfo `json:"indirect_referrer"`   // 间接推荐人(任务池2)
    Type3Pool TaskPoolUserInfo `json:"type3_pool"`                 // 类型3福利池(任务池3)
    Type4Pool TaskPoolUserInfo `json:"type4_pool"`                 // 类型4福利池(任务池4)
}

// 获取用户任务完成情况请求
type GetUserTasksStatusReq {
}

// 获取用户任务完成情况响应
type GetUserTasksStatusResp {
    DirectReferrer   bool `json:"direct_referrer"`    // 任务1：直接推荐人
    IndirectReferrer bool `json:"indirect_referrer"`  // 任务2：间接推荐人
    Type3Pool        bool `json:"type3_pool"`         // 任务3：类型3福利池
    Type4Pool        bool `json:"type4_pool"`         // 任务4：类型4福利池
}

// 获取用户任务完成进度响应
type GetUserTasksProgressResp {
    DirectReferrer   int32 `json:"direct_referrer"`    // 任务1：直接推荐人订单数
    IndirectReferrer int32 `json:"indirect_referrer"`  // 任务2：间接推荐人订单数
    Type3Pool        int32 `json:"type3_pool"`         // 任务3：类型3福利池订单数
    Type4Pool        int32 `json:"type4_pool"`         // 任务4：类型4福利池订单数
}

// 记录商品浏览请求
type RecordProductViewReq {
        SellerId    uint64 `json:"seller_id" validate:"required"`    // 用户ID
        ProductId uint64 `json:"product_id" validate:"required"` // 商品ID
        PoolType  int32  `json:"pool_type" validate:"required"`  // 任务池类型
        ShopAppid string `json:"shop_appid" validate:"required"` // 店铺AppID
    }

    // 记录商品浏览响应
type RecordProductViewResp {
        Success bool   `json:"success"` // 是否成功
        Message string `json:"message"` // 消息
    }