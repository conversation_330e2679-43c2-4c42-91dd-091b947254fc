package main

import (
	"flag"
	"fmt"
	"xj-serv/application/wapp/api/internal/config"
	"xj-serv/application/wapp/api/internal/handler"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/pkg/util/errutil"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/proc"

	"github.com/zeromicro/go-zero/core/conf"
	"github.com/zeromicro/go-zero/rest"
)

var configFile = flag.String("f", "etc/wapp-api.yaml", "the config file")

func main() {
	flag.Parse()

	var c config.Config
	conf.MustLoad(*configFile, &c)

	// 初始化日志配置
	logx.MustSetup(c.Log)
	proc.AddShutdownListener(func() {
		_ = logx.Close()
	})

	logx.DisableStat()

	server := rest.MustNewServer(c.RestConf)
	defer server.Stop()

	// 添加RPC错误处理中间件
	server.Use(errutil.RpcErrorMiddleware())

	ctx := svc.NewServiceContext(c)
	handler.RegisterHandlers(server, ctx)

	fmt.Printf("Starting server at %s:%d...\n", c.Host, c.Port)
	server.Start()
}
