package ec

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 提现预算计算
func WithdrawPreviewHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WithdrawPreviewReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := ec.NewWithdrawPreviewLogic(r.Context(), svcCtx)
		resp, err := l.WithdrawPreview(&req)
		result.HttpResult(r, w, resp, err)
	}
}
