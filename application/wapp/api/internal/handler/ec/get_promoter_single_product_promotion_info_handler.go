package ec

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 获取某个推客某个商品的内嵌商品卡片
func GetPromoterSingleProductPromotionInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetPromoterSingleProductPromotionInfoReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := ec.NewGetPromoterSingleProductPromotionInfoLogic(r.Context(), svcCtx)
		resp, err := l.GetPromoterSingleProductPromotionInfo(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
