package ec

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 获取达人合作商品列表
func GetTalentProductListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetTalentProductListReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := ec.NewGetTalentProductListLogic(r.Context(), svcCtx)
		resp, err := l.GetTalentProductList(&req)
		result.HttpResult(r, w, resp, err)
	}
}
