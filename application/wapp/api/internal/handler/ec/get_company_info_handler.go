package ec

import (
	"net/http"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/pkg/result"
)

// 获取公司信息
func GetCompanyInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := ec.NewGetCompanyInfoLogic(r.Context(), svcCtx)
		resp, err := l.GetCompanyInfo()
		result.HttpResult(r, w, resp, err)
	}
}
