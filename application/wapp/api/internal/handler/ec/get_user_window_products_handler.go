package ec

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 获取用户(绑定达人)橱窗商品列表
func GetUserWindowProductsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetUserWindowProductsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrMsg(r.Context(), "参数解析错误"))
			return
		}

		l := ec.NewGetUserWindowProductsLogic(r.Context(), svcCtx)
		resp, err := l.GetUserWindowProducts(&req)
		result.HttpResult(r, w, resp, err)
	}
}
