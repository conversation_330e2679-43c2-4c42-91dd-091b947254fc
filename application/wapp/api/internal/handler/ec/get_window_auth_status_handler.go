package ec

import (
	"net/http"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetWindowAuthStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetWindowAuthStatusReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := ec.NewGetWindowAuthStatusLogic(r.Context(), svcCtx)
		resp, err := l.GetWindowAuthStatus(&req)
		result.HttpResult(r, w, resp, err)
	}
}
