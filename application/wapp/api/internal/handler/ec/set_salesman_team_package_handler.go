package ec

import (
	"net/http"
	"xj-serv/pkg/result"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 代理商给业务经理设置团长套餐数量
func SetSalesmanTeamPackageHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SetSalesmanTeamPackageReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := ec.NewSetSalesmanTeamPackageLogic(r.Context(), svcCtx)
		resp, err := l.SetSalesmanTeamPackage(&req)
		result.HttpResult(r, w, resp, err)
	}
}
