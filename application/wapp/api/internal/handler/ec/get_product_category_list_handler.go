package ec

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
)

// 获取产品分类列表
func GetProductCategoryListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := ec.NewGetProductCategoryListLogic(r.Context(), svcCtx)
		resp, err := l.GetProductCategoryList()
		result.HttpResult(r, w, resp, err)
	}
}
