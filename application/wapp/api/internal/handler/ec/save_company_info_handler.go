package ec

import (
	"net/http"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// 保存公司信息
func SaveCompanyInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.CompanyInfoSaveReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := ec.NewSaveCompanyInfoLogic(r.Context(), svcCtx)
		resp, err := l.SaveCompanyInfo(&req)
		result.HttpResult(r, w, resp, err)
	}
}
