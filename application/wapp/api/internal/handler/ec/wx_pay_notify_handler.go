package ec

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/wapp/api/internal/logic/ec"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

// 微信支付回调
func WxPayNotifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		log := logx.WithContext(ctx)

		log.Infof("收到微信支付回调请求，URL: %s", r.URL.String())

		var req types.WxPayNotifyReq
		if err := httpx.Parse(r, &req); err != nil {
			log.Errorf("解析微信支付回调请求失败: %v", err)
			// 微信支付回调失败时，应该返回特定格式的响应
			resp := &types.WxPayNotifyResp{
				Code:    "FAIL",
				Message: "参数解析失败",
			}
			result.HttpResult(r, w, resp, nil)
			return
		}

		l := ec.NewWxPayNotifyLogic(r.Context(), svcCtx)
		resp, err := l.WxPayNotify(&req)
		if err != nil {
			log.Errorf("处理微信支付回调失败: %v", err)
			// 即使处理失败，也要返回标准格式的响应给微信
			failResp := &types.WxPayNotifyResp{
				Code:    "FAIL",
				Message: "处理失败",
			}
			result.HttpResult(r, w, failResp, nil)
			return
		}

		result.HttpResult(r, w, resp, nil)
	}
}
