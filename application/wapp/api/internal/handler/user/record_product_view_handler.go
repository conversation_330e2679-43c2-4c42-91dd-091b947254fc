package user

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/user"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 记录用户浏览商品信息
func RecordProductViewHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.RecordProductViewReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := user.NewRecordProductViewLogic(r.Context(), svcCtx)
		resp, err := l.RecordProductView(&req)
		result.HttpResult(r, w, resp, err)
	}
}
