package user

import (
	"net/http"
	"xj-serv/pkg/result"

	"xj-serv/application/wapp/api/internal/logic/user"
	"xj-serv/application/wapp/api/internal/svc"
)

// 获取用户任务完成进度
func GetUserTasksProgressHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := user.NewGetUserTasksProgressLogic(r.Context(), svcCtx)
		resp, err := l.GetUserTasksProgress()
		result.HttpResult(r, w, resp, err)
	}
}
