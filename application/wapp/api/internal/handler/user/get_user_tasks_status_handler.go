package user

import (
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/logic/user"
	"xj-serv/application/wapp/api/internal/svc"
)

// 获取用户任务完成情况
func GetUserTasksStatusHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetUserTasksStatusReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := user.NewGetUserTasksStatusLogic(r.Context(), svcCtx)
		resp, err := l.GetUserTasksStatus(&req)
		result.HttpResult(r, w, resp, err)
	}
}
