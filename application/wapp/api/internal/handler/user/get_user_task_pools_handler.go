package user

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/user"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 获取用户任务池
func GetUserTaskPoolsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetUserTaskPoolsReq
		if err := httpx.Parse(r, &req); err != nil {
			result.HttpResult(r, w, nil, xerr.NewErrCodeMsg(xerr.RequestParamError, err.Error()))
			return
		}

		l := user.NewGetUserTaskPoolsLogic(r.Context(), svcCtx)
		resp, err := l.GetUserTaskPools(&req)
		result.HttpResult(r, w, resp, err)
	}
}
