package wx_shop

import (
	"bytes"
	"io"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
	"xj-serv/application/wapp/api/internal/logic/wx_shop"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
)

// 微信小店支付成功消息回调
func MsgNotifyHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		logx.Debugf("接收到微信请求: %+v", r)
		var req types.DecryptMiniShopMsgReq
		if err := httpx.ParsePath(r, &req); err != nil {
			httpx.Error(w, err)
			return
		}
		if req.Code != "sxdd" {
			httpx.Ok(w)
		}

		req.Signature = r.URL.Query().Get("signature")
		req.Timestamp = r.URL.Query().Get("timestamp")
		req.Nonce = r.URL.Query().Get("nonce")
		req.Echostr = r.URL.Query().Get("echostr")
		req.MsgSignature = r.URL.Query().Get("msg_signature")

		// 读取请求体，并保存副本用于转发
		var buf bytes.Buffer
		teeReader := io.TeeReader(r.Body, &buf)
		body, err := io.ReadAll(teeReader)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}
		// 恢复请求体，以便后续处理
		r.Body = io.NopCloser(&buf)

		req.RawData = body
		logx.Debugf("接收到微信(原始)消息: %s \n", string(body))

		l := wx_shop.NewMsgNotifyLogic(r.Context(), svcCtx)

		resp, err := l.MsgNotify(&req)

		// 无论响应是否成功，都执行异步转发
		//go forwardRequest(r.Method, r.URL.String(), body, r.Header)

		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			_, err = w.Write([]byte(resp))
			if err != nil {
				httpx.ErrorCtx(r.Context(), w, err)
			} else {
				httpx.Ok(w)
			}
		}
	}
}

// forwardRequest 异步转发请求到目标服务器
func forwardRequest(method, urlPath string, body []byte, headers http.Header) {
	// 创建新的 HTTP 客户端
	client := &http.Client{
		Timeout: time.Second * 10,
	}

	// 构建目标 URL
	targetURL := "https://local.xjdy2024.com" + urlPath

	// 创建新请求
	req, err := http.NewRequest(method, targetURL, bytes.NewBuffer(body))
	if err != nil {
		logx.Errorf("创建转发请求失败: %v", err)
		return
	}

	// 复制请求头
	for name, values := range headers {
		for _, value := range values {
			req.Header.Add(name, value)
		}
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		logx.Errorf("转发请求失败: %v", err)
		return
	}
	defer resp.Body.Close()

	logx.Infof("转发请求成功，状态码: %d", resp.StatusCode)
}
