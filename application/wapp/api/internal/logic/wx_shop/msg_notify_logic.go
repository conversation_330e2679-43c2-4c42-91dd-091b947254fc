package wx_shop

import (
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"strconv"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/common"
)

var (
	WxMsgOrderPaid                        = "channels_ec_order_pay"
	WxMsgOrderCancel                      = "channels_ec_order_cancel"
	WxMsgSpuListing                       = "product_spu_listing"
	WxMsgSupplierUpdate                   = "head_supplier_item_update"                       // 机构定向商品同步
	WxMsgSupplierCommissionOrderUpdate    = "head_supplier_commission_order_update"           // 机构订单同步
	WxMsgSupplierSubProductBaseInfoUpdate = "head_supplier_subscribe_product_baseinfo_update" // 订阅商品基础信息变更通知
	WxMsgSupplierSubProductPlanInfoUpdate = "head_supplier_subscribe_product_planinfo_update" // 订阅商品计划信息变更通知
)

type MsgNotifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 微信小店支付成功消息回调
func NewMsgNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MsgNotifyLogic {
	return &MsgNotifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MsgNotifyLogic) MsgNotify(req *types.DecryptMiniShopMsgReq) (string, error) {
	var params pb.DecryptMiniShopMsgReq

	if err := copier.Copy(&params, req); err != nil {
		return "", err
	}

	decryptData, err := l.svcCtx.ShopRPC.DecryptMiniShopMsg(l.ctx, &params)
	// 打印decryptData
	//l.Infof("==========decryptData: %+v", decryptData)
	if err != nil {
		return "", err
	}

	//logx.Debugf("解密后的消息 %+v", decryptData)
	event, err := common.GetMapString(decryptData.DecryptData, "Event")
	if err != nil {
		return "", err
	}

	toUser, err := common.GetMapString(decryptData.DecryptData, "ToUserName")
	if err != nil {
		return "", err
	}

	fromUser, err := common.GetMapString(decryptData.DecryptData, "FromUserName")
	if err != nil {
		return "", err
	}

	logx.Debugf("解密后的事件 %+v", event)
	//logx.Debugf("解密后的消息 %+v", string(decryptData.DecryptData))
	switch event {
	case WxMsgOrderPaid:
		var Order struct {
			OrderInfo struct {
				OrderID json.RawMessage `json:"order_id"`
				PayTime json.RawMessage `json:"pay_time"`
			} `json:"order_info"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &Order); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", decryptData.AppId)
		logx.Debugf("解密后的orderid %+v", Order.OrderInfo.OrderID)
		resp, err := l.svcCtx.ShopRPC.PaySuccess(l.ctx, &pb.WxShopMsgReq{
			AppId:        decryptData.AppId,
			ToUserName:   toUser,
			FromUserName: fromUser,
			OrderId:      string(Order.OrderInfo.OrderID),
		})
		if err != nil || !resp.Success {
			return "FAILURE", err
		}
		return "success", nil
	case WxMsgOrderCancel:
		var Order struct {
			OrderInfo struct {
				OrderID json.RawMessage `json:"order_id"`
				PayTime json.RawMessage `json:"pay_time"`
			} `json:"order_info"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &Order); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", decryptData.AppId)
		logx.Debugf("解密后的orderid %+v", Order.OrderInfo.OrderID)

		resp, err := l.svcCtx.ShopRPC.PayCancel(l.ctx, &pb.WxShopMsgReq{
			AppId:        decryptData.AppId,
			ToUserName:   toUser,
			FromUserName: fromUser,
			OrderId:      string(Order.OrderInfo.OrderID),
		})
		if err != nil || !resp.Success {
			return "FAILURE", err
		}
		return "success", nil
	case WxMsgSpuListing:
		var ProductSpu struct {
			ProductSpuListing struct {
				Reason    string `json:"reason" gorm:"column:reason"`
				ProductID int    `json:"product_id" gorm:"column:product_id"`
				Status    int    `json:"status" gorm:"column:status"`
			} `json:"ProductSpuListing" gorm:"column:ProductSpuListing"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &ProductSpu); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", decryptData.AppId)
		logx.Debugf("解密后的productid %+v", ProductSpu.ProductSpuListing.ProductID)
		logx.Debugf("解密后的status %+v", ProductSpu.ProductSpuListing.Status)

		// 上架商品
		//if ProductSpu.ProductSpuListing.Status == 5 {
		//	resp, err := l.svcCtx.ShopRPC.ShopProductUpload(l.ctx, &pb.ShopProductDetailReq{
		//		ProductId: uint64(ProductSpu.ProductSpuListing.ProductID),
		//		AppId:     decryptData.AppId,
		//	})
		//	if err != nil || !resp.Success {
		//		return "FAILURE", err
		//	}
		//} else {
		//	resp, err := l.svcCtx.ShopRPC.ShopRemoveProduct(l.ctx, &pb.ShopProductDetailReq{
		//		ProductId: uint64(ProductSpu.ProductSpuListing.ProductID),
		//		AppId:     decryptData.AppId,
		//	})
		//	if err != nil || !resp.Success {
		//		return "FAILURE", err
		//	}
		//}
		return "success", nil
	case WxMsgSupplierUpdate:
		var SupplierInfo struct {
			ItemInfo struct {
				EventType    int      `json:"event_type" gorm:"column:event_type"`
				Appid        string   `json:"appid" gorm:"column:appid"`
				ProductID    string   `json:"product_id" gorm:"column:product_id"`
				Version      string   `json:"version" gorm:"column:version"`
				UpdateFields []string `json:"update_fields" gorm:"column:update_fields"`
			} `json:"item_info" gorm:"column:item_info"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &SupplierInfo); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", decryptData.AppId)
		logx.Debugf("解密后的productid %+v", SupplierInfo.ItemInfo.ProductID)
		logx.Debugf("解密后的event_type %+v", SupplierInfo.ItemInfo.EventType)
		logx.Debugf("解密后的update_fields %+v", SupplierInfo.ItemInfo.UpdateFields)

		productID, _ := strconv.Atoi(SupplierInfo.ItemInfo.ProductID)
		resp, err := l.svcCtx.WxLeagueRPC.SyncRemoteTalentProductByNotify(l.ctx, &pb.SyncRemoteTalentProductByNotifyReq{
			ProductId: int64(productID),
			ShopAppid: SupplierInfo.ItemInfo.Appid,
		})
		if err != nil || !resp.Success {
			return "FAILURE", err
		}

		return "success", nil
	case WxMsgSupplierCommissionOrderUpdate:
		var Order struct {
			OrderInfo struct {
				OrderStatus           int32  `json:"order_status" gorm:"column:order_status"`
				CommissionOrderStatus int32  `json:"commission_order_status" gorm:"column:commission_order_status"`
				SkuID                 string `json:"sku_id" gorm:"column:sku_id"`
				OrderID               string `json:"order_id" gorm:"column:order_id"`
				Version               string `json:"version" gorm:"column:version"`
			} `json:"order_info" gorm:"column:order_info"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &Order); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", decryptData.AppId)
		logx.Debugf("解密后的order_id%+v", Order.OrderInfo.OrderID)
		logx.Debugf("解密后的sku_id %+v", Order.OrderInfo.SkuID)
		logx.Debugf("解密后的order_status %+v", Order.OrderInfo.OrderStatus)
		logx.Debugf("解密后的commission_order_status %+v", Order.OrderInfo.CommissionOrderStatus)

		resp, err := l.svcCtx.WxLeagueRPC.SyncRemoteCommissionOrdersByNotify(l.ctx, &pb.SyncRemoteCommissionOrdersByNotifyReq{
			OrderID:               Order.OrderInfo.OrderID,
			SkuID:                 Order.OrderInfo.SkuID,
			OrderStatus:           Order.OrderInfo.OrderStatus,
			CommissionOrderStatus: Order.OrderInfo.CommissionOrderStatus,
		})
		if err != nil || !resp.Success {
			return "FAILURE", err
		}

		return "success", nil
	case WxMsgSupplierSubProductBaseInfoUpdate:
		var BaseInfo struct {
			ItemInfo struct {
				SkuList []struct {
					StockNum  int    `json:"stock_num" gorm:"column:stock_num"`
					SalePrice int    `json:"sale_price" gorm:"column:sale_price"`
					ThumbImg  string `json:"thumb_img" gorm:"column:thumb_img"`
				} `json:"sku_list" gorm:"column:sku_list"`
				DescInfo struct {
					Imgs []string `json:"imgs" gorm:"column:imgs"`
					Desc string   `json:"desc" gorm:"column:desc"`
				} `json:"desc_info" gorm:"column:desc_info"`
				Appid       string `json:"appid" gorm:"column:appid"`
				ProductID   string `json:"product_id" gorm:"column:product_id"`
				ProductInfo struct {
					SubTitle string   `json:"sub_title" gorm:"column:sub_title"`
					Title    string   `json:"title" gorm:"column:title"`
					HeadImgs []string `json:"head_imgs" gorm:"column:head_imgs"`
				} `json:"product_info" gorm:"column:product_info"`
				Version string `json:"version" gorm:"column:version"`
				Status  int    `json:"status" gorm:"column:status"`
			} `json:"item_info" gorm:"column:item_info"`
			CreateTime   int    `json:"CreateTime" gorm:"column:CreateTime"`
			Event        string `json:"Event" gorm:"column:Event"`
			ToUserName   string `json:"ToUserName" gorm:"column:ToUserName"`
			FromUserName string `json:"FromUserName" gorm:"column:FromUserName"`
			MsgType      string `json:"MsgType" gorm:"column:MsgType"`
		}
		if err = json.Unmarshal([]byte(decryptData.DecryptData), &BaseInfo); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", BaseInfo.ItemInfo.Appid)
		logx.Debugf("解密后的product_id %+v", BaseInfo.ItemInfo.ProductID)
		logx.Debugf("解密后的title %+v", BaseInfo.ItemInfo.ProductInfo.Title)
		logx.Debugf("解密后的status %+v", BaseInfo.ItemInfo.Status)

		if BaseInfo.ItemInfo.Status != 5 {
			_productId, _ := strconv.Atoi(BaseInfo.ItemInfo.ProductID)
			_, _ = l.svcCtx.WxLeagueRPC.DeleteTempProduct(l.ctx, &pb.DeleteTalentProductReq{
				ShopAppid: BaseInfo.ItemInfo.Appid,
				ProductId: uint64(_productId),
			})
		}

		return "success", nil
	case WxMsgSupplierSubProductPlanInfoUpdate:
		var PlanInfo struct {
			ItemInfo struct {
				StartTime            int    `json:"start_time" gorm:"column:start_time"`
				PlanType             int    `json:"plan_type" gorm:"column:plan_type"`
				EventType            int    `json:"event_type" gorm:"column:event_type"`
				HeadSupplierItemLink string `json:"head_supplier_item_link" gorm:"column:head_supplier_item_link"`
				Appid                string `json:"appid" gorm:"column:appid"`
				ProductID            string `json:"product_id" gorm:"column:product_id"`
				EndTime              int    `json:"end_time" gorm:"column:end_time"`
				CommissionRatio      int    `json:"commission_ratio" gorm:"column:commission_ratio"`
				Version              string `json:"version" gorm:"column:version"`
				PlanID               string `json:"plan_id" gorm:"column:plan_id"`
				ServiceRatio         int    `json:"service_ratio" gorm:"column:service_ratio"`
				PlanStatus           int    `json:"plan_status" gorm:"column:plan_status"`
			} `json:"item_info" gorm:"column:item_info"`
			CreateTime   int    `json:"CreateTime" gorm:"column:CreateTime"`
			Event        string `json:"Event" gorm:"column:Event"`
			ToUserName   string `json:"ToUserName" gorm:"column:ToUserName"`
			FromUserName string `json:"FromUserName" gorm:"column:FromUserName"`
			MsgType      string `json:"MsgType" gorm:"column:MsgType"`
		}

		if err = json.Unmarshal([]byte(decryptData.DecryptData), &PlanInfo); err != nil {
			return "", err
		}

		logx.Debugf("解密后的appid %+v", PlanInfo.ItemInfo.Appid)
		logx.Debugf("解密后的product_id %+v", PlanInfo.ItemInfo.ProductID)
		logx.Debugf("解密后的plan_type %+v", PlanInfo.ItemInfo.PlanType)
		logx.Debugf("解密后的event_type %+v", PlanInfo.ItemInfo.PlanType)
		logx.Debugf("解密后的plan_id %+v", PlanInfo.ItemInfo.PlanID)
		logx.Debugf("解密后的plan_status %+v", PlanInfo.ItemInfo.PlanStatus)

		//productID, _ := strconv.Atoi(PlanInfo.ItemInfo.ProductID)
		//resp, err := l.svcCtx.WxLeagueRPC.SyncRemotePromoteProductByNotify(l.ctx, &pb.SyncRemotePromoteProductByNotifyReq{
		//	ProductId:  uint64(productID),
		//	ShopAppid:  PlanInfo.ItemInfo.Appid,
		//	PlanStatus: int32(PlanInfo.ItemInfo.PlanStatus),
		//	PlanType:   2, //普通推广
		//	MsgName:    WxMsgSupplierSubProductPlanInfoUpdate,
		//	EventType:  int32(PlanInfo.ItemInfo.EventType),
		//})
		//if err != nil || !resp.Success {
		//	return "FAILURE", err
		//}
		return "success", nil
	}

	return "FAILURE", nil
}
