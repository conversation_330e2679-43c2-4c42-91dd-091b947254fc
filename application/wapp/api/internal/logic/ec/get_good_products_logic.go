package ec

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/util/errutil"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGoodProductsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取好物商品列表
func NewGetGoodProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodProductsLogic {
	return &GetGoodProductsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// Deprecated
func (l *GetGoodProductsLogic) GetGoodProducts(req *types.GetGoodProductsReq) (resp *types.GetGoodProductsResp, err error) {
	// 从上下文获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)

	// 构建RPC请求
	rpcReq := &pb.GetGoodProductsReq{
		PageSize: req.PageSize,
		NextKey:  req.NextKey,
		CatId:    uint64(req.CatId),
		Keyword:  req.Keyword,
		UserId:   userId,
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetGoodProducts(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("调用 WxLeagueRPC.GetGoodProducts 失败: %v", err)
		return nil, errutil.NewRpcError(l.ctx, err)
	}

	// 构建API响应
	resp = &types.GetGoodProductsResp{
		NextKey:     rpcResp.NextKey,
		HasMore:     rpcResp.HasMore,
		ProductList: make([]*types.GoodProductsItem, 0, len(rpcResp.ProductList)),
	}

	// 转换商品信息
	for _, product := range rpcResp.ProductList {
		productItem := &types.GoodProductsItem{
			ProductId:            product.ProductId,
			ShopAppid:            product.ShopAppid,
			Title:                product.Title,
			HeadImg:              product.HeadImgs[0],
			SalePrice:            float64(product.Skus[0].SalePrice) / 100.0,
			HeadSupplierItemLink: product.HeadSupplierItemLink,
		}

		resp.ProductList = append(resp.ProductList, productItem)
	}

	return resp, nil
}
