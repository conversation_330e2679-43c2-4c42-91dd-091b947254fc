package ec

import (
	"context"
	"github.com/jinzhu/copier"
	"xj-serv/application/wapp/rpc/ec/pb"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetProductCategoryListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取产品分类列表
func NewGetProductCategoryListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetProductCategoryListLogic {
	return &GetProductCategoryListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetProductCategoryListLogic) GetProductCategoryList() (resp *types.ProductCategoryListResp, err error) {
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetProductCategoryList(l.ctx, &pb.EmptyRequest{})
	if err != nil {
		l.Errorf("调用WxLeagueRPC.GetProductCategoryList失败: %v", err)
		return nil, err
	}

	resp = &types.ProductCategoryListResp{}
	if err = copier.Copy(&resp.List, rpcResp.List); err != nil {
		return nil, err
	}
	resp.Total = rpcResp.Total
	return resp, nil
}
