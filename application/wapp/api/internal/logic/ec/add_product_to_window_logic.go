package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddProductToWindowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 添加机构商品到橱窗
func NewAddProductToWindowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddProductToWindowLogic {
	return &AddProductToWindowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddProductToWindowLogic) AddProductToWindow(req *types.AddProductToWindowReq) (bool, error) {
	l.Infof("添加商品到橱窗请求: %+v", req)

	// 从JWT获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId <= 0 {
		l.Errorf("获取当前登录用户ID失败")
		return false, xerr.NewErrCode(xerr.TokenGenerateError)
	}

	// 调用RPC添加商品到橱窗
	rpcResp, err := l.svcCtx.WxLeagueRPC.AddProductToWindow(l.ctx, &pb.AddProductToWindowReq{
		UserId:    uint64(userId),
		ProductId: req.ProductId,
	})
	if err != nil {
		l.Errorf("调用RPC添加商品到橱窗失败: %v", err)
		return false, xerr.NewErrMsg(l.ctx, "添加商品到橱窗失败")
	}

	return rpcResp.Success, nil
}
