package ec

import (
	"context"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawPreviewLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 提现预算计算
func NewWithdrawPreviewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawPreviewLogic {
	return &WithdrawPreviewLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawPreviewLogic) WithdrawPreview(req *types.WithdrawPreviewReq) (resp *types.WithdrawPreviewResp, err error) {
	// 从上下文获取用户ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)

	// 参数验证
	if req.Amount <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 记录请求日志
	logx.WithContext(l.ctx).Infof("提现预算计算请求: 用户ID=%d, 金额=%.2f", jwtUserId, req.Amount)

	// 调用RPC服务进行提现预算计算
	rpcResp, err := l.svcCtx.WxLeagueRPC.WithdrawPreview(l.ctx, &pb.WithdrawPreviewReq{
		UserId: uint64(jwtUserId),
		Amount: req.Amount,
	})
	if err != nil {
		logx.WithContext(l.ctx).Errorf("调用提现预算计算RPC失败: %v", err)
		return nil, err
	}

	// 构造响应
	resp = &types.WithdrawPreviewResp{
		Amount:       rpcResp.Amount,
		ActualAmount: rpcResp.ActualAmount,
		Fee:          rpcResp.Fee,
		FeeRatio:     rpcResp.FeeRatio,
		FeeRatioText: rpcResp.FeeRatioText,
		NeedsReview:  rpcResp.NeedsReview,
	}

	logx.WithContext(l.ctx).Infof("提现预算计算完成: 用户ID=%d, 申请金额=%.2f元, 实际到账=%.2f元, 手续费=%.2f元, 需要审核=%t",
		jwtUserId, resp.Amount, resp.ActualAmount, resp.Fee, resp.NeedsReview)

	return resp, nil
}
