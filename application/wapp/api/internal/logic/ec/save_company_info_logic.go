package ec

import (
	"context"
	"encoding/base64"
	"strings"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type SaveCompanyInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 保存公司信息
func NewSaveCompanyInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveCompanyInfoLogic {
	return &SaveCompanyInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveCompanyInfoLogic) SaveCompanyInfo(req *types.CompanyInfoSaveReq) (resp *types.CompanyInfoResp, err error) {
	// 从JWT中获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "用户未登录")
	}

	// 验证营业执照图片
	if len(req.LicenseImg) > 0 {
		// 检查是否为base64格式
		if !strings.HasPrefix(req.LicenseImg, "data:image/") {
			return nil, xerr.NewErrMsg(l.ctx, "营业执照图片必须是base64格式的图片")
		}

		// 提取base64数据部分
		base64Data := ""
		parts := strings.Split(req.LicenseImg, ",")
		if len(parts) > 1 {
			base64Data = parts[1]
		} else {
			base64Data = parts[0]
		}

		// 解码base64以检查实际大小
		decoded, err := base64.StdEncoding.DecodeString(base64Data)
		if err != nil {
			l.Errorf("解码base64图片失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "无效的base64图片格式")
		}

		// 检查文件大小限制 (2MB)
		if len(decoded) > 2*1024*1024 {
			return nil, xerr.NewErrMsg(l.ctx, "营业执照图片不能超过5MB")
		}
	} else {
		return nil, xerr.NewErrMsg(l.ctx, "营业执照图片不能为空")
	}

	// 构建RPC请求
	rpcReq := &pb.CompanyInfoSaveReq{
		UserId: uint64(userId),
	}

	// 使用copier复制请求参数
	err = copier.Copy(rpcReq, req)
	if err != nil {
		l.Errorf("复制请求参数失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	// 调用RPC服务保存公司信息
	rpcResp, err := l.svcCtx.WxLeagueRPC.SaveCompanyInfo(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("保存公司信息失败: %v", err)
		return nil, err
	}

	// 创建响应对象
	resp = &types.CompanyInfoResp{}

	// 使用copier复制RPC响应到API响应
	err = copier.Copy(resp, rpcResp)
	if err != nil {
		l.Errorf("复制公司信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	return resp, nil
}
