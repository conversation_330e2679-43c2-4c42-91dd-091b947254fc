package ec

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
)

type GetUserWindowProductsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserWindowProductsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserWindowProductsLogic {
	return &GetUserWindowProductsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserWindowProductsLogic) GetUserWindowProducts(req *types.GetUserWindowProductsReq) (resp *types.GetUserWindowProductsResp, err error) {
	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetUserWindowProducts(l.ctx, &pb.GetUserWindowProductsReq{
		UserId:     req.UserId,
		NextOffset: req.NextOffset,
		PageSize:   req.PageSize,
	})
	if err != nil {
		l.Errorf("调用RPC获取用户橱窗商品列表失败: %v", err)
		return nil, err
	}

	// 构建响应
	result := &types.GetUserWindowProductsResp{
		Products:    make([]types.ProductListItem, 0, len(rpcResp.ProductList)),
		SharerAppid: rpcResp.SharerAppid,
		Total:       rpcResp.Total,
		NextOffset:  rpcResp.NextOffset,
		HasMore:     rpcResp.HasMore,
	}

	// 转换商品详情
	for _, product := range rpcResp.ProductList {
		// 转换SKU信息
		skus := make([]*types.SkuInfo, 0, len(product.Skus))
		for _, sku := range product.Skus {
			// 转换SKU属性
			skuAttrs := make([]types.SkuAttr, 0, len(sku.SkuAttrs))
			for _, attr := range sku.SkuAttrs {
				skuAttrs = append(skuAttrs, types.SkuAttr{
					AttrKey:   attr.AttrKey,
					AttrValue: attr.AttrValue,
				})
			}

			// 添加SKU信息
			skus = append(skus, &types.SkuInfo{
				SkuId:      sku.SkuId,
				ThumbImg:   sku.ThumbImg,
				SalePrice:  float64(sku.SalePrice) / 100.0, // 转换为元
				StockNum:   sku.StockNum,
				SkuAttrs:   skuAttrs,
				EarnAmount: sku.EarnAmount,
			})
		}

		// 构建描述信息
		var descInfo *types.DescInfo
		if len(product.HeadImgs) > 0 {
			descInfo = &types.DescInfo{
				Desc: "", // 如果有需要，可以从 product 中获取描述信息
			}
		}

		// 添加商品详情
		result.Products = append(result.Products, types.ProductListItem{
			ProductId: product.ProductId,
			ShopAppid: product.ShopAppid,
			Title:     product.Title,
			SubTitle:  product.SubTitle,
			HeadImgs:  product.HeadImgs,
			DescInfo:  descInfo,
			Skus:      skus,
		})
	}

	return result, nil
}
