package ec

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/pb" // 确保导入正确的pb包
	"xj-serv/pkg/ctxJwt"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type VideoFeedListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取达人平台推广的短视频列表
func NewVideoFeedListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *VideoFeedListLogic {
	return &VideoFeedListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *VideoFeedListLogic) VideoFeedList(req *types.VideoFeedListReq) (resp *types.VideoFeedListResp, err error) {
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)
	rpcReq := &pb.GetFeedListReq{
		TalentAppid: req.TalentAppid,
		UserId:      jwtUserId,
		PageReq: &pb.PageReq{
			PageNo:   uint64(req.PageNo),
			PageSize: uint64(req.PageSize),
			Keyword:  req.Keyword,
		},
	}

	// 调用RPC
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetFeedList(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("调用WxLeagueRPC.GetFeedList失败: %v", err)
		return nil, err
	}

	// 转换RPC响应到API响应
	resp = &types.VideoFeedListResp{
		Total:    rpcResp.Total,
		PageNo:   rpcResp.PageNo,
		PageSize: rpcResp.PageSize,
		List:     make([]*types.VideoFeedItem, 0, len(rpcResp.FeedList)),
	}

	for _, feed := range rpcResp.FeedList {
		apiFeedItem := &types.VideoFeedItem{
			ExportId:          feed.ExportId,
			TalentAppid:       feed.TalentAppid,
			FeedToken:         feed.FeedToken,
			PromoterShareLink: feed.PromoterShareLink,
		}
		if feed.ProductInfo != nil {
			// 注意价格单位转换：RPC返回的是分，API需要元
			productMiniPriceYuan := float64(feed.ProductInfo.ProductMiniPrice) / 100.0

			apiFeedItem.ProductInfo = &types.FeedProductInfo{
				ProductId:        feed.ProductInfo.ProductId,
				ProductName:      feed.ProductInfo.ProductName,
				ProductImgUrl:    feed.ProductInfo.ProductImgUrl,
				ProductMiniPrice: productMiniPriceYuan,
				EarnAmount:       feed.EarnAmount,
			}
		}
		resp.List = append(resp.List, apiFeedItem)
	}

	return resp, nil
}
