package ec

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/pb"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AggregationLiveRecordListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取达人平台直播和预约聚合列表
func NewAggregationLiveRecordListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AggregationLiveRecordListLogic {
	return &AggregationLiveRecordListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AggregationLiveRecordListLogic) AggregationLiveRecordList(req *types.AggregationLiveRecordListReq) (resp *types.AggregationLiveRecordListResp, err error) {
	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetAggregationLiveRecordList(l.ctx, &pb.GetAggregationLiveRecordListReq{
		Class: req.Class,
		PageReq: &pb.PageReq{
			PageNo:   uint64(req.PageNo),
			PageSize: uint64(req.PageSize),
		},
	})
	if err != nil {
		l.Errorf("调用WxLeagueRPC.GetAggregationLiveRecordList失败: %v", err)
		return nil, err
	}

	// 构建API响应
	resp = &types.AggregationLiveRecordListResp{
		Total: rpcResp.Total,
		List:  make([]types.AggregationLiveRecordItem, 0, len(rpcResp.List)),
	}

	// 转换RPC响应到API响应
	for _, item := range rpcResp.List {
		recordItem := types.AggregationLiveRecordItem{
			ID:                         item.ID,
			ExportID:                   item.ExportID,
			Description:                item.Description,
			PromoterShareLink:          item.PromoterShareLink,
			TalentAppid:                item.TalentAppid,
			TalentNickname:             item.TalentNickname,
			TalentHeadImg:              item.TalentHeadImg,
			PredictMaxCommissionAmount: item.PredictMaxCommissionAmount,
			Type:                       item.Type,
			InfoProgress:               item.InfoProgress,
			NoticeID:                   item.NoticeID,
			StartTime:                  item.StartTime,
			StartIsoTime:               item.StartIsoTime,
			SharerTotal:                item.SharerTotal,
			SharerHeadImgs:             item.SharerHeadImgs,
			Class:                      item.Class,
		}
		resp.List = append(resp.List, recordItem)
	}

	l.Infof("获取聚合直播列表成功，总记录数: %d, 当前页: %d, 每页大小: %d", rpcResp.Total, req.PageNo, req.PageSize)
	return resp, nil
}
