package ec

import (
	"context"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetSalesmanListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取员工列表
func NewGetSalesmanListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetSalesmanListLogic {
	return &GetSalesmanListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetSalesmanListLogic) GetSalesmanList(req *types.GetSalesmanListReq) (resp *types.GetSalesmanListResp, err error) {
	// 从JWT中获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "用户未登录")
	}

	// 验证分页参数
	if req.PageNo <= 0 {
		req.PageNo = 1
	}
	if req.PageSize <= 0 || req.PageSize > 100 {
		req.PageSize = 10
	}

	// 构建RPC请求
	rpcReq := &pb.GetSalesmanListReq{
		UserId: uint64(userId),
		PageReq: &pb.PageReq{
			PageNo:   uint64(req.PageNo),
			PageSize: uint64(req.PageSize),
			Keyword:  req.Keyword,
		},
	}

	// 调用RPC服务获取员工列表
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetSalesmanList(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("获取员工列表失败: %v", err)
		return nil, err
	}

	// 创建响应对象
	resp = &types.GetSalesmanListResp{
		List:  make([]types.SalesmanItem, 0),
		Total: rpcResp.Total,
	}

	// 转换员工列表数据
	if len(rpcResp.List) > 0 {
		err = copier.Copy(&resp.List, rpcResp.List)
		if err != nil {
			l.Errorf("复制员工列表数据失败: %v", err)
			return nil, xerr.NewErrMsg(l.ctx, "数据转换失败")
		}
	}

	return resp, nil
}
