package ec

import (
	"context"
	"fmt"
	"xj-serv/pkg/ctxJwt"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetShopLiveQrCodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取小店关联账号直播推广二维码
func NewGetShopLiveQrCodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetShopLiveQrCodeLogic {
	return &GetShopLiveQrCodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetShopLiveQrCodeLogic) GetShopLiveQrCode(req *types.GetShopLiveQrCodeReq) (resp *types.GetShopLiveQrCodeResp, err error) {
	// 从上下文获取用户ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)

	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetShopLiveRecordQrCode(l.ctx, &pb.GetShopLiveRecordQrCodeReq{
		UserId:       uint64(jwtUserId),
		ShopAppid:    req.ShopAppid,
		PromoterId:   req.PromoterId,
		PromoterType: int32(req.PromoterType),
		ExportId:     req.ExportId,
	})
	if err != nil {
		l.Logger.Errorf("获取小店关联账号直播推广二维码失败：%v", err)
		return nil, fmt.Errorf("获取小店关联账号直播推广二维码失败")
	}

	return &types.GetShopLiveQrCodeResp{
		QrcodeUrl: rpcResp.QrcodeUrl,
	}, nil
}
