package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/util/errutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWindowAuthStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWindowAuthStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWindowAuthStatusLogic {
	return &GetWindowAuthStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWindowAuthStatusLogic) GetWindowAuthStatus(req *types.GetWindowAuthStatusReq) (resp *types.GetWindowAuthStatusResp, err error) {
	l.Infof("获取达人橱窗授权状态请求: %+v", req)

	// 从JWT获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId <= 0 {
		l.Errorf("获取当前登录用户ID失败")
		return nil, xerr.NewErrCode(xerr.TokenGenerateError)
	}

	// 调用RPC获取授权状态
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetWindowAuthStatus(l.ctx, &pb.GetWindowAuthStatusReq{
		UserId: uint64(userId),
	})
	if err != nil {
		// 使用RPC错误处理工具，保留错误码
		return nil, errutil.NewRpcError(l.ctx, err)
	}

	// 构建响应
	return &types.GetWindowAuthStatusResp{
		WindowAuthStatus: int(rpcResp.WindowAuthStatus),
		OpenFinderId:     rpcResp.Openfinderid,
		OpenTalentId:     rpcResp.Opentalentid,
		AuthUrl:          rpcResp.AuthUrl,
		AuthWxaPath:      rpcResp.AuthWxaPath,
		AuthWxaAppid:     rpcResp.AuthWxaAppid,
		AuthWxaUsername:  rpcResp.AuthWxaUsername,
	}, nil
}
