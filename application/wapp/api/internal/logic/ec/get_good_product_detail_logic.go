package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/util/errutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGoodProductDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建获取好物商品详情逻辑实例
func NewGetGoodProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGoodProductDetailLogic {
	return &GetGoodProductDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 获取好物商品详情
func (l *GetGoodProductDetailLogic) GetGoodProductDetail(req *types.GetGoodProductDetailReq) (resp *types.GetGoodProductDetailResp, err error) {
	// 从上下文获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)

	// 构建RPC请求
	rpcReq := &pb.GetGoodProductDetailReq{
		ProductId: req.ProductId,
		ShopAppid: req.ShopAppid,
		UserId:    userId,
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetGoodProductDetail(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("调用 WxLeagueRPC.GetGoodProductDetail 失败: %v", err)
		return nil, errutil.NewRpcError(l.ctx, err)
	}

	// 构建API响应
	resp = &types.GetGoodProductDetailResp{
		ProductId:            rpcResp.ProductId,
		ShopAppid:            rpcResp.ShopAppid,
		Title:                rpcResp.Title,
		SubTitle:             rpcResp.SubTitle,
		HeadImgs:             rpcResp.HeadImgs,
		HeadSupplierItemLink: rpcResp.HeadSupplierItemLink,
		DescInfo: &types.DescInfo{
			Imgs: rpcResp.DescInfo.Imgs,
			Desc: rpcResp.DescInfo.Desc,
		},
		Skus: make([]*types.SkuInfo, 0, len(rpcResp.Skus)),
	}

	// 转换SKU信息
	for _, sku := range rpcResp.Skus {
		skuInfo := &types.SkuInfo{
			SkuId:      sku.SkuId,
			ThumbImg:   sku.ThumbImg,
			SalePrice:  float64(sku.SalePrice) / 100, // 从分转为元
			StockNum:   sku.StockNum,
			EarnAmount: sku.EarnAmount,
			SkuAttrs:   make([]types.SkuAttr, 0, len(sku.SkuAttrs)),
		}

		// 转换SKU属性
		for _, attr := range sku.SkuAttrs {
			skuInfo.SkuAttrs = append(skuInfo.SkuAttrs, types.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}

		resp.Skus = append(resp.Skus, skuInfo)
	}

	return resp, nil
}
