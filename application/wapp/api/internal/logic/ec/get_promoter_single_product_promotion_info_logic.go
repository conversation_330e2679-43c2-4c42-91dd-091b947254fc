package ec

import (
	"context"
	"fmt"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPromoterSingleProductPromotionInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取某个推客某个商品的内嵌商品卡片
func NewGetPromoterSingleProductPromotionInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPromoterSingleProductPromotionInfoLogic {
	return &GetPromoterSingleProductPromotionInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPromoterSingleProductPromotionInfoLogic) GetPromoterSingleProductPromotionInfo(req *types.GetPromoterSingleProductPromotionInfoReq) (resp *types.GetPromoterSingleProductPromotionInfoResp, err error) {
	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetPromoterSingleProductPromotionInfo(l.ctx, &pb.GetPromoterSingleProductPromotionInfoReq{
		ProductId:   req.ProductId,
		ShopAppid:   req.ShopAppid,
		SharerAppid: req.SharerAppid,
	})
	if err != nil {
		l.Logger.Errorf("获取商品内嵌卡片失败：%v", err)
		return nil, fmt.Errorf("获取商品内嵌卡片失败")
	}

	return &types.GetPromoterSingleProductPromotionInfoResp{
		ProductPromotionLink: rpcResp.ProductPromotionLink,
	}, nil
}
