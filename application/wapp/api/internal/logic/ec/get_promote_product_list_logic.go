package ec

import (
	"context"
	"math"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/common"
	"xj-serv/pkg/ctxJwt"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPromoteProductListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取可推广的商品列表
func NewGetPromoteProductListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPromoteProductListLogic {
	return &GetPromoteProductListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPromoteProductListLogic) GetPromoteProductList(req *types.GetPromoteProductListReq) (resp *types.GetPromoteProductListResp, err error) {
	// 从上下文获取用户ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)
	rpcReq := &pb.GetPromoteProductListReq{
		CatId: uint64(req.CatId),
		PageReq: &pb.PageReq{
			PageNo:   uint64(req.PageNo),
			PageSize: uint64(req.PageSize),
			Keyword:  req.Keyword,
		},
		SortReq: &pb.SortReq{
			SortBy: req.SortBy,
			Order:  req.Order,
		},
		IsRecommend: req.IsRecommend,
		IsPreferred: req.IsPreferred,
		IsWindow:    req.IsWindow,
		UserId:      jwtUserId,
	}

	rpcResp, err := l.svcCtx.WxLeagueRPC.GetPromoteProductList(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("调用 WxLeagueRPC.GetPromoteProductList 失败: %v", err)
		return nil, err
	}

	resp = &types.GetPromoteProductListResp{
		Total:       rpcResp.Total,
		ProductList: make([]*types.ProductListItem, 0, len(rpcResp.ProductList)),
	}
	for _, rpcItem := range rpcResp.ProductList {
		if rpcItem == nil {
			continue // 跳过 nil 项
		}

		// 转换DescInfo
		var descInfo *types.DescInfo
		if rpcItem.DescInfo != nil {
			descInfo = &types.DescInfo{
				Desc: rpcItem.DescInfo.Desc,
			}
		}

		// 转换Skus
		var skus []*types.SkuInfo
		for _, sku := range rpcItem.Skus {
			if sku == nil {
				continue
			}

			// 转换SkuAttr
			var skuAttrs []types.SkuAttr
			for _, attr := range sku.SkuAttrs {
				if attr == nil {
					continue
				}
				skuAttrs = append(skuAttrs, types.SkuAttr{
					AttrKey:   attr.AttrKey,
					AttrValue: attr.AttrValue,
				})
			}
			skus = append(skus, &types.SkuInfo{
				SkuId:      sku.SkuId,
				ThumbImg:   sku.ThumbImg,
				SalePrice:  math.Round(float64(sku.SalePrice)/100.0*100) / 100,
				StockNum:   sku.StockNum,
				SkuAttrs:   skuAttrs,
				EarnAmount: sku.EarnAmount,
			})
		}

		resp.ProductList = append(resp.ProductList, &types.ProductListItem{
			ProductId:  rpcItem.ProductId,
			ShopAppid:  rpcItem.ShopAppid,
			Title:      rpcItem.Title,
			SubTitle:   rpcItem.SubTitle,
			HeadImgs:   rpcItem.HeadImgs,
			DescInfo:   descInfo,
			Skus:       skus,
			MinPrice:   common.CentToYuanFloat(rpcItem.MinPrice),
			EarnAmount: rpcItem.EarnAmount,
		})
	}

	return resp, nil
}
