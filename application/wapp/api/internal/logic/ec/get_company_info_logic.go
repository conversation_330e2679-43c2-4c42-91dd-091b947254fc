package ec

import (
	"context"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取公司信息
func NewGetCompanyInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyInfoLogic {
	return &GetCompanyInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCompanyInfoLogic) GetCompanyInfo() (resp *types.CompanyInfoResp, err error) {
	// 从JWT中获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId == 0 {
		return nil, xerr.NewErrMsg(l.ctx, "用户未登录")
	}

	// 调用RPC服务获取公司信息
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetCompanyInfo(l.ctx, &pb.GetCompanyInfoReq{
		UserId: uint64(userId),
	})
	if err != nil {
		l.Errorf("获取公司信息失败: %v", err)
		return nil, err
	}

	// 创建响应对象
	resp = &types.CompanyInfoResp{}

	// 使用copier复制RPC响应到API响应
	err = copier.Copy(resp, rpcResp)
	if err != nil {
		l.Errorf("复制公司信息失败: %v", err)
		return nil, xerr.NewErrMsg(l.ctx, "数据转换失败")
	}

	return resp, nil
}
