package ec

import (
	"context"
	"xj-serv/pkg/ctxJwt"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type WithdrawLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 用户提现
func NewWithdrawLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WithdrawLogic {
	return &WithdrawLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WithdrawLogic) Withdraw(req *types.WithdrawReq) (resp *types.WithdrawResp, err error) {
	// 从上下文获取用户ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)
	// 参数验证
	if req.Amount <= 0 {
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 记录请求日志
	logx.WithContext(l.ctx).Infof("用户提现请求: 用户ID=%d, 金额=%f", jwtUserId, req.Amount)

	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.Withdraw(l.ctx, &pb.WithdrawReq{
		UserId: uint64(jwtUserId),
		Amount: req.Amount,
	})

	if err != nil {
		logx.WithContext(l.ctx).Errorf("提现RPC调用失败: %v", err)
		return nil, err
	}

	// 返回响应
	resp = &types.WithdrawResp{
		OutBillNo:    rpcResp.OutBillNo,
		PackageInfo:  rpcResp.PackageInfo,
		Desc:         rpcResp.Desc,
		ReviewStatus: int(rpcResp.ReviewStatus),
		Amount:       rpcResp.Amount,
		ActualAmount: rpcResp.ActualAmount,
		Fee:          rpcResp.Fee,
		FeeRatio:     rpcResp.FeeRatio,
	}

	logx.WithContext(l.ctx).Infof("用户提现成功: 商户单号=%s", resp.OutBillNo)

	return resp, nil
}
