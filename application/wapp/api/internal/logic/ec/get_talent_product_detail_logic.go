package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"
	"xj-serv/pkg/util/errutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTalentProductDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建逻辑实例
func NewGetTalentProductDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTalentProductDetailLogic {
	return &GetTalentProductDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// 获取合作商品详情
func (l *GetTalentProductDetailLogic) GetTalentProductDetail(req *types.GetPromoteProductDetailReq) (resp *types.GetPromoteProductDetailResp, err error) {
	// 参数校验
	if req.ProductId == 0 || req.ShopAppid == "" {
		l.Errorf("请求参数错误: ProductId=%d, ShopAppid=%s", req.ProductId, req.ShopAppid)
		return nil, xerr.NewErrCode(xerr.RequestParamError)
	}

	// 获取用户ID
	var userId int64
	userId = ctxJwt.GetJwtDataID(l.ctx)

	// 调用RPC服务获取合作商品详情
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetTalentProductDetail(l.ctx, &pb.GetPromoteProductDetailReq{
		ProductId: req.ProductId,
		ShopAppid: req.ShopAppid,
		UserId:    userId,
	})
	if err != nil {
		// 使用错误处理工具处理RPC错误
		return nil, errutil.NewRpcError(l.ctx, err)
	}

	// 构建响应数据
	resp = &types.GetPromoteProductDetailResp{
		Product: &types.ProductDetail{
			ShopAppid:            rpcResp.Product.ShopAppid,
			ProductId:            rpcResp.Product.ProductId,
			ProductPromotionLink: rpcResp.Product.ProductPromotionLink,
			ProductInfo: &types.ProductInfo{
				Title:    rpcResp.Product.ProductInfo.Title,
				SubTitle: rpcResp.Product.ProductInfo.SubTitle,
				HeadImgs: rpcResp.Product.ProductInfo.HeadImgs,
				DescInfo: &types.DescInfo{
					Imgs: rpcResp.Product.ProductInfo.DescInfo.Imgs,
					Desc: rpcResp.Product.ProductInfo.DescInfo.Desc,
				},
				Skus: make([]*types.SkuInfo, 0, len(rpcResp.Product.ProductInfo.Skus)),
			},
		},
		PublishCoupons:     make([]*types.Coupon, 0, len(rpcResp.PublishCoupons)),
		CooperativeCoupons: make([]*types.Coupon, 0, len(rpcResp.CooperativeCoupons)),
	}

	// 转换SKU信息
	for _, sku := range rpcResp.Product.ProductInfo.Skus {
		skuInfo := &types.SkuInfo{
			SkuId:      sku.SkuId,
			ThumbImg:   sku.ThumbImg,
			SalePrice:  float64(sku.SalePrice) / 100.0,
			StockNum:   sku.StockNum,
			EarnAmount: sku.EarnAmount,
			SkuAttrs:   make([]types.SkuAttr, 0, len(sku.SkuAttrs)),
		}

		// 转换SKU属性
		for _, attr := range sku.SkuAttrs {
			skuInfo.SkuAttrs = append(skuInfo.SkuAttrs, types.SkuAttr{
				AttrKey:   attr.AttrKey,
				AttrValue: attr.AttrValue,
			})
		}

		resp.Product.ProductInfo.Skus = append(resp.Product.ProductInfo.Skus, skuInfo)
	}

	// 转换优惠券信息 - 发布券
	for _, coupon := range rpcResp.PublishCoupons {
		resp.PublishCoupons = append(resp.PublishCoupons, &types.Coupon{
			CouponId: coupon.CouponId,
		})
	}

	// 转换优惠券信息 - 合作券
	for _, coupon := range rpcResp.CooperativeCoupons {
		resp.CooperativeCoupons = append(resp.CooperativeCoupons, &types.Coupon{
			CouponId: coupon.CouponId,
		})
	}

	return resp, nil
}
