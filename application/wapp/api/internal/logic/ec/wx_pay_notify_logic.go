package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/util/errutil"

	"github.com/zeromicro/go-zero/core/logx"
)

type WxPayNotifyLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 微信支付回调
func NewWxPayNotifyLogic(ctx context.Context, svcCtx *svc.ServiceContext) *WxPayNotifyLogic {
	return &WxPayNotifyLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *WxPayNotifyLogic) WxPayNotify(req *types.WxPayNotifyReq) (resp *types.WxPayNotifyResp, err error) {
	// 记录回调请求日志
	l.Infof("收到微信支付回调通知: ID=%s, EventType=%s, Summary=%s",
		req.Id, req.EventType, req.Summary)

	// 调用RPC服务处理回调
	rpcReq := &pb.WxPayNotifyReq{
		Id:           req.Id,
		CreateTime:   req.CreateTime,
		ResourceType: req.ResourceType,
		EventType:    req.EventType,
		Summary:      req.Summary,
		Resource: &pb.WxPayNotifyResourceInfo{
			OriginalType:   req.Resource.OriginalType,
			Algorithm:      req.Resource.Algorithm,
			Ciphertext:     req.Resource.Ciphertext,
			AssociatedData: req.Resource.AssociatedData,
			Nonce:          req.Resource.Nonce,
		},
	}

	rpcResp, err := l.svcCtx.WxLeagueRPC.WxPayNotify(l.ctx, rpcReq)
	if err != nil {
		l.Errorf("调用EC RPC处理微信支付回调失败: %v", err)
		return nil, errutil.NewRpcError(l.ctx, err)
	}

	// 检查事件类型是否为商家转账通知
	if req.EventType != "MCHTRANSFER.BILL.FINISHED" {
		l.Infof("收到非转账通知事件: %s", req.EventType)
		return &types.WxPayNotifyResp{
			Code:    "SUCCESS",
			Message: "非转账事件",
		}, nil
	}

	// 检查资源类型
	if req.ResourceType != "encrypt-resource" {
		l.Errorf("不支持的资源类型: %s", req.ResourceType)
		return &types.WxPayNotifyResp{
			Code:    "FAIL",
			Message: "不支持的资源类型",
		}, nil
	}

	l.Infof("微信支付回调处理成功: ID=%s", req.Id)

	return &types.WxPayNotifyResp{
		Code:    rpcResp.Code,
		Message: rpcResp.Message,
	}, nil
}
