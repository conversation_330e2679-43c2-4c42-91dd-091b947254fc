package ec

import (
	"context"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SetSalesmanTeamPackageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 代理商给业务经理设置团长套餐数量
func NewSetSalesmanTeamPackageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SetSalesmanTeamPackageLogic {
	return &SetSalesmanTeamPackageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SetSalesmanTeamPackageLogic) SetSalesmanTeamPackage(req *types.SetSalesmanTeamPackageReq) (resp bool, err error) {
	// 从JWT中获取代理商用户ID
	agentUserId := ctxJwt.GetJwtDataID(l.ctx)
	if agentUserId <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "代理商ID不能为空")
	}

	// 参数验证
	if req.SalesmanUserId <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "业务经理ID不能为空")
	}
	if req.Number <= 0 {
		return false, xerr.NewErrMsg(l.ctx, "套餐数量必须大于0")
	}

	// 调用RPC服务设置业务经理团长套餐数量
	rpcResp, err := l.svcCtx.WxLeagueRPC.SetSalesmanTeamPackage(l.ctx, &pb.SetSalesmanTeamPackageReq{
		AgentUserId:    uint64(agentUserId),
		SalesmanUserId: req.SalesmanUserId,
		Number:         req.Number,
	})

	if err != nil {
		l.Errorf("设置业务经理团长套餐数量失败: %v", err)
		return false, err
	}

	return rpcResp.Success, nil
}
