package ec

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/ec/pb"
	"xj-serv/pkg/result/xerr"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetLiveDetailByTalentAppidLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLiveDetailByTalentAppidLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLiveDetailByTalentAppidLogic {
	return &GetLiveDetailByTalentAppidLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLiveDetailByTalentAppidLogic) GetLiveDetailByTalentAppid(req *types.GetLiveDetailByTalentAppidReq) (resp *types.GetLiveDetailByTalentAppidResp, err error) {
	// 参数校验
	if req.TalentAppid == "" {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "达人ID不能为空")
	}

	// 调用RPC服务
	rpcResp, err := l.svcCtx.WxLeagueRPC.GetLiveDetailByTalentAppid(l.ctx, &pb.GetLiveDetailByTalentAppidReq{
		Id:          req.ID,
		TalentAppid: req.TalentAppid,
	})
	if err != nil {
		l.Errorf("调用GetLiveDetailByTalentAppid RPC失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "获取直播间详情失败")
	}

	// 构建响应
	predictMaxCommissionAmount := int8(rpcResp.PredictMaxCommissionAmount)
	resp = &types.GetLiveDetailByTalentAppidResp{
		TalentAppid:                rpcResp.TalentAppid,
		TalentNickname:             rpcResp.TalentNickname,
		TalentHeadImg:              rpcResp.TalentHeadImg,
		Description:                rpcResp.Description,
		PredictMaxCommissionAmount: &predictMaxCommissionAmount,
		ExportId:                   rpcResp.ExportId,
		PromoterShareLink:          rpcResp.PromoterShareLink,
		StartTime:                  &rpcResp.StartTime,
		StartIsoTime:               rpcResp.StartIsoTime,
		Type:                       rpcResp.Type,
		Class:                      rpcResp.Class,
		PromoterId:                 rpcResp.PromoterId,
		GoodsList:                  make([]types.LiveGoodsInfo, 0, len(rpcResp.GoodsList)),
	}

	// 复制商品列表
	if err := copier.Copy(&resp.GoodsList, rpcResp.GoodsList); err != nil {
		l.Errorf("复制商品列表失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "处理数据失败")
	}

	return resp, nil
}
