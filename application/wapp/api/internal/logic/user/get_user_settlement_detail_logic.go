package user

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserSettlementDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户余额明细
func NewGetUserSettlementDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserSettlementDetailLogic {
	return &GetUserSettlementDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserSettlementDetailLogic) GetUserSettlementDetail(req *types.GetUserSettlementDetailReq) (resp *types.GetUserSettlementDetailResp, err error) {
	// 从JWT中获取当前登录用户的ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)

	// 调用RPC服务获取结算明细数据
	// 构造RPC请求参数，包含月份和用户ID
	rpcResp, err := l.svcCtx.UserRPC.GetUserSettlementDetail(l.ctx, &pb.GetUserSettlementDetailReq{
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
		UserId:    uint64(jwtUserId), // 转换用户ID为uint64类型
		PageRequest: &pb.PageRequest{
			PageNo:   uint64(req.PageNo),
			PageSize: uint64(req.PageSize),
			Keyword:  req.Keyword,
		},
		Type: req.Type,
	})
	// 处理RPC调用错误
	if err != nil {
		// 记录错误日志
		l.Logger.Errorf("GetSettlementDetail error: %v", err)
		// 返回用户友好的错误信息
		return nil, xerr.NewErrMsg(l.ctx, "获取结算明细失败")
	}
	items := make([]types.UserSettlementDetail, 0, len(rpcResp.List))
	for _, item := range rpcResp.List {
		settlementItem := types.UserSettlementDetail{
			ID:           item.Id,           // 明细ID
			Amount:       item.Amount,       // 金额
			AmountChange: item.AmountChange, // 金额变动
			Type:         item.Type,         // 明细类型
			CreateTime:   item.CreateTime,   // 创建时间
			Remark:       item.Remark,       // 备注信息
			OrderId:      item.OrderId,      // 订单ID
			ProductName:  item.ProductName,  // 商品名称
		}
		items = append(items, settlementItem)
	}

	return &types.GetUserSettlementDetailResp{
		List:  items,
		Total: rpcResp.Total,
		Summary: types.SettlementSummary{
			TotalAmount:    rpcResp.Summary.TotalAmount,
			DirectAmount:   rpcResp.Summary.DirectAmount,
			IndirectAmount: rpcResp.Summary.IndirectAmount,
			AgentAmount:    rpcResp.Summary.AgentAmount,
		},
	}, nil
}
