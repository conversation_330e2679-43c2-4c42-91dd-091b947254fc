package user

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserTasksProgressLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户任务完成进度
func NewGetUserTasksProgressLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserTasksProgressLogic {
	return &GetUserTasksProgressLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserTasksProgressLogic) GetUserTasksProgress() (*types.GetUserTasksProgressResp, error) {
	// 获取当前登录用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户未登录")
	}

	// 调用RPC服务获取用户任务完成进度
	progressResp, err := l.svcCtx.BenefitsRPC.GetUserTasksProgress(l.ctx, &pb.GetUserTasksProgressReq{
		UserId: uint64(userId),
	})
	if err != nil {
		l.Errorf("获取用户任务完成进度失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "获取用户任务完成进度失败")
	}

	// 构建响应
	resp := &types.GetUserTasksProgressResp{
		DirectReferrer:   progressResp.DirectReferrer,
		IndirectReferrer: progressResp.IndirectReferrer,
		Type3Pool:        progressResp.Type3Pool,
		Type4Pool:        progressResp.Type4Pool,
	}

	return resp, nil
}
