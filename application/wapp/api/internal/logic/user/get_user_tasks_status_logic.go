package user

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserTasksStatusLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户任务完成情况
func NewGetUserTasksStatusLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserTasksStatusLogic {
	return &GetUserTasksStatusLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserTasksStatusLogic) GetUserTasksStatus(req *types.GetUserTasksStatusReq) (*types.GetUserTasksStatusResp, error) {
	// 获取当前登录用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户未登录")
	}

	// 调用RPC服务获取用户任务完成情况
	tasksStatusResp, err := l.svcCtx.BenefitsRPC.GetUserTasksStatus(l.ctx, &pb.GetUserTasksStatusReq{
		UserId: uint64(userId),
	})
	if err != nil {
		l.Errorf("获取用户任务完成情况失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "获取用户任务完成情况失败")
	}

	// 构建响应
	resp := &types.GetUserTasksStatusResp{
		DirectReferrer:   tasksStatusResp.DirectReferrer,
		IndirectReferrer: tasksStatusResp.IndirectReferrer,
		Type3Pool:        tasksStatusResp.Type3Pool,
		Type4Pool:        tasksStatusResp.Type4Pool,
	}

	return resp, nil
}
