package user

import (
	"context"
	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserTaskPoolsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户任务池
func NewGetUserTaskPoolsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserTaskPoolsLogic {
	return &GetUserTaskPoolsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserTaskPoolsLogic) GetUserTaskPools(req *types.GetUserTaskPoolsReq) (*types.GetUserTaskPoolsResp, error) {
	// 获取当前登录用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)
	if userId <= 0 {
		return nil, xerr.NewErrCodeMsg(xerr.RequestParamError, "用户未登录")
	}

	// 初始化响应
	resp := &types.GetUserTaskPoolsResp{}

	// 调用GetUserReferralChain服务获取直接和间接推荐人
	referralChainResp, err := l.svcCtx.BenefitsRPC.GetUserReferralChain(l.ctx, &pb.GetUserReferralChainReq{
		UserId: uint64(userId),
	})
	if err != nil {
		l.Errorf("获取用户推荐关系链失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "获取用户推荐关系链失败")
	}

	// 设置直接推荐人信息(任务池1)
	if referralChainResp.DirectReferrer != nil {
		resp.DirectReferrer = types.TaskPoolUserInfo{
			UserId:   referralChainResp.DirectReferrer.Id,
			FinderId: referralChainResp.DirectReferrer.FinderId,
			Nickname: referralChainResp.DirectReferrer.NickName,
			Avatar:   referralChainResp.DirectReferrer.Avatar,
			Mobile:   referralChainResp.DirectReferrer.Mobile,
		}
	}

	// 设置间接推荐人信息(任务池2)
	if referralChainResp.IndirectReferrer != nil {
		resp.IndirectReferrer = types.TaskPoolUserInfo{
			UserId:   referralChainResp.IndirectReferrer.Id,
			FinderId: referralChainResp.IndirectReferrer.FinderId,
			Nickname: referralChainResp.IndirectReferrer.NickName,
			Avatar:   referralChainResp.IndirectReferrer.Avatar,
			Mobile:   referralChainResp.IndirectReferrer.Mobile,
		}
	}

	// 调用GetRandomBenefitsPool服务获取类型3和类型4的福利池
	randomPoolResp, err := l.svcCtx.BenefitsRPC.GetRandomBenefitsPool(l.ctx, &pb.GetRandomBenefitsPoolReq{
		UserId: uint64(userId), // 排除自己
	})
	if err != nil {
		l.Errorf("获取随机福利池失败: %v", err)
		return nil, xerr.NewErrCodeMsg(xerr.ServerCommonError, "获取随机福利池失败")
	}

	// 设置类型3福利池用户信息(任务池3)
	if randomPoolResp.Type3Pool != nil && randomPoolResp.Type3Pool.UserInfo != nil {
		resp.Type3Pool = types.TaskPoolUserInfo{
			UserId:   randomPoolResp.Type3Pool.UserInfo.Id,
			FinderId: randomPoolResp.Type3Pool.UserInfo.FinderId,
			Nickname: randomPoolResp.Type3Pool.UserInfo.NickName,
			Avatar:   randomPoolResp.Type3Pool.UserInfo.Avatar,
			Mobile:   randomPoolResp.Type3Pool.UserInfo.Mobile,
		}
	}

	// 设置类型4福利池用户信息(任务池4)
	if randomPoolResp.Type4Pool != nil && randomPoolResp.Type4Pool.UserInfo != nil {
		resp.Type4Pool = types.TaskPoolUserInfo{
			UserId:   randomPoolResp.Type4Pool.UserInfo.Id,
			FinderId: randomPoolResp.Type4Pool.UserInfo.FinderId,
			Nickname: randomPoolResp.Type4Pool.UserInfo.NickName,
			Avatar:   randomPoolResp.Type4Pool.UserInfo.Avatar,
			Mobile:   randomPoolResp.Type4Pool.UserInfo.Mobile,
		}
	}

	return resp, nil
}
