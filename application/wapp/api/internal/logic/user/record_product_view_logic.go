package user

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type RecordProductViewLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 记录用户浏览商品信息
func NewRecordProductViewLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RecordProductViewLogic {
	return &RecordProductViewLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RecordProductViewLogic) RecordProductView(req *types.RecordProductViewReq) (resp *types.RecordProductViewResp, err error) {
	// 参数校验
	if req.ProductId <= 0 || req.ShopAppid == "" || req.PoolType <= 0 {
		return &types.RecordProductViewResp{
			Success: false,
			Message: "参数不完整",
		}, xerr.NewErrCode(xerr.RequestParamError)
	}

	userId := ctxJwt.GetJwtDataID(l.ctx)

	// 调用RPC服务记录商品浏览信息
	rpcResp, err := l.svcCtx.BenefitsRPC.RecordProductView(l.ctx, &pb.RecordProductViewReq{
		UserId:    uint64(userId),
		SellerId:  req.SellerId,
		ProductId: req.ProductId,
		PoolType:  req.PoolType,
		ShopAppid: req.ShopAppid,
	})

	if err != nil {
		logx.Errorf("调用RPC服务记录商品浏览信息失败: %v", err)
		return &types.RecordProductViewResp{
			Success: false,
			Message: "系统错误，请稍后重试",
		}, err
	}

	return &types.RecordProductViewResp{
		Success: rpcResp.Success,
		Message: rpcResp.Message,
	}, nil
}
