package user

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserIncomeStatLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户收益统计
func NewGetUserIncomeStatLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserIncomeStatLogic {
	return &GetUserIncomeStatLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserIncomeStatLogic) GetUserIncomeStat() (resp *types.UserIncomeStatResp, err error) {
	// 从JWT中获取当前登录用户的ID
	jwtUserId := ctxJwt.GetJwtDataID(l.ctx)

	// 调用RPC服务获取用户收益统计
	rpcResp, err := l.svcCtx.UserRPC.GetUserIncomeStat(l.ctx, &pb.GetUserIncomeStatReq{
		UserId: uint64(jwtUserId),
	})

	if err != nil {
		l.Logger.Errorf("获取用户收益统计失败: %v", err)
		return nil, err
	}

	// 构建响应
	resp = &types.UserIncomeStatResp{
		Balance:            rpcResp.Balance,
		TodayIncome:        rpcResp.TodayIncome,
		YesterdayIncome:    rpcResp.YesterdayIncome,
		CurrentMonthIncome: rpcResp.CurrentMonthIncome,
		LastMonthIncome:    rpcResp.LastMonthIncome,
		OrderCount:         rpcResp.OrderCount,
		FansCount:          rpcResp.FansCount,
		Withdraw:           rpcResp.Withdraw,
		Settlement:         rpcResp.Settlement,
	}

	return resp, nil
}
