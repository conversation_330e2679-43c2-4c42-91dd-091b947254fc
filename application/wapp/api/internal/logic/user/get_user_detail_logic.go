package user

import (
	"context"
	"xj-serv/application/wapp/rpc/xjs/pb"
	"xj-serv/pkg/ctxJwt"
	"xj-serv/pkg/result/xerr"

	"xj-serv/application/wapp/api/internal/svc"
	"xj-serv/application/wapp/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取用户详情
func NewGetUserDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserDetailLogic {
	return &GetUserDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserDetailLogic) GetUserDetail() (resp *types.UserDetailResp, err error) {
	// 从JWT获取用户ID
	userId := ctxJwt.GetJwtDataID(l.ctx)

	//// 调用RPC服务获取用户详情
	//userDetail, err := l.svcCtx.UserRPC.GetUserDetail(l.ctx, &pb.UserInfoReq{
	//	UserId: uint64(userId),
	//})
	//if err != nil {
	//	// 记录错误日志
	//	l.Logger.Errorf("GetSettlementDetail error: %v", err)
	//	// 返回用户友好的错误信息
	//	return nil, xerr.NewErrMsg(l.ctx, "获取用户信息失败")
	//}

	// 调用RPC服务获取用户详情
	userDetail, err := l.svcCtx.UserRPC.GetUserDetail(l.ctx, &pb.IDReq{Id: uint64(userId)})
	if err != nil {
		// 记录错误日志
		l.Logger.Errorf("GetSettlementDetail error: %v", err)
		// 返回用户友好的错误信息
		return nil, xerr.NewErrMsg(l.ctx, "获取用户信息失败")
	}

	// 转换响应结构
	resp = &types.UserDetailResp{
		CreatedAt:      userDetail.CreatedAt,
		Uuid:           userDetail.Uuid,
		Mobile:         userDetail.Mobile,
		NickName:       userDetail.NickName,
		Withdraw:       userDetail.Withdraw,
		Avatar:         userDetail.Avatar,
		Gender:         userDetail.Gender,
		Country:        userDetail.Country,
		Province:       userDetail.Province,
		City:           userDetail.City,
		AddressId:      userDetail.AddressId,
		Balance:        userDetail.Balance,
		Points:         userDetail.Points,
		PayMoney:       userDetail.PayMoney,
		ExpendMoney:    userDetail.ExpendMoney,
		GradeId:        userDetail.GradeId,
		Platform:       userDetail.Platform,
		LastLoginTime:  userDetail.LastLoginTime,
		Openid:         userDetail.Openid,
		Unionid:        userDetail.Unionid,
		SharerAppid:    userDetail.SharerAppid,
		ContactName:    userDetail.ContactName,
		ContactNumber:  userDetail.ContactNumber,
		ShareCode:      userDetail.ShareCode,
		ShareCodeImage: userDetail.ShareCodeImage,
		RegIpv4:        userDetail.RegIpv4,
		InviteFrom:     userDetail.InviteFrom,
		RegSource:      userDetail.RegSource,
		Level:          userDetail.Level,
		LevelName:      userDetail.LevelName,
		IsEnterprise:   userDetail.IsEnterprise,
		Birthday:       userDetail.Birthday,
		RoleId:         userDetail.RoleId,
		FinderId:       userDetail.FinderId,
	}

	return resp, nil
}
