package svc

import (
	"xj-serv/application/wapp/api/internal/config"
	"xj-serv/application/wapp/api/internal/middleware"
	"xj-serv/application/wapp/rpc/ec/client/wxleague"
	"xj-serv/application/wapp/rpc/ec/client/wxminishop"
	"xj-serv/application/wapp/rpc/pfs/client/wxapp"
	"xj-serv/application/wapp/rpc/xjs/client/benefits"
	"xj-serv/application/wapp/rpc/xjs/client/nas"
	"xj-serv/application/wapp/rpc/xjs/client/tasks"
	"xj-serv/application/wapp/rpc/xjs/client/user"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config          config.Config
	RequestUriCache rest.Middleware
	IpLimiter       rest.Middleware
	Limiter         rest.Middleware
	XjSignature     rest.Middleware
	JwtParse        rest.Middleware
	BizRedis        *redis.Redis
	UserRPC         user.User
	WxAppRPC        wxapp.WxApp
	TasksRPC        tasks.Tasks
	ShopRPC         wxminishop.WxMiniShop
	BenefitsRPC     benefits.Benefits
	NasRPC          nas.Nas
	WxLeagueRPC     wxleague.WxLeague
}

func NewServiceContext(c config.Config) *ServiceContext {
	rds := redis.MustNewRedis(c.BizRedis, redis.WithPass(c.BizRedis.Pass)) // jsonMark:骑着毛驴背单词
	svc := &ServiceContext{
		Config:      c,
		BizRedis:    rds,
		UserRPC:     user.NewUser(zrpc.MustNewClient(c.XjsRPC)),
		WxAppRPC:    wxapp.NewWxApp(zrpc.MustNewClient(c.PfsRPC)),
		TasksRPC:    tasks.NewTasks(zrpc.MustNewClient(c.XjsRPC)),
		BenefitsRPC: benefits.NewBenefits(zrpc.MustNewClient(c.XjsRPC)),
		NasRPC:      nas.NewNas(zrpc.MustNewClient(c.XjsRPC)),
		ShopRPC:     wxminishop.NewWxMiniShop(zrpc.MustNewClient(c.EcRPC)),
		WxLeagueRPC: wxleague.NewWxLeague(zrpc.MustNewClient(c.EcRPC)),
	}

	svc.RequestUriCache = middleware.NewRequestUriCacheMiddleware(rds, c).Handle
	svc.IpLimiter = middleware.NewIpLimiterMiddleware(rds, c).Handle
	svc.XjSignature = middleware.NewXjSignatureMiddleware(c).Handle
	svc.JwtParse = middleware.NewJwtParseMiddleware(c).Handle
	return svc
}
