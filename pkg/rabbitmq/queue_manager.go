// Package rabbitmq 提供了RabbitMQ消息队列的基础操作封装
// 包含连接管理、交换机和队列声明、消息发布和消费等功能
package rabbitmq

import (
	"fmt"
	"github.com/streadway/amqp"
	"github.com/zeromicro/go-zero/core/logx"
	"reflect"
	"sync"
)

// QueueConfig 定义队列的基本配置信息
type QueueConfig struct {
	Exchange   string // 交换机名称，用于接收和分发消息
	RoutingKey string // 路由键，用于将消息从交换机路由到队列
	QueueName  string // 队列名称，用于存储消息
}

// QueueParams 定义队列和交换机的参数选项
type QueueParams struct {
	Durable    bool // 是否持久化，true表示重启后队列仍存在
	AutoDelete bool // 是否自动删除，true表示当最后一个消费者断开连接时自动删除队列
	Exclusive  bool // 是否排他性，true表示只允许声明该队列的连接使用
	NoWait     bool // 是否等待服务器确认，true表示不等待服务器确认
	Mandatory  bool // 是否强制性，true表示如果消息无法路由到队列则返回给发布者
	Immediate  bool // 是否立即，true表示如果消息无法立即投递给消费者则返回给发布者
}

// QueueManager RabbitMQ队列管理器，提供队列注册、初始化和消息收发功能
type QueueManager struct {
	client      *RabbitMQ              // RabbitMQ客户端实例
	queueConfig map[string]QueueConfig // 队列配置映射，键为队列名称
	queueParams QueueParams            // 队列参数
	mu          sync.RWMutex           // 读写锁，保证并发安全
	logger      logx.Logger            // 日志记录器
}

// NewQueueManager 创建一个新的队列管理器实例
//
// 参数:
//   - client: RabbitMQ客户端实例
//   - params: 队列参数配置
//
// 返回:
//   - *QueueManager: 队列管理器实例
func NewQueueManager(client *RabbitMQ, params QueueParams) *QueueManager {
	return &QueueManager{
		client:      client,
		queueConfig: make(map[string]QueueConfig),
		queueParams: params,
		logger:      logx.WithCallerSkip(1),
	}
}

// RegisterQueue 注册队列配置到队列管理器
//
// 参数:
//   - name: 队列配置的名称，用于后续引用
//   - config: 队列配置信息
func (m *QueueManager) RegisterQueue(name string, config QueueConfig) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.queueConfig[name] = config
}

// GetQueueConfig 获取已注册的队列配置
//
// 参数:
//   - name: 队列配置的名称
//
// 返回:
//   - QueueConfig: 队列配置
//   - bool: 是否存在该配置
func (m *QueueManager) GetQueueConfig(name string) (QueueConfig, bool) {
	m.mu.RLock()
	defer m.mu.RUnlock()
	config, ok := m.queueConfig[name]
	return config, ok
}

// GetAllQueueConfigs 获取所有队列配置
func (m *QueueManager) GetAllQueueConfigs() map[string]QueueConfig {
	m.mu.RLock()
	defer m.mu.RUnlock()

	configs := make(map[string]QueueConfig)
	for k, v := range m.queueConfig {
		configs[k] = v
	}
	return configs
}

// InitializeQueues 初始化所有已注册的队列
// 包括声明交换机、队列和绑定关系
//
// 返回:
//   - error: 错误信息，如果初始化成功则为nil
func (m *QueueManager) InitializeQueues() error {
	// 复制队列配置，避免长时间持有锁
	m.mu.RLock()
	queueConfigs := make(map[string]QueueConfig, len(m.queueConfig))
	for k, v := range m.queueConfig {
		queueConfigs[k] = v
	}
	m.mu.RUnlock()

	// 使用复制的配置初始化队列
	for queueKey, queueConfig := range queueConfigs {
		if err := m.initializeQueue(queueKey, queueConfig); err != nil {
			return fmt.Errorf("初始化队列[%s]失败: %w", queueKey, err)
		}
	}

	return nil
}

// initializeQueue 初始化单个队列，包括声明交换机、队列和绑定关系
// 同时创建死信队列和死信交换机，用于处理无法正常消费的消息
//
// 参数:
//   - queueKey: 队列配置的键名
//   - queueConfig: 队列配置
//
// 返回:
//   - error: 错误信息，如果初始化成功则为nil
func (m *QueueManager) initializeQueue(queueKey string, queueConfig QueueConfig) error {
	// 声明主交换机
	err := m.client.DeclareExchange(
		queueConfig.Exchange,
		"direct", // 使用direct类型的交换机，根据routingKey精确匹配
		m.queueParams.Durable,
		m.queueParams.AutoDelete,
		m.queueParams.NoWait,
		nil,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("声明RabbitMQ交换机[%s]失败: %w", queueConfig.Exchange, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	// 声明死信交换机，用于处理无法正常消费的消息
	dlxName := queueConfig.Exchange + ".dlx"
	err = m.client.DeclareExchange(
		dlxName,
		"direct",
		m.queueParams.Durable,
		m.queueParams.AutoDelete,
		m.queueParams.NoWait,
		nil,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("声明RabbitMQ死信交换机[%s]失败: %w", dlxName, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	// 声明死信队列
	dlqName := queueConfig.QueueName + ".dlq"
	_, err = m.client.DeclareQueue(
		dlqName,
		m.queueParams.Durable,
		m.queueParams.AutoDelete,
		m.queueParams.Exclusive,
		m.queueParams.NoWait,
		nil,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("声明RabbitMQ死信队列[%s]失败: %w", dlqName, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	// 绑定死信队列到死信交换机
	err = m.client.BindQueue(
		dlqName,
		queueConfig.RoutingKey+".dlq",
		dlxName,
		m.queueParams.NoWait,
		nil,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("绑定RabbitMQ死信队列[%s]到死信交换机失败: %w", dlqName, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	// 为主队列设置死信交换机参数
	args := amqp.Table{
		"x-dead-letter-exchange":    dlxName,
		"x-dead-letter-routing-key": queueConfig.RoutingKey + ".dlq",
	}

	// 声明队列（使用死信参数）
	_, err = m.client.DeclareQueue(
		queueConfig.QueueName,
		m.queueParams.Durable,
		m.queueParams.AutoDelete,
		m.queueParams.Exclusive,
		m.queueParams.NoWait,
		args,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("声明RabbitMQ队列[%s]失败: %w", queueConfig.QueueName, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	// 绑定队列到交换机
	err = m.client.BindQueue(
		queueConfig.QueueName,
		queueConfig.RoutingKey,
		queueConfig.Exchange,
		m.queueParams.NoWait,
		nil,
	)
	if err != nil {
		wrappedErr := fmt.Errorf("绑定RabbitMQ队列[%s]到交换机失败: %w", queueConfig.QueueName, err)
		m.logger.Error(wrappedErr)
		return wrappedErr
	}

	m.logger.Infof("RabbitMQ队列[%s]初始化成功", queueKey)
	return nil
}

// PublishToQueue 发送消息到指定队列
//
// 示例:
//
//	err := queueManager.PublishToQueue("OrderQueue", []byte(`{"order_id": 12345}`))
//	if err != nil {
//	    log.Errorf("发送订单消息失败: %v", err)
//	}
//
// 参数:
//   - queueName: 队列名称，必须已通过RegisterQueue注册
//   - data: 消息内容，通常为JSON序列化后的字节数组
//
// 返回:
//   - error: 错误信息，如果发送成功则为nil
func (m *QueueManager) PublishToQueue(queueName string, data []byte) error {
	config, ok := m.GetQueueConfig(queueName)
	if !ok {
		return fmt.Errorf("队列[%s]配置不存在", queueName)
	}

	return m.client.PublishMessage(
		config.Exchange,
		config.RoutingKey,
		data,
	)
}

// PublishToQueueWithHeaders 发送带有自定义头部的消息到指定队列
//
// 参数:
//   - queueName: 队列名称，必须已通过RegisterQueue注册
//   - publishing: 消息内容和属性
//
// 返回:
//   - error: 错误信息，如果发送成功则为nil
func (m *QueueManager) PublishToQueueWithHeaders(queueName string, publishing amqp.Publishing) error {
	config, ok := m.GetQueueConfig(queueName)
	if !ok {
		return fmt.Errorf("队列[%s]配置不存在", queueName)
	}

	return m.client.PublishMessageWithHeaders(
		config.Exchange,
		config.RoutingKey,
		false, // mandatory
		false, // immediate
		publishing,
	)
}

// ConsumeFromQueue 从指定队列消费消息
//
// 示例:
//
//	msgs, err := queueManager.ConsumeFromQueue("OrderQueue", "order_consumer", false)
//	if err != nil {
//	    log.Errorf("启动消费者失败: %v", err)
//	    return
//	}
//
//	for msg := range msgs {
//	    // 处理消息
//	    log.Infof("收到消息: %s", string(msg.Body))
//	    msg.Ack(false) // 确认消息
//	}
//
// 参数:
//   - queueName: 队列名称，必须已通过RegisterQueue注册
//   - consumerName: 消费者名称，用于标识消费者
//   - autoAck: 是否自动确认消息，true表示自动确认，false表示需要手动确认
//
// 返回:
//   - <-chan amqp.Delivery: 消息通道
//   - error: 错误信息，如果启动消费者成功则为nil
func (m *QueueManager) ConsumeFromQueue(queueName, consumerName string, autoAck bool) (<-chan amqp.Delivery, error) {
	config, ok := m.GetQueueConfig(queueName)
	if !ok {
		return nil, fmt.Errorf("队列[%s]配置不存在", queueName)
	}

	return m.client.ConsumeMessages(
		config.QueueName,
		consumerName,
		autoAck,
		false, // 非排他
		false, // 接收所有消息
		false, // 等待服务器确认
		nil,   // 无额外参数
	)
}

// InitQueueManagerFromServiceConfig 从服务配置初始化队列管理器
func InitQueueManagerFromServiceConfig(config interface{}) (*QueueManager, error) {
	// 使用反射获取RabbitMQ配置
	configValue := reflect.ValueOf(config)
	if configValue.Kind() == reflect.Ptr {
		configValue = configValue.Elem()
	}

	// 获取RabbitMQ字段
	rabbitMQValue := configValue.FieldByName("RabbitMQ")
	if !rabbitMQValue.IsValid() {
		return nil, fmt.Errorf("配置中未找到RabbitMQ字段")
	}

	// 打印RabbitMQ配置结构
	logx.Infof("RabbitMQ配置类型: %s", rabbitMQValue.Type().String())

	// 获取Host字段
	hostValue := rabbitMQValue.FieldByName("Host")
	if !hostValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到Host字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到Host字段")
	}
	host := hostValue.String()
	logx.Infof("RabbitMQ Host: %s", host)

	// 获取Port字段
	portValue := rabbitMQValue.FieldByName("Port")
	if !portValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到Port字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到Port字段")
	}
	port := int(portValue.Int())
	logx.Infof("RabbitMQ Port: %d", port)

	// 获取Username字段
	usernameValue := rabbitMQValue.FieldByName("Username")
	if !usernameValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到Username字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到Username字段")
	}
	username := usernameValue.String()
	logx.Infof("RabbitMQ Username: %s", username)

	// 获取Password字段
	passwordValue := rabbitMQValue.FieldByName("Password")
	if !passwordValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到Password字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到Password字段")
	}
	password := passwordValue.String()

	// 获取VirtualHost字段
	vhostValue := rabbitMQValue.FieldByName("VirtualHost")
	if !vhostValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到VirtualHost字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到VirtualHost字段")
	}
	vhost := vhostValue.String()
	logx.Infof("RabbitMQ VirtualHost: %s", vhost)

	// 获取DefaultParams字段
	defaultParamsValue := rabbitMQValue.FieldByName("DefaultParams")
	if !defaultParamsValue.IsValid() {
		logx.Error("RabbitMQ配置中未找到DefaultParams字段")
		return nil, fmt.Errorf("RabbitMQ配置中未找到DefaultParams字段")
	}

	// 获取队列配置
	queuesValue := rabbitMQValue.FieldByName("Queues")
	if !queuesValue.IsValid() || queuesValue.Kind() != reflect.Map {
		logx.Error("配置中未找到有效的Queues字段")
		return nil, fmt.Errorf("配置中未找到有效的Queues字段")
	}

	// 打印队列配置数量
	logx.Infof("队列配置数量: %d", queuesValue.Len())

	// 获取队列配置的键
	queueKeys := queuesValue.MapKeys()
	for _, key := range queueKeys {
		logx.Infof("队列配置键: %s", key.String())
	}

	if host == "" {
		return nil, fmt.Errorf("RabbitMQ主机地址不能为空")
	}

	// 获取默认参数
	paramsValue := rabbitMQValue.FieldByName("DefaultParams")
	queueParams := QueueParams{
		Durable:    getBoolField(paramsValue, "Durable", true),
		AutoDelete: getBoolField(paramsValue, "AutoDelete", false),
		Exclusive:  getBoolField(paramsValue, "Exclusive", false),
		NoWait:     getBoolField(paramsValue, "NoWait", false),
		Mandatory:  getBoolField(paramsValue, "Mandatory", false),
		Immediate:  getBoolField(paramsValue, "Immediate", false),
	}

	// 获取队列配置
	queuesValue = rabbitMQValue.FieldByName("Queues")
	if !queuesValue.IsValid() || queuesValue.Kind() != reflect.Map {
		return nil, fmt.Errorf("配置中未找到有效的Queues字段")
	}

	// 转换队列配置
	queuesConfig := make(map[string]QueueConfig)
	for _, key := range queuesValue.MapKeys() {
		queueConfigValue := queuesValue.MapIndex(key)
		if !queueConfigValue.IsValid() {
			continue
		}

		queueName := key.String()
		exchange := getStructStringField(queueConfigValue, "Exchange")
		routingKey := getStructStringField(queueConfigValue, "RoutingKey")
		queueNameField := getStructStringField(queueConfigValue, "QueueName")

		// 验证必要字段
		if exchange == "" {
			return nil, fmt.Errorf("队列[%s]的Exchange不能为空", queueName)
		}
		if routingKey == "" {
			return nil, fmt.Errorf("队列[%s]的RoutingKey不能为空", queueName)
		}
		if queueNameField == "" {
			return nil, fmt.Errorf("队列[%s]的QueueName不能为空", queueName)
		}

		queuesConfig[queueName] = QueueConfig{
			Exchange:   exchange,
			RoutingKey: routingKey,
			QueueName:  queueNameField,
		}
	}

	// 创建队列管理器
	return NewQueueManagerFromConfig(host, port, username, password, vhost, queueParams, queuesConfig)
}

// getStringField 从反射值中获取字符串字段
// 如果字段不存在或类型不是字符串，则返回空字符串
//
// 参数:
//   - v: 反射值
//   - fieldName: 字段名称
//
// 返回:
//   - string: 字段值，如果字段不存在或类型不匹配则返回空字符串
func getStringField(v reflect.Value, fieldName string) string {
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.String {
		return ""
	}
	return field.String()
}

// getIntField 从反射值中获取整数字段
// 如果字段不存在或类型不是整数，则返回0
//
// 参数:
//   - v: 反射值
//   - fieldName: 字段名称
//
// 返回:
//   - int: 字段值，如果字段不存在或类型不匹配则返回0
func getIntField(v reflect.Value, fieldName string) int {
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.Int {
		return 0
	}
	return int(field.Int())
}

// getBoolField 从反射值中获取布尔字段
// 如果字段不存在或类型不是布尔，则返回默认值
//
// 参数:
//   - v: 反射值
//   - fieldName: 字段名称
//   - defaultValue: 默认值，当字段不存在或类型不匹配时返回
//
// 返回:
//   - bool: 字段值，如果字段不存在或类型不匹配则返回默认值
func getBoolField(v reflect.Value, fieldName string, defaultValue bool) bool {
	field := v.FieldByName(fieldName)
	if !field.IsValid() || field.Kind() != reflect.Bool {
		return defaultValue
	}
	return field.Bool()
}

// getStructStringField 从结构体反射值中获取字符串字段
// 处理接口和指针类型，确保能正确获取字段值
//
// 参数:
//   - v: 反射值
//   - fieldName: 字段名称
//
// 返回:
//   - string: 字段值，如果字段不存在或类型不匹配则返回空字符串
func getStructStringField(v reflect.Value, fieldName string) string {
	if v.Kind() == reflect.Interface {
		v = v.Elem()
	}
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return ""
	}
	return getStringField(v, fieldName)
}

// NewQueueManagerFromConfig 从配置创建队列管理器
// 创建RabbitMQ客户端，初始化队列管理器并注册队列
//
// 参数:
//   - host: RabbitMQ服务器主机地址
//   - port: RabbitMQ服务器端口
//   - username: 连接用户名
//   - password: 连接密码
//   - vhost: 虚拟主机名称
//   - params: 队列参数
//   - queuesConfig: 队列配置映射
//
// 返回:
//   - *QueueManager: 队列管理器实例
//   - error: 错误信息，如果创建成功则为nil
func NewQueueManagerFromConfig(host string, port int, username, password, vhost string, params QueueParams, queuesConfig map[string]QueueConfig) (*QueueManager, error) {
	// 创建RabbitMQ客户端
	client, err := NewRabbitMQ(host, port, username, password, vhost)
	if err != nil {
		return nil, fmt.Errorf("创建RabbitMQ客户端失败: %w", err)
	}

	// 创建队列管理器
	manager := NewQueueManager(client, params)

	// 注册队列
	for name, config := range queuesConfig {
		manager.RegisterQueue(name, config)
	}

	// 初始化队列
	if err := manager.InitializeQueues(); err != nil {
		client.Close() // 关闭连接
		return nil, fmt.Errorf("初始化队列失败: %w", err)
	}

	return manager, nil
}
