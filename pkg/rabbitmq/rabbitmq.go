// Package rabbitmq 提供了RabbitMQ消息队列的基础操作封装
// 包含连接管理、交换机和队列声明、消息发布和消费等功能
package rabbitmq

import (
	"fmt"
	"github.com/streadway/amqp"
	"github.com/zeromicro/go-zero/core/logx"
	"time"
)

// RabbitMQ 封装RabbitMQ连接和操作的基础结构
type RabbitMQ struct {
	conn    *amqp.Connection // RabbitMQ服务器连接
	channel *amqp.Channel    // 通信通道
}

// NewRabbitMQ 创建一个新的RabbitMQ实例
// 
// 参数:
//   - host: RabbitMQ服务器主机地址
//   - port: RabbitMQ服务器端口
//   - username: 连接用户名
//   - password: 连接密码
//   - vhost: 虚拟主机名称
//
// 返回:
//   - *RabbitMQ: RabbitMQ实例
//   - error: 错误信息，如果连接成功则为nil
func NewRabbitMQ(host string, port int, username, password, vhost string) (*RabbitMQ, error) {
	// 构建连接URL
	url := fmt.Sprintf("amqp://%s:%s@%s:%d/%s", username, password, host, port, vhost)

	// 建立连接
	conn, err := amqp.Dial(url)
	if err != nil {
		return nil, fmt.Errorf("连接RabbitMQ失败: %w", err)
	}

	// 创建通道
	channel, err := conn.Channel()
	if err != nil {
		conn.Close()
		return nil, fmt.Errorf("创建RabbitMQ通道失败: %w", err)
	}

	return &RabbitMQ{
		conn:    conn,
		channel: channel,
	}, nil
}

// DeclareExchange 声明交换机
//
// 参数:
//   - name: 交换机名称
//   - kind: 交换机类型，如"direct"、"fanout"、"topic"、"headers"
//   - durable: 是否持久化，true表示重启后交换机仍存在
//   - autoDelete: 是否自动删除，true表示当所有队列都不再使用时自动删除
//   - noWait: 是否等待服务器确认，true表示不等待服务器确认
//   - args: 额外参数
//
// 返回:
//   - error: 错误信息，如果声明成功则为nil
func (r *RabbitMQ) DeclareExchange(name, kind string, durable, autoDelete, noWait bool, args amqp.Table) error {
	return r.channel.ExchangeDeclare(
		name,       // 交换机名称
		kind,       // 交换机类型
		durable,    // 持久化
		autoDelete, // 自动删除
		false,      // 内部使用
		noWait,     // 不等待服务器确认
		args,       // 额外参数
	)
}

// DeclareQueue 声明队列
//
// 参数:
//   - name: 队列名称
//   - durable: 是否持久化，true表示重启后队列仍存在
//   - autoDelete: 是否自动删除，true表示当所有消费者断开连接时自动删除
//   - exclusive: 是否排他性，true表示只允许声明该队列的连接使用
//   - noWait: 是否等待服务器确认，true表示不等待服务器确认
//   - args: 额外参数
//
// 返回:
//   - amqp.Queue: 队列信息
//   - error: 错误信息，如果声明成功则为nil
func (r *RabbitMQ) DeclareQueue(name string, durable, autoDelete, exclusive, noWait bool, args amqp.Table) (amqp.Queue, error) {
	return r.channel.QueueDeclare(
		name,       // 队列名称
		durable,    // 持久化
		autoDelete, // 自动删除
		exclusive,  // 排他性
		noWait,     // 不等待服务器确认
		args,       // 额外参数
	)
}

// BindQueue 绑定队列到交换机
//
// 参数:
//   - queueName: 队列名称
//   - routingKey: 路由键
//   - exchangeName: 交换机名称
//   - noWait: 是否等待服务器确认，true表示不等待服务器确认
//   - args: 额外参数
//
// 返回:
//   - error: 错误信息，如果绑定成功则为nil
func (r *RabbitMQ) BindQueue(queueName, routingKey, exchangeName string, noWait bool, args amqp.Table) error {
	return r.channel.QueueBind(
		queueName,    // 队列名称
		routingKey,   // 路由键
		exchangeName, // 交换机名称
		noWait,       // 不等待服务器确认
		args,         // 额外参数
	)
}

// PublishMessage 发布消息到指定的交换机和路由键
//
// 示例:
//   err := rabbitMQ.PublishMessage("order_exchange", "new_order", []byte(`{"id":1234}`))
//   if err != nil {
//       log.Errorf("发布订单消息失败: %v", err)
//   }
//
// 参数:
//   - exchange: 交换机名称
//   - routingKey: 路由键
//   - body: 消息内容
//
// 返回:
//   - error: 错误信息，如果发布成功则为nil
func (r *RabbitMQ) PublishMessage(exchange, routingKey string, body []byte) error {
	return r.channel.Publish(
		exchange,   // 交换机名称
		routingKey, // 路由键
		false,      // 强制
		false,      // 立即
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
		},
	)
}

// PublishMessageWithHeaders 发布带有自定义头部的消息
//
// 参数:
//   - exchange: 交换机名称
//   - routingKey: 路由键
//   - mandatory: 是否强制，true表示如果消息无法路由到队列则返回给发布者
//   - immediate: 是否立即，true表示如果消息无法立即投递给消费者则返回给发布者
//   - publishing: 消息内容和属性
//
// 返回:
//   - error: 错误信息，如果发布成功则为nil
func (r *RabbitMQ) PublishMessageWithHeaders(exchange, routingKey string, mandatory, immediate bool, publishing amqp.Publishing) error {
	return r.channel.Publish(
		exchange,   // 交换机名称
		routingKey, // 路由键
		mandatory,  // 强制
		immediate,  // 立即
		publishing, // 消息内容
	)
}

// ConsumeMessages 消费消息
//
// 示例:
//   msgs, err := rabbitMQ.ConsumeMessages("order_queue", "order_consumer", false, false, false, false, nil)
//   if err != nil {
//       log.Errorf("启动消费者失败: %v", err)
//       return
//   }
//   
//   for msg := range msgs {
//       // 处理消息
//       log.Infof("收到消息: %s", string(msg.Body))
//       msg.Ack(false) // 确认消息
//   }
//
// 参数:
//   - queueName: 队列名称
//   - consumerName: 消费者名称，用于标识消费者
//   - autoAck: 是否自动确认消息，true表示自动确认，false表示需要手动确认
//   - exclusive: 是否排他性，true表示只允许该消费者访问队列
//   - noLocal: 是否接收自己发布的消息，true表示不接收
//   - noWait: 是否等待服务器确认，true表示不等待服务器确认
//   - args: 额外参数
//
// 返回:
//   - <-chan amqp.Delivery: 消息通道
//   - error: 错误信息，如果启动消费者成功则为nil
func (r *RabbitMQ) ConsumeMessages(queueName, consumerName string, autoAck, exclusive, noLocal, noWait bool, args amqp.Table) (<-chan amqp.Delivery, error) {
	return r.channel.Consume(
		queueName,    // 队列名称
		consumerName, // 消费者名称
		autoAck,      // 自动确认
		exclusive,    // 排他性
		noLocal,      // 不接收自己发布的消息
		noWait,       // 不等待服务器确认
		args,         // 额外参数
	)
}

// Close 关闭RabbitMQ连接和通道
//
// 返回:
//   - error: 错误信息，如果关闭成功则为nil
func (r *RabbitMQ) Close() error {
	var channelErr error
	if r.channel != nil {
		channelErr = r.channel.Close()
		if channelErr != nil {
			logx.Errorf("关闭RabbitMQ通道失败: %v", channelErr)
		}
	}

	if r.conn != nil {
		connErr := r.conn.Close()
		if connErr != nil {
			return fmt.Errorf("关闭RabbitMQ连接失败: %w", connErr)
		}
	}

	if channelErr != nil {
		return fmt.Errorf("关闭RabbitMQ通道失败: %w", channelErr)
	}

	return nil
}

// EnablePublishConfirm 启用发布确认模式
//
// 返回:
//   - error: 错误信息，如果启用成功则为nil
func (r *RabbitMQ) EnablePublishConfirm() error {
	return r.channel.Confirm(false)
}

// PublishMessageWithConfirm 带确认的消息发布
//
// 参数:
//   - exchange: 交换机名称
//   - routingKey: 路由键
//   - body: 消息内容
//
// 返回:
//   - error: 错误信息，如果发布成功则为nil
func (r *RabbitMQ) PublishMessageWithConfirm(exchange, routingKey string, body []byte) error {
	// 启用发布确认
	if err := r.EnablePublishConfirm(); err != nil {
		return fmt.Errorf("启用发布确认失败: %w", err)
	}

	// 获取确认通道
	confirms := r.channel.NotifyPublish(make(chan amqp.Confirmation, 1))

	// 发布消息
	if err := r.PublishMessage(exchange, routingKey, body); err != nil {
		return err
	}

	// 等待确认
	if confirmed := <-confirms; !confirmed.Ack {
		return fmt.Errorf("消息发布未被确认")
	}

	return nil
}

// MonitorConnection 监控RabbitMQ连接状态并在断开时尝试重连
//
// 参数:
//   - reconnectFunc: 重连函数，当连接断开时调用
func (r *RabbitMQ) MonitorConnection(reconnectFunc func() error) {
	go func() {
		// 获取连接关闭通知
		notifyClose := r.conn.NotifyClose(make(chan *amqp.Error))

		// 监听连接关闭事件
		for err := range notifyClose {
			logx.Errorf("RabbitMQ连接已关闭: %v", err)

			// 尝试重连
			for {
				if err := reconnectFunc(); err == nil {
					logx.Info("RabbitMQ重连成功")
					break
				}

				logx.Errorf("RabbitMQ重连失败，5秒后重试")
				time.Sleep(5 * time.Second)
			}
		}
	}()
}