package wxpayclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"

	"xj-serv/pkg/wxPay/models"
	"xj-serv/pkg/wxPay/wxpay_utility"

	"github.com/zeromicro/go-zero/core/logx"
)

// Config 微信支付客户端配置
type Config struct {
	// BaseURL 微信支付API基础URL
	BaseURL string `json:"base_url"`

	// Timeout 请求超时时间(秒)
	Timeout int `json:"timeout"`
}

// WxPayClient 微信支付API客户端
type WxPayClient struct {
	config    Config
	mchConfig *wxpay_utility.MchConfig
	client    *http.Client
}

// NewWxPayClient 创建新的微信支付API客户端
func NewWxPayClient(mchConfig *wxpay_utility.MchConfig) *WxPayClient {
	config := Config{
		BaseURL: "https://api.mch.weixin.qq.com",
		Timeout: 60, // 设置为60秒
	}

	// 创建带有优化配置的HTTP客户端
	return &WxPayClient{
		config:    config,
		mchConfig: mchConfig,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   100,
				IdleConnTimeout:       90 * time.Second,
				TLSHandshakeTimeout:   10 * time.Second,
				DisableKeepAlives:     false,
				ResponseHeaderTimeout: 30 * time.Second,
				ExpectContinueTimeout: 10 * time.Second,
				DisableCompression:    true,
			},
		},
	}
}

// Post 发送POST请求到微信支付API
func (c *WxPayClient) Post(ctx context.Context, path string, reqData interface{}, respData interface{}) error {
	// 构建完整URL
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", c.config.BaseURL, path))
	if err != nil {
		return fmt.Errorf("构建请求URL失败: %w", err)
	}

	// 序列化请求数据
	var reqBody []byte
	if reqData != nil {
		reqBody, err = json.Marshal(reqData)
		if err != nil {
			return fmt.Errorf("序列化请求数据失败: %w", err)
		}
	}

	// 添加详细日志
	logx.WithContext(ctx).Infof("微信支付API请求路径: %s", path)
	if len(reqBody) > 0 {
		logx.WithContext(ctx).Infof("微信支付API请求体: %s", string(reqBody))
	}

	// 创建请求
	httpRequest, err := http.NewRequestWithContext(ctx, "POST", reqUrl.String(), bytes.NewReader(reqBody))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpRequest.Header.Set("Accept", "application/json")
	httpRequest.Header.Set("Content-Type", "application/json")
	httpRequest.Header.Set("Wechatpay-Serial", c.mchConfig.WechatPayPublicKeyId())

	// 构建签名
	authorization, err := wxpay_utility.BuildAuthorization(
		c.mchConfig.MchId(),
		c.mchConfig.CertificateSerialNo(),
		c.mchConfig.PrivateKey(),
		"POST",
		reqUrl.Path,
		reqBody,
	)
	if err != nil {
		return fmt.Errorf("构建签名失败: %w", err)
	}
	httpRequest.Header.Set("Authorization", authorization)

	// 发送请求
	start := time.Now()
	resp, err := c.client.Do(httpRequest)
	requestDuration := time.Since(start)
	logx.WithContext(ctx).Infof("微信支付API请求耗时: %v", requestDuration)

	if err != nil {
		logx.WithContext(ctx).Errorf("微信支付API请求失败: %v, 路径: %s, 耗时: %v", err, path, requestDuration)
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应
	logx.WithContext(ctx).Infof("微信支付API响应状态码: %d", resp.StatusCode)
	logx.WithContext(ctx).Infof("微信支付API响应体: %s, 请求接口: %s", string(body), path)

	// 检查HTTP状态码
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// 2XX 成功，验证应答签名
		err = wxpay_utility.ValidateResponse(
			c.mchConfig.WechatPayPublicKeyId(),
			c.mchConfig.WechatPayPublicKey(),
			&resp.Header,
			body,
		)
		if err != nil {
			return fmt.Errorf("验证响应签名失败: %w", err)
		}

		// 反序列化响应
		if err := json.Unmarshal(body, respData); err != nil {
			return fmt.Errorf("反序列化响应失败: %w, 响应体: %s", err, string(body))
		}

		return nil
	} else {
		// 处理业务错误
		var apiError models.ApiError
		if err := json.Unmarshal(body, &apiError); err != nil {
			return &wxpay_utility.ApiException{
				StatusCode: resp.StatusCode,
				Header:     resp.Header,
				Body:       body,
			}
		}
		return &apiError
	}
}

// Get 发送GET请求到微信支付API
func (c *WxPayClient) Get(ctx context.Context, path string, queryParams map[string]string, respData interface{}) error {
	// 构建完整URL
	reqUrl, err := url.Parse(fmt.Sprintf("%s%s", c.config.BaseURL, path))
	if err != nil {
		return fmt.Errorf("构建请求URL失败: %w", err)
	}

	// 添加查询参数
	if len(queryParams) > 0 {
		query := reqUrl.Query()
		for key, value := range queryParams {
			query.Set(key, value)
		}
		reqUrl.RawQuery = query.Encode()
	}

	// 添加详细日志
	logx.WithContext(ctx).Infof("微信支付API GET请求路径: %s", reqUrl.String())

	// 创建请求
	httpRequest, err := http.NewRequestWithContext(ctx, "GET", reqUrl.String(), nil)
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpRequest.Header.Set("Accept", "application/json")
	httpRequest.Header.Set("Wechatpay-Serial", c.mchConfig.WechatPayPublicKeyId())

	// 构建签名
	authorization, err := wxpay_utility.BuildAuthorization(
		c.mchConfig.MchId(),
		c.mchConfig.CertificateSerialNo(),
		c.mchConfig.PrivateKey(),
		"GET",
		reqUrl.Path+"?"+reqUrl.RawQuery,
		nil,
	)
	if err != nil {
		return fmt.Errorf("构建签名失败: %w", err)
	}
	httpRequest.Header.Set("Authorization", authorization)

	// 发送请求
	start := time.Now()
	resp, err := c.client.Do(httpRequest)
	requestDuration := time.Since(start)
	logx.WithContext(ctx).Infof("微信支付API GET请求耗时: %v", requestDuration)

	if err != nil {
		logx.WithContext(ctx).Errorf("微信支付API GET请求失败: %v, 路径: %s, 耗时: %v", err, reqUrl.String(), requestDuration)
		return fmt.Errorf("发送HTTP GET请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 记录响应
	logx.WithContext(ctx).Infof("微信支付API GET响应状态码: %d", resp.StatusCode)
	logx.WithContext(ctx).Infof("微信支付API GET响应体: %s, 请求接口: %s", string(body), reqUrl.String())

	// 检查HTTP状态码
	// 参考 https://pay.weixin.qq.com/doc/v3/merchant/4012081717
	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		// 2XX 成功，验证应答签名
		err = wxpay_utility.ValidateResponse(
			c.mchConfig.WechatPayPublicKeyId(),
			c.mchConfig.WechatPayPublicKey(),
			&resp.Header,
			body,
		)
		if err != nil {
			return fmt.Errorf("验证响应签名失败: %w", err)
		}

		// 反序列化响应
		if err := json.Unmarshal(body, respData); err != nil {
			return fmt.Errorf("反序列化响应失败: %w, 响应体: %s", err, string(body))
		}

		return nil
	} else {
		// 处理业务错误
		var apiError models.ApiError
		if err := json.Unmarshal(body, &apiError); err != nil {
			return &wxpay_utility.ApiException{
				StatusCode: resp.StatusCode,
				Header:     resp.Header,
				Body:       body,
			}
		}
		return &apiError
	}
}
