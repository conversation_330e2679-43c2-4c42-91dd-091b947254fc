package wxpayclient

import (
	"context"
	"fmt"

	"xj-serv/pkg/wxPay/models"
)

// 商家转账API路径常量
const (
	// TransferToUserPath 发起转账API路径
	TransferToUserPath = "/v3/fund-app/mch-transfer/transfer-bills"

	// CancelTransferPath 撤销转账API路径
	CancelTransferPath = "/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s/cancel"

	// QueryTransferByOutBillNoPath 通过商户单号查询转账单API路径
	QueryTransferByOutBillNoPath = "/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/%s"

	// QueryTransferByWxBillNoPath 通过微信转账单号查询转账单API路径
	QueryTransferByWxBillNoPath = "/v3/fund-app/mch-transfer/transfer-bills/transfer-bill-no/%s"
)

// TransferToUser 发起转账
//
// 接口说明：商户可通过该接口将资金转到用户微信零钱，此时需用户先确认收款
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 转账请求参数
//
// 返回值：
//   - *models.TransferToUserResponse: 转账响应结果
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 转账金额单位为分
//   - 商户单号必须在商户系统内唯一
//   - 当返回商户订单状态为ACCEPTED时，需要检查商户资金是否足够，并一定要使用原商户单号及原参数重试
//   - 当返回错误码为"SYSTEM_ERROR"时，请不要更换商户单号，一定要使用原商户单号及原参数重试
func (c *WxPayClient) TransferToUser(
	ctx context.Context,
	req *models.TransferToUserRequest,
) (*models.TransferToUserResponse, error) {
	resp := &models.TransferToUserResponse{}

	err := c.Post(ctx, TransferToUserPath, req, resp)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// CancelTransfer 撤销转账
//
// 接口说明：商户可通过该接口撤销转账，撤销转账接口返回成功仅表示撤销请求已受理，
// 系统会异步处理退款等操作，需以最终查询转账单返回的状态为准
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 撤销转账请求参数
//
// 返回值：
//   - *models.CancelTransferResponse: 撤销转账响应结果
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 仅在用户确认收款之前可以撤销
//   - 当前商户单号单据状态已到达终态时，不可再撤销
func (c *WxPayClient) CancelTransfer(
	ctx context.Context,
	req *models.CancelTransferRequest,
) (*models.CancelTransferResponse, error) {
	resp := &models.CancelTransferResponse{}

	path := fmt.Sprintf(CancelTransferPath, req.OutBillNo)
	err := c.Post(ctx, path, nil, resp)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// QueryTransferByOutBillNo 通过商户单号查询转账单
//
// 接口说明：商户可通过该接口查询转账单状态，完成对账等操作
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 查询请求参数
//
// 返回值：
//   - *models.QueryTransferResponse: 查询响应结果
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 只支持查询最近30天内的商家转账订单
//   - 超过30天需通过资金账单对账确认
func (c *WxPayClient) QueryTransferByOutBillNo(
	ctx context.Context,
	req *models.QueryTransferByOutBillNoRequest,
) (*models.QueryTransferResponse, error) {
	resp := &models.QueryTransferResponse{}

	path := fmt.Sprintf(QueryTransferByOutBillNoPath, req.OutBillNo)
	err := c.Get(ctx, path, nil, resp)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// QueryTransferByWxBillNo 通过微信转账单号查询转账单
//
// 接口说明：商户可通过该接口查询转账单状态，完成对账等操作
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 查询请求参数
//
// 返回值：
//   - *models.QueryTransferResponse: 查询响应结果
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 只支持查询最近30天内的商家转账订单
//   - 超过30天需通过资金账单对账确认
func (c *WxPayClient) QueryTransferByWxBillNo(
	ctx context.Context,
	req *models.QueryTransferByWxBillNoRequest,
) (*models.QueryTransferResponse, error) {
	resp := &models.QueryTransferResponse{}

	path := fmt.Sprintf(QueryTransferByWxBillNoPath, req.TransferBillNo)
	err := c.Get(ctx, path, nil, resp)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}
