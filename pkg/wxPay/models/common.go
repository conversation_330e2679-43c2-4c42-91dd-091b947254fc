package models

import (
	"fmt"
	"time"
)

// BaseResponse 微信支付API通用响应结构
type BaseResponse struct {
	// Code 响应状态码
	Code string `json:"code,omitempty"`

	// Message 响应状态描述
	Message string `json:"message,omitempty"`

	// Detail 响应详细信息
	Detail string `json:"detail,omitempty"`
}

// IsSuccess 检查API调用是否成功
func (r *BaseResponse) IsSuccess() bool {
	return r.Code == "" // 微信支付成功时不返回错误码
}

// Error 实现error接口，方便错误处理
func (r *BaseResponse) Error() string {
	if r.IsSuccess() {
		return ""
	}
	return fmt.Sprintf("微信支付错误 - Code: %s, Message: %s, Detail: %s",
		r.Code, r.Message, r.Detail)
}

// ApiError 微信支付API错误结构
type ApiError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Detail  string `json:"detail"`
}

// Error 实现error接口
func (e *ApiError) Error() string {
	return fmt.Sprintf("微信支付API错误 - Code: %s, Message: %s, Detail: %s",
		e.Code, e.Message, e.Detail)
}

// 常用指针函数，方便创建指针类型

// String 复制 string 对象，并返回复制体的指针
func String(s string) *string {
	return &s
}

// Bool 复制 bool 对象，并返回复制体的指针
func Bool(b bool) *bool {
	return &b
}

// Int64 复制 int64 对象，并返回复制体的指针
func Int64(i int64) *int64 {
	return &i
}

// Int32 复制 int32 对象，并返回复制体的指针
func Int32(i int32) *int32 {
	return &i
}

// Float64 复制 float64 对象，并返回复制体的指针
func Float64(f float64) *float64 {
	return &f
}

// Time 复制 time.Time 对象，并返回复制体的指针
func Time(t time.Time) *time.Time {
	return &t
}

// 微信支付常用常量

// TransferScene 转账场景
const (
	// TransferSceneCommissionReward 佣金奖励
	TransferSceneCommissionReward = "1005"
)
