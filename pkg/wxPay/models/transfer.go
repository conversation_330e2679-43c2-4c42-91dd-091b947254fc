package models

// TransferBillStatus 转账单状态
type TransferBillStatus string

// 转账单状态常量
const (
	// TransferBillStatusAccepted 受理成功
	TransferBillStatusAccepted TransferBillStatus = "ACCEPTED"

	// TransferBillStatusProcessing 处理中，如准备中或营销校验中
	TransferBillStatusProcessing TransferBillStatus = "PROCESSING"

	// TransferBillStatusWaitUserConfirm 等待用户确认收款
	TransferBillStatusWaitUserConfirm TransferBillStatus = "WAIT_USER_CONFIRM"

	// TransferBillStatusTransfering 转账中，已进入微信支付操作流程
	TransferBillStatusTransfering TransferBillStatus = "TRANSFERING"

	// TransferBillStatusSuccess 转账成功
	TransferBillStatusSuccess TransferBillStatus = "SUCCESS"

	// TransferBillStatusFail 转账失败
	TransferBillStatusFail TransferBillStatus = "FAIL"

	// TransferBillStatusCanceling 撤销中
	TransferBillStatusCanceling TransferBillStatus = "CANCELING"

	// TransferBillStatusCancelled 转账撤销完成
	TransferBillStatusCancelled TransferBillStatus = "CANCELLED"
)

// Ptr 返回状态的指针
func (s TransferBillStatus) Ptr() *TransferBillStatus {
	return &s
}

// TransferSceneReportInfo 转账场景报备信息
type TransferSceneReportInfo struct {
	// InfoType 报备信息类型
	InfoType *string `json:"info_type,omitempty"`

	// InfoContent 报备信息内容
	InfoContent *string `json:"info_content,omitempty"`
}

// TransferToUserRequest 发起转账请求
type TransferToUserRequest struct {
	// Appid 申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid）
	Appid *string `json:"appid,omitempty"`

	// OutBillNo 商户系统内部订单号，只能是数字、大小写字母_-*且在同一个商户号下唯一
	OutBillNo *string `json:"out_bill_no,omitempty"`

	// TransferSceneId 转账场景ID，1005:佣金奖励
	TransferSceneId *string `json:"transfer_scene_id,omitempty"`

	// Openid 用户openid
	Openid *string `json:"openid,omitempty"`

	// UserName 收款用户真实姓名，加密传输
	UserName *string `json:"user_name,omitempty"`

	// TransferAmount 转账金额，单位为分
	TransferAmount *int64 `json:"transfer_amount,omitempty"`

	// TransferRemark 转账备注
	TransferRemark *string `json:"transfer_remark,omitempty"`

	// NotifyUrl 转账结果回调通知地址
	NotifyUrl *string `json:"notify_url,omitempty"`

	// UserRecvPerception 用户收款感知文案
	UserRecvPerception *string `json:"user_recv_perception,omitempty"`

	// TransferSceneReportInfos 转账场景报备信息列表
	TransferSceneReportInfos []TransferSceneReportInfo `json:"transfer_scene_report_infos,omitempty"`
}

// TransferToUserResponse 发起转账响应
type TransferToUserResponse struct {
	BaseResponse

	// OutBillNo 商户单号
	OutBillNo *string `json:"out_bill_no,omitempty"`

	// TransferBillNo 微信转账单号
	TransferBillNo *string `json:"transfer_bill_no,omitempty"`

	// CreateTime 转账单据创建时间
	CreateTime *string `json:"create_time,omitempty"`

	// State 转账单状态
	State *TransferBillStatus `json:"state,omitempty"`

	// PackageInfo 用户确认收款页面跳转参数
	PackageInfo *string `json:"package_info,omitempty"`
}

// CancelTransferRequest 撤销转账请求
type CancelTransferRequest struct {
	// OutBillNo 商户转账单号
	OutBillNo string `json:"out_bill_no"`
}

// CancelTransferResponse 撤销转账响应
type CancelTransferResponse struct {
	BaseResponse

	// OutBillNo 商户转账单号
	OutBillNo *string `json:"out_bill_no,omitempty"`

	// TransferBillNo 微信转账单号
	TransferBillNo *string `json:"transfer_bill_no,omitempty"`

	// CreateTime 转账单据创建时间
	CreateTime *string `json:"create_time,omitempty"`

	// UpdateTime 转账单据更新时间
	UpdateTime *string `json:"update_time,omitempty"`

	// State 转账单状态
	State *TransferBillStatus `json:"state,omitempty"`
}

// QueryTransferByOutBillNoRequest 通过商户单号查询转账单请求
type QueryTransferByOutBillNoRequest struct {
	// OutBillNo 商户转账单号
	OutBillNo string `json:"out_bill_no"`
}

// QueryTransferByWxBillNoRequest 通过微信转账单号查询转账单请求
type QueryTransferByWxBillNoRequest struct {
	// TransferBillNo 微信转账单号
	TransferBillNo string `json:"transfer_bill_no"`
}

// QueryTransferResponse 查询转账单响应
type QueryTransferResponse struct {
	BaseResponse

	// Appid 申请商户号的appid或商户号绑定的appid
	Appid *string `json:"appid,omitempty"`

	// Mchid 商户号
	Mchid *string `json:"mchid,omitempty"`

	// OutBillNo 商户转账单号
	OutBillNo *string `json:"out_bill_no,omitempty"`

	// TransferBillNo 微信转账单号
	TransferBillNo *string `json:"transfer_bill_no,omitempty"`

	// Openid 转账目标用户openid
	Openid *string `json:"openid,omitempty"`

	// UserName 收款用户真实姓名
	UserName *string `json:"user_name,omitempty"`

	// TransferAmount 转账金额，单位为分
	TransferAmount *int64 `json:"transfer_amount,omitempty"`

	// TransferRemark 转账备注
	TransferRemark *string `json:"transfer_remark,omitempty"`

	// TransferTime 转账发起时间
	TransferTime *string `json:"transfer_time,omitempty"`

	// TransferSceneId 转账场景ID
	TransferSceneId *string `json:"transfer_scene_id,omitempty"`

	// CreateTime 转账单据创建时间
	CreateTime *string `json:"create_time,omitempty"`

	// UpdateTime 转账单据更新时间
	UpdateTime *string `json:"update_time,omitempty"`

	// State 转账单状态
	State *TransferBillStatus `json:"state,omitempty"`

	// FailReason 转账失败原因
	FailReason *string `json:"fail_reason,omitempty"`
}

// TransferNotifyRequest 转账回调通知请求
type TransferNotifyRequest struct {
	// Id 通知ID
	Id *string `json:"id,omitempty"`

	// CreateTime 通知创建时间
	CreateTime *string `json:"create_time,omitempty"`

	// EventType 通知类型
	EventType *string `json:"event_type,omitempty"`

	// ResourceType 通知资源类型
	ResourceType *string `json:"resource_type,omitempty"`

	// Resource 通知资源数据
	Resource *TransferNotifyResource `json:"resource,omitempty"`

	// Summary 回调摘要
	Summary *string `json:"summary,omitempty"`
}

// TransferNotifyResource 转账回调通知资源数据
type TransferNotifyResource struct {
	// OriginalType 原始类型
	OriginalType *string `json:"original_type,omitempty"`

	// Algorithm 加密算法类型
	Algorithm *string `json:"algorithm,omitempty"`

	// Ciphertext 加密数据
	Ciphertext *string `json:"ciphertext,omitempty"`

	// AssociatedData 附加数据
	AssociatedData *string `json:"associated_data,omitempty"`

	// Nonce 随机串
	Nonce *string `json:"nonce,omitempty"`
}

// TransferNotifyData 转账回调通知解密后的数据
type TransferNotifyData struct {
	// Mchid 商户号
	Mchid *string `json:"mchid,omitempty"`

	// OutBillNo 商户转账单号
	OutBillNo *string `json:"out_bill_no,omitempty"`

	// TransferBillNo 微信转账单号
	TransferBillNo *string `json:"transfer_bill_no,omitempty"`

	// State 转账状态
	State *TransferBillStatus `json:"state,omitempty"`

	// TransferAmount 转账金额，单位为分
	TransferAmount *int64 `json:"transfer_amount,omitempty"`

	// SuccessTime 转账成功时间
	SuccessTime *string `json:"success_time,omitempty"`

	// FailReason 转账失败原因
	FailReason *string `json:"fail_reason,omitempty"`

	// Openid 转账目标用户openid
	Openid *string `json:"openid,omitempty"`

	// UserName 收款用户真实姓名
	UserName *string `json:"user_name,omitempty"`
}
