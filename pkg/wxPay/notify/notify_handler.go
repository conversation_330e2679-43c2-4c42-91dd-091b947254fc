package notify

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"xj-serv/pkg/wxPay/wxpay_utility"
)

// NotifyConfig 回调通知配置
type NotifyConfig struct {
	WechatPayPublicKeyId   string // 微信支付公钥ID
	WechatPayPublicKeyPath string // 微信支付公钥文件路径
	ApiV3Key               string // APIv3密钥（用于解密）
}

// NotifyHandler 回调通知处理器
type NotifyHandler struct {
	config    *NotifyConfig
	publicKey *rsa.PublicKey
}

// NewNotifyHandler 创建回调通知处理器
func NewNotifyHandler(config *NotifyConfig) (*NotifyHandler, error) {
	if config == nil {
		return nil, fmt.Errorf("配置不能为空")
	}

	if config.WechatPayPublicKeyId == "" {
		return nil, fmt.Errorf("微信支付公钥ID不能为空")
	}

	if config.WechatPayPublicKeyPath == "" {
		return nil, fmt.Errorf("微信支付公钥文件路径不能为空")
	}

	if config.ApiV3Key == "" {
		return nil, fmt.Errorf("APIv3密钥不能为空")
	}

	// 加载微信支付公钥
	publicKey, err := wxpay_utility.LoadPublicKeyWithPath(config.WechatPayPublicKeyPath)
	if err != nil {
		return nil, fmt.Errorf("加载微信支付公钥失败: %v", err)
	}

	return &NotifyHandler{
		config:    config,
		publicKey: publicKey,
	}, nil
}

// ValidateSignature 验证回调请求签名
func (h *NotifyHandler) ValidateSignature(r *http.Request, body []byte) error {
	if h.publicKey == nil {
		return fmt.Errorf("微信支付公钥未加载")
	}

	// 验证签名
	headers := &r.Header
	err := wxpay_utility.ValidateResponse(
		h.config.WechatPayPublicKeyId,
		h.publicKey,
		headers,
		body,
	)
	if err != nil {
		return fmt.Errorf("签名验证失败: %v", err)
	}

	return nil
}

// NotifyResource 回调资源信息
type NotifyResource struct {
	OriginalType   string `json:"original_type"`
	Algorithm      string `json:"algorithm"`
	Ciphertext     string `json:"ciphertext"`
	AssociatedData string `json:"associated_data"`
	Nonce          string `json:"nonce"`
}

// TransferNotifyData 转账回调解密后的数据
type TransferNotifyData struct {
	OutBillNo      string `json:"out_bill_no"`      // 商户单号
	TransferBillNo string `json:"transfer_bill_no"` // 微信单号
	State          string `json:"state"`            // 单据状态
	MchId          string `json:"mch_id"`           // 商户号
	TransferAmount int64  `json:"transfer_amount"`  // 转账金额（分）
	Openid         string `json:"openid"`           // 收款用户OpenID
	FailReason     string `json:"fail_reason"`      // 失败原因
	CreateTime     string `json:"create_time"`      // 单据创建时间
	UpdateTime     string `json:"update_time"`      // 最后一次状态变更时间
}

// DecryptNotifyData 解密回调通知数据
func (h *NotifyHandler) DecryptNotifyData(resource *NotifyResource) (*TransferNotifyData, error) {
	if resource == nil {
		return nil, fmt.Errorf("资源数据不能为空")
	}

	// 验证加密算法
	if resource.Algorithm != "AEAD_AES_256_GCM" {
		return nil, fmt.Errorf("不支持的加密算法: %s", resource.Algorithm)
	}

	// Base64解码密文
	ciphertext, err := base64.StdEncoding.DecodeString(resource.Ciphertext)
	if err != nil {
		return nil, fmt.Errorf("解码密文失败: %v", err)
	}

	// AES-256-GCM解密
	plaintext, err := h.aesGcmDecrypt(h.config.ApiV3Key, resource.Nonce, resource.AssociatedData, ciphertext)
	if err != nil {
		return nil, fmt.Errorf("AES-GCM解密失败: %v", err)
	}

	// 解析JSON数据
	var data TransferNotifyData
	if err := json.Unmarshal(plaintext, &data); err != nil {
		return nil, fmt.Errorf("解析JSON数据失败: %v", err)
	}

	return &data, nil
}

// aesGcmDecrypt AES-256-GCM解密
func (h *NotifyHandler) aesGcmDecrypt(key, nonce, associatedData string, ciphertext []byte) ([]byte, error) {
	// 验证密钥长度（AES-256需要32字节）
	if len(key) != 32 {
		return nil, fmt.Errorf("API密钥长度必须为32字节")
	}

	// 创建AES cipher
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %v", err)
	}

	// 创建GCM
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %v", err)
	}

	// 验证nonce长度
	if len(nonce) != gcm.NonceSize() {
		return nil, fmt.Errorf("nonce长度不正确，期望: %d，实际: %d", gcm.NonceSize(), len(nonce))
	}

	// 执行解密
	plaintext, err := gcm.Open(nil, []byte(nonce), ciphertext, []byte(associatedData))
	if err != nil {
		return nil, fmt.Errorf("解密失败: %v", err)
	}

	return plaintext, nil
}

// IsTransferNotify 检查是否为转账通知
func IsTransferNotify(eventType string) bool {
	return eventType == "MCHTRANSFER.BILL.FINISHED"
}

// IsValidResourceType 检查资源类型是否有效
func IsValidResourceType(resourceType string) bool {
	return resourceType == "encrypt-resource"
}
