package xerr

// 成功返回
const OK uint32 = 200

/**(前3位代表业务,后三位代表具体功能)**/

// 全局错误码
const EtcdCallError uint32 = 9000
const RabbitMQError uint32 = 9001
const ServerCommonError uint32 = 100001
const RequestParamError uint32 = 100002
const TokenExpireError uint32 = 100003
const TokenGenerateError uint32 = 100004
const TokenExchangeError uint32 = 100005
const DbError uint32 = 100006
const DbUpdateAffectedZeroError uint32 = 100007
const AccessDefined uint32 = 100008
const ParamsValidateError uint32 = 100009
const ThirdPartPlatformError uint32 = 100010
const UserOperationForbidden uint32 = 100011

// sys 模块
const CaptchaError uint32 = 200007
const UserPasswordError uint32 = 200008
const RepeatNameError uint32 = 200009
const RepeatContentError uint32 = 200010
const RefreshTokenVerifyFail uint32 = 200011

// sys_base 模块
// const REPEAT_OSS_GET_BUCKET_ERROR = 300001
// const REPEAT_OSS_PUT_BUCKET_ERROR = 300002
// const EMAIL_CANNOT_BE_EMPTY_ERROR = 300003
// const ALREADY_EXISTS_ERROR = 300004

// 计费相关
const NotPaidError uint32 = 40001
const InsufficientPoints uint32 = 40002
const DeductionError uint32 = 40003

const UserServError uint32 = 40100

// ai相关
const AiServError uint32 = 41001
const AiGetAssetsError uint32 = 41002

// 应用相关
const ProjectRuntimeError uint32 = 42001

// RAG应用相关
const RagRuntimeError uint32 = 43001

// 店铺相关
const MiniShopRuntimeError uint32 = 44001

// 机构相关
const ECommerceRuntimeError uint32 = 45001

// 微信相关
const WxError uint32 = 46001
