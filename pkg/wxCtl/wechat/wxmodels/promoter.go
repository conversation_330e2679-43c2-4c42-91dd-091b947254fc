package wxmodels

type PromoterRegisterAndBindStatusRequest struct {
	// SharerOpenid 推客在小程序中的 openid，和 SharerAppid 二选一
	SharerOpenid string `json:"sharer_openid,omitempty"`

	// SharerAppid 推客在微信电商平台注册的身份标识，和 SharerOpenid 二选一
	SharerAppid string `json:"sharer_appid,omitempty"`

	// IsSimpleRegister 是否走简易版本注册
	// 当走简易版本注册时，可以不要求推客开通商户号，但分佣只能走机构自己分佣；
	// 如果不走简易注册流程时，要求推客开通商户号作为收款账户，可以平台分佣
	IsSimpleRegister bool `json:"is_simple_register"`
}

type PromoterRegisterAndBindStatusResponse struct {
	BaseResponse

	// BindStatus 和机构的绑定状态
	// 0：未绑定
	// 1：已绑定
	BindStatus int `json:"bind_status"`

	// RegisterStatus 当前推客的注册状态
	// 0：未注册
	// 1：注册中，还未完成
	// 2：已完成注册
	// 3：用户未支付实名，需要把微信先支付实名才能继续注册
	RegisterStatus int `json:"register_status"`

	// RegisterBusinessType 注册流程时需要的businessType
	// register_status等于 0 或者 1时，调用注册流程时，openBusinessView需要的businessType
	RegisterBusinessType string `json:"register_business_type"`

	// RegisterQueryString 注册时需要的queryString参数
	RegisterQueryString string `json:"register_query_string"`

	// BindBusinessType 绑定流程时需要的businessType
	// bind_status等于0时，调用绑定流程时，openBusinessView需要的businessType
	BindBusinessType string `json:"bind_business_type"`

	// BindQueryString 绑定时需要的queryString参数
	BindQueryString string `json:"bind_query_string"`

	// SharerAppid 如果推客和机构已经绑定，就返回对应推客的 appid
	SharerAppid string `json:"sharer_appid"`
}

// 推客注册状态常量
const (
	// RegisterStatusNotRegistered 未注册
	RegisterStatusNotRegistered = 0

	// RegisterStatusInProgress 注册中，还未完成
	RegisterStatusInProgress = 1

	// RegisterStatusCompleted 已完成注册
	RegisterStatusCompleted = 2

	// RegisterStatusNeedPayment 用户未支付实名，需要把微信先支付实名才能继续注册
	RegisterStatusNeedPayment = 3
)

// 绑定状态常量
const (
	// BindStatusUnbind 未绑定
	BindStatusUnbind = 0

	// BindStatusBound 已绑定
	BindStatusBound = 1
)

// 分佣类型常量
const (
	// CommissionTypePlatform 平台分佣
	CommissionTypePlatform = 0

	// CommissionTypeInstitution 机构自己分佣
	CommissionTypeInstitution = 1
)

type SetSharerCommissionInfoRequest struct {
	// SharerAppid 推客在微信电商平台注册的身份标识
	SharerAppid string `json:"sharer_appid"`

	// CommissionType 分佣类型【0：平台分佣，1:机构自己分佣】
	CommissionType int `json:"commission_type"`

	// CommissionRatio 平台分佣时的分佣比例，范围为【100000 - 900000】，代表【10%-90%】
	CommissionRatio int `json:"commission_ratio"`
}

// 获取达人平台直播列表请求
type GetLiveRecordListRequest struct {
	TalentAppid      string `json:"talent_appid"`           // 达人平台的appid
	MiniProgramAppid string `json:"mini_program_appid"`     // 需要挂载的小程序appid
	SharerAppid      string `json:"sharer_appid,omitempty"` // 推客appid (可选)
}

// 直播信息
type LiveRecordInfo struct {
	ExportId          string `json:"export_id"`           // 直播id
	Description       string `json:"description"`         // 直播描述
	PromoterShareLink string `json:"promoter_share_link"` // 内嵌直播卡片时需要的推广参数
}

// 获取达人平台直播列表响应
type GetLiveRecordListResponse struct {
	BaseResponse
	LiveRecordList []LiveRecordInfo `json:"live_record_list"` // 直播列表
}

// Range 范围定义
type Range struct {
	Min string `json:"min,omitempty"` // 区间最小值
	Max string `json:"max,omitempty"` // 区间最大值
}

// SpuItemCondition 商品查询条件
type SpuItemCondition struct {
	SellingPriceRange   *Range   `json:"selling_price_range,omitempty"`    // 售卖价区间，单位是分
	MonthlySalesRange   *Range   `json:"monthly_sales_range,omitempty"`    // 月销量区间
	Flags               []string `json:"flags,omitempty"`                  // 保障标
	ServiceFeeRateRange *Range   `json:"service_fee_rate_range,omitempty"` // 服务费率区间，单位是10万分之
	CommissionRateRange *Range   `json:"commission_rate_range,omitempty"`  // 佣金率区间，单位是10万分之
	PromoteTimeRange    *Range   `json:"promote_time_range,omitempty"`     // 推广时间范围
}

// Category 商品类目查询条件
type Category struct {
	CategoryId   string   `json:"category_id,omitempty"`    // 商品类目
	CategoryName string   `json:"category_name,omitempty"`  // 商品类目名称
	CategoryIds1 []string `json:"category_ids_1,omitempty"` // 一级类目列表
	CategoryIds2 []string `json:"category_ids_2,omitempty"` // 二级类目列表
	CategoryIds3 []string `json:"category_ids_3,omitempty"` // 三级类目列表
}

// ProductListItem 商品列表项信息
type ProductListItem struct {
	ProductId         string `json:"product_id"` // 商品id
	ShopAppid         string `json:"shop_appid"` // 商品所属店铺appid
	HeadSupplierAppid string `json:"head_supplier_appid"`
}

// GetPromoteProductListRequest 获取可推广的商品列表请求
type GetPromoteProductListRequest struct {
	PlanType         int               `json:"plan_type"`                    // 商品的计划类型 1：定向计划 2：公开计划
	SpuItemCondition *SpuItemCondition `json:"spu_item_condition,omitempty"` // 商品查询条件
	Category         *Category         `json:"category,omitempty"`           // 商品类目查询条件
	Keyword          string            `json:"keyword,omitempty"`            // 搜索关键词
	NextKey          string            `json:"next_key,omitempty"`           // 分页参数
	PageSize         int               `json:"page_size"`                    // 一页获取多少个商品，最大20
}

// GetPromoteProductListResponse 获取可推广的商品列表响应
type GetPromoteProductListResponse struct {
	BaseResponse
	ProductList []ProductListItem `json:"product_list"` // 商品列表
	NextKey     string            `json:"next_key"`     // 分页参数
}

// GetPromoteProductDetailRequest 获取推广商品详情请求
type GetPromoteProductDetailRequest struct {
	ShopAppid          string `json:"shop_appid"`                     // 团长商品所属小店appid
	ProductId          uint64 `json:"product_id"`                     // 商品id
	PlanType           int    `json:"plan_type"`                      // 商品的计划类型 1：定向计划 2：公开计划
	GetAvailableCoupon bool   `json:"get_available_coupon,omitempty"` // 返回商品可用的机构券
}

// 商品详情相关结构体
type SkuAttr struct {
	AttrKey   string `json:"attr_key"`   // 属性键
	AttrValue string `json:"attr_value"` // 属性值
}

type SkuDeliverInfo struct {
	StockType                      int   `json:"stock_type"`                         // sku库存情况
	FullPaymentPresaleDeliveryType int   `json:"full_payment_presale_delivery_type"` // sku发货节点
	PresaleBeginTime               int64 `json:"presale_begin_time"`                 // 预售开始时间
	PresaleEndTime                 int64 `json:"presale_end_time"`                   // 预售结束时间
	FullPaymentPresaleDeliveryTime int   `json:"full_payment_presale_delivery_time"` // 发货时效
}

type SkuInfo struct {
	SkuId       string          `json:"sku_id"`                     // skuID
	ThumbImg    string          `json:"thumb_img"`                  // sku小图
	SalePrice   int64           `json:"sale_price"`                 // 售卖价格，分
	StockNum    int64           `json:"stock_num"`                  // 库存
	SkuAttrs    []SkuAttr       `json:"sku_attrs"`                  // sku属性
	DeliverInfo *SkuDeliverInfo `json:"sku_deliver_info,omitempty"` // 预售信息
}

type BaseSkuInfo struct {
	SkuId       uint64          `json:"sku_id"`                     // skuID
	ThumbImg    string          `json:"thumb_img"`                  // sku小图
	SalePrice   int64           `json:"sale_price"`                 // 售卖价格，分
	StockNum    int64           `json:"stock_num"`                  // 库存
	SkuAttrs    []SkuAttr       `json:"sku_attrs"`                  // sku属性
	DeliverInfo *SkuDeliverInfo `json:"sku_deliver_info,omitempty"` // 预售信息
}

type CatInfo struct {
	CatId   string `json:"cat_id"`   // 类目id
	CatName string `json:"cat_name"` // 类目名称
}
type BaseCatInfo struct {
	CatId   uint64 `json:"cat_id"`   // 类目id
	CatName string `json:"cat_name"` // 类目名称
}
type DescInfo struct {
	Imgs []string `json:"imgs"` // 商品详情图片
	Desc string   `json:"desc"` // 商品详情文字
}

type ProductInfo struct {
	Title                string    `json:"title"`                  // 标题
	SubTitle             string    `json:"sub_title"`              // 副标题
	HeadImgs             []string  `json:"head_imgs"`              // 主图
	DescInfo             DescInfo  `json:"desc_info"`              // 商品详情
	Cats                 []CatInfo `json:"cats"`                   // 类目信息
	CatsV2               []CatInfo `json:"cats_v2"`                // 新类目树
	Skus                 []SkuInfo `json:"skus"`                   // sku信息
	ProductPromotionLink string    `json:"product_promotion_link"` // 商品卡片透传参数
	Status               int       `json:"status"`                 // 商品状态
}

type CommissionInfo struct {
	Status       int   `json:"status"`        // 商品带货状态
	ServiceRatio int   `json:"service_ratio"` // One second service fee rate
	StartTime    int64 `json:"start_time"`    // Cooperation start time
	EndTime      int64 `json:"end_time"`      // Cooperation end time
}

type Coupon struct {
	CouponId string `json:"coupon_id"` // 券的id
}

type ProductDetail struct {
	ShopAppid      string         `json:"shop_appid"`      // 所属小店appid
	ProductId      uint64         `json:"product_id"`      // 商品id
	ProductInfo    ProductInfo    `json:"product_info"`    // 商品信息
	CommissionInfo CommissionInfo `json:"commission_info"` // 跟佣信息
}

// GetPromoteProductDetailResponse 获取推广商品详情响应
type GetPromoteProductDetailResponse struct {
	BaseResponse
	Product            ProductDetail `json:"product"`             // 商品详情
	PublishCoupons     []Coupon      `json:"publish_coupons"`     // 公开机构券
	CooperativeCoupons []Coupon      `json:"cooperative_coupons"` // 定向机构券
}

// 短视频商品信息
type FeedProductInfo struct {
	ProductId        uint64 `json:"product_id"`
	ProductName      string `json:"product_name"`
	ProductImgUrl    string `json:"product_img_url"`
	ProductMiniPrice int64  `json:"product_mini_price"` // 预期是分
}

// 短视频信息
type FeedInfo struct {
	ExportId                string          `json:"export_id"`
	TalentAppid             string          `json:"talent_appid"`
	PredictCommissionAmount int64           `json:"predict_commission_amount"` // 预期是分
	ProductInfo             FeedProductInfo `json:"product_info"`
}

// 获取达人平台推广的短视频信息请求
type GetFeedListRequest struct {
	TalentAppid string `json:"talent_appid,omitempty"` // 合作的达人平台id (可选)
	NextKey     string `json:"next_key"`               // 分页参数，第一页为空
	PageSize    int    `json:"page_size"`              // 一页获取多少个数据，最大 10
}

// 获取达人平台推广的短视频信息响应
type GetFeedListResponse struct {
	BaseResponse
	NextKey  string     `json:"next_key"`  // 下一页的 key 内容
	HasMore  bool       `json:"has_more"`  // 是否还有下一页
	FeedList []FeedInfo `json:"feed_list"` // 可以推广的短视频列表
}

// GetBindSharerListReq 获取机构绑定的推客列表请求
type GetBindSharerListReq struct {
	NextKey      string `json:"next_key"`                // 分页参数，第一页为空
	PageSize     int    `json:"page_size"`               // 一页获取多少个推客，最大 20
	SharerOpenid string `json:"sharer_openid,omitempty"` // 可选，查询某个推客的小程序openid
	SharerAppid  string `json:"sharer_appid,omitempty"`  // 可选，查询某个推客的appid
}

// SharerInfo 推客信息
type SharerInfo struct {
	SharerAppid     string `json:"sharer_appid"`     // 推客的 appid
	BindTime        int64  `json:"bind_time"`        // 绑定时间戳
	CommissionRatio int    `json:"commission_ratio"` // 分佣比例
	CommissionType  int    `json:"commission_type"`  // 分佣类型【0：平台分佣 1：机构分佣】
}

// GetBindSharerListResp 获取机构绑定的推客列表响应
type GetBindSharerListResp struct {
	BaseResponse
	NextKey        string        `json:"next_key"`         // 下一页分页参数
	SharerInfoList []*SharerInfo `json:"sharer_info_list"` // 推客的绑定参数列表
}

// GetBindTalentListReq 获取机构绑定的达人列表请求
type GetBindTalentListReq struct {
	NextKey     string `json:"next_key"`               // 分页参数，第一页为空
	PageSize    int    `json:"page_size"`              // 一页获取多少个推客，最大 10
	TalentAppid string `json:"talent_appid,omitempty"` // 可选，查询某个合作的达人平台 appid
}

// TalentInfo 达人平台信息
type TalentInfo struct {
	TalentAppid    string `json:"talent_appid"`    // 达人平台的id
	BindTime       int64  `json:"bind_time"`       // 绑定时间
	TalentNickname string `json:"talent_nickname"` // 达人平台的昵称
	TalentHeadImg  string `json:"talent_head_img"` // 达人平台的头像
}

// GetBindTalentListResp 获取机构绑定的达人列表响应
type GetBindTalentListResp struct {
	BaseResponse
	NextKey    string        `json:"next_key"`    // 下一页的 key
	HasMore    bool          `json:"has_more"`    // 是否还有下一页
	TalentList []*TalentInfo `json:"talent_list"` // 合作的达人平台列表
}

// LiveNoticeRecordInfo 直播预约信息
type LiveNoticeRecordInfo struct {
	NoticeId    string `json:"notice_id"`   // 预约id
	Description string `json:"description"` // 预约描述
	StartTime   int64  `json:"start_time"`  // 预约对应的开播时间
}

// GetLiveNoticeRecordListRequest 获取达人平台直播预约列表请求
type GetLiveNoticeRecordListRequest struct {
	TalentAppid      string `json:"talent_appid"`       // 达人平台的appid
	MiniProgramAppid string `json:"mini_program_appid"` // 需要挂载的小程序appid
}

// GetLiveNoticeRecordListResponse 获取达人平台直播预约列表响应
type GetLiveNoticeRecordListResponse struct {
	BaseResponse
	LiveNoticeRecordList []LiveNoticeRecordInfo `json:"live_notice_record_list"` // 直播预约数据
}

// GetLiveRecordQrCodeRequest 获取直播推广二维码请求
type GetLiveRecordQrCodeRequest struct {
	TalentAppid string `json:"talent_appid"` // 达人平台的appid
	SharerAppid string `json:"sharer_appid"` // 推客的appid
	ExportId    string `json:"export_id"`    // 直播id
}

// GetLiveRecordQrCodeResponse 获取直播推广二维码响应
type GetLiveRecordQrCodeResponse struct {
	BaseResponse
	QrcodeUrl string `json:"qrcode_url"` // 二维码的url
}

// GetLiveNoticeRecordQrCodeRequest 获取直播预约推广二维码请求
type GetLiveNoticeRecordQrCodeRequest struct {
	TalentAppid string `json:"talent_appid"` // 达人平台的appid
	SharerAppid string `json:"sharer_appid"` // 推客的appid
	NoticeId    string `json:"notice_id"`    // 直播预约id
}

// GetLiveNoticeRecordQrCodeResponse 获取直播预约推广二维码响应
type GetLiveNoticeRecordQrCodeResponse struct {
	BaseResponse
	QrcodeUrl string `json:"qrcode_url"` // 二维码的url
}

// GetProductPromotionQrcodeInfoRequest 获取推客商品推广二维码请求
type GetProductPromotionQrcodeInfoRequest struct {
	SharerAppid       string `json:"sharer_appid,omitempty"`        // 推客appid，和SharerOpenid二选一
	SharerOpenid      string `json:"sharer_openid,omitempty"`       // 推客在小程序中的openid，和SharerAppid二选一
	ProductId         uint64 `json:"product_id,omitempty"`          // 商品id，如果使用该参数，需要传入ShopAppid
	ShopAppid         string `json:"shop_appid,omitempty"`          // 商品所属店铺appid
	ProductShortLink  string `json:"product_short_link,omitempty"`  // 商品短链，和ProductId二选一
	HeadSupplierAppid string `json:"head_supplier_appid,omitempty"` // 商品所属供货机构appid
}

// GetProductPromotionQrcodeInfoResponse 获取推客商品推广二维码响应
type GetProductPromotionQrcodeInfoResponse struct {
	BaseResponse
	QrcodeUrl string `json:"qrcode_url"` // 推广二维码url
}

// GetProductPromotionLinkInfoRequest 获取推客商品推广短链请求
type GetProductPromotionLinkInfoRequest struct {
	SharerAppid      string `json:"sharer_appid,omitempty"`       // 推客appid，和SharerOpenid二选一
	SharerOpenid     string `json:"sharer_openid,omitempty"`      // 推客在小程序中的openid，和SharerAppid二选一
	ProductId        uint64 `json:"product_id,omitempty"`         // 商品id，如果使用该参数，需要传入ShopAppid
	ShopAppid        string `json:"shop_appid,omitempty"`         // 商品所属店铺appid
	ProductShortLink string `json:"product_short_link,omitempty"` // 商品短链，和ProductId二选一
}

// GetProductPromotionLinkInfoResponse 获取推客商品推广短链响应
type GetProductPromotionLinkInfoResponse struct {
	BaseResponse
	ShortLink string `json:"short_link"` // 推广短链
}

// GetLiveCommissionProductListRequest 获取某个达人平台当前直播的带货商品列表请求
type GetLiveCommissionProductListRequest struct {
	TalentAppid string `json:"talent_appid"` // 达人平台的appid
	NextKey     string `json:"next_key"`     // 分页参数，第一页为空，后面返回前面一页返回的数据
	PageSize    int    `json:"page_size"`    // 一页获取多少个商品，最大10
}

// LiveCommissionProductInfo 直播带货商品信息
type LiveCommissionProductInfo struct {
	ProductId               int64  `json:"product_id"`                // 商品id
	ProductName             string `json:"product_name"`              // 商品名称
	ProductImgUrl           string `json:"product_img_url"`           // 商品的图片url
	ProductPrice            int64  `json:"product_price"`             // 商品售价【单位：分】
	PredictCommissionAmount int64  `json:"predict_commission_amount"` // 机构预估可得佣金金额【单位：分】
}

// GetLiveCommissionProductListResponse 获取某个达人平台当前直播的带货商品列表响应
type GetLiveCommissionProductListResponse struct {
	BaseResponse
	NextKey     string                      `json:"next_key"`     // 下一页的key
	HasMore     bool                        `json:"has_more"`     // 是否还有下一页
	ProductList []LiveCommissionProductInfo `json:"product_list"` // 商品列表
}

// FeedInfoReq 短视频推广请求的单条短视频信息
type FeedInfoReq struct {
	ExportId string `json:"export_id"` // 短视频id
}

// GetFeedPromotionInfoRequest 获取短视频推广信息请求
type GetFeedPromotionInfoRequest struct {
	TalentAppid      string        `json:"talent_appid"`           // 达人平台的appid
	MiniProgramAppid string        `json:"mini_program_appid"`     // 需要挂载的小程序appid【需要和机构绑定】
	FeedList         []FeedInfoReq `json:"feed_list"`              // 需要生成token的短视频列表信息
	SharerAppid      string        `json:"sharer_appid,omitempty"` // 推客appid【为了生成某个推客推广的link】
}

// FeedInfoResp 短视频推广响应的单条短视频信息
type FeedInfoResp struct {
	ExportId          string `json:"export_id"`           // 短视频id
	FeedToken         string `json:"feed_token"`          // 内嵌短视频的卡片信息
	PromoterShareLink string `json:"promoter_share_link"` // 推广推客信息
}

// GetFeedPromotionInfoResponse 获取短视频推广信息响应
type GetFeedPromotionInfoResponse struct {
	BaseResponse
	FeedList []FeedInfoResp `json:"feed_list"` // 短视频推广信息列表
}

// GetPromoterSingleProductPromotionInfoRequest 获取某个推客某个商品的内嵌商品卡片请求
type GetPromoterSingleProductPromotionInfoRequest struct {
	// SharerAppid 推客appid，和SharerOpenid二选一
	SharerAppid string `json:"sharer_appid,omitempty"`

	// SharerOpenid 推客在小程序中的openid，和SharerAppid二选一
	SharerOpenid string `json:"sharer_openid,omitempty"`

	// ProductId 商品id，如果使用该参数，需要传入ShopAppid
	ProductId uint64 `json:"product_id,omitempty"`

	// ShopAppid 商品所属店铺appid
	ShopAppid string `json:"shop_appid,omitempty"`

	// ProductShortLink 商品短链，和ProductId二选一
	ProductShortLink string `json:"product_short_link,omitempty"`

	// HeadSupplierAppid 商品所属供货机构appid
	HeadSupplierAppid string `json:"head_supplier_appid"`
}

// GetPromoterSingleProductPromotionInfoResponse 获取某个推客某个商品的内嵌商品卡片响应
type GetPromoterSingleProductPromotionInfoResponse struct {
	BaseResponse

	// ProductPromotionLink 内嵌商品卡片的推广参数
	ProductPromotionLink string `json:"product_promotion_link"`
}

// GetBindShopListRequest 获取合作的小店列表请求
type GetBindShopListRequest struct {
	NextKey  string `json:"next_key"`  // 分页参数，第一页为空，后面返回前面一页返回的数据
	PageSize int    `json:"page_size"` // 一页获取多少个合作小店，最大 10
}

// ShopInfo 小店信息
type ShopInfo struct {
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	BindTime     int64  `json:"bind_time"`     // 绑定时间
	ShopNickname string `json:"shop_nickname"` // 小店的名称
	ShopHeadImg  string `json:"shop_head_img"` // 小店的头像
}

// GetBindShopListResponse 获取合作的小店列表响应
type GetBindShopListResponse struct {
	BaseResponse
	NextKey  string     `json:"next_key"`  // 下一页的 key
	HasMore  bool       `json:"has_more"`  // 是否还有下一页
	ShopList []ShopInfo `json:"shop_list"` // 合作的小店列表
}

// GetBindShopPromoterListRequest 获取合作小店的关联账号列表请求
type GetBindShopPromoterListRequest struct {
	ShopAppid string `json:"shop_appid"` // 小店appid
	NextKey   string `json:"next_key"`   // 分页参数，第一页为空，后面返回前面一页返回的数据
	PageSize  int    `json:"page_size"`  // 一页获取多少个关联账号，最大 10
}

// PromoterInfo 关联账号信息
type PromoterInfo struct {
	PromoterId     string `json:"promoter_id"`      // 关联账号的id
	AvatarImageUrl string `json:"avatar_image_url"` // 关联账号头像
	PromoterType   int    `json:"promoter_type"`    // 关联账号类型【1：视频号；4：公众号；5：服务号】
	PromoterName   string `json:"promoter_name"`    // 关联账号名称
}

// 关联账号类型常量
const (
	// PromoterTypeVideo 视频号
	PromoterTypeVideo = 1

	// PromoterTypeWeChatAccount 公众号
	PromoterTypeWeChatAccount = 4

	// PromoterTypeServiceAccount 服务号
	PromoterTypeServiceAccount = 5
)

// GetBindShopPromoterListResponse 获取合作小店的关联账号列表响应
type GetBindShopPromoterListResponse struct {
	BaseResponse
	NextKey      string         `json:"next_key"`      // 下一页的 key
	HasMore      bool           `json:"has_more"`      // 是否还有下一页
	PromoterList []PromoterInfo `json:"promoter_list"` // 合作小店的关联账号列表
}

// GetShopLiveRecordListRequest 获取小店关联账号的直播列表请求
type GetShopLiveRecordListRequest struct {
	ShopAppid        string `json:"shop_appid"`             // 小店的 appid
	PromoterId       string `json:"promoter_id"`            // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType     int    `json:"promoter_type"`          // 关联账号类型【需要是视频号】
	MiniProgramAppid string `json:"mini_program_appid"`     // 需要挂载的小程序appid【要和机构绑定】
	SharerAppid      string `json:"sharer_appid,omitempty"` // 推客appid【为了生成某个推客推广的 link】
}

// GetShopLiveRecordListResponse 获取小店关联账号的直播列表响应
type GetShopLiveRecordListResponse struct {
	BaseResponse
	LiveRecordList []LiveRecordInfo `json:"live_record_list"` // 当前直播的直播数据
}

// GetShopLiveNoticeRecordListRequest 获取小店关联账号的预约直播列表请求
type GetShopLiveNoticeRecordListRequest struct {
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	PromoterId   string `json:"promoter_id"`   // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType int    `json:"promoter_type"` // 关联账号类型【需要是视频号】
}

// GetShopLiveNoticeRecordListResponse 获取小店关联账号的预约直播列表响应
type GetShopLiveNoticeRecordListResponse struct {
	BaseResponse
	LiveNoticeRecordList []LiveNoticeRecordInfo `json:"live_notice_record_list"` // 直播预约数据
}

// GetShopLiveCommissionProductListRequest 获取小店关联账号的当前直播的自营商品列表请求
type GetShopLiveCommissionProductListRequest struct {
	NextKey      string `json:"next_key"`      // 分页参数，第一页为空，后面返回前面一页返回的数据
	PageSize     int    `json:"page_size"`     // 一页获取多少个商品，最大 10
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	PromoterId   string `json:"promoter_id"`   // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType int    `json:"promoter_type"` // 关联账号类型【需要是视频号】
}

// ShopLiveProductInfo 小店直播商品信息
type ShopLiveProductInfo struct {
	ProductId               int64  `json:"product_id"`                // 商品 id
	ProductName             string `json:"product_name"`              // 商品名称
	ProductImgUrl           string `json:"product_img_url"`           // 商品的图片 url
	ProductPrice            int64  `json:"product_price"`             // 商品售价【单位：分】
	PredictCommissionAmount int64  `json:"predict_commission_amount"` // 机构预估可得佣金金额【单位：分】
	CommissionRate          int64  `json:"commission_rate"`           // 商品的佣金比例
}

// GetShopLiveCommissionProductListResponse 获取小店关联账号的当前直播的自营商品列表响应
type GetShopLiveCommissionProductListResponse struct {
	BaseResponse
	NextKey     string                `json:"next_key"`     // 下一页的 key
	HasMore     bool                  `json:"has_more"`     // 是否还有下一页
	ProductList []ShopLiveProductInfo `json:"product_list"` // 商品列表
}

// GetShopLiveRecordQrCodeRequest 为推客生成小店关联账号直播的推广二维码请求
type GetShopLiveRecordQrCodeRequest struct {
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	PromoterId   string `json:"promoter_id"`   // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType int    `json:"promoter_type"` // 关联账号类型【需要是视频号】
	ExportId     string `json:"export_id"`     // 直播 id
	SharerAppid  string `json:"sharer_appid"`  // 推客的 appid
}

// GetShopLiveRecordQrCodeResponse 为推客生成小店关联账号直播的推广二维码响应
type GetShopLiveRecordQrCodeResponse struct {
	BaseResponse
	QrcodeUrl string `json:"qrcode_url"` // 二维码的 url
}

// GetShopLiveNoticeRecordQrCodeRequest 为推客生成小店关联账号直播预约的推广二维码请求
type GetShopLiveNoticeRecordQrCodeRequest struct {
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	PromoterId   string `json:"promoter_id"`   // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType int    `json:"promoter_type"` // 关联账号类型【需要是视频号】
	NoticeId     string `json:"notice_id"`     // 直播预约 id
	SharerAppid  string `json:"sharer_appid"`  // 推客的 appid
}

// GetShopLiveNoticeRecordQrCodeResponse 为推客生成小店关联账号直播预约的推广二维码响应
type GetShopLiveNoticeRecordQrCodeResponse struct {
	BaseResponse
	QrcodeUrl string `json:"qrcode_url"` // 二维码的 url
}

// GetShopFeedListRequest 获取小店关联账号的短视频列表请求
type GetShopFeedListRequest struct {
	NextKey      string `json:"next_key"`      // 分页参数，第一页为空，后面返回前面一页返回的数据
	PageSize     int    `json:"page_size"`     // 一页获取多少个数据，最大 10
	ShopAppid    string `json:"shop_appid"`    // 小店的 appid
	PromoterId   string `json:"promoter_id"`   // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType int    `json:"promoter_type"` // 关联账号类型【需要是视频号】
}

// ShopFeedInfo 小店关联账号短视频信息
type ShopFeedInfo struct {
	ExportId                string              `json:"export_id"`                 // 短视频 id
	PredictCommissionAmount int64               `json:"predict_commission_amount"` // 预期机构结算金额【单位：分】
	ProductInfo             ShopFeedProductInfo `json:"product_info"`              // 短视频挂载的商品信息
}

// ShopFeedProductInfo 小店短视频商品信息
type ShopFeedProductInfo struct {
	ProductId        uint64 `json:"product_id"`         // 商品id
	ProductName      string `json:"product_name"`       // 商品名称
	ProductImgUrl    string `json:"product_img_url"`    // 商品头图
	ProductMiniPrice int64  `json:"product_mini_price"` // 商品价格【单位：分】
}

// GetShopFeedListResponse 获取小店关联账号的短视频列表响应
type GetShopFeedListResponse struct {
	BaseResponse
	NextKey  string         `json:"next_key"`  // 下一页的 key 内容
	HasMore  bool           `json:"has_more"`  // 是否还有下一页
	FeedList []ShopFeedInfo `json:"feed_list"` // 可以推广的短视频列表
}

// ShopFeedInfoReq 小店短视频推广请求的单条短视频信息
type ShopFeedInfoReq struct {
	ExportId string `json:"export_id"` // 短视频id
}

// GetShopFeedPromotionInfoRequest 生成小店关联账号的短视频内嵌小程序推广信息请求
type GetShopFeedPromotionInfoRequest struct {
	FeedList         []ShopFeedInfoReq `json:"feed_list"`              // 需要生成token的短视频列表信息
	ShopAppid        string            `json:"shop_appid"`             // 小店的 appid
	PromoterId       string            `json:"promoter_id"`            // 关联账号的id，可从获取合作小店的关联账号列表中获取
	PromoterType     int               `json:"promoter_type"`          // 关联账号类型【需要是视频号】
	MiniProgramAppid string            `json:"mini_program_appid"`     // 需要挂载的小程序appid【需要和机构绑定】
	SharerAppid      string            `json:"sharer_appid,omitempty"` // 推客appid【为了生成某个推客推广的link】
}

// ShopFeedInfoResp 小店短视频推广响应的单条短视频信息
type ShopFeedInfoResp struct {
	ExportId          string `json:"export_id"`           // 短视频 id
	FeedToken         string `json:"feed_token"`          // 内嵌短视频的卡片信息
	PromoterShareLink string `json:"promoter_share_link"` // 推广推客信息
}

// GetShopFeedPromotionInfoResponse 生成小店关联账号的短视频内嵌小程序推广信息响应
type GetShopFeedPromotionInfoResponse struct {
	BaseResponse
	FeedList []ShopFeedInfoResp `json:"feed_list"` // 推客的绑定参数列表
}
