package wxmodels

// 获取合作小店详情请求
type ShopGetRequest struct {
	Appid    string `json:"appid"`               // 小店appid
	PageSize int    `json:"page_size,omitempty"` // 获取小店数量（不超过30）
}

// 小店基础信息
type BizBaseInfo struct {
	Appid      string `json:"appid"`       // 小店appid
	HeadImgUrl string `json:"headimg_url"` // 小店头像
	Nickname   string `json:"nickname"`    // 小店昵称
}

// 小店数据信息
type ShopDataInfo struct {
	Gmv                    int64 `json:"gmv"`                       // 合作动销GMV，单位：分
	ProductNumber          int   `json:"product_number"`            // 历史合作商品数
	SettleAmount           int64 `json:"settle_amount"`             // 已结算服务费，单位：分
	UnsettleAmount         int64 `json:"unsettle_amount"`           // 预计待结算服务费，单位：分
	ProductNumberToday     int   `json:"product_number_today"`      // 今日新增合作商品数
	ProductNumberSoldToday int   `json:"product_number_sold_today"` // 今日动销商品数
}

// 小店详情
type ShopDetail struct {
	BaseInfo     BizBaseInfo  `json:"base_info"`     // 小店基础信息
	DataInfo     ShopDataInfo `json:"data_info"`     // 小店数据信息
	Status       int          `json:"status"`        // 合作状态 1:邀请中 2:已接受邀请 3:已拒绝邀请 4:取消邀请 5:取消合作
	ApprovedTime int64        `json:"approved_time"` // 开始合作时间戳
}

// 获取合作小店详情响应
type ShopGetResponse struct {
	BaseResponse
	ShopDetail ShopDetail `json:"shop_detail"` // 小店详情
	NextKey    string     `json:"next_key"`    // 本次翻页的上下文
	HasMore    bool       `json:"has_more"`    // 是否还有剩余小店
}

// 获取合作小店列表请求
type ShopListGetRequest struct {
	PageSize int    `json:"page_size"`          // 获取小店数量（不超过30）
	NextKey  string `json:"next_key,omitempty"` // 由上次请求返回，顺序翻页时需要传入
}

// 合作小店列表项详情 (包含基本信息和状态)
type ShopListItemDetail struct {
	BaseInfo BizBaseInfo `json:"base_info"` // 小店基础信息
	Status   int         `json:"status"`    // 合作状态 1:邀请中 2:已接受邀请 3:已拒绝邀请 4:取消邀请 5:取消合作
}

// 获取合作小店列表响应
type ShopListGetResponse struct {
	BaseResponse
	ShopList []ShopListItemDetail `json:"shop_list"` // 小店详情列表
	NextKey  string               `json:"next_key"`  // 本次翻页的上下文
	HasMore  bool                 `json:"has_more"`  // 是否还有剩余小店
}

// 获取商品基础详情请求
type ProductDetailGetRequest struct {
	ShopAppid string `json:"shop_appid"` // 团长商品所属小店appid
	ProductId int64  `json:"product_id"` // 商品id
	PlanType  int    `json:"plan_type"`  // 可选参数，计划类型
}

// 店铺信息
type HeadSupplierShop struct {
	Name  string `json:"name,omitempty"`  // 店铺名称
	Score int    `json:"score,omitempty"` // 店铺评分，如463
	Icon  string `json:"icon,omitempty"`  // 店铺图标
}

// 快递信息-地址信息
type HeadSupplierAddressInfo struct {
	PostalCode   string `json:"postal_code,omitempty"`   // 邮政编码
	ProvinceName string `json:"province_name,omitempty"` // 省份名称
	CityName     string `json:"city_name,omitempty"`     // 城市名称
	CountyName   string `json:"county_name,omitempty"`   // 区县名称
}

// 快递信息
type HeadSupplierExpressInfo struct {
	SendTime       string                   `json:"send_time,omitempty"`       // 发货时间
	AddressInfo    *HeadSupplierAddressInfo `json:"address_info,omitempty"`    // 地址信息
	ShippingMethod string                   `json:"shipping_method,omitempty"` // 配送方式，如"NO_FREE"
}

// 额外服务信息
type HeadSupplierExtraService struct {
	SevenDayReturn   int `json:"seven_day_return,omitempty"`   // 7天无理由退换
	PayAfterUse      int `json:"pay_after_use,omitempty"`      // 先用后付
	FreightInsurance int `json:"freight_insurance,omitempty"`  // 运费险
	FakeOnePayThree  int `json:"fake_one_pay_three,omitempty"` // 假一赔三
	DamageGuarantee  int `json:"damage_guarantee,omitempty"`   // 破损包赔
}

// 统计数据信息
type HeadSupplierStatisticalData struct {
	GoodRatio     int `json:"good_ratio,omitempty"`       // 好评率
	SalesIn30Days int `json:"sales_in_30_days,omitempty"` // 近30天销量
}

// 商品基础详情的商品信息
type HeadSupplierProductInfo struct {
	Title             string                       `json:"title"`                         // 标题
	SubTitle          string                       `json:"sub_title,omitempty"`           // 副标题
	HeadImgs          []string                     `json:"head_imgs,omitempty"`           // 主图,多张,列表,最多9张
	DescInfo          *DescInfo                    `json:"desc_info,omitempty"`           // 商详信息
	CatsV2            []BaseCatInfo                `json:"cats_v2,omitempty"`             // 新类目树-类目信息
	ExpressInfo       *HeadSupplierExpressInfo     `json:"express_info,omitempty"`        // 快递信息
	ExtraService      *HeadSupplierExtraService    `json:"extra_service,omitempty"`       // 额外服务
	StatisticalData   *HeadSupplierStatisticalData `json:"statistical_data,omitempty"`    // 统计数据
	Skus              []BaseSkuInfo                `json:"skus,omitempty"`                // sku信息
	Status            int                          `json:"status,omitempty"`              // 商品状态：5-上架销售中，其他值为非正常状态
	MonthlySalesRange int                          `json:"monthly_sales_range,omitempty"` // 近30天销量范围
}

// 商品基础详情
type HeadSupplierProductDetail struct {
	ShopAppid   string                  `json:"shop_appid"`   // 所属小店appid
	ProductId   int64                   `json:"product_id"`   // 商品id
	ProductInfo HeadSupplierProductInfo `json:"product_info"` // 商品信息
	Shop        HeadSupplierShop        `json:"shop"`         // 店铺信息
}

// 获取商品基础详情响应
type ProductDetailGetResponse struct {
	BaseResponse
	Item HeadSupplierProductDetail `json:"item"` // 商品详情
}

// 获取佣金单详情请求
type GetCommissionOrderRequest struct {
	OrderId string `json:"order_id"` // 订单号，可从获取佣金单列表接口获得
	SkuId   string `json:"sku_id"`   // 商品skuid，可从获取佣金单列表接口获得
}

// 佣金单信息 - 小店商家信息
type CommissionOrderBizInfo struct {
	Appid string `json:"appid"` // 所属小店appid
}

// 佣金单信息 - 商品信息
type CommissionOrderProductInfo struct {
	ProductId     string `json:"product_id"`     // 商品id
	ThumbImg      string `json:"thumb_img"`      // sku小图
	ActualPayment int64  `json:"actual_payment"` // 实际支付金额
	Title         string `json:"title"`          // 商品标题
}

// 佣金单信息 - 订单信息
type CommissionOrderInfo struct {
	Status int `json:"order_status"` // 订单状态，枚举值参考OrderStatus
}

// 佣金单信息 - 分佣信息
type CommissionOrderCommissionInfo struct {
	ServiceRatio          int64                `json:"service_ratio"`                      // 服务费率[0, 1000000]
	ServiceAmount         int64                `json:"service_amount"`                     // 服务费金额
	ProfitShardingSucTime int64                `json:"profit_sharding_suc_time,omitempty"` // 服务费结算时间
	PromotionChannel      int64                `json:"promotion_channel,omitempty"`        // 0-橱窗带货，1-推客带货
	FinderInfo            CommissionFinderInfo `json:"finder_info"`                        // 带货达人信息
	SharerInfo            CommissionSharerInfo `json:"sharer_info,omitempty"`              // 推客分佣信息
	TalentInfo            CommissionTalentInfo `json:"talent_info,omitempty"`              // 带货达人信息
	AgencyInfo            CommissionAgencyInfo `json:"promoter_agency_info,omitempty"`     // 机构分佣信息
}

// 佣金单信息 - 推客分佣信息
type CommissionSharerInfo struct {
	SharerAppid  string `json:"sharer_appid"` // 推客openid
	Nickname     string `json:"nickname"`     // 推客昵称
	Ratio        int64  `json:"ratio"`        // 佣金率[0, 1000000]
	Amount       int64  `json:"amount"`       // 佣金
	Opensharerid string `json:"opensharerid"` // 新推客uin跟推客机构的bizuin构造的openid
}

// 佣金单信息 - 带货达人信息
type CommissionTalentInfo struct {
	TalentAppid  string `json:"talent_appid"` // 达人openid
	Nickname     string `json:"nickname"`     // 达人昵称
	Ratio        int64  `json:"ratio"`        // 佣金率[0, 1000000]
	Amount       int64  `json:"amount"`       // 佣金
	Opentalentid string `json:"opentalentid"` // 平台达人openid
}

// 佣金单信息 - 带货达人信息
type CommissionFinderInfo struct {
	Nickname     string `json:"nickname"`     // 达人昵称
	Ratio        int64  `json:"ratio"`        // 佣金率[0, 1000000]
	Amount       int64  `json:"amount"`       // 佣金
	OpenFinderId string `json:"openfinderid"` // 视频号openfinderid
}

// 佣金单信息 - 机构分佣信息
type CommissionAgencyInfo struct {
	Appid                 string `json:"appid"`                    // 机构appid
	Nickname              string `json:"nickname"`                 // 机构昵称
	Ratio                 int64  `json:"ratio"`                    // 佣金率[0, 1000000]
	Amount                int64  `json:"amount"`                   // 佣金
	ProfitShardingSucTime int64  `json:"profit_sharding_suc_time"` // 佣金结算时间
}

// 佣金单信息 - 订单详情
type CommissionOrderDetail struct {
	ShopInfo       CommissionOrderBizInfo        `json:"shop_info"`       // 小店商家信息
	ProductInfo    CommissionOrderProductInfo    `json:"product_info"`    // 佣金单商品信息
	OrderInfo      CommissionOrderInfo           `json:"order_info"`      // 订单信息
	CommissionInfo CommissionOrderCommissionInfo `json:"commission_info"` // 分佣信息
	BuyerInfo      CommissionOrderBuyerInfo      `json:"buyer_info"`      // 买家信息
}

// 佣金单信息 - 买家信息
type CommissionOrderBuyerInfo struct {
	Openid  string `json:"open_id"`  // 购买者的openid
	Unionid string `json:"union_id"` // 购买者的unionid
}

// 佣金单结构
type CommissionOrder struct {
	OrderId     string                `json:"order_id"`     // 订单号
	SkuId       string                `json:"sku_id"`       // 商品skuid
	CreateTime  int64                 `json:"create_time"`  // 秒级时间戳
	UpdateTime  int64                 `json:"update_time"`  // 秒级时间戳
	Status      int                   `json:"status"`       // 佣金单状态，枚举值详情请参考CommissionOrderStatus
	OrderDetail CommissionOrderDetail `json:"order_detail"` // 订单详情
}

// 获取佣金单详情响应
type GetCommissionOrderResponse struct {
	// BaseResponse
	ErrCode         int             `json:"errcode"`
	ErrMsg          string          `json:"errmsg"`
	CommissionOrder CommissionOrder `json:"commssion_order"` // 佣金单结构
}

// OrderStatus 订单状态
const (
	OrderStatusWaitPay            = 10  // 待付款
	OrderStatusWaitDeliver        = 20  // 待发货
	OrderStatusPartDeliver        = 21  // 部分发货
	OrderStatusWaitReceive        = 30  // 待收货
	OrderStatusComplete           = 100 // 完成
	OrderStatusAfterSaleCancelled = 200 // 全部商品售后之后，订单取消
	OrderStatusUserCancelled      = 250 // 未付款用户主动取消或超时未付款订单自动取消
)

// CommissionOrderStatus 佣金单状态
const (
	CommissionOrderStatusUnSettled = 20  // 未结算
	CommissionOrderStatusSettled   = 100 // 已结算
	CommissionOrderStatusCancelled = 200 // 取消结算
)

// 获取达人橱窗授权链接请求
type GetWindowAuthRequest struct {
	FinderId    string `json:"finder_id,omitempty"`    // 视频号finder_id【三选一】
	Openid      string `json:"openid,omitempty"`       // 小程序用户openid，需在团长端绑定小程序【三选一】
	TalentAppid string `json:"talent_appid,omitempty"` // 带货达人appid，【三选一】
}

// 授权信息
type AuthInfo struct {
	AuthUrl         string `json:"auth_url"`          // 授权链接
	AuthWxaPath     string `json:"auth_wxa_path"`     // 授权路径
	AuthWxaAppid    string `json:"auth_wxa_appid"`    // appid
	AuthWxaUsername string `json:"auth_wxa_username"` // 小程序name
}

// 获取达人橱窗授权链接响应
type GetWindowAuthResponse struct {
	BaseResponse
	AuthInfo     AuthInfo `json:"auth_info"`    // 授权链接信息
	OpenFinderId string   `json:"openfinderid"` // 视频号openfinderid
	OpenTalentId string   `json:"opentalentid"` // 达人绑定的带货助手平台的opentalentid
}

// 获取达人橱窗授权状态请求
type GetWindowAuthStatusRequest struct {
	FinderId     string `json:"finder_id,omitempty"`    // 视频号finder_id，登录视频号助手可以获取，未填openfinderid时必填，后续废弃
	OpenFinderId string `json:"openfinderid,omitempty"` // 视频号openfinderid，【四选一】
	TalentAppid  string `json:"talent_appid,omitempty"` // 带货达人appid，【四选一】
	OpenTalentId string `json:"opentalentid,omitempty"` // 带货达人opentalentid，【四选一】
}

// 获取达人橱窗授权状态响应
type GetWindowAuthStatusResponse struct {
	BaseResponse
	WindowAuthStatus int    `json:"window_auth_status"` // 是否授权，0: 未授权, 1: 已授权
	OpenFinderId     string `json:"openfinderid"`       // 对应的openfinderid
	OpenTalentId     string `json:"opentalentid"`       // 对应的opentalentid
}

// 添加团长商品到橱窗请求
type AddProductToWindowRequest struct {
	Appid           string `json:"appid,omitempty"`              // 团长appid
	FinderId        string `json:"finder_id,omitempty"`          // 视频号finder_id，未填openfinderid时必填，后续废弃
	OpenFinderId    string `json:"openfinderid,omitempty"`       // 视频号openfinderid，操作达人视频号橱窗时必填
	ProductId       uint64 `json:"product_id"`                   // 团长商品ID
	IsHideForWindow bool   `json:"is_hide_for_window,omitempty"` // 是否需要在个人橱窗页隐藏（默认为false）
	ProductLink     string `json:"product_link,omitempty"`       // 联盟商品链接，团长类商品必填
	PlanType        uint32 `json:"plan_type,omitempty"`          // 计划类型，1-普通计划
	OpenTalentId    string `json:"opentalentid,omitempty"`       // 带货达人opentalentid，操作达人带货助手橱窗时必填
}

// 获取合作商品列表请求
type GetCooperativeItemListRequest struct {
	CommissionType int    `json:"commission_type"` // 佣金分配类型 0：商家指定达人佣金, 1：机构指定达人佣金
	PageSize       int    `json:"page_size"`       // 一页获取多少个商品，最大 20
	NextKey        string `json:"next_key"`        // 分页参数，第一页为空，后面返回前面一页返回的数据
}

// 商品信息
type ProductInfoItem struct {
	ProductId            uint64 `json:"product_id"`              // 商品id
	ID                   int    `json:"id"`                      // 合作计划id
	HeadSupplierItemLink string `json:"head_supplier_item_link"` // 机构推广商品链接
}

// 获取合作商品列表响应
type GetCooperativeItemListResponse struct {
	BaseResponse
	List    []ProductInfoItem `json:"list"`     // 商品列表
	NextKey string            `json:"next_key"` // 分页参数
}

// 范围信息
type RangeInfoRequest struct {
	Min string `json:"min,omitempty"` // 区间最小值
	Max string `json:"max,omitempty"` // 区间最大值
}

// 商品查询条件
type SpuItemConditionRequest struct {
	SellingPriceRange   *RangeInfoRequest `json:"selling_price_range,omitempty"`    // 售卖价区间，单位是分
	MonthlySalesRange   *RangeInfoRequest `json:"monthly_sales_range,omitempty"`    // 月销量区间
	Flags               []string          `json:"flags,omitempty"`                  // 保障标
	ServiceFeeRateRange *RangeInfoRequest `json:"service_fee_rate_range,omitempty"` // 服务费率区间，单位是10万分之
	CommissionRateRange *RangeInfoRequest `json:"commission_rate_range,omitempty"`  // 佣金率区间，单位是10万分之
	PromoteTimeRange    *RangeInfoRequest `json:"promote_time_range,omitempty"`     // 推广时间范围
}

// 商品类目查询条件
type CategoryInfoRequest struct {
	CategoryId   uint64   `json:"category_id,omitempty"`    // 商品类目
	CategoryName string   `json:"category_name,omitempty"`  // 商品类目名称
	CategoryIds1 []uint64 `json:"category_ids_1,omitempty"` // 一级类目列表
	CategoryIds2 []uint64 `json:"category_ids_2,omitempty"` // 二级类目列表
	CategoryIds3 []uint64 `json:"category_ids_3,omitempty"` // 三级类目列表
}

// 获取机构选品广场商品列表请求
type GetSelectionProductsListRequest struct {
	NextKey          string                   `json:"next_key"`                     // 分页参数，第一页为空
	PageSize         int                      `json:"page_size"`                    // 一页获取多少个商品，最大 20
	PlanType         int                      `json:"plan_type,omitempty"`          // 计划类型，2-定向计划（默认），1-公开计划
	SpuSource        int                      `json:"spu_source,omitempty"`         // 供货类型；1-商家供货；2-机构供货，默认值1
	Keyword          string                   `json:"keyword,omitempty"`            // 关键词，用于商品名称的模糊匹配
	Category         *CategoryInfoRequest     `json:"category,omitempty"`           // 类目信息
	SpuItemCondition *SpuItemConditionRequest `json:"spu_item_condition,omitempty"` // 商品筛选条件
}

// 选品广场商品信息
type SelectionProductInfoItem struct {
	ProductId            string `json:"product_id"`              // 商品id
	ShopAppid            string `json:"shop_appid"`              // 商品所属店铺appid
	HeadSupplierItemLink string `json:"head_supplier_item_link"` // 团长商品链接
}

// 获取机构选品广场商品列表响应
type GetSelectionProductsListResponse struct {
	BaseResponse
	ProductList []SelectionProductInfoItem `json:"product_list"` // 商品列表
	NextKey     string                     `json:"next_key"`     // 分页参数
}

// ItemKey 商品概要
type ItemKey struct {
	Appid              string `json:"appid"`                // 团长appid
	ProductId          string `json:"product_id"`           // 团长商品ID
	ProductWindowId    uint64 `json:"product_window_id"`    // 商品在橱窗的ID，用来移除橱窗操作
	IsOperationAllowed bool   `json:"is_operation_allowed"` // 是否有操作权限，true-可操作；false-不可操作
}

// GetWindowAllRequest 获取橱窗上团长商品列表请求
type GetWindowAllRequest struct {
	Appid        string `json:"appid,omitempty"`          // 团长appid
	FinderId     string `json:"finder_id,omitempty"`      // 视频号finder_id,未填openfinderid时必填，后续废弃
	OpenFinderId string `json:"openfinderid,omitempty"`   // 视频号openfinderid，操作达人视频号橱窗时必填
	Offset       uint32 `json:"offset"`                   // 起始位置（从0开始）
	PageSize     uint32 `json:"page_size,omitempty"`      // 每页数量(默认100, 最大500)
	NeedTotalNum bool   `json:"need_total_num,omitempty"` // 是否需要返回橱窗上团长商品总数，默认为false
	IsGetAll     bool   `json:"is_get_all,omitempty"`     // 是否需要查询达人所有橱窗商品，默认为false
	OpenTalentId string `json:"opentalentid,omitempty"`   // 带货达人opentalentid，操作达人带货助手橱窗时必填
}

// GetWindowAllResponse 获取橱窗上团长商品列表响应
type GetWindowAllResponse struct {
	BaseResponse
	List       []ItemKey `json:"list"`        // 商品概要列表
	NextOffset uint32    `json:"next_offset"` // 下一页的位置
	HaveMore   bool      `json:"have_more"`   // 后面是否还有商品
	TotalNum   uint32    `json:"total_num"`   // 商品总数
}

// DescInfo 商品详情信息
type WindowDescInfo struct {
	Imgs []string `json:"imgs,omitempty"` // 商品详情图片(最多20张)
	Desc string   `json:"desc,omitempty"` // 商品详情文字
}

// CatInfo 类目信息
type WindowCatInfo struct {
	CatId string `json:"cat_id"` // 类目id
}

// ProductInfo 商品内容
type WindowProductInfo struct {
	Title                string          `json:"title"`                            // 标题
	SubTitle             string          `json:"sub_title,omitempty"`              // 副标题
	HeadImgs             []string        `json:"head_imgs,omitempty"`              // 主图,多张,列表,最多9张
	DescInfo             *WindowDescInfo `json:"desc_info,omitempty"`              // 商品详情信息
	Cats                 []WindowCatInfo `json:"cats,omitempty"`                   // 类目信息
	CatsV2               []WindowCatInfo `json:"cats_v2,omitempty"`                // 新类目树-类目信息
	ProductPromotionLink string          `json:"product_promotion_link,omitempty"` // 用于在小程序跳转小店场景添加商品时传递跟佣信息
}

// Item 商品所有信息
type Item struct {
	Appid       string            `json:"appid"`        // 所属小店appid
	ProductId   string            `json:"product_id"`   // 商品id
	ProductInfo WindowProductInfo `json:"product_info"` // 商品内容
}

// GetWindowDetailRequest 获取橱窗上团长商品详情请求
type GetWindowDetailRequest struct {
	Appid        string `json:"appid,omitempty"`        // 团长appid
	FinderId     string `json:"finder_id,omitempty"`    // 视频号finder_id，未填openfinderid时必填，后续废弃
	OpenFinderId string `json:"openfinderid,omitempty"` // 视频号openfinderid，操作达人视频号橱窗时必填
	ProductId    uint64 `json:"product_id"`             // 团长商品ID
	OpenTalentId string `json:"opentalentid,omitempty"` // 带货达人opentalentid，操作达人带货助手橱窗时必填
}

// GetWindowDetailResponse 获取橱窗上团长商品详情响应
type GetWindowDetailResponse struct {
	BaseResponse
	ProductDetail Item `json:"product_detail"` // 商品所有信息
}

// GetPromotionDetailRequest 获取商品推广参数详情请求
type GetPromotionDetailRequest struct {
	HeadSupplierItemLink string `json:"head_supplier_item_link"` // 机构推广商品链接
}

// NormalCommissionInfo 普通推广下的佣金信息
type NormalCommissionInfo struct {
	Ratio int64 `json:"ratio"` // 佣金费率[0, 1000000]
}

// PromotionCommissionInfo 商品佣金信息
type PromotionCommissionInfo struct {
	PlanType             int                   `json:"plan_type"`                        // 商品的计划类型 1：定向计划 2：公开计划
	CommissionType       int                   `json:"commission_type"`                  // 佣金分配类型 0：商家指定达人佣金 1：机构指定达人佣金
	Ratio                int64                 `json:"ratio"`                            // 佣金费率[0, 1000000]
	ServiceRatio         int64                 `json:"service_ratio"`                    // 服务费率[0, 1000000]
	StartTime            int64                 `json:"start_time"`                       // 计划开始时间
	EndTime              int64                 `json:"end_time"`                         // 计划结束时间
	NormalCommissionInfo *NormalCommissionInfo `json:"normal_commission_info,omitempty"` // 该商品普通推广下的佣金信息
}

// HeadSupplierInfo 供货机构信息
type HeadSupplierInfo struct {
	Name  string `json:"name"`  // 供货机构昵称
	Appid string `json:"appid"` // 团长appid
}

// CooperativeInfo 商品合作信息
type CooperativeInfo struct {
	CooperativeStatus int    `json:"cooperative_status"` // 商品合作状态 1：合作中
	IsHidden          bool   `json:"is_hidden"`          // 商品是否在商品列表页对外隐藏
	Link              string `json:"link,omitempty"`     // 带货链接，团长商品状态上架中返回
}

// PromotionItem 商品内容
type PromotionItem struct {
	ProductId        string                  `json:"product_id"`         // 商品id
	ShopAppid        string                  `json:"shop_appid"`         // 商品所属店铺appid
	SpuSource        int                     `json:"spu_source"`         // 1-商家供货；2-机构供货
	CommissionInfo   PromotionCommissionInfo `json:"commission_info"`    // 商品佣金信息
	HeadSupplierInfo HeadSupplierInfo        `json:"head_supplier_info"` // 供货机构信息
	CooperativeInfo  CooperativeInfo         `json:"cooperative_info"`   // 商品合作信息
}

// GetPromotionDetailResponse 获取商品推广参数详情响应
type GetPromotionDetailResponse struct {
	BaseResponse
	Item PromotionItem `json:"item"` // 商品推广详情
}

// SubscribeProductRequest 机构订阅商品请求
type SubscribeProductRequest struct {
	ProductId uint64 `json:"product_id"` // 要订阅的商品ID
}

// SubscribeProductResponse 机构订阅商品响应
type SubscribeProductResponse struct {
	BaseResponse
}

// UnsubscribeProductRequest 机构取消订阅商品请求
type UnsubscribeProductRequest struct {
	ProductId uint64 `json:"product_id"` // 要取消订阅的商品ID
}

// UnsubscribeProductResponse 机构取消订阅商品响应
type UnsubscribeProductResponse struct {
	BaseResponse
}
