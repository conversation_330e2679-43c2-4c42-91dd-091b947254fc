package wxmodels

import (
	"strings"
)

// BaseResponse 微信API通用响应结构
type BaseResponse struct {
	// ErrCode 错误码
	ErrCode int `json:"errcode"`

	// ErrMsg 错误信息
	ErrMsg string `json:"errmsg"`
}

// IsSuccess 检查API调用是否成功
func (r *BaseResponse) IsSuccess() bool {
	return r.ErrCode == 0
}

// CleanErrorMsg 返回清理过的错误消息，去除rid等调试信息
func (r *BaseResponse) CleanErrorMsg() string {
	if r.IsSuccess() {
		return ""
	}

	// 如果错误消息包含"rid:"，截取前面部分
	if ridIndex := strings.Index(r.ErrMsg, " rid:"); ridIndex != -1 {
		return strings.TrimSpace(r.ErrMsg[:ridIndex])
	}

	return r.ErrMsg
}

// Error 实现error接口，方便错误处理
func (r *BaseResponse) Error() string {
	if r.IsSuccess() {
		return ""
	}
	return r.CleanErrorMsg()
}
