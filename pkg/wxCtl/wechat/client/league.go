package wxClient

import (
	"context"
	"xj-serv/pkg/wxCtl/wechat/wxmodels"

	"github.com/pkg/errors"
)

// LeagueAPI 联盟带货机构API路径
const (
	LeagueHeadSupplierShopGetPath                  = "/channels/ec/league/headsupplier/shop/get"
	LeagueHeadSupplierShopListGetPath              = "/channels/ec/league/headsupplier/shop/list/get"
	LeagueHeadSupplierProductDetailGetPath         = "/channels/ec/league/headsupplier/productdetail/get"
	LeagueHeadSupplierOrderGetPath                 = "/channels/ec/league/headsupplier/order/get"
	LeagueHeadSupplierWindowAuthGetPath            = "/channels/ec/league/headsupplier/windowauth/get"
	LeagueHeadSupplierWindowAuthStatusGetPath      = "/channels/ec/league/headsupplier/windowauth/status/get"
	LeagueHeadSupplierWindowAddPath                = "/channels/ec/league/headsupplier/window/add"
	LeagueHeadSupplierCooperativeItemListGetPath   = "/channels/ec/league/headsupplier/cooperativeitem/list/get"
	LeagueHeadSupplierSelectionProductsListGetPath = "/channels/ec/league/headsupplier/selectionproducts/list/get"
	LeagueHeadSupplierWindowGetAllPath             = "/channels/ec/league/headsupplier/window/getall"
	LeagueHeadSupplierWindowGetDetailPath          = "/channels/ec/league/headsupplier/window/getdetail"
	LeagueHeadSupplierPromotionDetailGetPath       = "/channels/ec/league/headsupplier/item/promotiondetail/get"
	LeagueHeadSupplierSubscribeProductPath         = "/channels/ec/league/headsupplier/subscription/subscribe"
	LeagueHeadSupplierUnsubscribeProductPath       = "/channels/ec/league/headsupplier/subscription/unsubscribe"
)

// GetShopDetail 获取合作小店详情
//
// 接口说明：可通过该接口获取合作小店详情
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid和获取数量
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.ShopGetResponse: 包含小店详情信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetShopDetail(
	ctx context.Context,
	req *wxmodels.ShopGetRequest,
	accessToken string,
) (*wxmodels.ShopGetResponse, error) {
	resp := &wxmodels.ShopGetResponse{}

	err := c.Post(ctx, LeagueHeadSupplierShopGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopList 获取合作小店列表
//
// 接口说明：可通过该接口获取小店列表
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含分页信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.ShopListGetResponse: 包含小店列表信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetShopList(
	ctx context.Context,
	req *wxmodels.ShopListGetRequest,
	accessToken string,
) (*wxmodels.ShopListGetResponse, error) {
	resp := &wxmodels.ShopListGetResponse{}

	err := c.Post(ctx, LeagueHeadSupplierShopListGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetProductDetail 获取商品基础详情
//
// 接口说明：可通过该接口获取最详细的通用商品信息（例如商品销量、价格、图片等）
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含商品ID和所属小店appid
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.ProductDetailGetResponse: 包含商品详情信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetProductDetail(
	ctx context.Context,
	req *wxmodels.ProductDetailGetRequest,
	accessToken string,
) (*wxmodels.ProductDetailGetResponse, error) {
	resp := &wxmodels.ProductDetailGetResponse{}

	err := c.Post(ctx, LeagueHeadSupplierProductDetailGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetCommissionOrder 获取佣金单详情
//
// 接口说明：可通过该接口获取佣金单详情
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含订单ID和商品SKU ID
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetCommissionOrderResponse: 包含佣金单详情信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetCommissionOrder(
	ctx context.Context,
	req *wxmodels.GetCommissionOrderRequest,
	accessToken string,
) (*wxmodels.GetCommissionOrderResponse, error) {
	resp := &wxmodels.GetCommissionOrderResponse{}

	err := c.Post(ctx, LeagueHeadSupplierOrderGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if resp.ErrCode == 0 {
		return resp, nil
	}

	return nil, errors.New(resp.ErrMsg)
}

// GetWindowAuth 获取达人橱窗授权链接
//
// 接口说明：可通过该接口获取达人橱窗授权链接
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含视频号finder_id、小程序用户openid或带货达人appid（三选一）
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetWindowAuthResponse: 包含授权链接信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetWindowAuth(
	ctx context.Context,
	req *wxmodels.GetWindowAuthRequest,
	accessToken string,
) (*wxmodels.GetWindowAuthResponse, error) {
	resp := &wxmodels.GetWindowAuthResponse{}

	err := c.Post(ctx, LeagueHeadSupplierWindowAuthGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetWindowAuthStatus 获取达人橱窗授权状态
//
// 接口说明：可通过该接口获取达人橱窗授权状态
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含视频号finder_id、openfinderid、带货达人appid或opentalentid（四选一）
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetWindowAuthStatusResponse: 包含授权状态信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetWindowAuthStatus(
	ctx context.Context,
	req *wxmodels.GetWindowAuthStatusRequest,
	accessToken string,
) (*wxmodels.GetWindowAuthStatusResponse, error) {
	resp := &wxmodels.GetWindowAuthStatusResponse{}

	err := c.Post(ctx, LeagueHeadSupplierWindowAuthStatusGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// AddProductToWindow 添加团长商品到橱窗
//
// 接口说明：可通过该接口添加团长商品到橱窗
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含团长appid、视频号ID和商品ID等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.BaseResponse: 通用响应
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) AddProductToWindow(
	ctx context.Context,
	req *wxmodels.AddProductToWindowRequest,
	accessToken string,
) (*wxmodels.BaseResponse, error) {
	resp := &wxmodels.BaseResponse{}

	err := c.Post(ctx, LeagueHeadSupplierWindowAddPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetCooperativeItemList 获取合作商品列表
//
// 接口说明：可通过本接口查询你在选品广场添加过的商品或者通过机构商品添加和管理接口添加过的商品
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含佣金分配类型和分页信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetCooperativeItemListResponse: 包含合作商品列表
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetCooperativeItemList(
	ctx context.Context,
	req *wxmodels.GetCooperativeItemListRequest,
	accessToken string,
) (*wxmodels.GetCooperativeItemListResponse, error) {
	resp := &wxmodels.GetCooperativeItemListResponse{}

	err := c.Post(ctx, LeagueHeadSupplierCooperativeItemListGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetSelectionProductsList 获取机构选品广场商品列表
//
// 接口说明：带货机构如果要做带货达人和商家的撮合业务，可以通过本接口拉取属于机构的联盟公开商品池
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含分页信息、计划类型和供货类型等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetSelectionProductsListResponse: 包含选品广场商品列表
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetSelectionProductsList(
	ctx context.Context,
	req *wxmodels.GetSelectionProductsListRequest,
	accessToken string,
) (*wxmodels.GetSelectionProductsListResponse, error) {
	resp := &wxmodels.GetSelectionProductsListResponse{}

	err := c.Post(ctx, LeagueHeadSupplierSelectionProductsListGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetWindowProducts 获取橱窗上团长商品列表
//
// 接口说明：可通过该接口查询橱窗上团长商品列表
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含视频号信息、达人信息、分页信息等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetWindowAllResponse: 包含橱窗商品列表
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetWindowProducts(
	ctx context.Context,
	req *wxmodels.GetWindowAllRequest,
	accessToken string,
) (*wxmodels.GetWindowAllResponse, error) {
	resp := &wxmodels.GetWindowAllResponse{}

	err := c.Post(ctx, LeagueHeadSupplierWindowGetAllPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if resp.ErrCode != 0 {
		return resp, errors.New(resp.Error())
	}

	return resp, nil
}

// GetWindowDetail 获取橱窗上团长商品详情
//
// 接口说明：可通过该接口查询橱窗上团长商品详情
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含视频号信息、达人信息、商品ID等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetWindowDetailResponse: 包含橱窗商品详情
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetWindowDetail(
	ctx context.Context,
	req *wxmodels.GetWindowDetailRequest,
	accessToken string,
) (*wxmodels.GetWindowDetailResponse, error) {
	resp := &wxmodels.GetWindowDetailResponse{}

	err := c.Post(ctx, LeagueHeadSupplierWindowGetDetailPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetPromotionDetail 获取商品推广参数详情
//
// 接口说明：可通过该接口获取合作商品的推广参数（如佣金率、推广链接等）
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含机构推广商品链接
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetPromotionDetailResponse: 包含商品推广详情
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetPromotionDetail(
	ctx context.Context,
	req *wxmodels.GetPromotionDetailRequest,
	accessToken string,
) (*wxmodels.GetPromotionDetailResponse, error) {
	resp := &wxmodels.GetPromotionDetailResponse{}

	err := c.Post(ctx, LeagueHeadSupplierPromotionDetailGetPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// SubscribeProduct 机构订阅商品
//
// 接口说明：通过该接口，带货机构可对指定商品进行订阅
// 注：订阅后，商品的基础信息或者计划信息变化时会通过回调的方式通知机构
// API文档：https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/hs_subscribe.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含要订阅的商品ID
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 使用示例：
//
//	req := &wxmodels.SubscribeProductRequest{
//	    ProductId: 10000207528319,
//	}
//	err := client.SubscribeProduct(ctx, req, accessToken)
//	if err != nil {
//	    // 处理错误
//	    log.Printf("订阅商品失败: %v", err)
//	    return
//	}
//	log.Println("商品订阅成功")
func (c *WechatClient) SubscribeProduct(
	ctx context.Context,
	req *wxmodels.SubscribeProductRequest,
	accessToken string,
) error {
	resp := &wxmodels.SubscribeProductResponse{}

	err := c.Post(ctx, LeagueHeadSupplierSubscribeProductPath, req, resp, accessToken)
	if err != nil {
		return err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp
	}

	return nil
}

// UnsubscribeProduct 机构取消订阅商品
//
// 接口说明：通过该接口，带货机构可取消指定商品的订阅
// API文档：https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/hs_unsubscribe.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含要取消订阅的商品ID
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 使用示例：
//
//	req := &wxmodels.UnsubscribeProductRequest{
//	    ProductId: 10000207528319,
//	}
//	err := client.UnsubscribeProduct(ctx, req, accessToken)
//	if err != nil {
//	    // 处理错误
//	    log.Printf("取消订阅商品失败: %v", err)
//	    return
//	}
//	log.Println("取消商品订阅成功")
func (c *WechatClient) UnsubscribeProduct(
	ctx context.Context,
	req *wxmodels.UnsubscribeProductRequest,
	accessToken string,
) error {
	resp := &wxmodels.UnsubscribeProductResponse{}

	err := c.Post(ctx, LeagueHeadSupplierUnsubscribeProductPath, req, resp, accessToken)
	if err != nil {
		return err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp
	}

	return nil
}
