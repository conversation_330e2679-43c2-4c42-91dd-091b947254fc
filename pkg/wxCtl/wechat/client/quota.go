package wxClient

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
)

// 配额查询API
const (
	QuotaGetPath = "/cgi-bin/openapi/quota/get"
)

// QuotaGetRequest 配额查询请求
type QuotaGetRequest struct {
	CgiPath string `json:"cgi_path"`
}

// QuotaInfo 配额信息
type QuotaInfo struct {
	DailyLimit int `json:"daily_limit"` // 每日调用上限
	Used       int `json:"used"`        // 已使用次数
	Remain     int `json:"remain"`      // 剩余调用次数
}

// RateLimitInfo 频率限制信息
type RateLimitInfo struct {
	CallCount     int `json:"call_count"`     // 单位时间内调用上限
	RefreshSecond int `json:"refresh_second"` // 刷新周期（秒）
}

// QuotaGetResponse 配额查询响应
type QuotaGetResponse struct {
	Errcode            int           `json:"errcode"`
	Errmsg             string        `json:"errmsg"`
	Quota              QuotaInfo     `json:"quota"`                // 配额信息
	RateLimit          RateLimitInfo `json:"rate_limit"`           // 频率限制信息
	ComponentRateLimit RateLimitInfo `json:"component_rate_limit"` // 第三方平台频率限制信息
}

// GetApiQuota 获取API配额信息
func (c *WechatClient) GetApiQuota(ctx context.Context, cgiPath string, accessToken string) {
	// 构建请求参数
	req := &QuotaGetRequest{
		CgiPath: cgiPath,
	}

	resp := &QuotaGetResponse{}
	err := c.Post(ctx, QuotaGetPath, req, resp, accessToken)
	if err != nil {
		logx.Errorf("获取API配额信息失败: %v, cgiPath: %s", err, cgiPath)
		return
	}

	// 打印配额信息
	if resp.Errcode == 0 {
		logx.Infof("API配额信息 - 路径: %s, 每日上限: %d, 已使用: %d, 剩余: %d, 频率限制: %d次/%d秒",
			cgiPath, resp.Quota.DailyLimit, resp.Quota.Used, resp.Quota.Remain,
			resp.RateLimit.CallCount, resp.RateLimit.RefreshSecond)
	} else {
		logx.Errorf("获取API配额信息失败: errcode=%d, errmsg=%s, cgiPath: %s",
			resp.Errcode, resp.Errmsg, cgiPath)
	}
}

// AsyncCheckApiQuota 异步检查API配额
// 该函数会在协程中异步查询API配额，不会阻塞主流程
func (c *WechatClient) AsyncCheckApiQuota(path string, accessToken string) {
	// 如果是配额查询本身，则不再查询配额
	if path == QuotaGetPath {
		return
	}

	// 创建一个新的上下文，避免原始上下文被取消导致配额查询失败
	quotaCtx := context.Background()

	// 使用协程异步查询配额，不阻塞主流程
	go func() {
		// 避免递归调用，创建一个新的客户端实例
		quotaClient := NewWechatClient()
		quotaClient.GetApiQuota(quotaCtx, path, accessToken)
	}()
}
