package wxClient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

// Config 微信API客户端配置
type Config struct {
	// BaseURL 微信API基础URL
	BaseURL string `json:"base_url"`

	// Timeout 请求超时时间(秒)
	Timeout int `json:"timeout"`
}

// WechatClient 微信API客户端
type WechatClient struct {
	config Config
	client *http.Client
}

// NewWechatClient 创建新的微信API客户端
func NewWechatClient() *WechatClient {
	config := Config{
		BaseURL: "https://api.weixin.qq.com",
		Timeout: 60, // 已设置为60秒
	}

	// 创建带有优化配置的HTTP客户端
	return &WechatClient{
		config: config,
		client: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:          100,
				MaxIdleConnsPerHost:   100,
				IdleConnTimeout:       90 * time.Second,
				TLSHandshakeTimeout:   10 * time.Second,
				DisableKeepAlives:     false,
				ResponseHeaderTimeout: 30 * time.Second,
				ExpectContinueTimeout: 10 * time.Second,
				DisableCompression:    true,
			},
		},
	}
}

// Post 发送POST请求到微信API
func (c *WechatClient) Post(ctx context.Context, path string, reqData interface{}, respData interface{}, accessToken string) error {
	// 构建完整URL
	url := fmt.Sprintf("%s%s?access_token=%s", c.config.BaseURL, path, accessToken)

	// 异步查询API配额
	// defer c.AsyncCheckApiQuota(path, accessToken)

	// 序列化请求数据
	var jsonData []byte
	var err error
	if reqData != nil {
		jsonData, err = json.Marshal(reqData)
		if err != nil {
			return fmt.Errorf("序列化请求数据失败: %w", err)
		}
	}

	// 添加详细日志，但避免记录完整的access_token
	logx.WithContext(ctx).Infof("微信API请求路径: %s", path)
	// 只记录access_token的前10位和后10位
	tokenLength := len(accessToken)
	var tokenLog string
	if tokenLength > 20 {
		tokenLog = accessToken[:10] + "..." + accessToken[tokenLength-10:]
	} else {
		tokenLog = accessToken
	}
	logx.WithContext(ctx).Infof("微信API请求Token(部分): %s", tokenLog)

	if len(jsonData) > 0 {
		// 限制请求体日志长度
		reqBodyLog := string(jsonData)
		if len(reqBodyLog) > 500 {
			reqBodyLog = reqBodyLog[:500] + "...(已截断)"
		}
		logx.WithContext(ctx).Infof("微信API请求体: %s", reqBodyLog)
	}

	// 重试机制
	maxRetries := 3
	var lastErr error
	var resp *http.Response
	var body []byte
	var requestDuration time.Duration

	for i := 0; i < maxRetries; i++ {
		// 为每次请求创建新的上下文，避免上下文超时传递
		requestCtx, cancel := context.WithTimeout(context.Background(), time.Duration(c.config.Timeout)*time.Second)
		defer cancel()

		// 创建请求
		req, err := http.NewRequestWithContext(requestCtx, "POST", url, bytes.NewBuffer(jsonData))
		if err != nil {
			return fmt.Errorf("创建HTTP请求失败: %w", err)
		}
		req.Header.Set("Content-Type", "application/json")
		req.Header.Set("Accept", "application/json")

		// 发送请求
		start := time.Now()

		// 使用全局客户端，但设置更短的超时时间
		resp, err = c.client.Do(req)
		requestDuration = time.Since(start)
		logx.WithContext(ctx).Infof("微信API请求耗时(第%d次尝试): %v", i+1, requestDuration)

		if err != nil {
			lastErr = err
			// 记录错误但不包含完整URL
			logx.WithContext(ctx).Errorf("微信API请求失败(第%d次尝试): %v, 路径: %s, 耗时: %v",
				i+1, err, path, requestDuration)

			// 如果不是超时错误，不再重试
			if !isTimeoutError(err) {
				break
			}

			// 等待一段时间后重试，随着重试次数增加等待时间
			time.Sleep(time.Duration(1000*(i+1)) * time.Millisecond)
			continue
		}

		// 读取响应
		body, err = io.ReadAll(resp.Body)
		resp.Body.Close()

		if err != nil {
			lastErr = fmt.Errorf("读取响应失败: %w", err)
			logx.WithContext(ctx).Errorf("读取响应失败(第%d次尝试): %v", i+1, err)
			continue
		}

		// 请求成功，跳出重试循环
		lastErr = nil
		break
	}

	// 如果所有重试都失败
	if lastErr != nil {
		return fmt.Errorf("发送HTTP请求失败(已重试%d次): %w", maxRetries, lastErr)
	}

	// 记录响应，但限制长度
	logx.WithContext(ctx).Infof("微信API响应状态码: %d", resp.StatusCode)
	respBodyLog := string(body)
	if len(respBodyLog) > 500 {
		respBodyLog = respBodyLog[:500] + "...(已截断)"
	}
	logx.WithContext(ctx).Infof("微信API响应体: %s,请求接口：%s", respBodyLog, path)

	// 反序列化响应
	if err := json.Unmarshal(body, respData); err != nil {
		return fmt.Errorf("反序列化响应失败: %w, 响应体: %s", err, respBodyLog)
	}

	return nil
}

// isTimeoutError 判断是否是超时错误
func isTimeoutError(err error) bool {
	if err == nil {
		return false
	}

	errMsg := err.Error()
	return errMsg == "context deadline exceeded" ||
		errMsg == "net/http: request canceled" ||
		errMsg == "net/http: Client.Timeout exceeded" ||
		errMsg == "i/o timeout"
}
