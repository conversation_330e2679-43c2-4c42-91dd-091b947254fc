package wxClient

import (
	"context"

	"xj-serv/pkg/wxCtl/wechat/wxmodels"
)

// PromoterAPI 推客相关API路径
const (
	PromoterGetRegisterAndBindStatusPath         = "/channels/ec/promoter/get_promoter_register_and_bind_status"
	PromoterSetSharerCommissionInfoPath          = "/channels/ec/promoter/set_sharer_commission_info"
	PromoterGetLiveRecordListPath                = "/channels/ec/promoter/get_live_record_list"
	PromoterGetLiveNoticeRecordListPath          = "/channels/ec/promoter/get_live_notice_record_list"
	PromoterGetPromoteProductListPath            = "/channels/ec/promoter/get_promote_product_list"
	PromoterGetPromoteProductDetailPath          = "/channels/ec/promoter/get_promote_product_detail"
	PromoterGetFeedListPath                      = "/channels/ec/promoter/get_feed_list"
	PromoterGetBindSharerListPath                = "/channels/ec/promoter/get_bind_sharer_list"
	PromoterGetBindTalentListPath                = "/channels/ec/promoter/get_bind_talent_list"
	PromoterGetLiveRecordQrCodePath              = "/channels/ec/promoter/get_live_record_qr_code"
	PromoterGetLiveNoticeRecordQrCodePath        = "/channels/ec/promoter/get_live_notice_record_qr_code"
	PromoterGetProductPromotionQrcodeInfoPath    = "/channels/ec/promoter/get_product_promotion_qrcode_info"
	PromoterGetProductPromotionLinkInfoPath      = "/channels/ec/promoter/get_product_promotion_link_info"
	PromoterGetFeedPromotionInfoPath             = "/channels/ec/promoter/get_feed_promotion_info"
	PromoterGetSingleProductPromotionInfoPath    = "/channels/ec/promoter/get_promoter_single_product_promotion_info"
	PromoterGetBindShopListPath                  = "/channels/ec/promoter/get_bind_shop_list"
	PromoterGetBindShopPromoterListPath          = "/channels/ec/promoter/get_bind_shop_promoter_list"
	PromoterGetShopLiveRecordListPath            = "/channels/ec/promoter/get_shop_live_record_list"
	PromoterGetShopLiveNoticeRecordListPath      = "/channels/ec/promoter/get_shop_live_notice_record_list"
	PromoterGetShopLiveCommissionProductListPath = "/channels/ec/promoter/get_shop_live_commission_product_list"
	PromoterGetShopLiveRecordQrCodePath          = "/channels/ec/promoter/get_shop_live_record_qr_code"
	PromoterGetShopLiveNoticeRecordQrCodePath    = "/channels/ec/promoter/get_shop_live_notice_record_qr_code"
	PromoterGetShopFeedListPath                  = "/channels/ec/promoter/get_shop_feed_list"
	PromoterGetShopFeedPromotionInfoPath         = "/channels/ec/promoter/get_shop_feed_promotion_info"
)

// GetPromoterRegisterAndBindStatus 获取推客的注册和绑定状态
//
// 接口说明：可通过该接口，可获取到当前推客，在推客平台上的注册状态，以及和当前机构的绑定状态
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含推客信息和注册配置
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.PromoterRegisterAndBindStatusResponse: 包含推客注册和绑定状态的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetPromoterRegisterAndBindStatus(
	ctx context.Context,
	req *wxmodels.PromoterRegisterAndBindStatusRequest,
	accessToken string,
) (*wxmodels.PromoterRegisterAndBindStatusResponse, error) {
	resp := &wxmodels.PromoterRegisterAndBindStatusResponse{}

	err := c.Post(ctx, PromoterGetRegisterAndBindStatusPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// SetSharerCommissionInfo 设置推客的分佣模式和分佣比例
//
// 接口说明：可通过该接口，设置推客的分佣类型和比例信息
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含推客分佣信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) SetSharerCommissionInfo(
	ctx context.Context,
	req *wxmodels.SetSharerCommissionInfoRequest,
	accessToken string,
) error {
	resp := &wxmodels.BaseResponse{}

	err := c.Post(ctx, PromoterSetSharerCommissionInfoPath, req, resp, accessToken)
	if err != nil {
		return err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp
	}

	return nil
}

// GetLiveRecordList 获取达人平台直播列表
//
// 接口说明：可通过该接口，获取到当前推客的直播列表
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含达人平台appid、小程序appid和推客appid
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetLiveRecordListResponse: 包含直播列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetLiveRecordList(
	ctx context.Context,
	req *wxmodels.GetLiveRecordListRequest,
	accessToken string,
) (*wxmodels.GetLiveRecordListResponse, error) {
	resp := &wxmodels.GetLiveRecordListResponse{}

	err := c.Post(ctx, PromoterGetLiveRecordListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetPromoteProductList 获取可推广的商品列表
//
// 接口说明：可通过该接口，获取可推广的商品id列表
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含商品查询条件
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetPromoteProductListResponse: 包含可推广商品列表
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetPromoteProductList(
	ctx context.Context,
	req *wxmodels.GetPromoteProductListRequest,
	accessToken string,
) (*wxmodels.GetPromoteProductListResponse, error) {
	resp := &wxmodels.GetPromoteProductListResponse{}

	err := c.Post(ctx, PromoterGetPromoteProductListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetPromoteProductDetail 获取可以推广的商品详情
//
// 接口说明：可通过该接口获取推客商品的商品详情
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含商品ID、店铺ID和计划类型
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetPromoteProductDetailResponse: 包含商品详情信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetPromoteProductDetail(
	ctx context.Context,
	req *wxmodels.GetPromoteProductDetailRequest,
	accessToken string,
) (*wxmodels.GetPromoteProductDetailResponse, error) {
	resp := &wxmodels.GetPromoteProductDetailResponse{}

	err := c.Post(ctx, PromoterGetPromoteProductDetailPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetFeedList 获取达人平台推广的短视频信息
//
// 接口说明：可通过该接口，可获取达人平台推广的短视频信息
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/video/get_feed_list.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含分页和可选的达人appid
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetFeedListResponse: 包含短视频列表信息
//   - error: 请求过程中的错误
func (c *WechatClient) GetFeedList(
	ctx context.Context,
	req *wxmodels.GetFeedListRequest,
	accessToken string,
) (*wxmodels.GetFeedListResponse, error) {
	resp := &wxmodels.GetFeedListResponse{}

	err := c.Post(ctx, PromoterGetFeedListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetBindSharerList 获取机构绑定的推客列表
// 文档: https://developers.weixin.qq.com/doc/channels/API/league/promoter/get_sharer_list.html
func (c *WechatClient) GetBindSharerList(
	ctx context.Context,
	req *wxmodels.GetBindSharerListReq,
	accessToken string,
) (*wxmodels.GetBindSharerListResp, error) {
	resp := &wxmodels.GetBindSharerListResp{}

	err := c.Post(ctx, PromoterGetBindSharerListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetBindTalentList 获取机构绑定的达人列表
func (c *WechatClient) GetBindTalentList(
	ctx context.Context,
	req *wxmodels.GetBindTalentListReq,
	accessToken string,
) (*wxmodels.GetBindTalentListResp, error) {
	resp := &wxmodels.GetBindTalentListResp{}

	err := c.Post(ctx, PromoterGetBindTalentListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetLiveNoticeRecordList 获取达人平台直播预约列表
//
// 接口说明：可通过该接口，可获取达人平台的直播预约列表
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含达人平台appid和小程序appid
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetLiveNoticeRecordListResponse: 包含直播预约列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetLiveNoticeRecordList(
	ctx context.Context,
	req *wxmodels.GetLiveNoticeRecordListRequest,
	accessToken string,
) (*wxmodels.GetLiveNoticeRecordListResponse, error) {
	resp := &wxmodels.GetLiveNoticeRecordListResponse{}

	err := c.Post(ctx, PromoterGetLiveNoticeRecordListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetLiveRecordQrCode 为某个推客生成单个直播的推广二维码
//
// 接口说明：可通过该接口，获取推客推广特定直播的二维码URL
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_live_record_qr_code.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含达人平台appid、推客appid和直播id
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetLiveRecordQrCodeResponse: 包含二维码URL的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetLiveRecordQrCode(
	ctx context.Context,
	req *wxmodels.GetLiveRecordQrCodeRequest,
	accessToken string,
) (*wxmodels.GetLiveRecordQrCodeResponse, error) {
	resp := &wxmodels.GetLiveRecordQrCodeResponse{}

	err := c.Post(ctx, PromoterGetLiveRecordQrCodePath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetLiveNoticeRecordQrCode 为某个推客生成单个直播预约的推广二维码
//
// 接口说明：可通过该接口，获取推客推广特定直播预约的二维码URL
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_live_notice_record_qr_code.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含达人平台appid、推客appid和直播预约id
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetLiveNoticeRecordQrCodeResponse: 包含二维码URL的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetLiveNoticeRecordQrCode(
	ctx context.Context,
	req *wxmodels.GetLiveNoticeRecordQrCodeRequest,
	accessToken string,
) (*wxmodels.GetLiveNoticeRecordQrCodeResponse, error) {
	resp := &wxmodels.GetLiveNoticeRecordQrCodeResponse{}

	err := c.Post(ctx, PromoterGetLiveNoticeRecordQrCodePath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetProductPromotionQrcodeInfo 获取推客商品推广二维码
//
// 接口说明：可通过该接口，获取推客对某个商品的推广二维码
// API Doc: https://developers.weixin.qq.com/doc/channels/API/league/promoter/get_product_promotion_qrcode_info.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含推客信息和商品信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetProductPromotionQrcodeInfoResponse: 包含二维码URL的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetProductPromotionQrcodeInfo(
	ctx context.Context,
	req *wxmodels.GetProductPromotionQrcodeInfoRequest,
	accessToken string,
) (*wxmodels.GetProductPromotionQrcodeInfoResponse, error) {
	resp := &wxmodels.GetProductPromotionQrcodeInfoResponse{}

	err := c.Post(ctx, PromoterGetProductPromotionQrcodeInfoPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetProductPromotionLinkInfo 获取推客商品推广短链
//
// 接口说明：可通过该接口，获取推客对某个商品的推广短链
// API Doc: https://developers.weixin.qq.com/doc/channels/API/league/promoter/get_product_promotion_link_info.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含推客信息和商品信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetProductPromotionLinkInfoResponse: 包含推广短链的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetProductPromotionLinkInfo(
	ctx context.Context,
	req *wxmodels.GetProductPromotionLinkInfoRequest,
	accessToken string,
) (*wxmodels.GetProductPromotionLinkInfoResponse, error) {
	resp := &wxmodels.GetProductPromotionLinkInfoResponse{}

	err := c.Post(ctx, PromoterGetProductPromotionLinkInfoPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetLiveCommissionProductList 获取某个达人平台当前直播的带货商品列表
//
// 接口说明：可通过该接口，获取某个达人平台当前直播的带货商品列表
// API Doc: https://developers.weixin.qq.com/doc/channels/API/league/promoter/get_live_commission_product_list.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含达人平台appid、分页参数等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetLiveCommissionProductListResponse: 包含商品列表的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetLiveCommissionProductList(
	ctx context.Context,
	req *wxmodels.GetLiveCommissionProductListRequest,
	accessToken string,
) (*wxmodels.GetLiveCommissionProductListResponse, error) {
	resp := &wxmodels.GetLiveCommissionProductListResponse{}

	err := c.Post(ctx, "/channels/ec/promoter/get_live_commission_product_list", req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetFeedPromotionInfo 获取短视频推广信息
//
// 接口说明：可通过该接口，获取某个达人平台的某些短视频内嵌短视频卡片需要的feedtoken
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/video/get_feed_promotion_info.html
//
// 参数：
//   - ctx: 上下文
//   - req: 请求参数，包含达人平台appid、小程序appid、短视频列表等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetFeedPromotionInfoResponse: 包含短视频推广信息的响应
//   - error: 请求过程中的错误
func (c *WechatClient) GetFeedPromotionInfo(
	ctx context.Context,
	req *wxmodels.GetFeedPromotionInfoRequest,
	accessToken string,
) (*wxmodels.GetFeedPromotionInfoResponse, error) {
	resp := &wxmodels.GetFeedPromotionInfoResponse{}

	err := c.Post(ctx, PromoterGetFeedPromotionInfoPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetPromoterSingleProductPromotionInfo 获取某个推客某个商品的内嵌商品卡片
//
// 接口说明：可通过该接口，获取某个推客某个商品的内嵌商品卡片product_promotion_link，长期有效
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含推客信息、商品信息和供货机构信息
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetPromoterSingleProductPromotionInfoResponse: 包含内嵌商品卡片推广参数
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetPromoterSingleProductPromotionInfo(
	ctx context.Context,
	req *wxmodels.GetPromoterSingleProductPromotionInfoRequest,
	accessToken string,
) (*wxmodels.GetPromoterSingleProductPromotionInfoResponse, error) {
	resp := &wxmodels.GetPromoterSingleProductPromotionInfoResponse{}

	err := c.Post(ctx, PromoterGetSingleProductPromotionInfoPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetBindShopList 获取合作的小店列表
//
// 接口说明：可通过该接口，可获取合作的小店列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/shop/get_bind_shop_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含分页参数
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetBindShopListResponse: 包含合作小店列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetBindShopList(
	ctx context.Context,
	req *wxmodels.GetBindShopListRequest,
	accessToken string,
) (*wxmodels.GetBindShopListResponse, error) {
	resp := &wxmodels.GetBindShopListResponse{}

	err := c.Post(ctx, PromoterGetBindShopListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetBindShopPromoterList 获取合作小店的关联账号列表
//
// 接口说明：可通过该接口，可获取合作小店的关联账号列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/shop/get_bind_shop_promoter_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid和分页参数
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetBindShopPromoterListResponse: 包含关联账号列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
func (c *WechatClient) GetBindShopPromoterList(
	ctx context.Context,
	req *wxmodels.GetBindShopPromoterListRequest,
	accessToken string,
) (*wxmodels.GetBindShopPromoterListResponse, error) {
	resp := &wxmodels.GetBindShopPromoterListResponse{}

	err := c.Post(ctx, PromoterGetBindShopPromoterListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopLiveRecordList 获取小店关联账号的直播列表
//
// 接口说明：可通过该接口，可获取小店关联账号的直播列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_shop_live_record_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息、小程序appid等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopLiveRecordListResponse: 包含直播列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 小程序开发调用说明：
//
//	// 以内嵌直播组件方式调用
//	<channel-live
//	  feed-id="${export_id}"
//	  promoterShareLink="${promoter_share_link}"
//	/>
//
//	// 以jsapi跳转方式调用
//	wx.openChannelsLive({
//	    "feedId": "${export_id}",
//	    "promoterShareLink": "${promoter_share_link}"
//	})
func (c *WechatClient) GetShopLiveRecordList(
	ctx context.Context,
	req *wxmodels.GetShopLiveRecordListRequest,
	accessToken string,
) (*wxmodels.GetShopLiveRecordListResponse, error) {
	resp := &wxmodels.GetShopLiveRecordListResponse{}

	err := c.Post(ctx, PromoterGetShopLiveRecordListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopLiveNoticeRecordList 获取小店关联账号的预约直播列表
//
// 接口说明：可通过该接口，可获取小店关联账号的预约直播列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_shop_live_notice_record_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopLiveNoticeRecordListResponse: 包含预约直播列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
func (c *WechatClient) GetShopLiveNoticeRecordList(
	ctx context.Context,
	req *wxmodels.GetShopLiveNoticeRecordListRequest,
	accessToken string,
) (*wxmodels.GetShopLiveNoticeRecordListResponse, error) {
	resp := &wxmodels.GetShopLiveNoticeRecordListResponse{}

	err := c.Post(ctx, PromoterGetShopLiveNoticeRecordListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopLiveCommissionProductList 获取小店关联账号的当前直播的自营商品列表
//
// 接口说明：可通过该接口，可获取小店关联账号的当前直播的自营商品列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_shop_live_commission_product_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息、分页参数等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopLiveCommissionProductListResponse: 包含自营商品列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
//   - 每页最大返回10个商品
//   - 商品价格和佣金金额单位均为分
func (c *WechatClient) GetShopLiveCommissionProductList(
	ctx context.Context,
	req *wxmodels.GetShopLiveCommissionProductListRequest,
	accessToken string,
) (*wxmodels.GetShopLiveCommissionProductListResponse, error) {
	resp := &wxmodels.GetShopLiveCommissionProductListResponse{}

	err := c.Post(ctx, PromoterGetShopLiveCommissionProductListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopLiveRecordQrCode 为推客生成小店关联账号直播的推广二维码
//
// 接口说明：可通过该接口，为推客生成小店关联账号直播的推广二维码
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_shop_live_record_qr_code.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息、直播id、推客appid等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopLiveRecordQrCodeResponse: 包含推广二维码URL的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
//   - 直播ID可从"获取小店关联账号的直播列表"接口中获取
//   - 推客appid必须是已绑定的推客
func (c *WechatClient) GetShopLiveRecordQrCode(
	ctx context.Context,
	req *wxmodels.GetShopLiveRecordQrCodeRequest,
	accessToken string,
) (*wxmodels.GetShopLiveRecordQrCodeResponse, error) {
	resp := &wxmodels.GetShopLiveRecordQrCodeResponse{}

	err := c.Post(ctx, PromoterGetShopLiveRecordQrCodePath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopLiveNoticeRecordQrCode 为推客生成小店关联账号直播预约的推广二维码
//
// 接口说明：可通过该接口，为推客生成小店关联账号直播预约的推广二维码
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/live/get_shop_live_notice_record_qr_code.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息、直播预约id、推客appid等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopLiveNoticeRecordQrCodeResponse: 包含推广二维码URL的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
//   - 直播预约ID可从"获取小店关联账号的预约直播列表"接口中获取
//   - 推客appid必须是已绑定的推客
//   - 此接口用于预约直播的推广，与直播推广二维码不同
func (c *WechatClient) GetShopLiveNoticeRecordQrCode(
	ctx context.Context,
	req *wxmodels.GetShopLiveNoticeRecordQrCodeRequest,
	accessToken string,
) (*wxmodels.GetShopLiveNoticeRecordQrCodeResponse, error) {
	resp := &wxmodels.GetShopLiveNoticeRecordQrCodeResponse{}

	err := c.Post(ctx, PromoterGetShopLiveNoticeRecordQrCodePath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopFeedList 获取小店关联账号的短视频列表
//
// 接口说明：可通过该接口，可获取某个小店关联账号的短视频列表
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/video/get_shop_feed_list.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含小店appid、关联账号信息、分页参数等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopFeedListResponse: 包含短视频列表的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
//   - 每页最大返回10个短视频
//   - 预期佣金金额单位为分
func (c *WechatClient) GetShopFeedList(
	ctx context.Context,
	req *wxmodels.GetShopFeedListRequest,
	accessToken string,
) (*wxmodels.GetShopFeedListResponse, error) {
	resp := &wxmodels.GetShopFeedListResponse{}

	err := c.Post(ctx, PromoterGetShopFeedListPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}

// GetShopFeedPromotionInfo 生成小店关联账号的短视频内嵌小程序推广信息
//
// 接口说明：可通过该接口，生成小店关联账号的短视频内嵌小程序推广信息
// API Doc: https://developers.weixin.qq.com/doc/store/leagueheadsupplier/API/promotion/content/video/get_shop_feed_promotion_info.html
//
// 参数：
//   - ctx: 上下文，用于请求跟踪和取消
//   - req: 请求参数，包含短视频列表、小店appid、关联账号信息、小程序appid等
//   - accessToken: 接口调用凭证
//
// 返回值：
//   - *wxmodels.GetShopFeedPromotionInfoResponse: 包含短视频推广信息的详细信息
//   - error: 请求过程中发生的错误，如果成功则为nil
//
// 注意事项：
//   - 关联账号类型必须是视频号(PromoterTypeVideo)
//   - 关联账号ID可从"获取合作小店的关联账号列表"接口中获取
//   - 小程序appid需要和机构绑定
//   - 推客appid为可选参数，用于生成某个推客推广的link
//
// 小程序开发调用说明：
//
//	// 以内嵌短视频组件方式调用
//	<channel-video
//	  feed-token="${feed_token}"
//	  promoterShareLink="${promoter_share_link}"
//	/>
//
//	// 以jsapi跳转方式调用
//	wx.openChannelsActivity({
//	    "feedId": "${export_id}",
//	    "promoterShareLink": "${promoter_share_link}"
//	})
func (c *WechatClient) GetShopFeedPromotionInfo(
	ctx context.Context,
	req *wxmodels.GetShopFeedPromotionInfoRequest,
	accessToken string,
) (*wxmodels.GetShopFeedPromotionInfoResponse, error) {
	resp := &wxmodels.GetShopFeedPromotionInfoResponse{}

	err := c.Post(ctx, PromoterGetShopFeedPromotionInfoPath, req, resp, accessToken)
	if err != nil {
		return nil, err
	}

	// 检查业务错误
	if !resp.IsSuccess() {
		return resp, resp
	}

	return resp, nil
}
