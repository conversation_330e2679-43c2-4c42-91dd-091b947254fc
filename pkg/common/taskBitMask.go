package common

import (
	"fmt"
	"strings"
)

// TaskBitmask 基于位掩码的任务状态管理器
// 核心能力：
// 1. 用单个整数记录多个任务状态（每个任务对应一个二进制位）
// 2. 支持动态增删/查询任务完成状态
// 3. 二进制与人类可读格式互相转换
//
//
// ================= 使用示例 =================
//mask := NewTaskBitmask()
//
//// 标记任务2和3完成 -> 0110
//mask.CompleteTask(2).CompleteTask(3)
//
//// 输出状态
//fmt.Printf("当前掩码值（十进制）: %d\n", mask.mask)             // 输出: 6
//fmt.Printf("二进制表示: %s\n", mask.GetBinaryString())       // 输出: 0110
//fmt.Println("已完成任务:", mask.GetCompletedTasks())       // 输出: [2 3]
//fmt.Println("任务3是否完成?", mask.IsTaskCompleted(3))     // 输出: true
//
//// 从二进制字符串加载
//newMask := NewTaskBitmask()
//err := newMask.LoadFromBinary("0101") // 任务1,3完成
//if err != nil {
//	panic(err)
//}
//fmt.Println("\n从二进制加载后:")
//fmt.Println("已完成任务:", newMask.GetCompletedTasks()) // 输出: [1 3]
//

type TaskBitmask struct {
	mask int // 底层存储的位掩码值
	bits int // 位数，默认为4
}

// NewTaskBitmask 初始化一个空的任务掩码（所有任务未完成）
// 可选参数bits指定位数，默认为4
func NewTaskBitmask(bits ...int) *TaskBitmask {
	bitCount := 4 // 默认4位
	if len(bits) > 0 && bits[0] > 0 {
		bitCount = bits[0]
	}
	return &TaskBitmask{mask: 0, bits: bitCount}
}

// ================= 核心操作 =================

// CompleteTask 标记任务完成（支持链式调用）
// 参数：taskID 范围1-4（对应二进制位0-3）
func (t *TaskBitmask) CompleteTask(taskID int) *TaskBitmask {
	t.validateTaskID(taskID)
	t.mask |= 1 << (taskID - 1)
	return t
}

// UndoTask 取消任务完成状态
func (t *TaskBitmask) UndoTask(taskID int) *TaskBitmask {
	t.validateTaskID(taskID)
	t.mask &= ^(1 << (taskID - 1))
	return t
}

// IsTaskCompleted 检查任务是否完成
func (t *TaskBitmask) IsTaskCompleted(taskID int) bool {
	t.validateTaskID(taskID)
	return (t.mask & (1 << (taskID - 1))) != 0
}

// ================= 解析与转换 =================

// GetCompletedTasks 获取已完成任务ID列表（按升序）
func (t *TaskBitmask) GetCompletedTasks() []int {
	var tasks []int
	for i := 0; i < 4; i++ {
		if t.mask&(1<<i) != 0 {
			tasks = append(tasks, i+1)
		}
	}
	return tasks
}

// ================= 辅助方法 =================
// validateTaskID 验证任务ID是否在有效范围内
func (t *TaskBitmask) validateTaskID(taskID int) {
	if taskID < 1 || taskID > t.bits {
		panic(fmt.Sprintf("任务ID必须在1-%d范围内，当前输入：%d", t.bits, taskID))
	}
}

// IsAllCompleted 检查是否所有任务都已完成
func (t *TaskBitmask) IsAllCompleted(totalTasks ...int) bool {
	tasks := t.bits
	if len(totalTasks) > 0 && totalTasks[0] > 0 {
		tasks = totalTasks[0]
	}

	// 创建一个全部为1的掩码，长度为tasks
	allCompleted := (1 << tasks) - 1
	return t.mask == allCompleted
}

// GetBinaryString 获取二进制格式字符串
func (t *TaskBitmask) GetBinaryString() string {
	return fmt.Sprintf("%0*b", t.bits, t.mask)
}

// LoadFromBinary 从二进制字符串加载状态
func (t *TaskBitmask) LoadFromBinary(s string) error {
	// 移除可能的前缀/空格
	s = strings.TrimSpace(s)

	// 如果是MySQL的bit类型返回的[]byte，需要转换
	if len(s) > 0 && s[0] != '0' && s[0] != '1' {
		// 将[]byte转换为二进制字符串
		binaryStr := ""
		for _, b := range []byte(s) {
			binaryStr += fmt.Sprintf("%08b", b)
		}

		// 截取需要的位数
		if len(binaryStr) > t.bits {
			binaryStr = binaryStr[len(binaryStr)-t.bits:]
		} else {
			// 如果长度不足，前面补0
			for len(binaryStr) < t.bits {
				binaryStr = "0" + binaryStr
			}
		}
		s = binaryStr
	}

	if len(s) != t.bits {
		return fmt.Errorf("二进制长度必须为%d位", t.bits)
	}

	var mask int
	for i, ch := range s {
		if ch == '1' {
			mask |= 1 << (t.bits - 1 - i) // 注意字符串从左到右是高位到低位
		} else if ch != '0' {
			return fmt.Errorf("非法字符：%c", ch)
		}
	}
	t.mask = mask
	return nil
}

// ToBytesForDB 将位掩码转换为适合MySQL bit类型的[]byte
func (t *TaskBitmask) ToBytesForDB() []byte {
	binaryStr := t.GetBinaryString()

	// 确保二进制字符串长度为8的倍数
	for len(binaryStr)%8 != 0 {
		binaryStr = "0" + binaryStr
	}

	var result []byte
	for i := 0; i < len(binaryStr); i += 8 {
		end := i + 8
		if end > len(binaryStr) {
			end = len(binaryStr)
		}

		byteVal := 0
		for j := i; j < end; j++ {
			if binaryStr[j] == '1' {
				byteVal |= 1 << (7 - (j - i))
			}
		}
		result = append(result, byte(byteVal))
	}

	return result
}

// ToBitFormat 将任务位掩码转换为MySQL bit类型格式
func (t *TaskBitmask) ToBitFormat() []byte {
	// 创建一个字节数组，长度为掩码位数/8（向上取整）
	byteLen := (t.bits + 7) / 8
	bytes := make([]byte, byteLen)

	// 将位掩码转换为字节数组
	for i := 0; i < t.bits; i++ {
		if t.IsTaskCompleted(i + 1) {
			byteIndex := i / 8
			bitPosition := uint(7 - (i % 8)) // MySQL中bit是从左到右存储的
			bytes[byteIndex] |= 1 << bitPosition
		}
	}

	return bytes
}

// 从MySQL bit(8)格式加载的方法
func (t *TaskBitmask) LoadFromBitFormat(data []byte) error {
	if len(data) == 0 {
		t.mask = 0
		return nil
	}

	// 将[]byte转换为uint8
	t.mask = int(uint(data[0]))
	return nil
}
