package errutil

import (
	"regexp"
	"strings"

	"xj-serv/pkg/result/xerr"

	"google.golang.org/grpc/status"

	"github.com/zeromicro/go-zero/core/logx"
	"golang.org/x/net/context"
)

// ExtractRpcErrMsg 从RPC错误中提取干净的错误信息
// 移除gRPC错误前缀和rid调试信息
func ExtractRpcErrMsg(err error) string {
	if err == nil {
		return ""
	}

	// 从错误中获取错误消息
	errMsg := err.Error()

	// 尝试将错误转换为gRPC状态
	if st, ok := status.FromError(err); ok {
		errMsg = st.Message()
	}

	// 使用正则表达式移除gRPC错误前缀
	re := regexp.MustCompile(`rpc error: code = Code\(\d+\) desc = `)
	errMsg = re.ReplaceAllString(errMsg, "")

	// 去除尾部的rid信息
	if ridIndex := strings.Index(errMsg, " rid:"); ridIndex != -1 {
		errMsg = strings.TrimSpace(errMsg[:ridIndex])
	}

	return errMsg
}

// NewRpcError 将RPC错误转换为xerr.CodeError
// 保留原始错误码，但清理错误消息
func NewRpcError(ctx context.Context, err error) error {
	if err == nil {
		return nil
	}

	// 记录原始错误到日志
	logx.WithContext(ctx).Errorf("RPC调用错误: %v", err)

	// 提取干净的错误消息
	errMsg := ExtractRpcErrMsg(err)

	// 尝试获取错误码
	var errCode uint32 = xerr.ServerCommonError // 默认服务器通用错误
	if st, ok := status.FromError(err); ok {
		// 获取错误码 - gRPC错误码为int32，我们需要转为uint32
		// 如果是负数，我们使用默认错误码
		code := st.Code()
		if code > 0 {
			errCode = uint32(code)
		}
	}

	// 创建新的错误
	return xerr.NewErrCodeMsg(errCode, errMsg)
}

// WrapRpcError 包装RPC错误为xerr.CodeError
// 无需保留原始错误码，统一使用指定的错误码
func WrapRpcError(ctx context.Context, err error, errCode uint32) error {
	if err == nil {
		return nil
	}

	// 记录原始错误到日志
	logx.WithContext(ctx).Errorf("RPC调用错误: %v", err)

	// 提取干净的错误消息
	errMsg := ExtractRpcErrMsg(err)

	// 创建新的错误
	return xerr.NewErrCodeMsg(errCode, errMsg)
}

// WrapRpcErrorWithMsg 包装RPC错误，使用自定义消息前缀
// 保留原始错误的关键部分，但添加自定义消息前缀
func WrapRpcErrorWithMsg(ctx context.Context, err error, errCode uint32, msgPrefix string) error {
	if err == nil {
		return nil
	}

	// 记录原始错误到日志
	logx.WithContext(ctx).Errorf("RPC调用错误: %v", err)

	// 提取干净的错误消息
	errMsg := ExtractRpcErrMsg(err)

	// 如果提供了消息前缀，则添加前缀
	if msgPrefix != "" {
		errMsg = msgPrefix + ": " + errMsg
	}

	// 创建新的错误
	return xerr.NewErrCodeMsg(errCode, errMsg)
}
