package errutil

import (
	"net/http"
	"xj-serv/pkg/result"
	"xj-serv/pkg/result/xerr"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
	"google.golang.org/grpc/status"
)

// RpcErrorHandler 统一处理API层的RPC错误
// 可以作为go-zero的错误处理器使用
func RpcErrorHandler(err error) (int, interface{}) {
	// 先尝试从gRPC错误中提取错误码和错误消息
	var errCode uint32 = xerr.ServerCommonError
	errMsg := ExtractRpcErrMsg(err)

	// 检查是否为gRPC状态错误
	if st, ok := status.FromError(err); ok {
		code := st.Code()
		if code > 0 {
			errCode = uint32(code)
		}
	} else if e, ok := err.(*xerr.CodeError); ok {
		// 检查是否为自定义错误
		errCode = e.GetErrCode()
	}

	// 记录错误日志
	logx.Errorf("API错误: %+v, 处理后错误码: %d, 错误消息: %s", err, errCode, errMsg)

	// 返回统一的错误响应格式
	return http.StatusOK, result.Error(errCode, errMsg)
}

// RpcErrorMiddleware 创建一个中间件，用于统一处理API层的RPC错误
// 注册方式: server.Use(errutil.RpcErrorMiddleware())
func RpcErrorMiddleware() rest.Middleware {
	return func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 设置自定义错误处理器
			httpx.SetErrorHandler(RpcErrorHandler)

			// 调用下一个处理器
			next(w, r)
		}
	}
}
